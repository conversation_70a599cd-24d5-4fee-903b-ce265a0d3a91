// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { getAnalytics } from "firebase/analytics";
import { getAuth, RecaptchaVerifier, signInWithPhoneNumber } from "firebase/auth";
// TODO: Add SDKs for Firebase products that you want to use
// https://firebase.google.com/docs/web/setup#available-libraries

// Your web app's Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: "AIzaSyDT6ZwD1Ge8TqgedTe46dqx23RJHdp8soI",
  authDomain: "yateem-opticians-wc.firebaseapp.com",
  projectId: "yateem-opticians-wc",
  storageBucket: "yateem-opticians-wc.appspot.com",
  messagingSenderId: "283876162544",
  appId: "1:283876162544:web:8c84783ee2398aa7dbfb35",
  measurementId: "G-NKWM47K258",
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const analytics = getAnalytics(app);
const auth = getAuth(app);
const initFirebase = () => {
  return app;
};
export { auth, initFirebase, RecaptchaVerifier, signInWithPhoneNumber };
