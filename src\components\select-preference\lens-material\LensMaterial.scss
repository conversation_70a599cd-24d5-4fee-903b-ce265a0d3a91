.lens-meterial {

    &_box {
        border: 1px solid rgba(228, 228, 228, 1);
        border-radius: 1.5rem;
        padding: 1.3rem;
        cursor: pointer;

        &.isActive {
            border: 2px solid rgba(0, 0, 0, 1);
            cursor: pointer;
        }

        &:last-child {
            &.isActive {
                .lens-meterial_choose-color ul li.isActive {
                    border: 3px solid rgb(255, 255, 255);
                    outline: 2px solid #000000;
                    position: relative;

                    &::before {
                        content: "";
                        background-image: url(../../../../public/images/modal/tick.svg);
                        background-repeat: no-repeat;
                        background-position: center;
                        width: 100%;
                        height: 100%;
                    }
                }
            }
        }

        &:not(:last-child) {
            margin-bottom: 2rem;
        }

        img {
            width: 8rem;
            height: 7rem;
        }

        h5 {
            font-size: 1.4rem;
            font-weight: 500;
            line-height: 2.1rem;
            letter-spacing: -0.011em;
            color: #000000;
        }

        h6 {
            font-size: 1.3rem;
            font-weight: 400;
            line-height: 1.6rem;
            color: #000000;
        }

        span {
            color: rgba(55, 65, 81, 1);
            font-size: 1.4rem;
            font-weight: 400;
            margin-top: 1.5rem;
            display: inline-block;
        }
    }

    &_flex {
        display: flex;
        column-gap: 1rem;
        align-items: center;
    }

    &_choose-color {
        padding-left: 9rem;

        span {
            font-size: 1.4rem;
            font-weight: 400;
            line-height: 1.7rem;
            margin-top: 0rem;
            color: rgba(55, 65, 81, 0.5);

        }

        ul {
            display: flex;
            column-gap: 1.2rem;
            justify-content: flex-start !important;
            margin-bottom: 0 !important;
            margin-top: 0.5rem;

            li {
                width: 3.8rem;
                height: 3.8rem;
                border-radius: 50%;

                // &.isActive {
                //     border: 3px solid rgb(255, 255, 255);
                //     outline: 2px solid #000000;
                //     position: relative;
                // }

                &::after {
                    content: none !important;
                }
            }
        }
    }
}