import React, { Fragment, useContext, useEffect, useState } from "react";
import useFilter from "@/hooks/useFilter";
import { useCallback } from "react";
import { useSearchParams } from "next/navigation";
import { FilterContext } from "@/contexts/FilterContaxt";
import { TranslationContext } from "@/contexts/Translation";

const AgeGroupData = [
  {
    age: "2-5 yrs (22)",
  },

  {
    age: " 5-8 yrs (38)",
  },

  {
    age: "   8-12 yrs (53)",
  },
];

function CheckBoxFilter({ filter, name, getCount, childs, left }: any) {

  const { active, setActive } = useFilter(name);
  const { allFilters }: any = useContext(FilterContext)
  const { translation }: any = useContext(TranslationContext)
  const [visibleItems, setVisibleItems] = useState(12);
  const handleFilter = (e: any) => {
    let value = e.target.value;
    setActive((prevActive) => {
      if (prevActive.includes(value)) {
        return prevActive.filter((item) => item !== value);
      } else {
        return [...prevActive, value];
      }
    });
    allFilters?.[name]?.setActive((prevActive: any) => {
      if (prevActive.includes(value)) {
        return prevActive.filter((item: any) => item !== value);
      } else {
        return [...prevActive, value];
      }
    })
  }

  const handleViewMore = () => {
    setVisibleItems((prev: any) => prev + 12);
  };
  console.log(filter)
  return (
    <>
      {filter?.slice(0, visibleItems)?.map((items: any) => (
        <Fragment key={items?._id}>
          <label key={items?._id} htmlFor={items?._id}
            style={{
              marginLeft: left ? '1.5rem' : 0,
              cursor: items?.active != undefined ? !items?.active ? "not-allowed" : "pointer" : "pointer"
            }}
          >
            {!items.title && <input
              type="checkbox"
              id={items?._id}
              value={items?._id}
              onChange={handleFilter}
              checked={active.includes(items?._id)}
              name={name}
              className="age-input"
              disabled={items.title || (items?.active != undefined ? !items?.active : false)}
            />}
            <span style={{ opacity: (items.title || (items?.active != undefined ? !items?.active : false)) ? 0.7 : 1 }}>{items.title ? items.title : items.name}
              {(getCount && (!items.title && (items?.active != undefined ? items?.active : true))) && <>(<Count items={items} active={active} getCount={getCount} name={name} />)</>}
            </span>
          </label>
          {(items?.childs) &&
            <CheckBoxFilter getCount={getCount} name={"subCategory"} left={true} filter={items.childs} />}
        </Fragment>
      ))}
      {visibleItems < filter?.length && (
        <button onClick={handleViewMore} className="view-more-button">
          {translation?.productListing?.viewMore ?? "View More"}
        </button>
      )}
    </>
  );
}

function Count({ items, active, getCount, name }: any) {
  const [count, setCount] = useState(0)
  const params = useSearchParams()

  useEffect(() => {
    async function displayCount() {
      try {
        const paramsObject: any = {};

        for (const [key, value] of params.entries()) {
          if (!paramsObject[key]) {
            paramsObject[key] = [value];
          } else {
            paramsObject[key].push(value);
          }
        }

        if (name === "subChildCategory" || name === "subCategory" || name === "color") {
          paramsObject[name] = [items?._id]
        } else {
          paramsObject[name] = [items.name]
        }

        const count = await getCount(1, paramsObject);
        setCount(count)
      } catch (error) {
        return null
      }
    }
    displayCount()
  }, [active])

  return count
}

export default CheckBoxFilter;
