import React from "react";

export default function LogoIcon(props: React.ComponentProps<"svg">) {
  return (
    <svg {...props} xmlns="http://www.w3.org/2000/svg" fill="none">
      <g fill="#fff" fill-rule="evenodd" clip-path="url(#a)" clip-rule="evenodd">
        <path d="M165.501 28.15c.804-2.701 2.143-4.755 4.04-6.139 1.898-1.406 4.286-2.12 7.165-2.12 6.607 0 12.41 6.807 14.687 12.343a22.865 22.865 0 0 1 1.138 14.061c1.384-1.674 2.478-3.549 3.326-5.624 1.049-2.59 1.584-5.268 1.584-7.99 0-2.813-.491-5.47-1.45-7.947-.96-2.477-2.389-4.71-4.263-6.696-2.054-2.187-4.375-3.861-6.964-5.022a19.123 19.123 0 0 0-8.036-1.763c-3.481 0-6.763.781-9.798 2.388-3.036 1.585-5.602 3.84-7.745 6.719a18.222 18.222 0 0 0-2.879 5.825c-3.214 10.982 3.727 9.464 9.798 9.42h21.628c-.312 2.9-1.473 5.2-3.459 6.874-1.987 1.674-4.554 2.522-7.701 2.522-2.098 0-3.995-.447-5.669-1.317-8.058-4.24-2.321-3.705-14.106-3.705 1.718 4.397 4.263 7.812 7.678 10.245 3.415 2.433 7.343 3.638 11.829 3.638 3.014 0 5.781-.536 8.303-1.585a20.38 20.38 0 0 0 5.67-3.616c1.897-4.932 1.897-10.557-.223-15.847-2.165-5.357-3.482-4.642-9.196-4.642h-15.357v-.023ZM84.011 52.924h-6.763a2.462 2.462 0 0 1-2.455-2.455v-.402a21.277 21.277 0 0 1-12.097 3.75c-11.763 0-21.294-9.464-21.294-21.137s9.531-21.137 21.294-21.137c11.762 0 21.293 9.464 21.293 21.137v20.244h.022ZM62.718 20.382c6.674 0 12.097 5.513 12.097 12.32 0 6.808-5.423 12.321-12.097 12.321-6.674 0-12.098-5.513-12.098-12.32 0-6.808 5.424-12.321 12.098-12.321ZM238.979 25.315c1.54-2.456 4.218-4.085 7.209-4.085 4.71 0 8.616 4.018 8.616 8.883V54.22h4.307c2.679 0 4.888-2.299 4.888-5.111V30.113c0-10.11-8.012-18.391-17.811-18.391-4.576 0-8.749 1.808-11.919 4.754a19.872 19.872 0 0 1 4.71 8.839ZM202.485 54.24h9.196V30.113c0-4.865 3.884-8.883 8.616-8.883 4.709 0 8.615 4.018 8.615 8.883V54.22h4.308c2.678 0 4.888-2.299 4.888-5.111V30.113c0-10.11-8.013-18.391-17.811-18.391-9.799 0-17.812 8.28-17.812 18.391v24.128ZM92.537 8.485V.428h4.375c2.723 0 4.955 2.187 4.955 4.865v8.214h6.294v8.348h-6.272V35.76c0 3.639 2.277 6.808 5.491 8.102 4.174 1.697 3.214 1.875 3.259 5.648v4.352c-9.955 0-18.102-8.147-18.102-18.08v-13.95h-6.16v-8.325h6.16V8.485ZM36.402.428h-9.196V15.94c0 4.71-3.883 8.616-8.615 8.616-4.71 0-8.616-3.884-8.616-8.616V.428H5.69C3.01.428.802 2.66.802 5.383v10.58c0 9.798 8.013 17.81 17.811 17.81 9.799 0 17.812-8.012 17.812-17.81V.427h-.023ZM27.206 33.17v2.857c0 4.71-3.883 8.616-8.615 8.616a8.698 8.698 0 0 1-4.754-1.429c-3.594-2.366-2.634-2.924-5.893-2.946H1.248c1.92 7.767 9.018 13.57 17.365 13.57 9.799 0 17.812-8.012 17.812-17.81V23.305a19.563 19.563 0 0 1-9.219 9.865Z" />
        <path d="M140.794 22.011c1.897 1.384 3.236 3.438 4.04 6.138v.023h-15.357c-.651 0-1.245-.01-1.79-.018-4.235-.066-5.487-.086-7.406 4.66-2.12 5.29-2.12 10.915-.223 15.847a20.38 20.38 0 0 0 5.67 3.616c2.522 1.05 5.289 1.585 8.303 1.585 4.486 0 8.414-1.205 11.829-3.638 3.415-2.433 5.96-5.848 7.678-10.245-6.209 0-7.554-.149-8.616.33-.953.431-1.678 1.369-5.49 3.375-1.674.87-3.571 1.317-5.669 1.317-3.147 0-5.714-.848-7.701-2.522-1.986-1.674-3.147-3.973-3.459-6.875h21.628c.644.005 1.298.026 1.949.047 5.481.18 10.722.35 7.849-9.466a18.222 18.222 0 0 0-2.879-5.825c-2.143-2.88-4.709-5.134-7.745-6.719-3.035-1.607-6.317-2.388-9.798-2.388-2.768 0-5.447.58-8.036 1.763-.875.393-1.719.844-2.533 1.353l-.368-.639a4.724 4.724 0 0 0-6.473-1.74c-2.054 1.182-2.745 3.816-1.563 5.87l1.728 2.99a20.054 20.054 0 0 0-2.018 3.884c-.959 2.478-1.45 5.134-1.45 7.946 0 2.723.535 5.402 1.584 7.99.848 2.076 1.942 3.951 3.326 5.625a22.865 22.865 0 0 1 1.138-14.061c2.277-5.536 8.08-12.343 14.687-12.343 2.879 0 5.267.714 7.165 2.12Z" />
      </g>
      <defs>
        <clipPath id="a">
          <path fill="#fff" d="M.802.428H264v53.814H.802z" />
        </clipPath>
      </defs>
    </svg>
  );
}
