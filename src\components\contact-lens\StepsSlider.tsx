"use client";

import React from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import {
  Autoplay,
  Pagination,
  Navigation,
  Mousewheel,
  Keyboard,
} from "swiper/modules";

const colors = [
  "#151965",
  "#28548E",
  "#2E84AF",
  "#28548E",
  "#151965",
  "#28548E",
  "#2E84AF",
  "#28548E",
];

export default function StepsSlider({ data }: any) {
  return (
    <div className="guide_slider">
      <div className="container">
        <Swiper
          pagination={{
            type: "progressbar",
          }}
          className="mySwiper"
          slidesPerView={3}
          cssMode={true}
          mousewheel
          keyboard
          spaceBetween={100}
          modules={[Autoplay, Pagination, Navigation, Mousewheel, Keyboard]}
          speed={800}
          autoplay={{
            delay: 2500,
            pauseOnMouseEnter: true,
          }}
          breakpoints={{
            320: {
              slidesPerView: 1,
              spaceBetween: 20,
            },

            578: {
              slidesPerView: 2,
              spaceBetween: 40,
            },

            991: {
              slidesPerView: 3,
              spaceBetween: 100,
            },
          }}
        >
          {data.map((items: any, index: number) => (
            <SwiperSlide key={index}>
              <div
                className="guide_downarrow"
                style={{ backgroundColor: colors[index] }}
              ></div>
              <div
                className="guide_round"
                style={{ backgroundColor: colors[index] }}
              >
                <span className="pb-2">STEP</span>
                <h2>0{index + 1}</h2>
              </div>

              <div className="guide_content">
                <h5>{items.title}</h5>
                <p>{items.description}</p>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </div>
  );
}
