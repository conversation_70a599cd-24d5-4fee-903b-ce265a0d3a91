.store-locator {
  text-align: center;
  max-width: 1440px;
  margin: 0 auto;
  margin-top: 3.5rem;

  .dropdown {
    .dropdown-toggle {
      background-color: black;
      border: 2px solid black;
      font-size: 1.5rem;
      padding: 1rem;
    }

    .dropdown-menu {
      // min-width: 10rem;
      font-size: 1.5rem;
      min-width: 100%;

      a {
        font-size: 1.5rem;
        transition: 0.2s all ease;
        padding: .5rem 1rem;

        &:hover {
          background-color: black;
          color: white;
        }
      }
    }
  }

  .gm-style-iw-chr {
    height: 15px;
  }

  .gm-style .gm-style-iw-c {
    border-radius: 4px;
  }

  @media (max-width: 575.98px) {
    margin-top: 1.5rem;
  }

  &>p {
    margin-top: 1.3rem;
    line-height: 2rem;
    text-align: left;

    @media (max-width: 575.98px) {
      margin-top: 1.5rem;
      text-align: left;
    }
  }

  &_map {
    margin-top: 2.4rem;

    @media (max-width: 575.98px) {
      margin-top: 1.5rem;
    }

    iframe {
      width: 100%;
      min-height: 63.7rem;

      @media (max-width: 575.98px) {
        min-height: 46.3rem;
      }
    }
  }

  .map-page {
    padding: 1rem;

    .search {
      flex-direction: column;

      .filter-applied {
        &>button {
          align-self: center;
        }
        padding: 1rem;

        .filter-item,
        button {
          display: flex;
          align-items: center;
          // background: #F2F4F9;
          // padding: 1rem 2rem;
          border-radius: 1rem;
          flex-wrap: wrap;

          span {
            background: black;
            border: none;
            padding: .5rem 1rem;
            font-size: 1.5rem;
            cursor: pointer;
            margin: .5rem;
            border-radius: 1rem;
            color: white;
            display: flex;
            align-items: center;
            gap: .5rem;

            button {
              background: transparent;
              border: none;
              padding: 0;
              font-size: 1.5rem;
              cursor: pointer;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
        }
      }

      .search-btns {
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        gap: 1rem;
        margin: 1rem 0;
        flex-wrap: wrap;
        width: 100%;

        .location-btns {
          grid-column: 4 / 7;
          align-items: center;

          @media (max-width: 450px) {
            gap: .5rem
          }

          @media (max-width: 1024px) {
            grid-column: 1 / 7;
          }

          button {
            background: black;
            border-radius: 4px;
            border: 1px solid black;
            color: white;
            padding: 1rem 2rem;
            width: 100%;
            display: flex;
            align-items: center;
            gap: 1rem;
            justify-content: center;

            @media (max-width: 450px) {
              font-size: 1.2rem;
              gap: 0.5rem;
              padding: .5rem 1rem;
            }
          }
        }

        .filters {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          grid-column: 1 / 7;
          margin-top: .5rem;

          @media (max-width: 450px) {
            grid-template-columns: 1fr 1fr;
            font-size: 1.2rem;
          }

          gap: 1rem;

          @media (max-width: 450px) {
            gap: .5rem
          }

          width: 100%;

          .dropdown {
            width: 100%;

            button {
              display: flex;
              width: 100%;
              font-size: 1.4rem;
              align-items: center;
              gap: 1rem;
              justify-content: center;
              padding: .5rem 1rem;
            }
          }
        }
      }

    }

    .places-autocomplete {
      grid-column: 1 / 4;

      @media (max-width: 1024px) {
        grid-column: 1 / 7;
      }

      padding: 1rem;
      display: flex;
      flex-direction: column;
      gap: 1rem;
      width: 100%;

      label {
        text-align: left;
        color: #00000070;
      }

      input {
        border: none;
        border-bottom: 1px solid #00000056;
        padding-bottom: 1rem;

        &:focus-visible {
          outline: none;
        }
      }
    }

    .map {
      height: 79svh;
      width: 100%;

      .info-window {
        display: flex;
        overflow: hidden;
        position: relative;
        flex-direction: column;
        align-items: flex-start;
        cursor: pointer;
        border: none;
        background: #F2F4F9;
        padding: 0;
        box-shadow: rgba(0, 0, 0, 0.05) 0px 6px 24px 0px, rgba(0, 0, 0, 0.08) 0px 0px 0px 1px;
        border-radius: 1.5rem;
        min-width: 28rem;

        @media (max-width: 425px) {
          min-width: 20rem;
        }

        z-index: 10;
        transform: translate(-13rem, -17rem);

        .list-item-content {
          padding: 2rem;
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          gap: 1rem;
          width: 100%;
        }

        img {
          max-height: 15rem;

          @media (max-width: 425px) {
            max-height: 12rem;
          }
        }

        h4 {
          font-size: 2rem;
        }

        p {
          margin: 1rem 0;
          max-width: 350px;
          line-height: unset;
          font-size: 1.5rem;
          text-align: left;
        }

        a {
          margin-top: auto;

          button {
            border-radius: 2rem;
            font-size: 1.2rem;
            padding: .8rem 1.8rem;
          }
        }

        .close-icon {
          position: absolute;
          right: 10px;
          top: 10px;
          cursor: pointer;
        }
      }

    }

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      height: auto;
    }

    .map-list {
      @media (max-width: 768px) {
        flex-direction: column-reverse;
      }
    }

    .list {
      display: flex;
      flex-direction: column;
      gap: 1rem;
      height: 79svh;
      min-width: 350px;
      overflow-y: auto;
      padding: 1rem;

      @media (max-width: 768px) {
        height: auto;
      }

      .list-items {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 2rem;
        margin-top: 2rem;

        .list-item {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          cursor: pointer;
          border: none;
          background: #F2F4F9;
          padding: 0;
          // box-shadow: rgba(0, 0, 0, 0.05) 0px 6px 24px 0px, rgba(0, 0, 0, 0.08) 0px 0px 0px 1px;
          border-radius: 1.5rem;
          border: 1px solid #EBEBEB;
          overflow: hidden;

          &:hover{
            box-shadow: rgba(0, 0, 0, 0.05) 0px 6px 24px 0px, rgba(0, 0, 0, 0.08) 0px 0px 0px 1px;
          }

          .list-item-content {
            padding: 2rem;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
          }

          h4 {
            font-size: 1.8rem;
          }

          p {
            margin: .5rem 0;
            max-width: 350px;
            line-height: unset;
            font-size: 1.5rem;
            text-align: left;
          }

          a {
            margin-top: auto;

            button {
              
              border-radius: 3rem;
              font-size: 1.5rem;
              padding: 1rem 2rem;
            }
          }
        }
      }
    }
  }

}




.app.rtl {
  .store-locator {
    .btn {
      transform: rotate(180deg);
    }
  }
}