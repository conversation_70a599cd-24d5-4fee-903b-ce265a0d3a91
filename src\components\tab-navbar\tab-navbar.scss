.tab-navbar {
  background-color: #f2f4f9;
  margin-bottom: 4rem;

  @media (max-width: 1199.98px) {
    display: none;
  }

  @media (max-width: 575.98px) {
    margin-bottom: 3.1rem;
  }

  &_list {
    display: flex;
    column-gap: 4rem;
    flex-wrap: nowrap;
    white-space: nowrap;
    overflow: auto;

    &::-webkit-scrollbar {
      display: none;
    }

    @media (max-width: 575.98px) {
      column-gap: 2rem;
    }
  }

  &_item {
    padding: 2.4rem 0;

    @media (max-width: 575.98px) {
      padding: 1.5rem 0;
      font-size: 1.4rem;
    }

    a {
      color: #000;
      font-size: 1.6rem;
      font-weight: 400;
      letter-spacing: 0.032rem;
      opacity: 0.5;
      white-space: nowrap;

      @media (max-width: 575.98px) {
        font-size: 1.4rem;
      }
    }

    &.active {
      border-bottom: 2px solid #000;

      a {
        color: #000;
        opacity: 1;
      }
    }
  }

  .logout-btn {
    display: flex;
    align-items: center;

    button {
      border: none;
      background-color: transparent;
      padding: 0;
      color: #000;
      opacity: 0.5;
    }
  }
}
