"use client";

import OrderStatus from "@/components/order-status/OrderStatus";
import "./try-cart-order.scss";
import Image from "next/image";
import Link from "next/link";
import { HistoryContext } from "@/contexts/HistoryProvider";
import { useContext, useEffect, useRef, useState } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import api from "@/config/axios.interceptor";
import { endpoints } from "@/config/apiEndpoints";
import LogoAnimation from "@/components/LogoAnimation/LogoAnimation";
import FormattedDate from "@/components/common/FormattedDate";
import EmptyState from "@/components/empty-states/EmptyState";
import TryCartPopup from "@/components/try-cart-popup/TryCartPopup";
import { toast } from "sonner";
import { useReactToPrint } from "react-to-print";

function TryCartOrder() {
  const { setPrevPage } = useContext(HistoryContext);
  const [show, setShow] = useState(false);
  const [imageSrc, setImageSrc] = useState<any>({});
  const [isPrintLoading, setIsPrintLoading] = useState(false);
  const [isEmptying, setIsEmptying] = useState(false);
  useEffect(() => {
    setPrevPage({
      title: "My Try Cart Order",
      url: "/my-accounts/try-cart-order",
    });
  }, []);
  const queryClient = useQueryClient();
  const {
    data: order,
    isLoading,
    isError,
  } = useQuery({
    queryKey: ["try-cart-order"],
    retry: false,
    queryFn: () => {
      return api
        .get(endpoints.tryCartOrder)
        .then((res) => {
          return res.data?.result;
        })
        .catch((e) => {
          throw new Error(e.response.data.message);
        });
    },
  });
  const printRef = useRef(null);
  const downloadInvoice = useReactToPrint({
    content: () => printRef.current,
    bodyClass: "print-Padding",
    onBeforeGetContent: () => setIsPrintLoading(true),
    onBeforePrint: () => setIsPrintLoading(false),
  });

  const moveToCart = (product: string, size: string) => {
    api
      .post(endpoints.addToCart, {
        product: product,
        orderNo: order?.orderNo,
        quantity: 1,
        size: size,
      })
      .then((res) => {
        queryClient
          .invalidateQueries({ queryKey: ["try-cart-order"] })
          .then(() => {
            toast.success(res.data.message);
          });
        queryClient.invalidateQueries({ queryKey: ["cart"] });
        queryClient.invalidateQueries({ queryKey: ["user"] });
        // toast.success(res.data.message);
      })
      .catch((e) => {
        toast.error(e.response.data.message);
      });
  };
  const removeFromTryCartOrder = (
    type: string = "all",
    product?: string,
    size?: string
  ) => {
    if (type === "all") setIsEmptying(true);
    api
      .post(endpoints.tryCartNotInterested, {
        product: product,
        orderNo: order?.orderNo,
        size: size,
        type: type,
      })
      .then((res) => {
        queryClient
          .invalidateQueries({ queryKey: ["try-cart-order"] })
          .then(() => {
            setIsEmptying(false);
            toast.success(res.data.message);
          });
        queryClient.invalidateQueries({ queryKey: ["user"] });
        // toast.success(res.data.message);
      })
      .catch((e) => {
        toast.error(e.response.data.message);
      });
  };

  if (isLoading) {
    return (
      <div className="d-flex justify-content-center align-items-center py-5 my-5">
        <LogoAnimation />
      </div>
    );
  }
  if (isError) {
    return (
      <div className="text-center trycart-order">
        <EmptyState title="No Active Orders Found!" icon="address" />
        {/* <p>Try Cart order will show once the current Try Cart checkout.</p> */}
        <button className="btn tryCartButton" onClick={() => setShow(true)}>
          Show my Try Cart
        </button>
        <TryCartPopup show={show} handleClose={() => setShow(false)} />
      </div>
    );
  }

  return (
    <div ref={printRef} className="printSection">
      <div className="printHeader d-print-block d-none">
        <img src="/images/footer/logo.png" alt="" />
      </div>
      <div className="trycart-order">
        <div className="trycart-order_wrapper">
          <h2>Order no: {order?.orderNo} </h2>
          <OrderStatus history={order?.history} />
          <div className="trycart-order_table">
            <div className="table">
              <div className="table_body">
                {order?.products.map((product: any, index: number) => (
                  <div className="table_row" key={product?._id}>
                    <div className="table_col-one">
                      <Link href={`/product/${product?.slug}`}>
                        <Image
                          quality={100}
                          priority
                          src={
                            imageSrc?.[product?._id]
                              ? imageSrc?.[product?._id]
                              : product?.thumbnail
                          }
                          width={120}
                          height={120}
                          alt={product?.name}
                          onError={() =>
                            setImageSrc({
                              [product?._id]: "/images/product/noImage.jpg",
                            })
                          }
                        />
                      </Link>
                      <Link href={`/product/${product?.slug}`}>
                        <ul>
                          <li>{product?.name}</li>
                          {product?.color && <li>Color : {product?.color}</li>}
                          {product?.size?.name && (
                            <li>Size : {product?.size?.name}</li>
                          )}
                          <li className="d-sm-none d-block">
                            {product?.currency} {product?.price}
                          </li>
                        </ul>
                      </Link>
                    </div>

                    <div className="table_col-two">
                      <h5>
                        {product?.currency} {product?.price}
                      </h5>

                      <button
                        onClick={() =>
                          moveToCart(product?._id, product?.size?._id)
                        }
                        className="move-cart-btns d-print-none"
                      >
                        Move to cart
                      </button>
                      <button
                        className="not-btn d-print-none"
                        onClick={() =>
                          removeFromTryCartOrder(
                            "single",
                            product?._id,
                            product?.size?._id
                          )
                        }
                      >
                        Not Interested
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="trycart-detail">
        <div className="trycart-detail_left">
          <div className="trycart-detail_cutemer-details">
            <h4>Customer Details</h4>
            <div className="box">
              <ul>
                <li>{order?.address?.name}</li>
                <li>
                  <Link href="">{order?.address?.number}</Link>
                </li>
                <li>
                  {order?.address?.suiteUnit && (
                    <span>{order?.address?.suiteUnit},</span>
                  )}
                  {order?.address?.street}, {order?.address?.city},
                  {order?.address?.emirates},{order?.address?.country} -
                  {order?.address?.postalCode}
                </li>
                <li>
                  {order?.address?.countryCode}-{order?.address?.mobile}
                </li>
              </ul>
            </div>
          </div>

          {/* <div className="trycart-detail_shipping-details">
            <h4>Shipping Details</h4>
            <div className="delivery-details_box">
              <div className="box">
                <div className="box_content">
                  <div className="box_title">
                    <h5>Arrives by Monday, Sep 20</h5>
                  </div>
                  <div className="box_logo">
                    <Image
                      quality={100}
                      priority
                      src="/images/common/fedex.png"
                      width={87}
                      height={39}
                      alt="fedex"
                    />
                    <span>FedEx Home Delivery</span>
                  </div>
                </div>
                <div className="box_barcode">
                  <Image
                    quality={100}
                    priority
                    src="/images/common/barcode.png"
                    width={172}
                    height={65}
                    alt="barcode"
                  />
                </div>
              </div>
            </div>
          </div> */}
        </div>

        <div className="trycart-detail_right">
          <h5>Order Summary</h5>
          <p>
            An amount will be charged for trying products at home this will be
            deducted when you purchase the product
          </p>

          <ul>
            {order?.invoice?.map((amount: any, index: number) => (
              <li key={"invoice" + index}>
                <div
                  className={
                    index !== order?.invoice?.length - 1
                      ? "table_summary-title"
                      : "table_summary-totaltext"
                  }
                >
                  <h5>
                    {amount?.label}
                    {/* {index === 0 &&
                    <span>({order?.products?.length} Items)</span> 
                    } */}
                  </h5>
                </div>
                <div
                  className={
                    index !== order?.invoice?.length - 1
                      ? "table_summary-amount"
                      : "table_summary-totalprice"
                  }
                >
                  <h5>
                    {amount?.currency}
                    <span> {amount?.value}</span>
                  </h5>
                </div>
              </li>
            ))}
          </ul>

          <button
            className="d-print-none"
            onClick={() => removeFromTryCartOrder()}
          >
            Empty Cart
            {isEmptying && (
              <span className="ms-2 spinner-border" role="status"></span>
            )}
          </button>
        </div>
      </div>

      <div className="trycart-invoice">
        <div className="invoice">
          <ul>
            <li>
              Order Date{" "}
              <span>
                <FormattedDate date={order?.orderDate} />
              </span>
            </li>
            {/* <li>
              Return Date{" "}
              <span>
                <FormattedDate date={order?.returnDate} />{" "}
              </span>
            </li> */}
          </ul>
          <ul>
            <li>
              Order Status <span>: {order?.orderStatus}</span>
            </li>
            <li>
              Payment Method <span>: {order?.paymentMethod} </span>
            </li>
          </ul>
          <button className="d-print-none" onClick={downloadInvoice}>
            Download Invoice
            {isPrintLoading && (
              <span className="ms-2 spinner-border" role="status"></span>
            )}
          </button>
        </div>
      </div>
    </div>
  );
}

export default TryCartOrder;
