"use client";

import "./rating-popup.scss";
import Modal from "react-bootstrap/Modal";
import StarRating from "../star-rating/StarRating";
import { useState } from "react";
import { set, useForm } from "react-hook-form";
import api from "@/config/axios.interceptor";
import { endpoints } from "@/config/apiEndpoints";
import { toast } from "sonner";

interface ReviewInputs {
  message: string;
  rating: number;
}
function RatingPopup({ product }: any) {
  const [show, setShow] = useState(false);
  const [imgPreviews, setImgPreviews] = useState<string[]>([]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm<ReviewInputs>();

  const handleClose = () => {
    setShow(false);
  };
  const handleShow = (e: any) => {
    setShow(true);
  };

  const setStarRating = (rating: number) => {
    setValue("rating", rating);
  };

  const onSubmit = (data: ReviewInputs) => {
    if (!data.rating) {
      toast.error("Please select rating");
      return;
    }

    const review = {
      productId: product._id,
      rating: Number(data.rating),
      message: data.message,
      images: imgPreviews,
    };

    api.post(endpoints.addReview, review).then((res) => {
      if (res.data.errorCode === 0) {
        toast.success(res.data.message);
      } else {
        toast.error(res.data.message);
      }
    });
    setShow(false);
  };

  const handleImageUpload = (e: any) => {
    const file = e.target.files;
    const formData = new FormData();
    formData.append("file", file[0]);
    api.post("/media-upload", formData).then((res) => {
      setImgPreviews((prev) => [...prev, res.data?.fileUrl]);
    });
  };

  const handleDelete = (index: number) => {
    const newImgPreviews = [...imgPreviews];
    newImgPreviews.splice(index, 1);
    setImgPreviews(newImgPreviews);
  };

  return (
    <>
      {/* <button onClick={handleShow} className="reviewBtn-pop">
        Write a Review
      </button> */}
      <Modal
        show={show}
        onHide={handleClose}
        backdrop="static"
        keyboard={false}
        centered
        className="rating-popup">
        <Modal.Header closeButton></Modal.Header>
        <Modal.Body>
          <h2>Write a review</h2>
          <StarRating setStarRating={setStarRating} initialRating={0} />

          <div className="image-upload-review">
            {imgPreviews?.map((img, i) => (
              <div className="preview" key={img + i}>
                <span className="preview-delete-btn" onClick={() => handleDelete(i)}>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="14"
                    height="14"
                    viewBox="0 0 24 24"
                    fill="currentColor">
                    <path d="M11.9997 10.5865L16.9495 5.63672L18.3637 7.05093L13.4139 12.0007L18.3637 16.9504L16.9495 18.3646L11.9997 13.4149L7.04996 18.3646L5.63574 16.9504L10.5855 12.0007L5.63574 7.05093L7.04996 5.63672L11.9997 10.5865Z"></path>
                  </svg>
                </span>
                <img src={process.env.NEXT_PUBLIC_IMAGE_DOMAIN_CDN + img} alt="uploaded-image" />
              </div>
            ))}

            {imgPreviews?.length < 5 && (
              <>
                <label
                  className="image-upload-review-label"
                  htmlFor="file-upload"
                  title="Upload image">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    width="24"
                    height="24"
                    fill="rgba(0,0,0,1)">
                    <path d="M21 15V18H24V20H21V23H19V20H16V18H19V15H21ZM21.0082 3C21.556 3 22 3.44495 22 3.9934V13H20V5H4V18.999L14 9L17 12V14.829L14 11.8284L6.827 19H14V21H2.9918C2.44405 21 2 20.5551 2 20.0066V3.9934C2 3.44476 2.45531 3 2.9918 3H21.0082ZM8 7C9.10457 7 10 7.89543 10 9C10 10.1046 9.10457 11 8 11C6.89543 11 6 10.1046 6 9C6 7.89543 6.89543 7 8 7Z"></path>
                  </svg>
                </label>
                <input
                  onChange={handleImageUpload}
                  accept="image/*"
                  id="file-upload"
                  className="d-none"
                  type="file"
                />
              </>
            )}
          </div>
          <form onSubmit={handleSubmit(onSubmit)}>
            <label htmlFor="">Write a Review</label>
            <textarea
              {...register("message", {
                required: "Please add review.",
                minLength: { value: 5, message: "Your Review is too short!" },
              })}
              placeholder="Type Message here"></textarea>
            {errors.message && <p>{errors.message.message}</p>}
            <button type="submit">Submit</button>
          </form>
        </Modal.Body>
      </Modal>
    </>
  );
}

export default RatingPopup;
