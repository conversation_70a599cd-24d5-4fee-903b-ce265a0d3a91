"use client";

import { endpoints } from "@/config/apiEndpoints";
import api from "@/config/axios.interceptor";
import { AuthContext } from "@/contexts/AuthProvider";
import { sendGTMEvent } from "@next/third-parties/google";
import { useQueryClient } from "@tanstack/react-query";
import { createHmac } from "crypto";
import { motion } from "framer-motion";
import { useRouter } from "next/navigation";
import { useContext, useEffect, useState } from "react";
import { toast } from "sonner";

export default function WishlistBtn({ product, price, savings }: any) {

  const queryClient = useQueryClient();
  const { userProfile } = useContext(AuthContext);
  const router = useRouter();
  const handleWishList = (e: any) => {
    const queryCache = queryClient.getQueryCache()
    const queryKeys = queryCache.getAll().map(cache => cache.queryKey)
    const productsKey = queryKeys.filter((key: any) => key[0] === "products")
    const productsSearch = queryKeys.filter((key: any) => key[0] === "search")
    const productDetails = queryKeys.filter((key: any) => key[0] === "product" && key[1] === product?.slug)
    const productCollection = queryKeys.filter((key: any) => key[0] === "collection")
    if (!product?.isWishlisted) {
      let eventData: any = {
        event: "add_to_wishlist",
        ecommerce: {
          currency: "AED",
          value: price - savings,
          items: [
            {
              item_id: product?.sku,
              item_name: product?.name,
              index: 0,
              discount: savings,
              item_brand: product?.brand,
              item_category: product?.category?.[0]?.name,
              item_category2: product?.category?.[1]?.name,
              item_variant: product?.color,
              price: price,
              quantity: 1,
              // quantity: 1,
            }
          ]
        }
      }
      if (userProfile) {
        const mobile = createHmac('sha256', '123').update(userProfile?.mobile).digest('hex')
        let email = null;
        if (userProfile?.email) email = createHmac('sha256', '123').update(userProfile?.email).digest('hex')
        eventData.user = {
          mobile,
          email,
          user_id: userProfile?._id
        }
      }
      sendGTMEvent({ ecommerce: null })
      sendGTMEvent(eventData)
    }

    e.nativeEvent.stopImmediatePropagation();
    e?.preventDefault();
    e?.stopPropagation();
    api
      .post(endpoints.manageWishlist, { product: product?._id })
      .then((res) => {
        if (res.data.errorCode === 0) {
          const modQueries: any = [...productsKey, ...productsSearch]

          modQueries.forEach((key: any) => {
            queryClient.setQueryData(key, (prev: any) => {
              const newData = prev?.pages?.map((page: any) =>
              ({
                ...page,
                products: page?.products?.map((item: any) => {
                  let obj = {
                    ...item
                  }
                  for (let [key, value] of Object.entries(item) as any) {
                    if (value?._id === product?._id) {
                      obj[key] = {
                        ...value,
                        isWishlisted: !value?.isWishlisted
                      }
                    }
                  }
                  return obj
                }
                )
              }))
              return prev ? {
                ...prev,
                pages: newData
              } : prev
            })
          });

          [...productDetails].forEach((key: any) => {
            queryClient.setQueryData(key, (prev: any) => {
              const newData = {
                ...prev,
                isWishlisted: !prev?.isWishlisted
              }
              return prev ? newData : prev
            })
          })

          productCollection.forEach((key: any) => {
            queryClient.setQueryData(key, (prev: any) => {
              const newData = prev?.products?.map((item: any) => {
                let obj = {
                  ...item
                }
                for (let [key, value] of Object.entries(item) as any) {
                  if (value?._id === product?._id) {
                    obj[key] = {
                      ...value,
                      isWishlisted: !value?.isWishlisted
                    }
                  }
                }
                return obj
              })
              return prev ? {
                ...prev,
                products: newData
              } : prev
            })
          })


          queryClient.invalidateQueries({ queryKey: ["wishList"] });
          // queryClient.invalidateQueries({ queryKey: ["collection"] });
          queryClient.invalidateQueries({ queryKey: ["cart", "counts"] });

          toast.success(res.data.message);
        }
      })
      .catch((err) => {
        toast.error(err.response.data.message);
        if (err.response?.status === 401) {
          router.push(`/login?type=wishlist&id=${product?._id}`);
          console.log("push")
          // router.push(`/login`);
        }
      });
  };
  return (
    <motion.button
      whileTap={{ scale: 1.3 }}
      transition={{ type: "spring", stiffness: 400, damping: 10 }}
      onClick={handleWishList}
      className="isActive"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="23"
        viewBox="0 0 24 23"
        fill="none"
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M3.62916 12.1744C2.64183 9.1054 3.79663 5.28972 7.03284 4.25266C8.73514 3.70573 10.8359 4.16196 12.0293 5.80092C13.1547 4.1015 15.3161 3.7094 17.0166 4.25266C20.2519 5.28972 21.4131 9.1054 20.4267 12.1744C18.89 17.0391 13.8228 19.0945 12.0293 19.5731C10.1422 19.0945 5.21736 17.0959 3.62916 12.1744Z"
          fill={product?.isWishlisted ? "black" : "none"}
          stroke="black"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </motion.button>
  );
}
