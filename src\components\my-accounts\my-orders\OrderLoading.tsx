"use client";
import "./my-orders.scss";
import ViewDetailBtn from "@/components/view-detail-btn/ViewDetailBtn";

import GenericBackButton from "@/components/GenericBackButton/GenericBackButton";



function OrderLoading() {
    return (
        <section className="orders position-relative">
            <GenericBackButton style={{ top: "5px" }} />
            <div className="skeletonLoader">
                <h2 style={{ visibility: "hidden" }}>My Orders</h2>
            </div>
            {[1, 2, 3]?.map((order: any) => (
                <div className="orders_body" key={order}>
                    <div className="orders_top">
                        <div className="orders_id">
                            <div className="skeletonLoader">
                                <h5 style={{ visibility: "hidden" }}>Order no</h5>
                            </div>
                            <h6>
                                <p className="skeletonLoader">
                                    <span style={{ visibility: "hidden" }}>Order Date</span>
                                </p>
                                <p className="skeletonLoader">
                                    <span style={{ visibility: "hidden" }}>Order Date</span>
                                </p>
                            </h6>
                            <h6 className="skeletonLoader">
                                <span style={{ visibility: "hidden" }}>Order Date</span>
                            </h6>
                        </div>
                        <div className="orders_status">
                            <h6 className="skeletonLoader">
                                <span style={{ visibility: "hidden" }}>Order Status</span>
                            </h6>
                            <h6 className="skeletonLoader">
                                <span style={{ visibility: "hidden" }}>Payment Method</span>
                            </h6>
                        </div>
                        <div className="orders_btn">
                            <div className="skeletonLoader">
                                <div style={{ visibility: "hidden" }}>
                                    View details
                                </div>
                            </div>
                            {/* {!excludeStatuses?.includes(order?.orderStatus) && (
                    <CancelOrder order={order?._id} />
                  )} */}
                        </div>
                    </div>

                    <div className="orders_bottom">
                        {[1, 2]?.map((product: any) => (
                            <div className="orders_detail" key={product}>
                                <div className="skeletonLoader" style={{ width: "120px", height: "120px" }}></div>
                                <ul>
                                    <li className="orders_detail-modal">{product?.name}</li>
                                    <li className="orders_detail-color">
                                        <div className="skeletonLoader">
                                            <p style={{ visibility: "hidden" }}>Color</p>
                                        </div>
                                    </li>
                                    <li className="orders_detail-size">
                                        <div className="skeletonLoader">
                                            <p style={{ visibility: "hidden" }}>Size</p>
                                        </div>
                                    </li>
                                    <li className="orders_detail-price">
                                        <div className="skeletonLoader">
                                            <p style={{ visibility: "hidden" }}>AED</p>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        ))}
                    </div>
                </div>
            ))}
        </section>
    );
}

export default OrderLoading;
