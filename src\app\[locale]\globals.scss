.form-error {
  position: absolute;
  bottom: -20px;
  font-size: 12px;
  left: 0;
}

.app.rtl {
  .form-error {
    position: absolute;
    bottom: -20px;
    font-size: 12px;
    right: 0;
  }
}

input[type="checkbox"] {
  accent-color: #000;
}

.py-20 {
  padding-top: 20px;
  padding-bottom: 20px;
}

::-moz-selection {
  /* Code for Firefox */
  color: white;
  background: black;
}

::selection {
  color: white;
  background: black;
}

.page-center {
  display: grid;
  place-items: center;
  padding-top: 30px !important;
  padding-bottom: 30px !important;
  min-height: 200px;
}

.skeletonLoader {
  animation: skeleton 1.5s infinite alternate;
}

.cursor-pointer {
  cursor: pointer;
}

@keyframes skeleton {
  0% {
    background-color: rgba(165, 165, 165, 0.1);
  }

  50% {
    background-color: rgba(165, 165, 165, 0.3);
  }

  100% {
    background-color: rgba(165, 165, 165, 0.1);
  }
}

img {
  opacity: 100%;
  transition: opacity 0.5s ease-in-out;
}

img.opacity-0 {
  opacity: 0 !important;
}

// body::-webkit-scrollbar {
//   width: 5px;
// }

// body::-webkit-scrollbar-track {
//   box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
// }

// body::-webkit-scrollbar-thumb {
//   background-color: #1f2738;
//   outline: 1px solid slategrey;
//   border-radius: 8px;
// }

.bestsellerx {
  a:hover {
    background-color: #000 !important;
    color: #fff !important;
  }
}

/* width */
::-webkit-scrollbar {
  width: 6px;
}

/* Track */
::-webkit-scrollbar-track {
  background: #ffffff;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #111827;
  // border-radius: 3px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #555;
}

option {
  font-size: 16px;
  line-height: 60px;

  &:hover {
    background-color: #000 !important;
  }
}

.cursor-not-allowed {
  cursor: not-allowed !important;
}

.coming-soon {
  img {
    max-width: 32.9rem;
    width: 100%;
    height: auto;
    display: flex;
    margin: 0 auto;
    margin-top: 3.9rem;
  }

  &-div {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    h5 {
      font-size: 2.4rem;
      font-weight: 600;
      line-height: 2.4rem;
      margin-top: 2rem;
      color: #000;
    }

    p {
      font-size: 1.5rem;
      font-weight: 400;
      line-height: 2.4rem;
      margin-top: 1rem;
      color: #000;
    }

    .primary-btn {
      margin-top: 2.4rem;
      background-color: #111827;
      color: #ffffff;
      border: 1px solid #111827;

      &:hover {
        color: #111827;
      }
    }
  }
}

.whatsapp-wrapper {
  position: fixed;
  z-index: 999;
  right: 1.5rem;
  bottom: 1.5rem;

  @media (max-width: 1200px) {
    bottom: 8rem
  }
}

.whatsapp-float {
  width: fit-content;
}

.app.rtl {
  .swiper-button-prev {
    &::after {
      transform: rotate(180deg);
    }
  }

  .swiper-button-next {
    &::after {
      transform: rotate(180deg);
    }
  }
}