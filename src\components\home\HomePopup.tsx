"use client"

import { HistoryContext } from "@/contexts/HistoryProvider";
import { useContext, useEffect, useState } from "react";
import { AnimatePresence, motion } from "framer-motion";
import Image from "next/image";
import "./homePopup.scss";

export default function HomePopup() {

    const [isPopup, setIsPopup] = useState(false)

    useEffect(()=>{
    const show = localStorage.getItem('welcome-popup') == "hide" ? false: true
    setIsPopup(show)
    }, [])

    const closePopup = () => {
        setIsPopup(false)
        localStorage.setItem('welcome-popup', "hide")
    }
    return <AnimatePresence>
        {isPopup && <div className="popup-wrapper" style={{ position: "fixed", top: 0, left: 0, zIndex: 99999999, width: "100vw", height: "100vh", display: "flex", alignItems: "center", justifyContent: "center" }}>

            <motion.div
                initial={{ opacity: 0, scale: 0.5, }}
                animate={{ opacity: 1, scale: 1, }}
                transition={{
                    duration: 0.8,
                    ease: [0, 0.71, 0.2, 1.01],
                }}
                style={{ display: 'flex', alignItems: "center", justifyContent: "center", position: "relative" }}
                className="motion-wrapper"
            >
                <button onClick={closePopup} style={{ width: "30px", height: "30px", background: "black", display: "flex", alignItems: "center", justifyContent: "center", border: "none", borderRadius: "50%", position: "absolute", top: -8, right: -8 }}>
                    <Image width={25} height={25} style={{ height: "100%" }} src="/images/common/close.png" alt="close" />
                </button>
                <Image quality={100} priority
                    className="popup"
                    src="/images/popup.png"
                    width={815}
                    height={600}
                    sizes="(max-width: 850px): 90vw"
                    alt="logo"
                />
            </motion.div>
        </div>}
    </AnimatePresence>
}