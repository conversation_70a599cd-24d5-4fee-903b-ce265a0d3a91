"use client";

import Rating from "@/components/product-detail/rating/Rating";
import Review from "@/components/product-detail/review/Review";
import { endpoints } from "@/config/apiEndpoints";
import api from "@/config/axios.interceptor";
import { useLocaleContext } from "@/contexts/LocaleProvider";
import { useQuery } from "@tanstack/react-query";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

export const calculateSavings = (price: number, offerPrice: number) => {
  return price - offerPrice;
};

export default function ProductVariantSection({ product, translation }: any) {
  const searchParams = useSearchParams();
  const active = searchParams?.get("size");
  const [price, setPrice] = useState({
    price:  product?.price?.aed,
    offerPrice: product?.offerPrice?.aed || null,
  });

  const {currencyCode} = useLocaleContext()
 

  const calculateDiscountPercentage = (price: number, offerPrice: number) => {
    return ((price - offerPrice) / price) * 100;
  };

  const savings = calculateSavings(price.price, price.offerPrice);
  const discountPercentage = calculateDiscountPercentage(price.price, price.offerPrice);

  useEffect(() => {
    if (active && product?.customizable) {
      const varient = product?.productType == "contactLens" ? product?.contactSizes.find((size: any) => size?.size?._id === active) : product?.sizes.find((size: any) => size?.size?._id === active);
    
      if (varient) {
        setPrice((prev) => ({
          ...prev, price: varient?.price,
          offerPrice: varient?.offerPrice < 1 || varient?.offerPrice >= varient?.price ?
            null
            : varient?.offerPrice,
        }));
      }
    }
  }, [searchParams]);

  // const {
  //   data: reviews,
  //   isLoading,
  //   error,
  // } = useQuery({
  //   queryKey: ["reviews", product?.mainProduct || product?._id],
  //   queryFn: () => {
  //     return api
  //       .get(`${endpoints.reviews}/${product?.mainProduct || product?._id}`)
  //       .then((res) => {
  //         return res.data.result;
  //       });
  //   },
  // });
  // console.log(price?.offerPrice)
  return (
    <div className="block-two">
      <div className="d-flex align-items-center gap-4">
        {(price.price != 0) ? (
          <h2 className="d-flex align-items-center gap-2">
            {currencyCode + " "} {price?.offerPrice ? price.offerPrice : price.price}
            {product?.isTaxIncluded && <span className="vat">({translation?.inc || "inc"} VAT)</span>}
          </h2>
        ) : "N/A"}
        {price.offerPrice && price.price !== price.offerPrice ? (
          <div>
            <h5>{currencyCode + " "} {price?.price}</h5>
          </div>
        ) : ""}
      </div>

      {((price?.offerPrice && product?.showDiscountPercentage) && Number(savings.toFixed(2)) > 0.01) ? <div className="savings">
        <h5
          style={{
            textDecoration: "none",
            color: "black",
          }}
        >
          {translation?.saving || "Saving"} <span>{currencyCode + " "}{savings.toFixed(2)}</span>
          <span className="off">{discountPercentage.toFixed(0)}% OFF</span>
        </h5>
      </div> : ""}

      {/* <div>
        <div className="d-flex align-items-center" style={{ columnGap: "1.2rem" }}>
          <Rating rating={reviews?.rating || 0} />
          <Review
            count={reviews?.reviewsCount}
            reviews={reviews?.reviews}
            rating={reviews?.rating}
          />
        </div>

        <h6>
          <span>{Math.round((product?.rating * 100) / 5)}% </span> of buyers have recommended this.
        </h6>
      </div> */}
    </div>
  );
}
