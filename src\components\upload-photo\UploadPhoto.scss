  .upload-photo {
      padding: 4rem 5rem 0rem 6.2rem;

      @media (max-width: 575.98px) {
          padding: 1.5rem;
          padding-bottom: 0;
      }

      &_header {
          display: flex;
          justify-content: space-between;
          margin-bottom: 2rem;

          h2 {
              text-align: left;
              line-height: 3.1rem;
              display: flex;
              align-items: center;
              column-gap: 1.4rem;
              font-size: 2.5rem;
              font-weight: 500;

              @media (max-width: 575.98px) {
                  font-size: 2.2rem;
                  color: #242731;
              }

              img {
                  width: 4rem;
                  height: 4rem;
                  object-fit: contain;

                  @media (max-width: 575.98px) {
                      display: none;
                  }
              }
          }

          button {
              background-color: transparent;
              border: none;
              padding: 0;
          }
      }

      &_box {
          border: 1px dashed #C9C9C9;
          border-radius: 1.5rem;
          min-height: 13.5rem;
          background-color: #F2F4F9;
          position: relative;

          input {
              opacity: 0;
              width: 100%;
              min-height: 13.5rem;
              cursor: pointer;
              position: relative;
              z-index: 1;
          }

          img {
              width: 10rem;
              height: 5rem;
              left: 50%;
              top: 50%;
              transform: translate(-50%, -50%);
              pointer-events: none;
              position: absolute;
          }

          p{
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
          }
      }

      ul {
          margin-top: 1rem;

          li {
              font-size: 1.2rem;
              font-family: 300;
              color: #000000;
              line-height: 2.3rem;
          }
      }
  }

  #check{
    display: flex;
    gap:.51rem;
    align-items: center;
    margin: 1rem 0;

    p{
        font-weight: 600;
    }
  }