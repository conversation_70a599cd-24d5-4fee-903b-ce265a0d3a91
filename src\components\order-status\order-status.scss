.status {
  &_bar {
    display: flex;
    align-items: center;
    column-gap: 6.6rem;
    margin-top: 4.3rem;

    @media (max-width: 991.98px) {
      column-gap: 3.4rem;
    }

    @media (max-width: 575.98px) {
      margin-top: 2.8rem;
    }

    li {
      font-size: 1.6rem;
      font-weight: 500;
      display: flex;
      align-items: center;
      column-gap: 0.6rem;
      position: relative;
      color: rgba(0, 0, 0, 0.5);

      &.isActive {
        color: #000;

        .status_icon {
          background-color: #4bae4f;
        }
      }

      &:not(:last-child) {
        &::after {
          content: "";
          height: 0.1rem;
          background-color: #000;
          width: 4.5rem;
          position: absolute;
          left: 100%;
          top: 50%;
          transform: translate(23%, 50%);

          @media (max-width: 991.98px) {
            width: 2.5rem;
            transform: translate(17%, 50%);
          }
        }
      }

      .status_icon {
        width: 3.2rem;
        height: 3.2rem;
        border-radius: 50%;
        background-color: #868686;
        display: inline-block;
        position: relative;

        @media (max-width: 575.98px) {
          width: 2.8241rem;
          height: 2.8241rem;
        }

        &::after {
          content: "";
          background-image: url(../../../public/images/common/check.png);
          background-position: center;
          background-size: contain;
          background-repeat: no-repeat;
          width: 3.2rem;
          height: 3.2rem;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);

          @media (max-width: 575.98px) {
            width: 2.2rem;
            height: 2.2rem;
          }
        }
      }

      .status_text {
        @media (max-width: 767.98px) {
          display: none;
        }
      }
    }

    .last-item {
      .status_text {
        @media (max-width: 767.98px) {
          display: block !important;
        }
      }
    }
  }
}

.app.rtl {
  .status {
    &_bar {
      li {
        &:not(:last-child) {
          &::after {
            right: 100%;
            transform: translate(-15%, 50%);

            @media (max-width: 991.98px) {
              width: 2.5rem;
              transform: translate(-20%, 50%);
            }
          }
        }
      }
    }
  }
}