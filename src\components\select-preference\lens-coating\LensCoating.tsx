import React, { useState, useEffect } from "react";
import "./LensCoating.scss";
import { endpoints } from "@/config/apiEndpoints";
import { useQuery } from "@tanstack/react-query";
import api from "@/config/axios.interceptor";
import { useSearchParams } from "next/navigation";
import { useLocaleContext } from "@/contexts/LocaleProvider";
function LensCoating({
  lensData,
  handleLensData,
  sum,
  setSum,
  setSelected,
}: any) {
  const [activeCoating, setActiveCoating] = useState(-1);
  const [localSum, setLocalSum] = useState(sum);
  const searchParams = useSearchParams();
  const {currencyCode} = useLocaleContext()
  const {
    data: coatings,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["coating", lensData?.brand],
    queryFn: () => {
      return api
        .get(`${endpoints.lensCoating}/${lensData?.brand}`)
        .then((res) => {
          if (res.status === 200) {
            return res.data?.result?.coatings;
          } else [];
        });
    },
  });

  const handleDivClick = (index: number) => {
    setActiveCoating(index);
  };

  useEffect(() => {
    if (coatings?.length && lensData?.coating) {
      setActiveCoating(
        coatings?.findIndex((item: any) => item._id === lensData?.coating)
      );
      setSum("coating",coatings[coatings?.findIndex((item: any) => item._id === lensData?.coating)]?.price||0);
      setSelected(true);
    }

    coatings?.length === 0 && setSelected(true);
  }, [coatings, lensData?.coating]);

  return (
    <div className="lens-coating">
      {coatings?.map((item: any, index: number) => {
        return (
          <div
            className={`lens-coating_box ${
              activeCoating === index ? "isActive" : ""
            }`}
            key={index}
            onClick={() => {
              handleDivClick(index);
              handleLensData({ coating: item?._id });
              setSum("coating", item?.price);
              const newSum = sum + item?.price;
              setSelected(true);
              handleLensData({
                sum: newSum,
                size: searchParams?.get("size"),
              });
            }}
          >
            <h5>{item?.name}</h5>
            <h6>+ {currencyCode + " "} {item?.price}</h6>
          </div>
        );
      })}
    </div>
  );
}

export default LensCoating;
