"use client";

import Link from "next/link";
import "./checkout.scss";
import CheckoutOrderSummary from "@/components/checkout-order-summary/CheckoutOrderSummary";
import MyAddressPopUp from "@/components/my-address-popup/MyAddressPopUp";
import { useContext, useEffect, useState } from "react";
import { getAddress } from "@/lib/methods/user";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import LogoAnimation from "@/components/LogoAnimation/LogoAnimation";
import EmptyState from "@/components/empty-states/EmptyState";
import api from "@/config/axios.interceptor";
import { endpoints } from "@/config/apiEndpoints";
import { toast } from "sonner";
import { CheckoutContext } from "@/contexts/CheckoutContext";
import { AuthContext, AuthProvider } from "@/contexts/AuthProvider";
import { sendGTMEvent } from "@next/third-parties/google";
import { getCart, getSubscriptionSummary, getTryCart } from "@/lib/methods/cart";
import { TranslationContext } from "@/contexts/Translation";
import Image from "next/image";
import { createHmac } from "crypto";
import { useLocaleContext } from "@/contexts/LocaleProvider";

function Page({ params }: { params: { id: string; type: string } }) {
  const [show, setShow] = useState(false);
  const { userProfile } = useContext(AuthContext);
  const [editAddress, setEditAddress] = useState<any>(null);
  const { selectedAddress, setSelectedAddress, boundingRect, id } =
    useContext(CheckoutContext);
  const queryClient = useQueryClient();
  const { translation } = useContext(TranslationContext);
  const { currentLocale, currencyCode } = useLocaleContext()

  // const {
  //   data: shippingCharge,
  //   isLoading: shippingChargeLoading,
  //   error,
  //   refetch,
  // } = useQuery({
  //   queryKey: ["shipping-charge", selectedAddress],
  //   queryFn: () => {
  //     return api
  //       .post(`${endpoints.shippingCharge}`, { address: selectedAddress })
  //       .then((res) => {
  //         if (res.status === 200) {
  //           return res.data?.result;
  //         } else {
  //           return [];
  //         }
  //       });
  //   },
  // });

  // useEffect(() => {
  //   refetch();
  //   refetchdefaultAddress();
  // }, [selectedAddress]);

  const refetchdefaultAddress = () => {
    queryClient.invalidateQueries({ queryKey: ["shipping-charge"] });
  };

  const handleClose = () => {
    setEditAddress(null);
    setShow(false);
  };
  const handleShow = () => setShow(true);
  const { data: address, isLoading } = useQuery({
    queryKey: ["address", currentLocale?.split("-")[0]],
    queryFn: getAddress,
  });

  const handleEdit = (data: any) => {
    setEditAddress(data);
    handleShow();
  };

  const setDefault = (address: any) => {
    api
      .put(`${endpoints.updateAddress}/${address?.refid}`, {
        ...address,
        isDefault: true,
      })
      .then((res) => {
        if (res.data.errorCode === 0) {
          toast.success("Address set as default");
          queryClient.invalidateQueries({ queryKey: ["address"] });
        }
      });
  };

  const deleteAddress = (id: string) => {
    api.put(`${endpoints.deleteAddress}/${id}`).then((res) => {
      if (res.data.errorCode === 0) {
        toast.success("Address deleted");
        queryClient.invalidateQueries({ queryKey: ["address"] });
      }
    });
  };

  useEffect(() => {
    if (!isLoading && address?.length === 0) {
      setShow(true);
    }
    if (!isLoading && address?.length === 1) {
      setSelectedAddress(address[0]._id);
    }
  }, [address, isLoading]);

  const { data: cart } = useQuery({
    queryKey: [
      params?.type === "try-cart"
        ? "tryCart"
        : params?.type === "subscription"
          ? "subscription-summary"
          : "cart", currentLocale?.split("-")[0]
    ],
    queryFn:
      params?.type === "try-cart"
        ? getTryCart
        : params?.type === "subscription"
          ? () => getSubscriptionSummary(id)
          : getCart,
  });

  const sendDataLayer = () => {
    sendGTMEvent({ ecommerce: null })
    let eventData: any = {
      event: "add_shipping_info",
      ecommerce: {
        currency: currencyCode,
        value: cart?.total,
        shipping_tier: "Ground",
        coupon: cart?.couponCode,
        items: cart?.products?.map((item: any, i: number) => {
          return {
            item_id: item?.sku,
            item_name: item?.name,
            index: i,
            item_brand: item?.brand,
            item_category: item?.category?.[0]?.name,
            item_category2: item?.category?.[1]?.name,
            item_variant: item?.color,
            price: item?.price,
            quantity: item.quantity,
          }
        })
      }
    }
    if (userProfile) {
      const mobile = createHmac('sha256', '123').update(userProfile?.mobile).digest('hex')
      let email = null;
      if (userProfile?.email) email = createHmac('sha256', '123').update(userProfile?.email).digest('hex')
      eventData.user = {
        mobile,
        email,
        user_id: userProfile?._id
      }
    }
    sendGTMEvent(eventData)
  }

  return (
    <>
      <div
        className="checkout"
        style={{ minHeight: `${boundingRect?.height + 120}px` }}
      >
        <div className="container">
          <div className="checkout_wrapper">
            <div className="checkout_left heightfix">
              <h4>{translation?.cartPage?.addShipping ?? "Add Shipping Address"}</h4>
              <button onClick={handleShow} className="new-address">
                {translation?.myAccount?.addressAdd ?? "Add New Address"}
              </button>

              <div className="checkout_left-flex">
                {address
                  ?.sort((a: any, b: any) => b.isDefault - a.isDefault)
                  ?.map((items: any) => (
                    <div
                      onClick={() => setSelectedAddress(items?._id)}
                      style={{ position: "relative" }}
                      className={`checkout_card ${items?._id === selectedAddress ? "selected" : ""
                        }`}
                      key={items?._id}
                    >
                      {items?._id === selectedAddress && (
                        <Image unoptimized style={{ width: "fit-content", position: 'absolute', top: -10, right: -10 }} src="/icons/address-check.svg" alt="check" width={30} height={30} />
                      )}
                      <div style={{ display: "flex", gap: "1rem", alignItems: "center" }}>
                        <h5>{items.name}</h5>
                        <div style={{ padding: "0.3rem 1rem", backgroundColor: "#0000001A", borderRadius: "0.4rem" }}>
                          {
                            items?.type === "Home" ? translation?.myAccount?.addressHome
                              : items?.type === "Work" ? translation?.myAccount?.addressWork
                                : translation?.myAccount?.addressOther
                          }
                        </div>
                      </div>
                      <ul>
                        <li>
                          <Link href="">{items.number}</Link>
                        </li>

                        <li>
                          {items?.countryCode}-{items?.mobile}
                        </li>

                        <li>
                          {items.suiteUnit && <span>{items.suiteUnit},</span>}
                          {items.street}, {items.city},{items.emirates},
                          {items.country} -{items.postalCode}
                        </li>

                        <li className="cta">
                          <button
                            className={items?.isDefault ? "activeBtn" : ""}
                            disabled={items?.isDefault}
                            onClick={() => setDefault(items)}
                          >
                            {items?.isDefault
                              ? (translation?.myAccount?.addressDefault ?? "Default billing address")
                              : (translation?.myAccount?.setDefaultAddress ?? "Set as default billing address")}
                          </button>
                        </li>

                        <li className="btns">
                          <button onClick={() => deleteAddress(items?.refid)}>
                            {translation?.myAccount?.addressRemove ?? "Remove"}
                          </button>
                          <button onClick={() => handleEdit(items)}>
                            {translation?.myAccount?.addressEdit ?? "Edit"}
                          </button>
                        </li>
                      </ul>
                    </div>
                  ))}
              </div>

              {!isLoading && address?.length === 0 && (
                <EmptyState
                  icon="address"
                  title="No Address Found!"
                  description="Please Add Address"
                />
              )}
              {isLoading && (
                <LogoAnimation className="d-flex justify-content-center py-5" />
              )}
              <Link
                href={{
                  pathname: `/checkout/${params.type}/shipping-method`,
                  // pathname: `/checkout/${params.type}/payment-method`,
                  // query: { address: selectedAddress },
                }}
                onClick={sendDataLayer}
              >
                <button
                  disabled={(!isLoading && address?.length === 0) || !selectedAddress}
                  className="continue-delivery-btn"
                >
                  {translation?.cartPage?.continueToDelivery ?? "Continue to Delivery"}
                </button>
              </Link>
              {/* <label htmlFor="checkbox">
                <input type="checkbox" id="checkbox" />
                Save my information for a faster checkout
              </label> */}
            </div>

            <div className="checkout_right">
              <CheckoutOrderSummary
                params={params}
                shippingCharge={
                  // params?.type === "cart" ? shippingCharge?.data?.price || 0 : 0
                  params?.type === "cart" ? 0 || 0 : 0
                }
              />
            </div>
          </div>
        </div>
      </div>
      {show && (
        <AuthProvider>

          <MyAddressPopUp
            show={show}
            handleClose={handleClose}
            address={editAddress}
            setAddress={(e) => {
              setSelectedAddress(e);
            }}
          />
        </AuthProvider>
      )}
    </>
  );
}

export default Page;
