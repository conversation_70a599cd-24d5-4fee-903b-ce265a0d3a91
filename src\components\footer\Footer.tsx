import Link from "next/link";
import "./footer.scss";
import Image from "next/image";
import NewsLetterInput from "./NewsLetterInput";
import { LocaleSwitch } from "./LocaleSwitch";
import { endpoints } from "@/config/apiEndpoints";
import { cookies } from "next/headers";

export default async function Footer({ locale, translation }: { locale: string; translation: any }) {
  // const cookieLocale = cookies().get("Next-Locale")?.value || "sa-en";
    const [storeId, language] = locale.split("-");
  const res = await fetch(process.env.NEXT_PUBLIC_API_URL + endpoints.footer, {
    headers: {
      "Content-Type": "application/json",
      Accept: "application/json",
      language: language || "en",
      storeid: storeId || "sa"
    },
    next: {
      tags: ["footer"],
    },
    cache: "force-cache"
  });
  const data = await res.json();
  const footerData = data.result;

  const icons = [
    "/images/footer/f4.svg",
    "/images/footer/f3.svg",
    "/images/footer/f2.svg",
    "/images/footer/f1.svg"
  ]


  return (
    <footer>
      <div className="prefooter">
        <div className="container">
          <div className="prefooter_wrapper">
            {translation?.footer?.top?.map((item: any, n:number) => {
              if(n == 1) return null;
              return (
              <div className="prefooter_items" key={n}>
                <Image
                  priority
                  quality={100}
                  src={icons[n]}
                  width={40}
                  height={40}
                  alt="Picture of the author"
                />
                <div className="prefooter_content">
                  <h5>{item.title}</h5>
                  <h6>{item.description}</h6>
                </div>
              </div>
            )
            })}
          </div>
        </div>
      </div>

      <div className="footer">
        <div className="container">
          <div className="footer_top">
            <div className="footer_logo">
              <Link data-disable-nprogress={true} href="/">
                <Image
                  quality={100}
                  // src="/images/footer/logo.png"
                  src={footerData?.logo ?? "/images/footer/logo.png"}
                  width={210}
                  height={41}
                  alt="Picture of the author"
                />
              </Link>
              <div className="footer_icons">
                {/* <span>{translation?.footer?.followUs ?? 'Follow Us'}{" :"}</span> */}
                {footerData?.socialMedia?.map((social: any) => (
                  <Link
                    data-disable-nprogress={true}
                    key={social?.name + social?.title}
                    target="_blank"
                    title={social?.name}
                    href={social?.link || ""}
                  // href={"/images/footer/social.png"}
                  style={{display: "flex"}}
                  >
                    <Image
                      quality={100}
                      src={social?.icon ?? ""}
                      width={20}
                      height={20}
                      alt={social?.name || "Social media"}
                    />
                  </Link>
                ))}
                {/* <Link data-disable-nprogress={true}  target="_blank" href={footerData?.socialMedia?.facebook}>
                  <Image  quality={100}
                    src="/images/footer/facebook.png"
                    width={20}
                    height={20}
                    alt=""
                  />
                </Link>
                <Link data-disable-nprogress={true}  target="_blank" href={footerData?.socialMedia?.twitter}>
                  <Image  quality={100}
                    src="/images/footer/social.png"
                    width={20}
                    height={20}
                    alt=""
                  />
                </Link>
                <Link data-disable-nprogress={true}  target="_blank" href={footerData?.socialMedia?.instagram}>
                  <Image  quality={100}
                    src="/images/footer/instagram.png"
                    width={20}
                    height={20}
                    alt=""
                  />
                </Link>
                <Link data-disable-nprogress={true}  target="_blank" href={footerData?.socialMedia?.linkedin}>
                  <Image  quality={100}
                    src="/images/footer/linkedin.png"
                    width={20}
                    height={20}
                    alt=""
                  />
                </Link> */}
              </div>
            </div>
            <NewsLetterInput translation={translation} />
          </div>

          <div className="footer_middle">
            <div className="footer_info">
              <h5>{translation?.footer?.doYouNeedHelp ?? "Do you need help ?"}</h5>
              <p>{footerData?.contact?.address}</p>
              <a
                data-disable-nprogress={true}
                href={"mailto:" + footerData?.contact?.email}
                className="footer_info-mail"
              >
                {footerData?.contact?.email}
              </a>

              <div className="d-sm-block d-flex justify-content-center gap-3">
                <a
                  data-disable-nprogress={true}
                  href={"tel:" + footerData?.contact?.mobileOne}
                  className="footer_info-number"
                >
                  {footerData?.contact?.mobileOne}
                </a>

                <a
                  data-disable-nprogress={true}
                  href={"tel:" + footerData?.contact?.mobileTwo}
                  className="footer_info-number"
                >
                  {footerData?.contact?.mobileTwo}
                </a>
              </div>
            </div>

            <div className="footer_links">
              <h5>{footerData?.footer?.firstCol?.title}</h5>
              <ul>
                {footerData?.footer?.firstCol?.links?.map((data: any) => (
                  <li key={data?.title}>
                    <Link data-disable-nprogress={true} href={data?.link || ""}>
                      {data?.title}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            <div className="footer_links">
              <h5>{footerData?.footer?.secondCol?.title}</h5>
              <ul>
                {footerData?.footer?.secondCol?.links?.map((data: any) => (
                  <li key={data?.title}>
                    <Link data-disable-nprogress={true} href={data?.link || ""}>
                      {data?.title}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            <div className="footer_links">
              <h5>{footerData?.footer?.thirdCol?.title}</h5>
              <ul>
                {footerData?.footer?.thirdCol?.links?.map((data: any) => (
                  <li key={data?.title}>
                    <Link data-disable-nprogress={true} href={data?.link || ""}>
                      {data?.title}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            <div className="footer_links">
              <h5>{footerData?.footer?.fourthCol?.title}</h5>
              <ul>
                {footerData?.footer?.fourthCol?.links?.map((data: any) => (
                  <li key={data?.title}>
                    <Link scroll={true} data-disable-nprogress={true} href={data?.link || ""}>
                      {data?.title}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          <div className="footer_bottom">
            <p>
              {translation?.footer?.poweredBy ?? 'Powered'}{" "}
              <Link
                data-disable-nprogress={true}
                href="https://webcastle.ae"
                target="_blank"
              >
                <Image
                  quality={100}
                  src={"/icons/webcastle.svg"}
                  style={{ width: "80px", marginBottom: "6px" }}
                  width={80}
                  alt="webcastle logo"
                  height={17}
                />{" "}
              </Link>
              | {footerData?.copyRight}
            </p>
            {/* <LocaleSwitch /> */}
          </div>
        </div>
      </div>
    </footer>
  );
}
