"use client";

import { Swiper, SwiperSlide } from "swiper/react";
import "./store-locator-slider.scss";
import {
  Pagination,
  Navigation,
  Autoplay,
  Keyboard,
  Mousewheel,
} from "swiper/modules";
import Link from "next/link";
import Image from "next/image";
import { useEffect, useState } from "react";

function StoreLocatorSlider({ stores }: any) {
  const [filteredStores, setFilteredStores] = useState(stores);
  const [query, setQuery] = useState<string>("");

  useEffect(() => {
    if (query === "") return setFilteredStores(stores);
    setFilteredStores(
      stores.filter(
        (store: any) =>
          store.name.toLowerCase().includes(query.toLowerCase()) ||
          store.address.toLowerCase().includes(query.toLowerCase())
      )
    );
  }, [query]);

  return (
    <section className="locator-slider">
      <div className="container">
        <div className="locator-slider_search">
          <h4>Find Our Stores</h4>
          <label htmlFor="">City, Country or Location</label>
          <input
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            id=""
            name=""
            type="text"
          />
          <button>
            <Image quality={100} priority className="object-contain"
              src="/images/common/Magnifier.png"
              width={24}
              height={24}
              alt="search"
            />
          </button>
        </div>

        <Swiper
          slidesPerView={3}
          spaceBetween={32}
          cssMode
          keyboard
          mousewheel
          pagination={{
            type: "progressbar",
          }}
          navigation={false}
          modules={[Pagination, Navigation, Autoplay, Keyboard, Mousewheel]}
          speed={500}
          autoplay={{
            delay: 2000,
            disableOnInteraction: false,
            pauseOnMouseEnter: true,
          }}
          className="mySwiper"
          breakpoints={{
            320: {
              slidesPerView: 1,
              spaceBetween: 20,
            },

            650: {
              slidesPerView: 2,
            },

            900: {
              slidesPerView: 3,
            },
          }}
        >
          {filteredStores?.map((items: any, index: any) => (
            <SwiperSlide key={index}>
              <h5>{items?.name}</h5>
              <ul>
                <li>{items?.address}</li>
                <li>
                  <Link href={`tel:${items?.countryCode + items?.mobile}`}>
                    {items?.countryCode + " " + items?.mobile}
                  </Link>
                </li>
                <li>
                  <Link href={"mailto:" + items?.email}>{items.email}</Link>
                </li>
              </ul>
              <Link
                target="_blank"
                href={`https://www.google.com/maps?q=${items?.coordinates?.lat},${items?.coordinates?.long}`}
                className="primary-btn"
              >
                Get Directions
              </Link>
            </SwiperSlide>
          ))}

          {filteredStores?.length === 0 && query !== "" && (
            <SwiperSlide>No Results Found!</SwiperSlide>
          )}
        </Swiper>
      </div>
    </section>
  );
}

export default StoreLocatorSlider;
