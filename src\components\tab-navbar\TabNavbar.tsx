"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import "./tab-navbar.scss";
import { useState } from "react";
import Button from "react-bootstrap/Button";
import LogoutPopUp from "../logout-popup/LogoutPopUp";

export default function TabNavbar({ navlinks, hideLogout, translation }: any) {
  const pathname = usePathname();
  const [show, setShow] = useState(false);

  const handleClose = () => setShow(false);
  const handleShow = () => setShow(true);

  const isActive = (path: string) => {
    return pathname?.split("/")[3] === path?.split("/")?.[2];
  };

  return (
    <nav className="tab-navbar">
      <div className="container">
        <ul className="tab-navbar_list">
          {navlinks.map((link: any) => (
            <li
              key={link.href}
              className={isActive(link.href) ? "tab-navbar_item active" : "tab-navbar_item"}>
              <Link href={link.href}>{link.name}</Link>
            </li>
          ))}
          {!hideLogout && (
            <li className="logout-btn">
              <button onClick={handleShow}>
               {translation?.logout ?? "Logout"}
              </button>
              <LogoutPopUp show={show} handleClose={handleClose} />
            </li>
          )}
        </ul>
      </div>
    </nav>
  );
}
