import ReactQueryProvider from "@/contexts/ReactQuerProvider";
import { ReactNode, Suspense } from "react";
import { Metadata, Viewport } from "next";
import { NavigationEvents } from "@/components/navigation-events";
import { Outfit } from "next/font/google";
import Head from "next/head";
import Script from "next/script";
import { GoogleAnalytics } from '@next/third-parties/google'
import { GoogleTagManager } from '@next/third-parties/google'
import { cookies } from "next/headers";

type Props = {
  children: ReactNode;
};

export const metadata: Metadata = {
  title: "Yateem Optician",
  description: "Yateem Optician | Shop for the best contact lenses, spectacles and more",
  keywords:
    "yateem optician, yateem, optician, contact lenses, spectacles, glasses, eyeglasses, sunglasses, prescription lenses, prescription contact lenses, prescription spectacles, prescription glasses, prescription eyeglasses, prescription sunglasses",
  metadataBase: new URL("https://yateem.com"),
  alternates: {
    languages: {
      en: "/en",
      ar: "/ar",
    },
  },
  openGraph: {
    images: ["/opengraph-image.png"],
  },
  verification: { google: "EGuVZ8w6tQ_or029KIMR_3uuPbdW1VaPVHxyCwdSeHs" }
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1.0,
  // Also supported by less commonly used
  interactiveWidget: "resizes-content",
};

const outfit = Outfit({ subsets: ["latin"] });
const TAMARA_PUBLIC_KEY = process.env.TAMARA_PUBLIC_KEY ?? "";
const TABBY_PUBLIC_KEY = process.env.NEXT_PUBLIC_TABBY_PUBLIC_KEY ?? "";

// Since we have a `not-found.tsx` page on the root, a layout file
// is required, even if it's just passing children through.
export default function RootLayout(props: {
  children: React.ReactNode;
  auth: React.ReactNode;
  params: { locale: string };
}) {
  const GAID = process.env.GAID ?? "";
  const GTID = process.env.GTID ?? "";
  const Cookies = cookies();
  const locale = Cookies.get("Next-Locale")?.value || "ae-en";
  console.log(locale)
  return (
    <html>
      <head>
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
        />
        <meta name="google-site-verification" content="EGuVZ8w6tQ_or029KIMR_3uuPbdW1VaPVHxyCwdSeHs" />

        <Script id='tabby-script' defer src="https://checkout.tabby.ai/tabby-promo.js" strategy="lazyOnload" async
        // dangerouslySetInnerHTML={{
        //   __html: `
        //   new TabbyPromo({
        //     selector: '#tabby',
        //     publicKey: "${TABBY_PUBLIC_KEY}",
        //     merchantCode: "YOUAE",
        //   });
        // `,
        // }}
        ></Script>

      </head>
      {/* <Script src="https://www.googletagmanager.com/gtag/js?id=G-YGRP8XTNC2" /> */}
      {/* <Script id="google-analytics" strategy="afterInteractive">
        {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());

          gtag('config', 'G-YGRP8XTNC2');
        `}
      </Script> */}
      <body
        // tabIndex={0} 
        className={`${outfit.className} main-body ${props.params.locale?.split("-")[1] === "ar" ? "rtl" : ""}`}>
        <ReactQueryProvider>
          {props.children}
          {props.auth}
          {/* added for debugging, can omit in production */}
          {/* <Suspense fallback={null}>
            <NavigationEvents />
          </Suspense> */}
        </ReactQueryProvider>
        <GoogleAnalytics gaId={GAID} />
        <GoogleTagManager gtmId={GTID} />
        <Script id="clarity-script" strategy="afterInteractive">
          {`
                (function(c,l,a,r,i,t,y){
        c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
        t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
        y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
    })(window, document, "clarity", "script", "qjeu2zzhpn");

            window.tamaraWidgetConfig = {
          lang: ${locale?.split("-")[1]},
          country: "AE",
          publicKey: "${TAMARA_PUBLIC_KEY}"
        }

          `}
        </Script>
        <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-KGC7XJ83"
          height="0" width="0" style={{ display: "none", visibility: "hidden" }}></iframe></noscript>

        <Script defer src="https://cdn.tamara.co/widget-v2/tamara-widget.js" strategy="lazyOnload"></Script>
      </body>

    </html>
  );
}
