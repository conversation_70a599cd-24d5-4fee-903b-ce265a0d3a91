"use client";
import { useState } from "react";
import Offcanvas from "react-bootstrap/Offcanvas";
import Filter from "../filter/Filter";
import useFilter from "@/hooks/useFilter";

type SortType = {
  value: string;
  label: string;
};


function MobileSort({ show, handleClose, translation }: any) {
  const { active, setActive } = useFilter("sort");
  
  const sortOptions: SortType[] = [
    { value: "0", label: translation.newArrivals ?? "New Arrivals" },
    { value: "1", label: translation.sale ?? "Sale" },
    { value: "2", label: translation.exclusive ?? "Exclusive" },
    { value: "3", label: translation.priceLowToHigh ?? "Price: Low to High" },
    { value: "4", label: translation.priceHighToLow ?? "Price: High to Low" },
  ];
  
  const [selectedSort, setSelectedSort] = useState<string>(
    sortOptions[0].value
  );
  const handleSortChange = (value: string) => {
    setSelectedSort(value);
  };

  const handleSubmit = () => {
    setActive([selectedSort]);
    handleClose();
  };
  return (
    <>
      <Offcanvas
        className="sort-offcanvas"
        placement="bottom"
        show={show}
        onHide={handleClose}
      >
        {/* <Offcanvas.Header closeButton></Offcanvas.Header> */}
        <button className="sort-close-btn" style={{margin: '1rem', border: 'none', marginLeft: 'auto', background: 'none'}} onClick={handleClose}>
        <svg fill="#5e5e5e" height="16px" width="16px" version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink" viewBox="0 0 460.775 460.775" xmlSpace="preserve" stroke="#000000"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="M285.08,230.397L456.218,59.27c6.076-6.077,6.076-15.911,0-21.986L423.511,4.565c-2.913-2.911-6.866-4.55-10.992-4.55 c-4.127,0-8.08,1.639-10.993,4.55l-171.138,171.14L59.25,4.565c-2.913-2.911-6.866-4.55-10.993-4.55 c-4.126,0-8.08,1.639-10.992,4.55L4.558,37.284c-6.077,6.075-6.077,15.909,0,21.986l171.138,171.128L4.575,401.505 c-6.074,6.077-6.074,15.911,0,21.986l32.709,32.719c2.911,2.911,6.865,4.55,10.992,4.55c4.127,0,8.08-1.639,10.994-4.55 l171.117-171.12l171.118,171.12c2.913,2.911,6.866,4.55,10.993,4.55c4.128,0,8.081-1.639,10.992-4.55l32.709-32.719 c6.074-6.075,6.074-15.909,0-21.986L285.08,230.397z"></path> </g></svg>
        </button>
        <Offcanvas.Body>
          <h4>{translation?.sortBy ?? "Sort"}</h4>
          {sortOptions?.map((sort, index) => {
            return (
              <label htmlFor={sort?.value} key={index}>
                <input
                  type="radio"
                  id={sort?.value}
                  name="sort"
                  checked={selectedSort === sort.value}
                  onChange={() => handleSortChange(sort.value)}
                />
                {sort?.label}
              </label>
            );
          })}

          {/* <label htmlFor="lowtohigh">
            <input type="radio" id="lowtohigh" defaultChecked name="sort" />
            Price low to high
          </label>

          <label htmlFor="hightolow">
            <input type="radio" id="hightolow" name="sort" />
            Price high to low
          </label>

          <label htmlFor="trending">
            <input type="radio" id="trending" name="sort" />
            Trending
          </label> */}

          <button className="apply" onClick={handleSubmit}>
            {translation?.apply ?? "Apply"}
          </button>
        </Offcanvas.Body>
      </Offcanvas>
    </>
  );
}

export default MobileSort;
