import api from "@/config/axios.interceptor";
import "./enquity.scss";
import { endpoints } from "@/config/apiEndpoints";
import ErrorPage from "@/components/404/ErrorPage";
import EnquiryForm from "./EnquiryForm";
import Link from "next/link";
import { AuthProvider } from "@/contexts/AuthProvider";
import Store from "./Store";

async function Page({ searchParams }: { searchParams: { brand: string } }) {
  try {
    const brand = searchParams.brand;
    const res = await api
      .get(`${endpoints.availableStores}/${brand}`)
      .then((res) => res.data)
      .catch((e) => {
        throw new Error(e);
      });
    const stores = res.result;

    return (
      <main>
        <div className="enquiry">
          <div className="container">
            <div className="enquiry_flex">
              <Store stores={stores} />
              <AuthProvider>
                <EnquiryForm brand={brand} />
              </AuthProvider>
            </div>
          </div>
        </div>
      </main>
    );
  } catch (err) {
    return <ErrorPage errorcode="404" />;
  }
}

export default Page;
