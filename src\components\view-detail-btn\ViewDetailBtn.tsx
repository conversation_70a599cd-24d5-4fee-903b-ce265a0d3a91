import Link from "next/link";
import "./view-btn.scss";
import { TranslationContext } from "@/contexts/Translation";
import { useContext } from "react";

function ViewDetailBtn({ link }: any) {
  const { translation: { orderPage} } = useContext(TranslationContext);
  return (
    <>
      <Link href={link} className="view-detail">
        {orderPage?.viewDetail ?? "View Detail"} 
      </Link>
    </>
  );
}

export default ViewDetailBtn;
