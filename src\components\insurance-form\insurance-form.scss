.insurance-form {
  margin-top: 3.5rem;

  @media (max-width: 575.98px) {
    margin-top: 1.5rem;
    order: 2;
  }

  &_wrapper {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-top: 3.4rem;

    @media (max-width: 575.98px) {
      flex-direction: column;
    }

    .terms-text {
      button {
        color: #000000 !important;
      }
    }
  }

  &_inputs {
    width: calc(50% - 4.3rem);
    position: relative;

    @media (max-width: 767.98px) {
      width: calc(50% - 2rem);
    }

    @media (max-width: 575.98px) {
      width: 100%;
    }

    &:not(:last-child) {
      margin-bottom: 3.6rem;

      @media (max-width: 575.98px) {
        margin-bottom: 3rem;
      }
    }

    // &:nth-of-type(7) {
    //   &::after {
    //     content: "";
    //     background-image: url(../../../public/images/common/fi-rr-clip.png);
    //     background-repeat: no-repeat;
    //     background-size: cover;
    //     background-position: right;
    //     width: 20px;
    //     height: 20px;
    //     position: absolute;
    //     right: 0;
    //   }
    // }

    label {
      margin-bottom: 0.8rem;
      color: #808080;
      font-size: 1.3rem;
      font-weight: 400;

      @media (max-width: 575.98px) {
        font-size: 1.4rem;
        font-size: 1.5rem;
      }
    }

    input {
      width: 100%;
      border: none;
      border-bottom: 1px solid #e2e4e5;
      padding-left: 1.5rem;
      padding-bottom: 0.8rem;
      position: relative;
      z-index: 2;
      color: #000000 !important;

      &::placeholder {
        color: #d8d8d8;
        font-size: 1.8rem;
        font-weight: 400;

        @media (max-width: 575.98px) {
          font-size: 1.5rem;
        }
      }

      &:focus-visible {
        border-bottom: 1px solid #e2e4e5;
        outline: none;
        box-shadow: none;
      }
    }

    input[type="file"] {
      padding-right: 5rem;
    }

    input[type="button"] {
      color: #000000 !important;
    }

    select {
      width: 100%;
      border: none;
      border-bottom: 1px solid #e2e4e5;
      padding-left: 1.5rem;
      padding-bottom: 0.8rem;
      appearance: none;
      background-color: transparent;
      color: black !important;

      &::placeholder {
        color: #242426;
        font-size: 1.8rem;
        font-weight: 400;
      }

      &:focus-visible {
        border-bottom: 1px solid #e2e4e5;
        outline: none;
        box-shadow: none;
      }
    }

    .select {
      position: relative;

      .select-container {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        z-index: 100;

        .drop-item {
          z-index: 10;

          img {
            width: 30px;
            margin-right: 10px;
          }
        }
      }

      .css-lkh0o5-menu {
        margin-top: 29px !important;
      }

      // label {
      //   color: #242426;
      //   font-size: 1.4rem;
      //   font-weight: 400;
      //   line-height: 2rem;
      //   padding-bottom: 0.8rem;
      // }

      .countrycode {
        display: flex;
        column-gap: 1.6rem;
        align-items: baseline;

        .countrycode-icon {
          position: relative;
          max-width: 80px;

          &::after {
            content: "";
            background-image: url("../../../public/images/common/Icon.png");
            width: 2.4rem;
            height: 2.4rem;
            position: absolute;
            right: -7px;
            z-index: 5;
          }
        }
      }

      .react-select {
        width: 100%;
        max-width: 80px;
        top: 36px;
        left: 0;
        z-index: 10;
        opacity: 0;
      }

      input[type="number"]::-webkit-inner-spin-button,
      input[type="number"]::-webkit-outer-spin-button {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
      }

      input {
        width: 100%;
        color: #242426;
        font-size: 1.8rem;
        font-weight: 400;
        line-height: 2.8rem;
        border: none;
        border-bottom: 1px solid #e2e4e5;
        padding: 0 1.5rem;
        padding-bottom: 0.8rem;

        &:focus-within {
          outline: none;
          border-bottom: 1px solid #e2e4e5;
        }

        &::placeholder {
          color: #cacaca;
          font-size: 1.8rem;
          font-weight: 400;
        }
      }

    }

    .inp {
      display: none;
    }

    p {
      font-size: 1.3rem;
      font-weight: 400;
      line-height: 1.9rem;
      letter-spacing: -0.011em;
      color: rgba(0, 0, 0, 0.8);
      margin-top: 1.5rem;
      opacity: 50%;
    }

    .css-1jqq78o-placeholder {
      font-size: 1.3rem;
      font-weight: 400;
      color: #999999;
      line-height: 1.6rem;
    }

    .css-13cymwt-control {
      border: none;
      border-bottom: 1px solid rgba(0, 0, 0, 0.15) !important;
      border-radius: 0;
      width: 100%;

      .css-1fdsijx-ValueContainer {
        padding: 0 !important;
        margin: 0 !important;

        .css-qbdosj-Input {
          padding: 0;
          margin: 0;
        }
      }

      .css-1u9des2-indicatorSeparator {
        display: none;
      }

      .css-1xc3v61-indicatorContainer {
        padding-right: 0;
      }

      .css-13cymwt-control {
        background-color: red;

        &:hover {
          border: none;
          background-color: blue;
        }
      }
    }

    .css-1dimb5e-singleValue {
      color: #000000;
      font-size: 1.3rem;
      font-weight: 400;
    }

    .css-t3ipsp-control {
      border: none !important;
      border-bottom: 1px solid rgba(0, 0, 0, 0.15) !important;
      outline: none;
      box-shadow: none;
      padding: 0;
      width: 100%;

      .css-1fdsijx-ValueContainer {
        padding: 0;
        margin: 0;
      }

      .css-1xc3v61-indicatorContainer {
        padding-right: 0;
      }
    }

    .css-1nmdiq5-menu {
      color: #000000;
      font-size: 1.3rem;
      font-weight: 400;
      z-index: 10;

      div {
        &>div {
          transition: .2s;

          &[aria-selected="true"] {
            background-color: black;
            color: white;
          }

          &[aria-selected="false"] {
            background-color: white;
            color: black;
          }

          &:hover {
            background-color: black;
            color: white;
          }
        }
      }

    }

    .css-1u9des2-indicatorSeparator {
      display: none;
    }

  }

  &_btn {
    text-align: center;
    width: 100%;
    margin-top: 1rem;

    .primary-btn {
      background-color: #000000;
      color: #fff;
      border: 1px solid #000;
      padding: 1.5rem 3.9rem;

      @media (max-width: 575.98px) {
        margin-top: 0rem;
        padding: 1.1rem 3.9rem;
      }

      &:hover {
        background-color: #ffffff;
        color: #000;
      }
    }
  }

  &_description {
    p {
      line-height: 2.72rem;
    }
  }
}

.app.rtl{
  .back{
    button{
      svg{
        transform: rotate(180deg);
      }
    }
  }
}