.select-preference {
    &.modal {
        &.show .modal-dialog {
            transform: none !important;
        }

        .modal-dialog {
            margin: 0;
            width: 100%;
            max-width: 68.3rem;
            position: fixed;
            right: 0;
            height: 100%;
        }

        .modal-header {
            padding: 0;
            border: none;

            h2 {
                text-align: left;
                line-height: 3.1rem;
                display: flex;
                align-items: center;
                column-gap: 1.4rem;
                font-size: 2.5rem;
                font-weight: 500;

                @media (max-width: 575.98px) {
                    font-size: 2.2rem;
                    color: #242731;
                }

                img {
                    width: 4rem;
                    height: 4rem;
                    object-fit: contain;

                    @media (max-width: 575.98px) {
                        display: none;
                    }
                }
            }

            .btn-close {
                background-image: url(../../../public/images/common/close.svg);
                width: 4.8rem;
                height: 4.8rem;
                background-position: center;
                background-repeat: no-repeat;
                background-size: contain;
                opacity: 1;
                padding: 0;

                @media (max-width: 575.98px) {
                    width: 2.8rem;
                    height: 2.8rem;
                    transform: none;
                }

                &:focus {
                    box-shadow: none;
                    outline: none;
                    border: none;
                }
            }
        }

        .modal-content {
            border: none;
            background: #fff;
            border-radius: 0rem;
            overflow-y: auto;
            height: 100vh;

            @media (max-width: 575.98px) {
                height: 100%;
            }
        }

        .modal-wrap {
            padding: 4rem 5rem 0rem 6.2rem;

            @media (max-width: 575.98px) {
                padding: 1.5rem;
                padding-bottom: 0;
            }

        }

        .modal-body {
            padding: 0;
            margin-top: 2.5rem;

            @media (max-width: 575.98px) {
                margin-top: 2.1rem;
            }

            ul {
                display: flex;
                justify-content: space-between;
                margin-bottom: 2rem;
                width: 100%;
                max-width: 51.4rem;

                @media (max-width: 575.98px) {
                    margin-bottom: 2.5rem;
                }

                li {
                    display: flex;
                    align-items: center;
                    column-gap: 1rem;
                    position: relative;
                    cursor: pointer;

                    @media (max-width: 575.98px) {
                        flex-direction: column;
                        align-items: flex-start;
                    }

                    &:not(:last-child) {
                        &::after {
                            content: "";
                            position: absolute;
                            background-color: rgba(0, 0, 0, 0.4);
                            height: 1px;
                            width: 4.5rem;
                            left: 94%;
                            transform: translateX(50%);

                            @media (max-width: 575.98px) {
                                top: 13px;
                                left: 70%;
                                transform: none;
                            }
                        }
                    }

                    .number {
                        width: 2.8rem;
                        height: 2.8rem;
                        border-radius: 50%;
                        background-color: rgba(0, 0, 0, 0.5);
                        display: inline-block;
                        color: #ffffff;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 1.3rem;
                        font-weight: 500;
                    }

                    .title {
                        color: #000000;
                        font-size: 1.5rem;
                        font-weight: 500;
                        opacity: 40%;

                        @media (max-width: 575.98px) {
                            margin-top: 9px;
                        }
                    }

                    &.isActive {
                        &::after {
                            background-color: rgba(0, 0, 0, 1);
                        }

                        .number {
                            background-color: #000000;
                        }

                        .title {
                            opacity: 1;
                        }
                    }
                }


            }

        }

        .modal-footer {
            padding: 0;
            border: none;
            background-color: #F2F4F9;
            padding: 0.8rem 4.5rem .8rem 6.2rem;
            margin-top: auto;
            display: flex;
            justify-content: space-between;
            flex-wrap: nowrap;

            @media (max-width: 575.98px) {
                margin-top: 2.5rem;
                padding: 1rem 1.5rem;
            }

            button {
                color: #fff;
                text-align: center;
                font-size: 1.5rem;
                font-weight: 500;
                border: none;
                background-color: #000;
                height: 4.5rem;
                width: 15rem;
                border-radius: 6rem;
                padding: 0;
                margin: 0;

                &.disabled {
                    background-color: #797A7D;
                }
            }

            h5 {
                font-size: 1.4rem;
                font-weight: 600;
                color: #000000;
                margin: 0;

                span {
                    font-size: 1.8rem;
                }
            }
        }
    }

    &_box {
        border: 1px solid #E4E4E4;
        border-radius: 1.5rem;
        // cursor: pointer;
        display: flex;
        padding: 2.2rem;
        column-gap: 2rem;
        position: relative;

        &::after {
            content: "";
            background-image: url(../../../public/images/modal/Arrow-left.svg);
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 1.4rem;
            height: 1.4rem;
            right: 3.8rem;
        }

        &:not(:last-child) {
            margin-bottom: 1.5rem;
        }

        img {
            width: 4.8rem;
            height: 4.8rem;
        }

        h5 {
            font-size: 1.6rem;
            font-weight: 600;
            line-height: 2.4rem;
            letter-spacing: -0.011em;

            @media (max-width: 575.98px) {
                font-size: 1.4rem;
                font-weight: 500;
                line-height: 1.7rem;
            }
        }

        p {
            font-size: 1.3rem;
            font-size: 400;
            line-height: 1.6rem;
            max-width: 33.4rem;
            width: 100%;

            @media (max-width: 575.98px) {
                margin-top: 0.6rem;
                max-width: 19.8rem;
            }
        }
    }

    .skip {
        color: rgba(114, 108, 108, 1);
        font-size: 1.4rem;
        font-weight: 400;
        line-height: 1.7rem;
        text-align: right;
        display: block;
        text-decoration: underline;
        margin-top: 1rem;
    }

    .cta {
        font-size: 1.2rem;
        font-weight: 400;
        line-height: 1.5rem;
        color: #000000;
        margin-top: 2.4rem;

        a {
            color: #000000;
        }
    }
}