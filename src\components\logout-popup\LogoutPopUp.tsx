"use client";

import { logout } from "@/lib/methods/auth";
import "./logoout-popup.scss";
import Modal from "react-bootstrap/Modal";
import { useContext } from "react";
import { TranslationContext } from "@/contexts/Translation";

function LogoutPopUp({ show, handleClose }: any) {
  const { translation: { popup }} = useContext(TranslationContext)
  return (
    <Modal
      className="logout-popup"
      show={show}
      onHide={handleClose}
      backdrop="static"
      keyboard={false}
      centered>
      <Modal.Header closeButton></Modal.Header>
      <Modal.Body>
        <h2>{popup.logoutTxt ?? "Are you sure want to logout ?"}</h2>
        <div className="logout-popup-btn">
          <button className="logout-btn" onClick={() => logout({ popup })}>
            {popup.logoutBtn ?? "Logout"}
          </button>
          <button className="cancel-btn" onClick={handleClose}>
            {popup.cancel ?? "Cancel"}
          </button>
        </div>
      </Modal.Body>
    </Modal>
  );
}

export default LogoutPopUp;
