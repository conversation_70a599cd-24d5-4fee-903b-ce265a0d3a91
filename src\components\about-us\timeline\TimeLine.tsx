import "./timeline.scss";
import Image from "next/image";

function TimeLine({ timelineData }: any) {
  return (
    <section className="timeline">
      <div className="container">
        <div className="timeline_head">
          <h2>{timelineData?.title}</h2>
          <p>{timelineData?.description}</p>
        </div>
        {timelineData?.timeline?.length > 0 ? <div className="timeline_wrapper">
          <ul>
            {timelineData?.timeline?.map((items: any, index: any) => (
              <li key={index + items?._id}>
                <div className="content">
                  <Image quality={100} priority src={items.image ?? ""} width={379} height={165} alt="timeline image" />
                  <h3>{items.title}</h3>
                  <p>{items.description}</p>
                </div>
                <div className="time">
                  <h4>{items.year}</h4>
                </div>
              </li>
            ))}

            <div style={{ clear: "right" }} />
          </ul>
        </div>: ""}
      </div>
    </section>
  );
}

export default TimeLine;
