export const powers = [
    "-14.00", "-13.75", "-13.50", "-13.25", "-13.00", "-12.75", "-12.50", "-12.25", "-12.00", "-11.75",
    "-11.50", "-11.25", "-11.00", "-10.75", "-10.50", "-10.25", "-10.00", "-9.75", "-9.50", "-9.25",
    "-9.00", "-8.75", "-8.50", "-8.25", "-8.00", "-7.75", "-7.50", "-7.25", "-7.00", "-6.75", "-6.50",
    "-6.25", "-6.00", "-5.75", "-5.50", "-5.25", "-5.00", "-4.75", "-4.50", "-4.25", "-4.00", "-3.75",
    "-3.50", "-3.25", "-3.00", "-2.75", "-2.50", "-2.25", "-2.00", "-1.75", "-1.50", "-1.25", "-1.00",
    "-0.75", "-0.50", "-0.25", "None/Planar", "+0.25", "+0.50", "+0.75", "+1.00", "+1.25", "+1.50", "+1.75", "+2.00",
    "+2.25", "+2.50", "+2.75", "+3.00", "+3.25", "+3.50", "+3.75", "+4.00", "+4.25", "+4.50", "+4.75", "+5.00",
    "+5.25", "+5.50", "+5.75", "+6.00", "+6.25", "+6.50", "+6.75", "+7.00", "+7.25", "+7.50", "+7.75", "+8.00",
    "+8.25", "+8.50", "+8.75", "+9.00", "+9.25", "+9.50", "+9.75", "+10.00"
]

export const pdValues = [
    "20.00mm", "20.50mm", "21.00mm", "21.50mm", "22.00mm", "22.50mm", "23.00mm", "23.50mm", "24.00mm", "24.50mm",
    "25.00mm", "25.50mm", "26.00mm", "26.50mm", "27.00mm", "27.50mm", "28.00mm", "28.50mm", "29.00mm", "29.50mm",
    "30.00mm", "30.50mm", "31.00mm", "31.50mm", "32.00mm", "32.50mm", "33.00mm", "33.50mm", "34.00mm", "34.50mm",
    "35.00mm", "35.50mm", "36.00mm", "36.50mm", "37.00mm", "37.50mm", "38.00mm", "38.50mm", "39.00mm", "39.50mm",
    "40.00mm"
]

export const axis = [
    "0°", "1°", "2°", "3°", "4°", "5°", "6°", "7°", "8°", "9°", "10°", "11°", "12°", "13°", "14°", "15°",
    "16°", "17°", "18°", "19°", "20°", "21°", "22°", "23°", "24°", "25°", "26°", "27°", "28°", "29°",
    "30°", "31°", "32°", "33°", "34°", "35°", "36°", "37°", "38°", "39°", "40°", "41°", "42°", "43°",
    "44°", "45°", "46°", "47°", "48°", "49°", "50°", "51°", "52°", "53°", "54°", "55°", "56°", "57°",
    "58°", "59°", "60°", "61°", "62°", "63°", "64°", "65°", "66°", "67°", "68°", "69°", "70°", "71°",
    "72°", "73°", "74°", "75°", "76°", "77°", "78°", "79°", "80°", "81°", "82°", "83°", "84°", "85°",
    "86°", "87°", "88°", "89°", "90°", "91°", "92°", "93°", "94°", "95°", "96°", "97°", "98°", "99°",
    "100°", "101°", "102°", "103°", "104°", "105°", "106°", "107°", "108°", "109°", "110°", "111°", "112°",
    "113°", "114°", "115°", "116°", "117°", "118°", "119°", "120°", "121°", "122°", "123°", "124°", "125°",
    "126°", "127°", "128°", "129°", "130°", "131°", "132°", "133°", "134°", "135°", "136°", "137°", "138°",
    "139°", "140°", "141°", "142°", "143°", "144°", "145°", "146°", "147°", "148°", "149°", "150°", "151°",
    "152°", "153°", "154°", "155°", "156°", "157°", "158°", "159°", "160°", "161°", "162°", "163°", "164°",
    "165°", "166°", "167°", "168°", "169°", "170°", "171°", "172°", "173°", "174°", "175°", "176°", "177°",
    "178°", "179°", "180°"
]

