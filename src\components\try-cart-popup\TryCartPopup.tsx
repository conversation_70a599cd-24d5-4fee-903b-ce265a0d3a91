"use client";

import "./try-cart-popup.scss";
import Button from "react-bootstrap/Button";
import Modal from "react-bootstrap/Modal";
import Image from "next/image";
import { useEffect, useMemo, useState } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import api from "@/config/axios.interceptor";
import { endpoints } from "@/config/apiEndpoints";
import { toast } from "sonner";
import { getTryCart } from "@/lib/methods/cart";
import { useRouter, useSearchParams } from "next/navigation";
import { useLocaleContext } from "@/contexts/LocaleProvider";

type TryCartProps = {
  show?: boolean;
  handleClose?: () => void;
  showBtn?: boolean;
  product?: any;
  showMsg?: boolean;
};

function TryCartPopup({
  show,
  handleClose,
  showBtn,
  product,
  showMsg,
}: TryCartProps) {
  const [showTryCart, setShowTryCart] = useState(false);
  const handleShowTryCart = () => setShowTryCart(true);
  const router = useRouter();
  const queryClient = useQueryClient();
  const searchParams = useSearchParams();
  const [selectedSize, setSelectedSize] = useState(
    product?.sizes?.[0]?._id || ""
  );
  const size =
    searchParams?.get("size") || product?.sizes?.[0]?.size?._id || null;

  useEffect(() => {
    const sizeFromParams = searchParams?.get("size");
    if (sizeFromParams) {
      setSelectedSize(sizeFromParams);
    }
  }, [searchParams]);
  const {currencyCode} = useLocaleContext()

  const addtoTryCart = () => {
    api
      .post(endpoints.addToTryCart, {
        product: product?._id,
        size: size,
      })
      .then((res) => {
        if (res.data.errorCode === 0) {
          queryClient.invalidateQueries({ queryKey: ["tryCart"] }).then(() => {
            handleShowTryCart();
          });
          queryClient.invalidateQueries({ queryKey: ["cart", "counts"] });
          toast.success(res.data.message, {
            position: "bottom-center",
          });
        } else {
          toast.error(res.data.message);
        }
      })
      .catch((err) => {
        const error = err.response.data.message;
        toast.error(error);
        if (err.response.data.message !== "Already added in try cart") {
          router.push("/login");
        }
        return error;
      });
  };

  const removeFromTryCart = (id: any, size?: any) => {
    api
      .post(endpoints.removeTryCart, {
        product: id,
        size: size,
      })
      .then((res) => {
        if (res.data.errorCode === 0) {
          queryClient.invalidateQueries({ queryKey: ["tryCart"] }).then(() => {
            handleShowTryCart();
          });
          queryClient.invalidateQueries({ queryKey: ["cart", "counts"] });
          toast.success(res.data.message);
        } else {
          toast.error(res.data.message);
        }
      })
      .catch((err) => {
        const error = err.response.data.message;
        toast.error(error);
        return error;
      });
  };
  const handleCloseTryCart = () => {
    setShowTryCart(false);
    if (handleClose) handleClose();
  };

  const checkout = () => {
    router.push("/checkout/try-cart");
    handleCloseTryCart();
  };

  const {
    data: TryCartData,
    isLoading,
    error,
  } = useQuery({ queryKey: ["tryCart"], queryFn: getTryCart });

  const totalPrice: any = useMemo<number>(() => {
    return TryCartData?.products
      ? TryCartData?.products?.reduce(
          (acc: any, item: any) => acc + item.price,
          0
        )
      : 0;
  }, [TryCartData]);

  const navigateToProducts = () => {
    router.push("/products/home-try-on?tryHome=true");
    handleCloseTryCart();
  };

  return (
    <>
      {showBtn && (
        <button className="add-try-cart" onClick={addtoTryCart}>
          <Image
            quality={100}
            priority
            src="/images/common/try-cart-white.svg"
            width={1000}
            height={1000}
            alt="try cart"
          />
          Add to Try Cart
        </button>
      )}
      <Modal
        className="try-cart-popup"
        show={showTryCart || show}
        onHide={handleCloseTryCart}
        backdrop="static"
        keyboard={false}
        scrollable={true}
      >
        <Modal.Header closeButton></Modal.Header>

        <h2>
          Try Cart Items (
          {(TryCartData?.products?.length > 0 &&
            TryCartData?.products?.length?.toString()?.padStart(2, "0")) ||
            0}
          )
        </h2>
        <Modal.Body>
          {TryCartData?.products?.length > 0 &&
            TryCartData?.products?.map((items: any, index: any) => (
              <div key={items?._id} className="try-cart-flex">
                <Image
                  quality={100}
                  priority
                  src={items?.thumbnail ?? ""}
                  width={75}
                  height={75}
                  alt="product image"
                />
                <div className="try-cart-items">
                  <ul>
                    <li>
                      {items.name}
                      <button
                        onClick={() =>
                          removeFromTryCart(items?._id, items?.size?._id)
                        }
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="20"
                          viewBox="0 0 16 20"
                          fill="none"
                        >
                          <path
                            d="M6.9 8C6.9 7.50294 6.49706 7.1 6 7.1C5.50294 7.1 5.1 7.50294 5.1 8H6.9ZM5.1 16C5.1 16.4971 5.50294 16.9 6 16.9C6.49706 16.9 6.9 16.4971 6.9 16H5.1ZM10.9 8C10.9 7.50294 10.4971 7.1 10 7.1C9.50294 7.1 9.1 7.50294 9.1 8H10.9ZM9.1 16C9.1 16.4971 9.50294 16.9 10 16.9C10.4971 16.9 10.9 16.4971 10.9 16H9.1ZM3.09202 18.782L3.50061 17.9801H3.50061L3.09202 18.782ZM2.21799 17.908L3.01989 17.4994L3.01989 17.4994L2.21799 17.908ZM13.782 17.908L12.9801 17.4994V17.4994L13.782 17.908ZM12.908 18.782L12.4994 17.9801L12.4994 17.9801L12.908 18.782ZM1 4.1C0.502944 4.1 0.1 4.50294 0.1 5C0.1 5.49706 0.502944 5.9 1 5.9V4.1ZM15 5.9C15.4971 5.9 15.9 5.49706 15.9 5C15.9 4.50294 15.4971 4.1 15 4.1V5.9ZM3.6 5C3.6 5.49706 4.00294 5.9 4.5 5.9C4.99706 5.9 5.4 5.49706 5.4 5H3.6ZM10.6 5C10.6 5.49706 11.0029 5.9 11.5 5.9C11.9971 5.9 12.4 5.49706 12.4 5H10.6ZM5.1 8V16H6.9V8H5.1ZM9.1 8V16H10.9V8H9.1ZM13.1 5V15.8H14.9V5H13.1ZM10.8 18.1H5.2V19.9H10.8V18.1ZM1.1 5V15.8H2.9V5H1.1ZM5.2 18.1C4.6251 18.1 4.24805 18.0993 3.95969 18.0757C3.68185 18.053 3.5665 18.0137 3.50061 17.9801L2.68343 19.5839C3.04536 19.7683 3.42395 19.838 3.81312 19.8698C4.19177 19.9007 4.6548 19.9 5.2 19.9V18.1ZM1.1 15.8C1.1 16.3452 1.0993 16.8082 1.13024 17.1869C1.16203 17.576 1.23167 17.9546 1.41608 18.3166L3.01989 17.4994C2.98632 17.4335 2.94696 17.3182 2.92426 17.0403C2.9007 16.752 2.9 16.3749 2.9 15.8H1.1ZM3.50061 17.9801C3.29363 17.8746 3.12535 17.7064 3.01989 17.4994L1.41608 18.3166C1.69411 18.8622 2.13776 19.3059 2.68343 19.5839L3.50061 17.9801ZM13.1 15.8C13.1 16.3749 13.0993 16.752 13.0757 17.0403C13.053 17.3182 13.0137 17.4335 12.9801 17.4994L14.5839 18.3166C14.7683 17.9546 14.838 17.576 14.8698 17.1869C14.9007 16.8082 14.9 16.3452 14.9 15.8H13.1ZM10.8 19.9C11.3452 19.9 11.8082 19.9007 12.1869 19.8698C12.576 19.838 12.9546 19.7683 13.3166 19.5839L12.4994 17.9801C12.4335 18.0137 12.3182 18.053 12.0403 18.0757C11.752 18.0993 11.3749 18.1 10.8 18.1V19.9ZM12.9801 17.4994C12.8746 17.7064 12.7064 17.8746 12.4994 17.9801L13.3166 19.5839C13.8622 19.3059 14.3059 18.8622 14.5839 18.3166L12.9801 17.4994ZM1 5.9H2V4.1H1V5.9ZM2 5.9H14V4.1H2V5.9ZM14 5.9H15V4.1H14V5.9ZM5.4 4.2C5.4 3.00396 6.48645 1.9 8 1.9V0.1C5.64756 0.1 3.6 1.86142 3.6 4.2H5.4ZM8 1.9C9.51355 1.9 10.6 3.00396 10.6 4.2H12.4C12.4 1.86142 10.3524 0.1 8 0.1V1.9ZM3.6 4.2V5H5.4V4.2H3.6ZM10.6 4.2V5H12.4V4.2H10.6Z"
                            fill="#FF9C9C"
                          />
                        </svg>
                      </button>
                    </li>
                    {items?.color && <li>Color :{items?.color}</li>}
                    <li>
                      {items?.size && <>Size:</>} {items?.size?.name}{" "}
                      <span>{currencyCode}{items.price?.toFixed(2)}</span>
                    </li>
                  </ul>
                </div>
              </div>
            ))}
          {Array.from({
            length: TryCartData?.products?.length
              ? 4 - TryCartData?.products?.length
              : 4,
          }).map((_, index) => (
            <div className="try-cart-flex add-new" key={index}>
              <Image
                quality={100}
                priority
                src="/images/common/fi_1829412.png"
                width={32}
                height={32}
                alt="product image"
              />
              <div className="try-cart-add">
                <h5>Product Name</h5>
                <button onClick={navigateToProducts}>Add New Product</button>
              </div>
            </div>
          ))}
        </Modal.Body>

        <Modal.Footer>
          <div className="try-cart-footer">
            <p>Product added to Try cart</p>
            <h6>Basket Subtotal: {currencyCode + " "} {totalPrice || 0}</h6>
          </div>
          <Button onClick={checkout}>Order Trial</Button>
        </Modal.Footer>
      </Modal>
    </>
  );
}

export default TryCartPopup;
