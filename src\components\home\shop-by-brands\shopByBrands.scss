.gallery {
  &.homeGallery {
    background: #222325;
    overflow: hidden;
    margin-top: 5rem;
    padding: 2.1rem 0;

    @media (max-width: 767.98px) {
      padding: 1.3rem 0;
      background: #000;
      margin-top: 3rem;
    }
  }

  &_marquee {
    .rfm-child {
      opacity: 0.5;

      &:nth-child(even) {
        opacity: 1;
      }

      h5 {
        color: #fff;
        font-size: 1rem;
        font-weight: 300;
        letter-spacing: 0.1rem;
        white-space: nowrap;
      }
    }
  }

  &_title {
    h2 {
      color: #fff;
      text-align: center;
      margin-top: 4rem;

      @media (max-width: 767.98px) {
        margin-top: 2.3rem;
      }
    }
  }

  &_images {
    column-count: 3;
    gap: 3rem;
    margin-top: 3rem;

    @media (max-width: 767.98px) {
      margin-top: 2.4rem;
      column-count: unset;
    }
  }

  &_item {
    position: relative;
    height: 100%;
    display: block;
    border-radius: 3rem;
    margin-bottom: 3rem;

    &::after {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(180deg, rgba(0, 0, 0, 0) 56.97%, rgba(0, 0, 0, 0.455797) 75.67%, rgba(0, 0, 0, 0.6) 100%);
      pointer-events: none;
      border-radius: 3rem;
    }

    img {
      height: 100%;
    }

    .img-1 {
      height: 446px;
      // height: auto;
      border-radius: 3rem;
    }

    &:nth-child(2),
    &:nth-child(3),
    &:nth-child(6) {
      .img-1 {
        height: 320px;
      }
    }

    .brand-name {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      bottom: 80px;
      z-index: 9;

      h4 {
        font-size: 32px;
        font-weight: 500;
        line-height: 40.32px;
        letter-spacing: 0.05em;
        text-align: center;
        color: #ffffff;
        text-transform: uppercase;
      }
    }
  }

  &_slider {
    .swiper-slide{
      margin: auto 0;
    }
    .swiper-slide img {
      height: auto;
      border-radius: 3rem;
    }

    .swiper-pagination {
      position: unset;
      margin-bottom: 2.7rem;

      .swiper-pagination-bullet {
        background-color: rgba(255, 255, 255, 0.6);
        width: 0.8rem;
        height: 0.8rem;

        &.swiper-pagination-bullet-active {
          width: 1rem;
          height: 1rem;
          background-color: #fff;
        }
      }
    }
  }

  &_view-all {
    text-align: center;

    a {
      color: #000000;
      padding: 1.5rem 3.5rem;
      border-radius: 5rem;
      font-size: 1.5rem;
      font-weight: 500;
      line-height: 1.8rem;
      margin-top: 1.3rem;
      margin-bottom: 3.3rem;
      background-color: #ffffff;
      display: inline-block;
      transition: all 0.3s ease;

      &:hover {
        color: #ffffff;
        background-color: #000000;
        transition: all 0.3s ease;
      }
    }
  }
}

// .scrolling-text {
//   white-space: nowrap;
//   overflow: hidden;
//   animation: scroll 20s linear infinite;
// }

// @keyframes scroll {
//   0% {
//     transform: translateX(100%);
//   }
//   100% {
//     transform: translateX(-100%);
//   }
// }