.insurance {
  min-height: 37.6rem;
  position: relative;
  display: flex;
  align-items: center;

  @media (max-width: 575.98px) {
    min-height: 57rem;
    align-items: flex-end;
    padding-bottom: 2.7rem;
  }

  &::after {
    content: "";
    background-image: url(../../../../public/images/home/<USER>
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;

    @media (max-width: 575.98px) {
      background-image: url(../../../../public/images/home/<USER>
    }
  }

  &_bg {
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;
    display: flex;

    img {
      height: auto;
    }
  }

  &_content {
    position: relative;
    z-index: 1;
    padding: 2.6rem 0;

    @media (max-width: 575.98px) {
      padding: 2.6rem 0;
      padding-bottom: 0;
    }

    h2 {
      color: #ffffff;
      text-align: left;
      max-width: 39rem;
      width: 100%;
      line-height: 4rem;

      @media (max-width: 575.98px) {
        max-width: 32.3rem;
        font-size: 2.2rem;
        line-height: 2.77rem;
      }
    }

    p {
      margin-top: 1rem;
      max-width: 44rem;
      width: 100%;
      color: #ffffff;

      @media (max-width: 575.98px) {
        max-width: 32.3rem;
        margin-top: 1.6rem;
      }
    }


  }
}

.app.rtl {
  .insurance_content h2 {
    text-align: right;
  }
  .insurance::after{
    transform: rotateY(180deg);
  }
}