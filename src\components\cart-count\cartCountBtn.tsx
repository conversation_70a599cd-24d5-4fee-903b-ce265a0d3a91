"use client";

import React, { useContext, useState } from "react";
import "../count/count.scss";
import { addToCart, removeFromCart, updateCart } from "@/lib/methods/cart";
import { useQueryClient } from "@tanstack/react-query";
import { sendGTMEvent } from "@next/third-parties/google";
import { createHmac } from "crypto";
import { AuthContext } from "@/contexts/AuthProvider";
import { useLocaleContext } from "@/contexts/LocaleProvider";

export default function CartCountBtn({
  initial,
  product,
  lensId,
}: {
  product: any;
  initial: number;
  lensId?: string | undefined;
}) {
  // const { count, increment, decrement } = useCounter(initial);

  const [counter, setCount] = useState(initial);
  const [loading, setLoading] = useState(false);
  const { userProfile } = useContext(AuthContext);
  const { currencyCode } = useLocaleContext()
  const queryClient = useQueryClient();
  const handleUpdate = (count: number) => {
    setLoading(true)
    if (counter === 1 && count === -1)
      removeFromCart(product?.productid, product?.size?._id, lensId).then(
        (res) => {
          setCount(0);
          queryClient.invalidateQueries({ queryKey: ["cart"] });
          queryClient.invalidateQueries({ queryKey: ["cart", "counts"] });
          setLoading(false)
        }
      ).finally(()=>setLoading(false))
    else
      updateCart(product?.productid, count, product?.size?._id, lensId, false).then(
        (res) => {
          setCount(counter + count);
          queryClient.invalidateQueries({ queryKey: ["cart"] });
          queryClient.invalidateQueries({ queryKey: ["cart", "counts"] });
          setLoading(false)
        }
      ).finally(()=>setLoading(false))

    if (count === -1) {
      sendGTMEvent({ ecommerce: null })
      let eventData: any = {
        event: "remove_from_cart",
        ecommerce: {
          currency: currencyCode,
          value: product?.price,
          items: [{
            item_id: product?.sku,
            item_name: product?.name,
            index: 0,
            item_brand: product?.brand,
            item_category: product?.category?.[0]?.name,
            item_category2: product?.category?.[1]?.name,
            item_variant: product?.color,
            price: product?.price,
            quantity: 1,
          }]
        }
      }
      if (userProfile) {
        const mobile = createHmac('sha256', '123').update(userProfile?.mobile).digest('hex')
        let email = null;
        if (userProfile?.email) email = createHmac('sha256', '123').update(userProfile?.email).digest('hex')
        eventData.user = {
          mobile,
          email,
          user_id: userProfile?._id
        }
      }
      sendGTMEvent(eventData)
    } else {
      sendGTMEvent({ ecommerce: null })
      let eventData: any = {
        event: "add_to_cart",
        ecommerce: {
          currency: currencyCode,
          value: product?.price,
          items: [
            {
              item_id: product?.sku,
              item_name: product?.name,
              index: 0,
              item_brand: product?.brand,
              item_category: product?.category?.[0]?.name,
              item_category2: product?.category?.[1]?.name,
              item_variant: product?.color,
              price: product?.price,
              quantity: (product?.contactLensDetails?.multiple ? 2 : 1),
              // quantity: 1,
            }
          ]
        }
      }
      if (userProfile) {
        const mobile = createHmac('sha256', '123').update(userProfile?.mobile).digest('hex')
        let email = null;
        if (userProfile?.email) email = createHmac('sha256', '123').update(userProfile?.email).digest('hex')
        eventData.user = {
          mobile,
          email,
          user_id: userProfile?._id
        }
      }
      sendGTMEvent(eventData)
    }
  };

  return (
    <>
      <div className="count">
        <button disabled={loading || counter == 1} onClick={() => handleUpdate(-1)}>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="11"
            height="2"
            viewBox="0 0 11 2"
            fill="none"
          >
            <path
              d="M10.2412 1L1 1"
              stroke="#3C4242"
              strokeWidth="1.03964"
              strokeLinecap="round"
            />
          </svg>
        </button>
        <span>{counter * (product?.contactLensDetails?.multiple ? 2 : 1)}</span>
        <button disabled={loading} onClick={() => handleUpdate(+1)}>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="11"
            height="12"
            viewBox="0 0 11 12"
            fill="none"
          >
            <path
              d="M5.86279 1.37891V10.6201M10.4834 5.99951L1.24219 5.99951"
              stroke="#3C4242"
              strokeWidth="1.03964"
              strokeLinecap="round"
            />
          </svg>
        </button>
      </div>
    </>
  );
}
