"use client";

import BreadCrumbs from "@/components/breadcrumbs/BreadCrumbs";
import { HistoryContext } from "@/contexts/HistoryProvider";
import { getBanner } from "@/lib/methods/products";
import { useQuery } from "@tanstack/react-query";
import { useContext, useEffect } from "react";

export default function BreadCrumpExtended({ product }: any) {
  const { prevPage } = useContext(HistoryContext);

  // const { data, isLoading } = useQuery({
  //   queryKey: ['product', prevPage.url],
  //   queryFn: () => getBanner(prevPage),
  // })

  return (
    <BreadCrumbs
      backHome="Home"
      currentPage={[
        {
          title: `${
            prevPage?.url.includes("products")
              ? prevPage?.title || "Products"
              : "Products"
          }`,
          link: prevPage?.url.includes("search")? "/products": (prevPage?.url || "/products"),
        },
        { title: product?.name, link: "" },
      ]}
      // image={prevPage.url?.length > 0? isLoading? "/images/common/blurred_banner.png": data?.banner: "/images/common/banner.png"}
      image={""}
      isLoading={false}
    />
  );
}
