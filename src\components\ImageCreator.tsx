export type Props = {
  text?: string;
  phone?: string;
  font?: string;
};
export const runtime = "edge";
export default function ImageCreator(props?: Props) {
  const { text, phone, font = "50px" } = props || { text: "Yateem" };
  const avatarColors = [
    "#FF6B6B", // Coral Red
    "#6B5B95", // Royal Purple
    "#70C1B3", // Turquoise Green
    "#FFA07A", // Light Salmon
    "#48C9B0", // Medium Turquoise
    "#F7DC6F", // Pastel Yellow
    "#A569BD", // Rich Purple
    "#5499C7", // Sky Blue
    "#E59866", // Darker Apricot
    "#82E0AA", // Light Sea Green
  ];
  const index = Number(phone?.slice(-1)) || Math.floor(Math.random() * avatarColors.length) || 0;
  const firstLetter = text?.slice(0, 1).toUpperCase();

  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: avatarColors[index],
        height: "100%",
        width: "100%",
        userSelect: "none",
      }}>
      <h1
        style={{
          color: "white",
          fontSize: font || "50px",
          fontWeight: "bold",
        }}>
        {firstLetter}
      </h1>
    </div>
  );
}
