"use client";

import { endpoints } from "@/config/apiEndpoints";
import api from "@/config/axios.interceptor";
import { AuthContext } from "@/contexts/AuthProvider";
import React, { useContext } from "react";
import { SubmitHandler, useForm } from "react-hook-form";
import { toast } from "sonner";
import { countryCodeWithFlags } from "@/lib/countryCodeWithFlag";
import Select from "react-select";
import { TranslationContext } from "@/contexts/Translation";
import { useLocaleContext } from "@/contexts/LocaleProvider";

interface EnquiryFormProps {
  brand: string;
  name: string;
  mobile: string;
  countryCode: string;
  email: string;
}

export default function EnquiryForm({ brand }: { brand: string }) {
  const { userProfile } = useContext(AuthContext);
  const { currentLocale:locale } = useLocaleContext()
  const { translation: {formFields, other}} = useContext(TranslationContext)
  const {
    register,
    handleSubmit,
    reset,
    setValue,
    formState: { errors },
  } = useForm<EnquiryFormProps>({
    defaultValues: {
      brand: brand,
      countryCode: userProfile?.countryCode,
      name: userProfile?.name,
      mobile: userProfile?.mobile,
      email: userProfile?.email,
    },
  });

  const onSubmit: SubmitHandler<EnquiryFormProps> = (data: EnquiryFormProps) => {
    api.post(endpoints.productEnquiry, data).then((res) => {
      if (res.data.errorCode === 0) {
        reset();
        toast.success(locale.includes("en") ? "Enquiry sent successfully" : "تم إرسال الاستفسار بنجاح");
      }
    });
  };
  return (
    <form className="enquiry_right" onSubmit={handleSubmit(onSubmit)}>
      <h4>{other?.enquireWithUs ?? "Enquire with us"}</h4>

      <div className="enquiry_input">
        <label htmlFor="">{formFields?.fullName ?? "Full name"}</label>
        <input
          {...register("name", { required: (formFields?.fullNameRequiredError ?? "Name is Required") })}
          type="text"
          placeholder="Alex Smith"
          name="name"
        />
        <small className="form-error text-danger">{errors.name?.message}</small>
      </div>

      <div className="enquiry_input">
        <div>
          <div className="enquiry_select">
            <div className="select">
              <label htmlFor="">{formFields?.phoneNumber ?? "Enter your phone number"}</label>
              <Select
                className="select-container"
                onChange={(e: any) => setValue("countryCode", e.value)}
                styles={
                  {
                    // option:(state)=>{}
                  }
                }
                theme={(theme) => ({
                  ...theme,
                  borderRadius: 0,
                  colors: {
                    ...theme.colors,
                    primary25: "#ccc",
                    primary: "black",
                  },
                })}
                classNames={{
                  control: (state) => "react-select",

                  dropdownIndicator: () => "d-none",
                  option: (state) =>
                    state.isSelected ? "option selected" : "option",
                  menu: () => "menu",
                }}
                formatOptionLabel={(country) => (
                  <div className="drop-item">
                    <img src={country.image} alt="" />
                    {country.label}
                  </div>
                )}
                options={countryCodeWithFlags?.map((country) => ({
                  label: country.name,
                  value: country.dial_code,
                  image: country.image,
                }))}
              />

              <div className="countrycode">
                <div className="countrycode-icon">
                  <input
                    {...register("countryCode")}
                    id="countryCode"
                    name="countryCode"
                  />
                </div>

                <input
                  {...register("mobile",
                    {
                      required: (formFields?.phoneNumberRequiredError ?? "Phone Number is Required"),
                    }
                  )}
                  tabIndex={1}
                  type="tel"
                  id="mobile"
                  placeholder="************"
                />
              </div>
            </div>
          </div>
          <small className="form-error text-danger">{errors.mobile?.message}</small>
        </div>
      </div>

      <div className="enquiry_input">
        <label htmlFor="">{formFields?.emailAddress ?? "Enter your email"}</label>
        <input
          {...register("email", {
            pattern: {
              value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
              message: (formFields?.emailAddressInvalidError ?? "Invalid email address"),
            },
          })}
          type="email"
          placeholder="<EMAIL>"
        />
      </div>

      <div className="enquiry_btn">
        <button type="submit">{formFields?.submit ?? "Submit"}</button>
      </div>
    </form>
  );
}
