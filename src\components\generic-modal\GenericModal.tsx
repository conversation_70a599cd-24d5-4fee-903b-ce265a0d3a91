"use client";

import "./generic-modal.scss";
import Modal from "react-bootstrap/Modal";

interface ModalProps {
  show: boolean;
  callBack: () => void;
  closeFn: () => void;
  title: string;
  primary: string;
  secondary: string;
}

function GenericModal({ show, callBack, closeFn, title, primary, secondary }: ModalProps) {
  return (
    <Modal
      className="generic-popup"
      show={show}
      onHide={closeFn}
      backdrop="static"
      keyboard={false}
      centered>
      <Modal.Header closeButton></Modal.Header>
      <Modal.Body>
        <h2>{title}</h2>
        <div className="generic-popup-btn">
          <button className="generic-btn" onClick={callBack}>
            {primary}
          </button>
          <button className="cancel-btn" onClick={closeFn}>
            {secondary}
          </button>
        </div>
      </Modal.Body>
    </Modal>
  );
}

export default GenericModal;
