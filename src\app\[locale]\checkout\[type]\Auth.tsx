"use client";

import Link from "next/link";
import "./checkout.scss";
import { usePathname, useRouter } from "next/navigation";
import { CheckoutProvider } from "@/contexts/CheckoutContext";
import { useContext, useEffect, useState } from "react";
import { getUser } from "@/lib/methods/auth";
import { TranslationContext } from "@/contexts/Translation";
import { useQuery } from "@tanstack/react-query";
import { getCart } from "@/lib/methods/cart";
import { useLocaleContext } from "@/contexts/LocaleProvider";

export default function Auth({
  children,
  params,
  user,
}: {
  children: React.ReactNode;
  params: { id: string; type: string };
  user: any;
}) {


  const router = useRouter();
  const pathname = usePathname();

  const { translation: { cartPage } } = useContext(TranslationContext);
  const { changeLocale, currentLocale } = useLocaleContext()
  const { data: cart, isLoading } = useQuery({ queryKey: ["cart", currentLocale?.split("-")[0]], queryFn: getCart });

    useEffect(() => {
    if (!isLoading && params?.type == "cart") {
      if ((!cart || !cart.products) || cart.products.length === 0) {
        router.push("/cart");
      }
    }
  }, [isLoading]);

  if (!user || user.isGuest) {
    router.push("/login");
    return null;
  }
  const isActive = (path: string) => {
    const regex = /^\/([a-z]{2})-(en|ar)(\/.*)$/;
    const match = pathname.match(regex);
    const newPathname = match ? match[3] : pathname;
    return newPathname === path;
  };

  return (
    <>
      <div className="checkout">
        <div className="container">
          <div className="checkout_wrapper">
            <div className="checkout_left">
              <div className="status">
                <ul className="status_bar">
                  <li className={isActive(`/checkout/${params.type}`) ? "isActive" : ""}>
                    <Link href={`/checkout/${params.type}`}>
                      <span className="status_icon">01</span>
                      <span className="status_text">{cartPage?.shippingDetails ?? "Shipping Details"}</span>
                    </Link>
                  </li>
                  <li
                    className={
                      isActive(`/checkout/${params.type}/shipping-method`) ? "isActive" : ""
                    }>
                    <Link href={`/checkout/${params.type}/shipping-method`}>
                      <span className="status_icon">02</span>
                      <span className="status_text">{cartPage?.shippingMethods ?? "Shipping Methods"}</span>
                    </Link>
                  </li>
                  <li
                    className={
                      isActive(`/checkout/${params.type}/payment-method`) ? "isActive" : ""
                    }>
                    <Link href={`/checkout/${params.type}/payment-method`}>
                      <span className="status_icon">03</span>
                      <span className="status_text">{cartPage?.paymentMethods ?? "Payment Methods"}</span>
                    </Link>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
      <CheckoutProvider> {children} </CheckoutProvider>
    </>
  );
}
