import Link from "next/link";
import BreadCrumbs from "@/components/breadcrumbs/BreadCrumbs";
import { Navbar } from "react-bootstrap";
import TabNavbar from "@/components/tab-navbar/TabNavbar";
import { getUser } from "@/lib/methods/auth";
import ErrorPage from "@/components/404/ErrorPage";
import { getTranslation } from "@/lib/methods/translation";

export default async function layout({ children, params }: { children: React.ReactNode, params: { locale: string } }) {
  try {
    const [res, { result: { myAccount } }] = await Promise.all([getUser(), getTranslation(params.locale)])

    if (res.isGuest) throw new Error("401");

    const navlinks = [
      { name: myAccount?.myProfile ?? "My Profile", href: "/my-accounts", path: "" },
      { name: myAccount?.myOrders ?? "My Orders", href: "/my-accounts/my-orders", path: "my-orders" },
      // { name: "Try Cart Order", href: "/my-accounts/try-cart-order", path: "try-cart-order" },
      { name: myAccount?.myWishlist ?? "My Wishlist", href: "/my-accounts/my-wishlist", path: "my-wishlist" },
      { name: myAccount?.myAddressBook ?? "My Address Book", href: "/my-accounts/my-address-book", path: "my-address-book" },
      { name: myAccount?.mySubscription ?? "My Subscription", href: "/my-accounts/my-subscription", path: "my-subscription" },
      { name: myAccount?.myPrescription ?? "My Prescription", href: "/my-accounts/my-prescription", path: "my-prescription" },
      { name: myAccount?.myCashbacks ?? "My Cashbacks", href: "/my-accounts/my-cashbacks", path: "my-cashbacks" },
      // { name: "Logouts", href: "/my-accounts/logtout" },
    ];
    return (
      <>
        <BreadCrumbs
          backHome="Home"
          currentPage="My Accounts"
          image="/images/common/banner.png"
        />
        <TabNavbar translation={myAccount} navlinks={navlinks} />
        <main>
          <div className="container">{children}</div>
        </main>
      </>
    );
  } catch (err: any) {
    return <ErrorPage errorcode="401" />;
  }
}
