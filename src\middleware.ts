// import { NextResponse } from 'next/server'
// import type { NextRequest } from 'next/server'


// export function middleware(request: NextRequest) {
//     // Example function to validate auth
//     const token = request.cookies.get('access_token')?.value
//     if (token) {
//         return NextResponse.next()
//     }


//     const loginUrl = new URL('/login', request.url)
//     loginUrl.searchParams.set('from', request.nextUrl.pathname)

//     return NextResponse.redirect(loginUrl)
// }

// export const config = {
//     matcher: '/my-accounts/:path*',
// }

// middleware.ts
import { createI18nMiddleware } from 'next-international/middleware'
import { NextRequest, NextResponse } from 'next/server'
import { getMultiStores } from './lib/methods/multistore';

// const I18nMiddleware = createI18nMiddleware({
// locales: ['sa-en', 'sa-ar', "ae-en", "ae-ar", "qa-en", "qa-ar", "om-en", "om-ar", "bh-en", "bh-ar"],
// defaultLocale: 'ae-en',
// resolveLocaleFromRequest: (request: NextRequest) => {
//     const ar = request.headers.get('referer')?.split("/").includes("ar")
//     if (ar) return 'ar';
//     return 'en';
// }
// })

function getI18Middleware(locales: string[]) {
    const I18nMiddleware = createI18nMiddleware({
        locales: locales,
        defaultLocale: 'ae-en',
    })

    return I18nMiddleware
}

export async function middleware(request: NextRequest) {
    let userDetails;
    const stores = await getMultiStores()

    const locales: string[] = [];
    stores?.result?.forEach((item: any) => {
        locales.push(`${item?.storeId}-en`)
        locales.push(`${item?.storeId}-ar`)
    })
    const I18nMiddleware = getI18Middleware(locales)
    if (request.nextUrl.pathname.includes('/my-accounts')) {

        const token = request.cookies.get('access_token')?.value
        if (token) {
            return I18nMiddleware(request)
        } else {
            const loginUrl = new URL('/', request.url)
            loginUrl.searchParams.set('from', request.nextUrl.pathname)
            return NextResponse.redirect(loginUrl)
        }
    }

    return I18nMiddleware(request)
}

export const config = {
    matcher: ['/((?!api|static|login|.*\\..*|_next|favicon.ico|robots.txt).*)']
}