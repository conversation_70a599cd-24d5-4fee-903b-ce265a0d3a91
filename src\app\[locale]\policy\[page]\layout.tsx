"use client";
import "./policyStyle.scss";
import BreadCrumbs from "@/components/breadcrumbs/BreadCrumbs";
import TabNavbar from "@/components/tab-navbar/TabNavbar";
import { TranslationContext } from "@/contexts/Translation";
import { useParams, usePathname } from "next/navigation";
import { useContext } from "react";

export default function Layout({ children }: { children: React.ReactNode }) {
  const params = useParams();
  const policy = typeof params?.page === "string" ? params.page.split("-").join(" ") : "Policy";
  const { translation: { policy: translation } }: any = useContext(TranslationContext);

  const navlinks = [
    { name: (translation?.termsAndCondition ?? "Terms and Conditions"), href: "/policy/terms-and-conditions" },
    { name: (translation?.privacyPolicy ?? "Privacy Policy"), href: "/policy/privacy-policy" },
    { name: (translation?.cookiePolicy ?? "Cookie Policy"), href: "/policy/cookie-policy" },
    { name: (translation?.shippingPolicy ?? "Shipping Policy"), href: "/policy/shipping-policy" },
    { name: (translation?.returnPolicy ?? "Return Policy"), href: "/policy/return-policy" },
    // { name: (translation?.refundPolicy ?? "Refund Policy"), href: "/policy/refund-policy" },
  ];

  return (
    <>
      <BreadCrumbs backHome="Home" currentPage={policy} image="/images/common/banner.png" />
      <TabNavbar navlinks={navlinks} hideLogout />
      <main>
        <div className="container" style={{minHeight: "20vh"}}>{children}</div>
      </main>
    </>
  );
}
