.brandcoll {
  margin-top: 5rem;

  &_wrapper {
    .swiper {
      margin-top: 3rem;
    }

    .swiper-slide {
      position: relative;
      &::after {
        content: "";
        background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.5) 100%);
        position: absolute;
        height: 100%;
        width: 100%;
        left: 0;
        border-radius: 2rem;
      }

      img {
        border-radius: 2rem;
        height: 51rem;

        @media (max-width: 575.98px) {
          height: 40.4706rem;
        }
      }
    }
  }

  &_card-content {
    position: absolute;
    bottom: 35px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    z-index: 2;

    h5 {
      color: #fff;
      font-size: 2.4rem;
      font-weight: 500;

      @media (max-width: 575.98px) {
        font-size: 2.2rem;
      }
    }

    a {
      margin-top: 2rem;
      display: inline-block;
      font-size: 1.5rem;
      font-style: normal;
      font-weight: 500;
      letter-spacing: 0.075rem;
      text-decoration-line: underline;
      color: #fff;
      &:hover {
        text-decoration: none;
      }

      @media (max-width: 575.98px) {
        margin-top: 1.4rem;
        font-size: 1.4rem;
      }
    }
  }
}
