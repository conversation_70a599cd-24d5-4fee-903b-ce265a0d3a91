import Button from "react-bootstrap/Button";
import Modal from "react-bootstrap/Modal";
import "./verify.scss";
import React, { useEffect, useState } from "react";
import OtpInput from "react-otp-input";
import { useCountdown } from "usehooks-ts";
import api from "@/config/axios.interceptor";
import { toast } from "sonner";
import { endpoints } from "@/config/apiEndpoints";
import { useQueryClient } from "@tanstack/react-query";
import { RecaptchaVerifier, signInWithPhoneNumber } from "firebase/auth";
import { useRouter, useSearchParams } from "next/navigation";

function Verify({
  show,
  phone,
  stepHandler,
  handleClose,
  setOtp,
  otp,
  setUser,
  translation,
}: any) {
  const queryClient = useQueryClient();

  const [count, { startCountdown, resetCountdown }] = useCountdown({
    countStart: 59,
    intervalMs: 1000,
  });
  const [recaptchaCount, setRecaptchaCount] = useState(1);
  const [loading, setLoading] = useState(false)
  const [resendLoading, setResendLoading] = useState(false)
  const [showSingUp, setShowSingUp] = useState(false);
  const [uid, setUid] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const searchParams = useSearchParams()
  const router = useRouter();

  const handleShowSingUp = () => {
    setShowSingUp(true);
    handleClose();
  };

  const resendOtp = async () => {
    if (phone?.mobile == null) {
      toast.error("something wrong try to again send otp");
      return;
    }

    try {
      setResendLoading(true)
      
      // const formatMobile = phone?.countryCode + phone?.mobile;
      const payload = {
        countryCode: phone?.countryCode,
        mobile: phone?.mobile,
      }
      const res = await api.post(endpoints.getOtp, payload);
      toast.success(translation?.popup?.otpSentSuccess ?? "otp sended successfully");
      resetCountdown();
      startCountdown();
    } catch (error) {
      toast.error(translation?.popup?.somethingWentWron ?? "Something went wrong. Please try again later.");
    }finally{
      setResendLoading(false)
    }
  };

  // const checkOtp = async () => {
  //   setLoading(true)
  //   if (typeof window !== "undefined") {
  //     if (window.confirmationResult) {
  //       window.confirmationResult
  //         .confirm(otp)
  //         .then(async (res: any) => {
  //           setUser(res?.user);
  //           setUid(res?.user?.uid);
  //           await verifyOtp(res?.user?.uid);
  //         })
  //         .catch((err: any) => {
  //           setErrorMessage("Invalid OTP. Please try again.");
  //           setLoading(false)
  //         });
  //     } else {
  //       console.error("Confirmation result is not available.");
  //       setErrorMessage("Confirmation result is not available.");
  //       setLoading(false)
  //     }
  //   }
  // };

  const verifyOtp = async () => {
    setLoading(true)
    let data = {
      ...phone,
      // mobile:phone?.mobile?.startsWith("0") ? phone?.mobile?.slice(1) : phone?.mobile,
      mobile: phone?.mobile?.replaceAll(" ", ""),
      otp: Number(otp),
      // uid: uid,
    };
    try {
      const res = await api.post(endpoints.verifyOtp, data);
      const currentDate = new Date();
      const nextWeek = new Date(
        currentDate.getTime() + 7 * 24 * 60 * 60 * 1000
      );
      const expires = nextWeek.toUTCString();
      if (res.data.errorCode === 0) {
        document.cookie = `access_token=${res.data.result.token}; expires=${expires}; path=/`;
        queryClient.invalidateQueries({ queryKey: ["user"] });
        queryClient.invalidateQueries({ queryKey: ["cart"] });
        queryClient.invalidateQueries({ queryKey: ["address"] });
        if (!res.data.result.isExisting) {
          // toast.success(translation?.popup?.selectAccountType ?? "Please Select how you want to continue", {});
          stepHandler("signup");
        } else {
          toast.success(
            `${translation?.popup?.welcomeBack ?? "Welcome Back"} ${res.data.result?.customer?.name
              ? res.data.result?.customer?.name
              : ""
            }`,
            {}
          );

          switch (searchParams.get("type")) {
            case 'checkout':
              router.back();
              setTimeout(() => {
                router.push("/checkout/cart");
              }, 10)
              break;
            case 'wishlist':
              try {
                const res = await api.post(endpoints.manageWishlist, { product: searchParams.get('id') })
                if (res.data.errorCode === 0) {
                  queryClient.invalidateQueries({ queryKey: ["wishList"] });
                  queryClient.invalidateQueries({ queryKey: ["cart", "counts"] });
                  toast.success(res.data.message);
                }
                setTimeout(() => {
                  window && window.location.reload()
                }, 10)
                router.back();
              } catch (error: any) {
                toast.error(error.response.data.message);
                router.back();
              }
              break;
            default:
              setTimeout(() => {
                window && window.location.reload()
              }, 10)
              router.back()
              break;
          }
        }
      } else {
        setLoading(false)
        toast.error(translation?.popup?.invalidOTP ?? "Invalid OTP", {});
      }
    } catch (err: any) {
      setLoading(false)
      toast.error(err.response?.data?.message || "OTP verification failed", {});
    } finally{
      setLoading(false)
    }
  };

  useEffect(() => {
    startCountdown();
  }, []);

  useEffect(() => {
    if (count === 1) {
      setRecaptchaCount(recaptchaCount + 1);
    }
  }, [count]);

  return (
    <>
      <Modal
        key={recaptchaCount}
        className="verify-popup"
        show={show}
        onHide={handleClose}
        backdrop="static"
        keyboard={false}
      >
        <Modal.Header closeButton></Modal.Header>
        <Modal.Body>
          <h2 dangerouslySetInnerHTML={{ __html: translation?.verifyTitle }}>
            {/* Verify <br /> Your Phone Number */}
          </h2>

          <div className="verify-popup-input">
            <h6>{translation?.login?.verifyTxt ?? "Please enter the 6 digit code we sent to"}</h6>
            <span>{phone?.countryCode + "-" + phone?.mobile}</span>

            <label htmlFor="">{translation?.login?.confirmationCode ?? "Confirmation code"}</label>
            <div className="verify-popup-otp">
              <OtpInput
                value={otp}
                onChange={setOtp}
                numInputs={6}
                inputType="number"
                renderInput={(props) => (
                  <input
                    {...props}
                    onKeyUp={(e) => {
                      if (e.key === "Enter") {
                        verifyOtp();
                      }
                      
                    }}
                    placeholder="----"
                  />
                )}
              />
              <div className="count-down">
                00:{count.toString().padStart(2, "0")}
              </div>
            </div>
            {errorMessage && <div style={{ color: "red" }}>{errorMessage}</div>}
            <div
              className="d-flex align-items-center"
              style={{ marginTop: "1rem" }}
            >
              <p>{translation?.login?.didntReceiveCode ?? "Didn’t you receive any code?"}</p>
              <button
                disabled={count > 0 || resendLoading}
                onClick={resendOtp}
                className="resend-otp"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 20 20"
                  fill="none"
                >
                  <path
                    d="M14.7085 5.29134C13.5001 4.08301 11.8418 3.33301 10.0001 3.33301C6.3168 3.33301 3.3418 6.31634 3.3418 9.99967C3.3418 13.683 6.3168 16.6663 10.0001 16.6663C13.1085 16.6663 15.7001 14.5413 16.4418 11.6663H14.7085C14.0251 13.608 12.1751 14.9997 10.0001 14.9997C7.2418 14.9997 5.00013 12.758 5.00013 9.99967C5.00013 7.24134 7.2418 4.99967 10.0001 4.99967C11.3835 4.99967 12.6168 5.57467 13.5168 6.48301L10.8335 9.16634H16.6668V3.33301L14.7085 5.29134Z"
                    fill="black"
                  />
                </svg>
                {translation?.login?.sendAgain ?? "Send again"}
              </button>
            </div>
          </div>

          <Button disabled={loading} onClick={() => verifyOtp()}>{loading ? ((translation?.login?.verifying ?? "Verifying") + "...") : (translation?.login?.verify ?? "Verify")}</Button>
          <div id="recaptcha"></div>
        </Modal.Body>
      </Modal>
    </>
  );
}

export default Verify;
