import InsuranceForm from "@/components/insurance-form/InsuranceForm";
import BreadCrumbs from "@/components/breadcrumbs/BreadCrumbs";
import Image from "next/image";
import "./insurance.scss";
import Faq from "@/components/faq/Faq";
import { getProviders, getFaqs, getContent } from "@/lib/methods/insurance";
import { getUser } from "@/lib/methods/auth";
import GenericBackButton from "@/components/GenericBackButton/GenericBackButton";
import { Metadata } from "next";

export async function generateMetadata({ params }: { params: any }): Promise<Metadata> {
  const content = await getContent();
  const locale = params.locale;

  return {
    title: locale === "ar" ? content?.seoDetails?.title?.ar : content?.seoDetails?.title?.en || "Yateem Optician",
    description: locale === "ar" ? content?.seoDetails?.description?.ar : content?.seoDetails?.description?.en || "Yateem Optician description",
    keywords: locale === "ar" ? content?.seoDetails?.keywords?.ar : content?.seoDetails?.keywords?.en || "Yateem Optician keywords",
    openGraph: {
      title: locale === "ar" ? content?.seoDetails?.title?.ar : content?.seoDetails?.title?.en || "Yateem Optician",
      description: locale === "ar" ? content?.seoDetails?.description?.ar : content?.seoDetails?.description?.en || "Yateem Optician description",
      type: "website",
      images: [
        {
          url: content?.seoDetails?.ogImage,
          width: 742,
          height: 396,
          alt: locale === "ar" ? content?.seoDetails?.title?.ar : content?.seoDetails?.title?.en || "Yateem Optician",
          type: "image/jpeg",
        },
      ],
    },
    alternates: {
      canonical: locale === "ar" ? content?.seoDetails?.canonical?.ar : content?.seoDetails?.canonical?.en,
    },
  };
}

async function Insurance({params}:any) {
  const insuranceProviders = await getProviders();
  const faqs = await getFaqs();
  const content = await getContent();
  const user = await getUser();
  

  return (
    <main className="wrap">
      <BreadCrumbs backHome={"Home"} currentPage={`/ Insurance`} image="/images/common/banner2.png" />
      <InsuranceForm content={content} insuranceProviders={insuranceProviders?.result} user={user} />

      <section className="insurance-info  position-relative">
        <div className="container">
          <div className="insurance-info_wrapper">
            {/* <span className="d-sm-none d-block">
              <GenericBackButton style={{ top: "5px" }} />
            </span>
            <h2 className="d-sm-none d-block">{content?.pageTitle}</h2> */}
            <div
              className="pt-3"
              dangerouslySetInnerHTML={{ __html: content?.descriptionOne }}></div>
            <ul className="insurance-info_logos">
              {insuranceProviders?.result?.map((item: any) => (
                <li key={item?._id}>
                  <Image quality={100} priority src={item?.logo ?? ""} width={209} height={80} alt={item?.name} />
                </li>
              ))}
            </ul>
            <div
              className="default_style_ul"
              dangerouslySetInnerHTML={{ __html: content?.descriptionTwo }}></div>
          </div>
        </div>
      </section>

      <Faq
        data={faqs?.result?.[0]?.faq}
        title={faqs?.result?.[0]?.title}
        description={faqs?.result?.[0]?.description}
      />
    </main>
  );
}

export default Insurance;
