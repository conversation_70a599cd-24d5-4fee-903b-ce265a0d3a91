.virtual-slider-modal {
  .swiper-slide {
    background-color: #fff;
    &:has(img.opacity-0) {
      background-color: #f1f1f1;
      background-image: url(/images/common/logo.png);
      animation: skeleton 1s linear infinite alternate;
      background-repeat: no-repeat;
      background-position: center;
      background-size: 40%;
    }
  }
  .modal-header {
    position: absolute;
    top: 0;
    z-index: 2;
    right: 0;
    border: none;

    .btn-close {
      padding: 0;
      margin-right: 4.5rem;
      margin-top: 4rem;
      box-shadow: none;
      opacity: 1;
      filter: brightness(0) saturate(100%) invert(99%) sepia(1%) saturate(0%)
        hue-rotate(69deg) brightness(104%) contrast(101%)
        drop-shadow(0 0 0.5rem #000000);

      @media (max-width: 575.98px) {
        margin-right: 0;
        margin-top: 1rem;
      }
    }
  }

  .virtual-slider {
    position: relative;

      height: 100svh;
      display: flex;
      flex-direction: column;
    .swiper-button-prev,
    .swiper-button-next {
      display: none;
    }
    img {
      object-fit: contain;
    }

    .swiper-thumb {
      height: 100%;
      width: 100%;
    }
    .swiper-thumbs {
      img {
        object-fit: contain;
      }
    }

    .swiper-gallery {
      position: absolute;
      bottom: 35px;
      z-index: 11;
      left: 0;
      width: 100%;
      // overflow-x: auto;

      @media (max-width: 575.98px) {
        // position: unset;
        margin-top: 3rem;
        padding: 0 7.5px;
      }

      .swiper-wrapper {
        @media (min-width: 576px) {
          justify-content: center;
        }
      }

      .swiper-slide {
        border-radius: 2rem;
        background-color: transparent;
        @media (min-width: 576px) {
          width: 10.8rem !important;
          height: 10.8rem;
        }

        @media (max-width: 575.98px) {
          height: 8.2rem;
        }

        img {
          border-radius: 2rem;
          cursor: pointer;
          height: 100%;
        }
      }
    }
  }
}
