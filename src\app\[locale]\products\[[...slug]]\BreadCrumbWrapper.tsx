// "use client"

// import { endpoints } from "@/config/apiEndpoints";
// import { useInfiniteQuery } from "@tanstack/react-query";
// import api from "@/config/axios.interceptor";
// import BreadCrumbs from "@/components/breadcrumbs/BreadCrumbs";

// export function BreadCrumbWrapper({ slug, pageLimit, brandPage, query}: any){

//       const getProducts = async ({ pageParam = 1 }) => {
//         const res = await api.post(endpoints.products, {
//           page: pageParam,
//           limit: pageLimit,
//           keyword: slug,
//           brandPage,
//           ...query,
//         });
//         return res.data.result;
//       };
//       const {
//         data: pageData,
//         isLoading,
//       } = useInfiniteQuery({
//         queryKey: ["products", slug, query],
//         queryFn: getProducts,
//         getNextPageParam: (lastPage: any, allPages) => {
//           const nextPage = lastPage.nextPage;
//           return nextPage;
//         },
//         initialPageParam: 1, // Add this line with an appropriate initial value
//       });
 
//       return (

//       )
// }