.review-popup {
  &.modal {
    .modal-dialog-centered {
      @media (max-width: 575.98px) {
        align-items: flex-end;
      }
    }
    .modal-header {
      padding: 0;
      border: none;

      .btn-close {
        background-image: url(../../../public/images/common/close.png);
        width: 4.8rem;
        height: 4.8rem;
        background-position: center;
        background-repeat: no-repeat;
        background-size: contain;
        opacity: 1;
        padding: 0;

        @media (max-width: 575.98px) {
          width: 2.8rem;
          height: 2.8rem;
        }

        &:focus {
          box-shadow: none;
          outline: none;
          border: none;
        }
      }
    }

    .modal-content {
      border: none;
      border-radius: 3rem;
      padding: 1.8rem 2.1rem 4.2rem 2.1rem;

      @media (max-width: 575.98px) {
        padding: 2.1rem 1.7rem 4.2rem 1.7rem;
        border-radius: 2rem 2rem 0rem 0rem;
      }
    }

    .modal-body {
      padding: 0;
      display: flex;
      flex-direction: column;
      align-items: center;

      h2 {
        text-align: left;
        line-height: 4rem;
        width: 100%;

        @media (max-width: 575.98px) {
          line-height: 3rem;
          font-size: 2.2rem;
        }
      }

      .file-upload {
        margin-top: 1.1rem;
        border-radius: 1.2rem;
        background: #ededed;
        width: 100%;
        position: relative;
        cursor: pointer;

        label {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          color: #242426;
          font-size: 1.5rem;
          font-weight: 400;
          line-height: 2.8rem;
          display: flex;
          flex-direction: column;
          align-items: center;

          img {
            width: 2.4rem;
            height: 2.4rem;
            margin-bottom: 0.5rem;
          }
        }

        input {
          opacity: 0;
          height: 12.1rem;
          width: 100%;
          cursor: pointer;
        }
      }

      p {
        margin-top: 1rem;
        color: #52525b;
        font-size: 1.4rem;
        font-weight: 400;
        line-height: 3rem;
        width: 100%;
      }

      .button {
        margin-top: 2.5rem;
        color: #fff;
        text-align: center;
        font-size: 1.5rem;
        font-weight: 500;
        border: none;
        background-color: #000;
        height: 5.6rem;
        width: 100%;
        border-radius: 6rem;

        @media (max-width: 575.98px) {
          width: 100%;
          height: 4.5rem;
        }
      }

      .upload {
        &_inputs {
          width: 100%;
          label {
            display: block;
            color: #808080;
            font-size: 1.3rem;
            font-weight: 400;
            line-height: 2rem;
            padding-bottom: 0.8rem;
          }

          input {
            border: none;
            border-bottom: 1px solid #e2e4e5;
            color: #242426;
            font-size: 1.8rem;
            font-weight: 400;
            padding-bottom: 0.8rem;
            padding-left: 1.6rem;
            width: 100%;

            @media (max-width: 575.98px) {
              font-size: 1.6rem;
            }

            &::placeholder {
              color: #d4d4d4;
              font-size: 1.8rem;
              font-weight: 400;

              @media (max-width: 575.98px) {
                font-size: 1.6rem;
              }
            }

            &:focus-within {
              outline: none;
              border-bottom: 1px solid #e2e4e5;
            }
          }
        }
      }
    }
  }
}
