import { endpoints } from "@/config/apiEndpoints";
import { cookies } from "next/headers";

export const getProviders = async () => {
    const locale = cookies().get("Next-Locale")?.value || "sa-en";
    const [storeId, language] = locale.split("-");
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}${endpoints.insuranceProviders}`, {
        method: "GET",
        headers: {
            "Content-Type": "application/json",
            language: language || "en",
            storeid: storeId || "sa"
        },
        next: {
            tags: ["insurance-providers"]
        }
    });
    const data = await response.json();
    return data;
}

export const getFaqs = async () => {
    const locale = cookies().get("Next-Locale")?.value || "sa-en";
    const [storeId, language] = locale.split("-");
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}${endpoints.insuranceFaq}`, {
        method: "GET",
        headers: {
            "Content-Type": "application/json",
            language: language || "en",
            storeid: storeId || "sa"
        },
        next: {
            tags: ["insurance-faq"]
        }
    });
    const data = await response.json();
    return data;
}

export const getStores = async () => {
    const locale = cookies().get("Next-Locale")?.value || "sa-en";
    const [storeId, language] = locale.split("-");
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}${endpoints.stores}`, {
        method: "GET",
        headers: {
            "Content-Type": "application/json",
            language: language || "en",
            storeid: storeId || "sa"
        },
        next: {
            tags: ["stores"]
        }
    });
    const data = await response.json();
    return data;
}

export const getContent = async () => {
    const locale = cookies().get("Next-Locale")?.value || "sa-en";
    const [storeId, language] = locale.split("-");
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}${endpoints.insuranceContent}`, {
        method: "GET",
        headers: {
            "Content-Type": "application/json",
            language: language || "en",
            storeid: storeId || "sa"
        },
        next: {
            tags: ["insurance-content"]
        }
    });
    const data = await response.json();
    const content = data.result[0];
    return content;
}
