.ship-method {
  position: relative;

  .option:hover {
    background: black !important;

    div {
      filter: invert(1);
    }
  }

  .option.selected {
    background: black !important;
    color: white !important;

    div {
      filter: invert(0);
    }
  }

  &_wrapper {
    display: flex;
    column-gap: 3.4rem;

    @media (max-width: 991.98px) {
      flex-direction: column;
    }
  }

  &_left {
    width: 65%;

    @media (max-width: 991.98px) {
      width: 100%;
    }

    .ship-method-container {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 2rem;
      margin-top: 2rem;
      @media (max-width: 767.98px) {
        grid-template-columns: 1fr;
      }
    }

    h4 {
      margin-top: 6.4rem;
      color: #000;
      font-size: 2.4rem;
      font-weight: 500;

      @media (max-width: 575.98px) {
        margin-top: 3.3rem;
        font-size: 2rem;
      }
    }

    button {
      width: 26.6rem;
      height: 5.6rem;
      border-radius: 6rem;
      background: #000;
      border: 0;
      color: #fff;
      font-size: 1.5rem;
      font-weight: 500;
      margin-top: 6rem;

      @media (max-width: 575.98px) {
        width: 100%;
        margin-top: 1.8rem;
      }
    }
  }

  &_right {
    width: 35%;
    background: #f2f4f9;

    @media(min-width: 991.98px) {
      padding: 3.5rem 11.6rem 11rem 4.4rem;
      margin-top: -7rem;
      position: absolute;
      right: 0;
    }

    @media (max-width: 991.98px) {
      position: unset;
      width: 100%;
      margin-top: 10.5rem;
      padding: 4rem;
    }

    @media (max-width: 767.98px) {
      margin-top: 4.7rem;
    }

    @media (max-width: 575.98px) {
      padding: 2.5rem 2rem 5.6rem 2.2rem;
      margin-bottom: 1.9rem;
    }

    h5 {
      color: #000;
      font-size: 2.4rem;
      font-weight: 500;
      margin-bottom: 2rem;

      @media (max-width: 575.98px) {
        font-size: 2rem;
        line-height: 2.5rem;
        margin-bottom: 2.5rem;
      }
    }
  }

  &_border {
    border-radius: 2rem;
    border: 1px solid #ebebeb;
    background: #fdfdfe;
    padding: 3rem 2.4rem 2.5rem 2.3rem;

    @media (max-width: 575.98px) {
      padding: 2.5rem 2.1rem 2.9rem 2.1rem;
    }

    h5 {
      color: #242426;
      font-size: 1.6rem;
      font-weight: 600;
      line-height: 2.8rem;

      &.bold {
        @media (max-width: 575.98px) {
          font-size: 2rem;
        }
      }
    }

    p {
      color: #808080;
      font-size: 1.5rem;
      font-weight: 400;
      margin-top: 0.5rem;
    }

    h6 {
      color: #000;
      font-size: 1.4rem;
      font-weight: 600;

      span {
        font-size: 1.8rem;
      }
    }
  }

  &_flex {
    display: flex;
    justify-content: space-between;
    flex-direction: column;

    @media (max-width: 575.98px) {
      margin-top: 1.9rem;
      padding-top: 1.9rem;
    }
  }

  .css-1u9des2-indicatorSeparator {
    width: 0 !important;
  }

  select {
    width: 100%;
    border: none;
    border-bottom: 1px solid #e2e4e5;
    padding-left: 1.5rem;
    padding-bottom: 0.8rem;
    appearance: none;
    background-color: transparent;
    color: black !important;

    &::placeholder {
      color: #242426;
      font-size: 1.8rem;
      font-weight: 400;
    }

    &:focus-visible {
      border-bottom: 1px solid #e2e4e5;
      outline: none;
      box-shadow: none;
    }
  }

  .select {
    position: relative;

    .select-container {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      z-index: 100;

      .drop-item {
        z-index: 10;

        img {
          width: 30px;
          margin-right: 10px;
        }
      }
    }

    .css-lkh0o5-menu {
      margin-top: 29px !important;
    }

    // label {
    //   color: #242426;
    //   font-size: 1.4rem;
    //   font-weight: 400;
    //   line-height: 2rem;
    //   padding-bottom: 0.8rem;
    // }

    .countrycode {
      display: flex;
      column-gap: 1.6rem;
      align-items: baseline;

      .countrycode-icon {
        position: relative;
        max-width: 80px;

        &::after {
          content: "";
          background-image: url("../../../../../../public/images/common/Icon.png");
          width: 2.4rem;
          height: 2.4rem;
          position: absolute;
          right: -7px;
          z-index: 5;
        }
      }
    }

    .react-select {
      width: 100%;
      max-width: 80px;
      top: 36px;
      left: 0;
      z-index: 10;
      opacity: 0;
    }

    input[type="number"]::-webkit-inner-spin-button,
    input[type="number"]::-webkit-outer-spin-button {
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
    }

    input {
      width: 100%;
      color: #242426;
      font-size: 1.8rem;
      font-weight: 400;
      line-height: 2.8rem;
      border: none;
      border-bottom: 1px solid #e2e4e5;
      padding: 0 1.5rem;
      padding-bottom: 0.8rem;

      &:focus-within {
        outline: none;
        border-bottom: 1px solid #e2e4e5;
      }

      &::placeholder {
        color: #cacaca;
        font-size: 1.8rem;
        font-weight: 400;
      }
    }

  }

}

.app.rtl {
  .ship-method {
    &_right {
      @media(min-width: 991.98px) {
        padding: 3.5rem 4.4rem 11rem 11.6rem;
        right: unset;
        left: 0;
      }

      @media (max-width: 575.98px) {
        padding: 2.5rem 2.2rem 5.6rem 2rem;
        margin-bottom: 1.9rem;
      }
    }
  }
}

.paynow-btn {
  border-radius: 6rem;
  background: #000;
  padding: 1.7rem 0 1.7rem 0;
  width: 100%;
  color: #fff;
  font-size: 1.5rem;
  font-weight: 500;
  display: inline-block;
  text-align: center;
  margin-top: 7.3rem;
  text-decoration: none;
  transition: all 500ms cubic-bezier(0.19, 1, 0.22, 1);
  margin-left: 0;

  &:hover {
    background-color: transparent;
    color: #000;
    border: 1px solid;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  @media (max-width: 575.98px) {
    margin-top: 4.6rem;
  }
}