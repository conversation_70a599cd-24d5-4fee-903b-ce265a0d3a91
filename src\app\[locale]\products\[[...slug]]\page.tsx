import ListingPage from "@/components/listing-page/ListingPage";
import { endpoints } from "@/config/apiEndpoints";
import api from "@/config/axios.interceptor";
import { dehydrate, HydrationBoundary, QueryClient } from "@tanstack/react-query";
import { Metadata } from "next";
import { notFound } from "next/navigation";


export async function generateMetadata({ params }: { params: any }): Promise<Metadata> {
  try {
    const res = await api.post(endpoints.products, {
      keyword: params.slug?.[0],
      page: 1,
      limit: process.env.PAGESIZE || 15,
    });
    const data = await res.data.result;
    const locale = params.locale;

    return {
      title: locale === "ar" ? data?.seoDetails?.title?.ar : data?.seoDetails?.title?.en || "Yateem Optician",
      description: locale === "ar" ? data?.seoDetails?.description?.ar : data?.seoDetails?.description?.en || "Yateem Optician description",
      keywords: locale === "ar" ? data?.seoDetails?.keywords?.ar : data?.seoDetails?.keywords?.en || "Yateem Optician keywords",
      openGraph: {
        title: locale === "ar" ? data?.seoDetails?.title?.ar : data?.seoDetails?.title?.en || "Yateem Optician",
        description: locale === "ar" ? data?.seoDetails?.description?.ar : data?.seoDetails?.description?.en || "Yateem Optician description",
        type: "website",
        images: [
          {
            url: data?.seoDetails?.ogImage,
            width: 742,
            height: 396,
            alt: locale === "ar" ? data?.seoDetails?.title?.ar : data?.seoDetails?.title?.en || "Yateem Optician",
            type: "image/jpeg",
          },
        ],
      },
      alternates: {
        canonical: locale === "ar" ? data?.seoDetails?.canonical?.ar : data?.seoDetails?.canonical?.en,
      },
    };
  } catch (error: any) {
    console.log(error, "error");
    if (error?.status == 404) {
      return notFound();
    }
    return {
      title: "Yateem Optician",
      description: "Yateem Optician | Shop for the best contact lenses, spectacles and more",
    };
  }
}

async function Page({ params, searchParams }: any) {
  const queryClient = new QueryClient();
  const pageLimit = process.env.PAGESIZE || 15;

  const query: any = {}
  for (let param in searchParams) {
    if (typeof searchParams[param] == "string") {
      query[param] = [searchParams[param]]
    } else {
      query[param] = searchParams[param]
    }
  }

  const getProducts = async ({ pageParam = 1 }: { pageParam: number }) => {
    const res = await api.post(endpoints.products, {
      keyword: params.slug?.[0],
      page: pageParam,
      limit: pageLimit,
      server: true,
      ...query
    });
    return res.data.result;
  };

  const keys = ["filters", params.slug?.[0]]
  if (params.slug?.[0].includes("contact-lens")) {
    keys.push(query)
  }
  console.log(keys, "keys");
  await Promise.all([
    // api.get(endpoints.filters + `?category=${params.slug?.[0]}`),
    queryClient.prefetchQuery({
      queryKey: keys,
      queryFn: () => {
        return api.post(endpoints.filters + `?category=${params.slug?.[0]}`, {
          ...query
        }).then((res) => res.data?.result);
      },
    }),
    queryClient.prefetchInfiniteQuery({
      queryKey: ["products", params.slug?.[0] || "allProducts", query],
      queryFn: getProducts,
      initialPageParam: 1,
      getNextPageParam: (lastPage, pages) => lastPage.nextPage,
      pages: 2,
    })
  ])

  // const filters = filterData.data?.result || {};
  // const UrlParams = useSearchParams();
  // const paramsObject: any = {};
  // for (const [key, value] of searchParams?.entries()) {
  //   if (!paramsObject[key]) {
  //     paramsObject[key] = [value];
  //   } else {
  //     paramsObject[key].push(value);
  //   }
  // }

  // const pageData: any = queryClient.getQueryData(["products", params.slug?.[0] || "allProducts", query]);

  return (
    <main>

      {/* <BrandsDetail /> */}
      <HydrationBoundary state={dehydrate(queryClient)}>
        <>
          {/* <BreadCrumbWrapper
            slug={params.slug?.[0] || "allProducts"}
            pageLimit={pageLimit}
            allFilters={filters}
            query={query}
          /> */}
          <ListingPage
            slug={params.slug?.[0] || "allProducts"}
            pageLimit={pageLimit}
            // allFilters={filters}
            query={query}
            breadCrump={true}
          />
        </>
      </HydrationBoundary>
    </main>
  );
}

export default Page;
