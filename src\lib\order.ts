export type OrderStatusType = {
    [key: string]: { text: string; icon: string; status: string; }
}

export const orderStatusArray: OrderStatusType = {
    "PLACED": { text: "Order Placed", icon: "status_icon_check", status: "PLACED" },
    "CONFIRMED": { text: "Confirmed", icon: "status_icon_check", status: "CONFIRMED" },
    "SHIPPED": { text: "Shipped", icon: "status_icon_check", status: "SHIPPED" },
    "OUT FOR DELIVERY": { text: "Out for Delivery", icon: "status_icon_check", status: "OUT FOR DELIVERY" },
    "DELIVERED": { text: "Delivered", icon: "status_icon_check", status: "DELIVERED" },
    "CANCELLED": { text: "Cancelled", icon: "status_icon_cross", status: "CANCELLED" },
    "FAILED": { text: "Failed", icon: "status_icon_cross", status: "FAILED" },
    "RETURNED": { text: "Returned", icon: "status_icon_cross", status: "RETURNED" },
    "REFUNDED": { text: "Refunded", icon: "status_icon_cross", status: "REFUNDED" }
}


export const getOrderStatus = (status: string[]) => {
    return status.map((item: string) => {
        return orderStatusArray[item]
    })
}