"use client";

import { useForm } from "react-hook-form";
import "./contact-form.scss";
import { useContext, useEffect, useState } from "react";
import SuccessPopup from "../success-popup/SuccessPopup";
import api from "@/config/axios.interceptor";
import { endpoints } from "@/config/apiEndpoints";
import { toast } from "sonner";
import Select from "react-select";
import { countryCodeWithFlags } from "@/lib/countryCodeWithFlag";
import { getUser } from "@/lib/methods/auth";
import { TranslationContext } from "@/contexts/Translation";
import { useLocaleContext } from "@/contexts/LocaleProvider";

type ContactInputs = {
  fullName: string;
  email: string;
  countryCode: string;
  mobile: string;
  store: string;
  message: string;
};



function ContactForm({ stores, user, title }: any) {
  const [successModal, setSuccessModal] = useState(false);
  const [isSubmitting, setisSubmitting] = useState(false);
  const { currentLocale:locale, countryCodes } = useLocaleContext()
  const { register, handleSubmit, setFocus, setValue, reset, getValues, formState: { errors } } = useForm<ContactInputs>({
    defaultValues: {
      fullName: user?.name,
      email: user?.email,
      countryCode: user?.countryCode || countryCodes[locale.split("-")[0]],
      mobile: user?.mobile,
      store: "",
    },
  });

  const { translation: { formFields, popup } }: any = useContext(TranslationContext)

  const successModalData = {
    title: popup?.enquirySubmitTitle ?? "Your Enqury Submitted",
    description:
      popup?.enquirySubmitDescription ?? "Congratulations! 🎉 Your message has been successfully sent. Thank you for reaching out! We'll be in touch shortly to assist you further.",
    primaryBtnTxt: popup?.enquirySubmitButton ?? "Close",
  };

  const onSubmit = (data: ContactInputs) => {
    setisSubmitting(true);
    api
      .post(endpoints.contactForm, data)
      .then((res) => {
        setisSubmitting(false);
        if (res.data?.errorCode === 0) {
          setSuccessModal(true);
          reset();
        } else {
          toast.error(res.data?.message);
        }
      })
      .catch((err) => {
        toast.error(err.response.data.message);
        setisSubmitting(false);
      });
  };

  useEffect(() => {
    setFocus("fullName");
  }, []);
  return (
    <>
      <section className="contact-form">
        <h2>{title}</h2>
        <div className="container">
          <form
            className="contact-form_wrapper"
            onSubmit={handleSubmit(onSubmit)}
          >
            <div className="contact-form_inputs">
              <label htmlFor="name">{ formFields?.fullName ?? 'Full name'}*</label>
              <input
                {...register("fullName", {
                  required: (formFields?.fullNameRequiredError ?? "Please enter a valid name!"),
                })}
                type="text"
                name="fullName"
                placeholder="Alex Smith"
              />
              {errors.fullName && (
                <small className="form-error text-danger">
                  {errors.fullName.message}
                </small>
              )}
            </div>

            <div className="contact-form_inputs">
              <label htmlFor="email" className="email">{formFields?.emailAddress ?? 'Email Address'}*</label>
              <input
                {...register("email", {
                  required: (formFields?.emailAddressRequiredError ?? "Please enter email!"),
                  pattern: {
                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                    message: (formFields?.emailAddressInvalidError ?? "Invalid email address"),
                  },
                  validate: (value) => {
                    if (value.includes("@yahoo.com")) {
                      return (formFields?.emailAddressYahooError ?? "We don't accept Yahoo accounts");
                    }
                  },
                })}
                type="email"
                name="email"
                placeholder="<EMAIL>"
              />
              {errors.email && (
                <small className="form-error text-danger">
                  {errors.email.message}
                </small>
              )}
            </div>

            <div className="contact-form_inputs">
              <label htmlFor="phone">{formFields?.phoneNumber ?? 'Phone Number'}*</label>

              <div className="countrycode">
                <div className="countrycode-icon">
                  <Select
                    className="select-container"
                    onChange={(e: any) => setValue("countryCode", e.value)}
                    styles={
                      {
                        // option:(state)=>{}
                      }
                    }
                    // defaultMenuIsOpen={true}
                    theme={(theme) => ({
                      ...theme,
                      borderRadius: 0,
                      colors: {
                        ...theme.colors,
                        primary25: "#ccc",
                        primary: "black",
                      },
                    })}
                    classNames={{
                      control: (state) => "react-select",

                      dropdownIndicator: () => "d-none",
                      option: (state) =>
                        state.isSelected ? "option selected" : "option",
                      menu: () => "menu",
                    }}
                    formatOptionLabel={(country) => (
                      <div className="drop-item">
                        <img src={country.image} alt="" />
                        {country.label}
                      </div>
                    )}
                    options={countryCodeWithFlags?.map((country) => ({
                      label: country.name,
                      value: country.dial_code,
                      image: country.image,
                    }))}
                  />
                </div>
                <input
                  {...register("countryCode")}
                  id="countryCode"
                  name="countryCode"
                />


                <input className="px-0"
                  {...register("mobile", {
                    required: (formFields?.phoneNumberRequiredError ?? "Please enter a valid phone number!"),
                    pattern: {
                      value: /^[0-9]{9}/,
                      message: (formFields?.phoneNumberInvalidError ?? "Invalid Phone Number"),
                    },
                  })}
                  tabIndex={1}
                  type="number"
                  name="mobile"
                  placeholder="************"
                />

                {errors.mobile && (
                  <small className="form-error text-danger">
                    {errors.mobile.message}
                  </small>
                )}
              </div>
            </div>

            <div className="contact-form_inputs">
              <label htmlFor="store">{formFields?.storeEnquiry ?? 'Store Enquiry'}*</label>
              <select
                {...register("store", { required: (formFields?.storeEnquiryRequiredError ?? "Please select a store!") })}
                name="store"
                defaultValue=""
              >
                <option value="" disabled>
                  {formFields?.selectStore ?? 'Select Store'}
                </option>
                {stores.stores.map((store: any) => (
                  <option key={store._id} value={store._id}>
                    {store?.name}
                  </option>
                ))}
              </select>
              {errors.store && (
                <small className="form-error text-danger">
                  {errors.store.message}
                </small>
              )}
            </div>

            <div className="contact-form_inputs">
              <label htmlFor="">{formFields?.message ?? 'Message'}*</label>
              <textarea
                {...register("message", {
                  required: (formFields?.messageRequiredError ?? "Please enter a message!"),
                })}
                name="message"
                placeholder={formFields?.message ?? 'Message'}
              ></textarea>
              {errors.message && (
                <small className="form-error text-danger">
                  {errors.message.message}
                </small>
              )}
            </div>

            <div className="contact-form_btn">
              <input
                type="submit"
                className="primary-btn"
                disabled={isSubmitting}
                value={(formFields.submit ?? "Submit")}
              />
            </div>
          </form>
        </div>
      </section>

      <SuccessPopup
        show={successModal}
        handleClose={() => setSuccessModal(false)}
        data={successModalData}
      />
    </>
  );
}

export default ContactForm;
