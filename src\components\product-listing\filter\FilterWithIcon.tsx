import React, { useContext, useState } from "react";
import useFilter from "@/hooks/useFilter";
import Image from "next/image";
import { useCallback } from "react";
import FallbackImage from "@/components/fallback-image/FallbackImage";
import { TranslationContext } from "@/contexts/Translation";

function FilterWithIcon({ filter, name, showName }: any) {
  const { active, setActive } = useFilter(name);
  const [visibleItems, setVisibleItems] = useState(12);
  const { translation }: any = useContext(TranslationContext)

  const handleFilter = (e: any) => {
    let value = e.target.value;
    let newActive;
    if (active.includes(value)) {
      newActive = active.filter((item: any) => item !== value);
    } else {
      newActive = [...active, value];
    }
    setActive(newActive)
  }

  const handleViewMore = () => {
    setVisibleItems((prev: any) => prev + 12);
  };

  return (
    <>
      {filter?.slice(0, visibleItems)?.map((items: any) => {
        return (
          <label
            className="brands-border"
            key={items?._id}
            htmlFor={items?._id}
            style={{ opacity: items?.active != undefined ? (items?.active ? 1 : 0.4) : 1, cursor: items?.active != undefined ? !items?.active ? "not-allowed" : "pointer" : "pointer" }}
          >
            <input
              style={{ cursor: items?.active != undefined ? !items?.active ? "not-allowed" : "pointer" : "pointer" }}
              onChange={handleFilter}
              value={items?._id}
              checked={active?.includes(items?._id)}
              name={name}
              className="brands-input"
              type="checkbox"
              id={items?._id}
              disabled={items?.active != undefined ? !items?.active : false}
            />
            {/* <Image
              quality={100}
              priority
              src={items?.logo ?? ""}
              width={81}
              height={45}
              alt={items?.name}
            /> */}
            <FallbackImage
              src={items?.logo}
              alt={items?.name}
              width={90}
              height={50}
              quality={100}
              fallbackSrc="/images/product/noImage.jpg" />
            {showName && <span>{items.name}</span>}
          </label>
        );
      })}
      {visibleItems < filter?.length && (
        <button onClick={handleViewMore} className="view-more-button">
          {translation?.productListing?.viewMore ?? "View More"}
        </button>
      )}
    </>
  );
}

export default FilterWithIcon;
