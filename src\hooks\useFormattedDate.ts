import { useState, useEffect } from 'react';

const useFormattedDate = (orderDate: string | null): string => {
    const [formattedDate, setFormattedDate] = useState<string>('');

    useEffect(() => {
        const formatDate = (dateString: string): string => {
            const dateObj = new Date(dateString);

            const months: string[] = [
                "January", "February", "March", "April", "May", "June", "July",
                "August", "September", "October", "November", "December"
            ];

            const day: number = dateObj.getDate();
            const month: string = months[dateObj.getMonth()];
            const year: number = dateObj.getFullYear();
            let hours: number = dateObj.getHours();
            const minutes: number = dateObj.getMinutes();
            const period: string = hours >= 12 ? "PM" : "AM";

            hours = hours % 12 || 12; // Convert to 12-hour format

            return `${day} ${month} ${year} ${hours}:${minutes.toString().padStart(2, '0')} ${period}`;
        };

        if (orderDate) {
            const formatted: string = formatDate(orderDate);
            setFormattedDate(formatted);
        }
    }, [orderDate]);

    return formattedDate;
};

export default useFormattedDate;
