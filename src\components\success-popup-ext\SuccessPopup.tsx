import Modal from "react-bootstrap/Modal";
import Image from "next/image";
import "./success-popup.scss";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { useLocaleContext } from "@/contexts/LocaleProvider";

type SuccessProps = {
  show: boolean;
  handleClose: () => void;
  data: SuccessDataProps;
  isFail?: boolean;
  side?: boolean;
};

type SuccessDataProps = {
  title: string;
  description: string;
  primaryBtn: {
    type: "link" | "button";
    text: string;
    link?: string;
    onClick?: () => void;
  };
  secondaryBtn?: {
    type: "link" | "button";
    text: string;
    link?: string;
    onClick?: () => void;
  }
};

function SuccessPopupExt({ show, handleClose, data, isFail, side }: SuccessProps) {
  const router = useRouter();
  const { currentLocale: locale } = useLocaleContext()

  // below useEffect is causing the back button to always redirect to home screen, need code review

  // useEffect(() => {
  //   const handlePopState = () => {
  //     // Redirect to the home screen
  //     router.push('/');
  //   };
  //   window.addEventListener('popstate', handlePopState);
  //   return () => {
  //     window.removeEventListener('popstate', handlePopState);
  //   };
  // }, []);

  return (
    <>
      <Modal
        className={`success-popup ${side && "success-popup-side"} ${locale == "ar" ? "rtl" : ""}`}
        show={show}
        onHide={handleClose}
        backdrop="static"
        keyboard={false}
        centered={!side}
      >
        <Modal.Header closeButton></Modal.Header>
        <Modal.Body>
          <Image
            quality={100}
            priority
            src={
              isFail ? "/images/common/failed.gif" : "/images/common/thanks.gif"
            }
            width={364}
            height={273}
            alt="tick"
            style={{ objectFit: isFail ? "contain" : "cover" }}
          />
          <h4>{data?.title}</h4>
          <p>{data?.description}</p>

          <div className="success-btn">
            {data?.primaryBtn?.type === "link" ? (
              <Link href={data?.primaryBtn?.link || "/"}>
                <button className="continue-shopping">
                  {data?.primaryBtn?.text}
                </button>
              </Link>
            ) : (
              <button
                onClick={data?.primaryBtn?.onClick}
                className="continue-shopping"
              >
                {data?.primaryBtn?.text}
              </button>
            )}
            {data?.secondaryBtn?.type === "link" ? (
              <Link href={data?.secondaryBtn?.link || "/"}>
                <button className="view-order">{data?.secondaryBtn?.text}</button>
              </Link>
            ) : (
              <button
                onClick={data?.secondaryBtn?.onClick}
                className="view-order"
              >
                {data?.secondaryBtn?.text}
              </button>
            )}
          </div>
        </Modal.Body>
      </Modal>
    </>
  );
}

export default SuccessPopupExt;
