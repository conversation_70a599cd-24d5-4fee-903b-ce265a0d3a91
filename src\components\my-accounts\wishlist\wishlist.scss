.wishlist {
  &_grids {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    column-gap: 1.8rem;
    row-gap: 2rem;
    margin-top: 4.4rem;

    @media (max-width: 767.98px) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media (max-width: 575.98px) {
      margin-top: 2.8rem;
      gap: 1.2rem;
    }

    .product-card {
      border-radius: 1.5rem;
      background: #f2f4f9;

      @media (max-width: 575.98px) {
        padding: 0.8rem 0 2.8rem 0;
      }

      .add-to-cart {
        margin-top: 2rem;
      }

      @media (max-width: 575.98px) {
        img {
          height: 7.8rem;
          margin: 0rem 0rem 1rem 0;
        }

        label {
          font-size: 1.2rem;
          padding: 0.3rem 1.2rem;
        }

        .brand-name {
          font-size: 1.2rem;
          line-height: 1.5rem;
        }

        .product-modal {
          font-size: 1.3rem;
          line-height: 1.6rem;
          margin-top: 0.5rem;
        }

        .product-price-detail {
          font-size: 1.2rem;
          line-height: 1.5rem;
          column-gap: 0.5rem;
        }

        .add-to-cart {
          margin-top: 1.7rem;
        }
      }
    }
  }
}
