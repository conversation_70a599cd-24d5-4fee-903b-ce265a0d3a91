"use client"

import Link from "next/link";
import "./breadcrumbs.scss";
import Image from "next/image";
import { useContext, useEffect, useState } from "react";
import { HistoryContext } from "@/contexts/HistoryProvider";
import { TranslationContext } from "@/contexts/Translation";

type Props = {
  backHome: string;
  currentPage: string | breadcrump[];
  image: string;
  isLoading?: boolean;
};
type breadcrump = {
  title: string;
  link: string;
};

function BreadCrumbs({ backHome, currentPage, image, isLoading }: Props) {
  // titleLink:any, currentPage:any, image:any
  const [imgSrc, setImgSrc] = useState("/images/common/blurred_banner.png")
  const { prevPage } = useContext(HistoryContext);

  const { translation: { breadCrump: translation } } = useContext(TranslationContext)


  useEffect(() => {
    if (prevPage?.url?.length > 0) {
      if (!isLoading) {
        setImgSrc(image)
      }
    } else {
      setImgSrc("/images/common/banner.png")
    }
  }, [isLoading])

  const breadcrumbs = [
    {
      string: "insurance",
      value: translation?.insurance ?? "Insurance"
    },
    {
      string: "about us",
      value: translation?.aboutUs ?? "About Us"
    },
    {
      string: "store locator",
      value: translation?.storeLocator ?? "Store Locator"
    },
    {
      string: "contact us",
      value: translation?.contactUs ?? "Contact Us"
    },
    {
      string: "brands",
      value: translation?.brands ?? "Brands"
    },
    {
      string: "my accounts",
      value: translation?.myAccount ?? "My Account"
    },
    {
      string: "my cart",
      value: translation?.myAccount ?? "My Cart"
    },
    {
      string: "privacy policy",
      value: translation?.privacyPolicy ?? "My Cart"
    },
    {
      string: "terms and conditions",
      value: translation?.termsAndCondition ?? "Terms And Conditions"
    },
    {
      string: "cookie policy",
      value: translation?.cookiePolicy ?? "Cookie policy"
    },
    {
      string: "shipping policy",
      value: translation?.cookiePolicy ?? "Shiping Policy"
    },
    {
      string: "return policy",
      value: translation?.returnPolicy ?? "Return Policy"
    },
    {
      string: "refund policy",
      value: translation?.refundPolicy ?? "Refund Policy"
    },
    {
      string: "all products",
      value: translation?.allProducts ?? "All products"
    },
    {
      string: "blogs",
      value: translation?.blogs ?? "Blogs"
    },
    {
      string: "products",
      value: translation?.products ?? "Products"
    },
    {
      string: "category",
      value: translation?.category ?? "Category"
    },
  ]

  function getCurrent(page: string) {
    console.log(page)
    const lower = page?.toLocaleLowerCase()
    for (let breadcrumb of breadcrumbs) {
      if (lower?.includes(breadcrumb.string)) {
        return `${breadcrumb.value}`
      }
    }
    return page
  }

  return (
    <nav className="breadcrumb m-0">
      <div className="container">
        <div className="breadcrumb_wrapper">
          <div className="breadcrumb_link">
            <ul>
              <li className="breadcrumb_item me-2">
                <Link href="/">{backHome == "Home" ? (translation?.home ?? "Home") : backHome}</Link>
              </li>
              
              {typeof currentPage === "string" ? (
                <>
                <li style={{color: "white", padding: "0 2px"}}>/</li>
                <li className="breadcrumb_item text-capitalize active" aria-current="page">
                  {getCurrent(currentPage)}
                </li>
                </>
              ) : (
                currentPage.map((items: breadcrump, index: number) => (
                  <>
                    <li style={{color: "white", padding: "0 5px"}}>{" "}/{" "}</li>

                  <li
                    className={`breadcrumb_item array_item text-capitalize ${index === currentPage.length - 1 ? "active" : ""
                      }`}
                    key={index}>
                      {index === currentPage.length - 1 ? (
                        <span>{getCurrent(items.title)}</span>
                      ) : (
                        <Link href={items.link}>{getCurrent(items.title)}</Link>
                      )}
                  </li>
                  </>
                ))
              )}
            </ul>
          </div>

          <div className="breadcrumb_image">
            <Image quality={100} priority onError={() => setImgSrc("/images/common/banner.png")} src={imgSrc} width={690} height={290} alt="banner" />
          </div>
        </div>
      </div>
    </nav>
  );
}

export default BreadCrumbs;
