.checkout {
  position: relative;

  .status {
    &_bar {
      display: flex;
      align-items: center;
      column-gap: 6.6rem;
      margin-top: 3.3rem;

      @media (max-width: 991.98px) {
        column-gap: 3.4rem;
      }

      @media (max-width: 575.98px) {
        margin-top: 3.4rem;
      }

      li {
        font-size: 1.4rem;
        font-weight: 500;
        display: flex;
        align-items: center;
        column-gap: 0.6rem;
        position: relative;
        color: rgba(0, 0, 0, 0.5);

        @media (max-width: 575.98px) {
          font-size: 1.2rem;
        }

        a {
          display: flex;
          align-items: center;
          column-gap: 1.5rem;
        }

        &.isActive {
          color: #000;

          .status_icon {
            background-color: #000;

            @media (max-width: 575.98px) {
              width: 3.4rem;
              height: 3.4rem;
              font-size: 1.6rem;
            }
          }

          .status_text {
            font-size: 2rem;

            @media (max-width: 767.98px) {
              display: block;
              line-height: 1.8rem;
              font-size: 1.5rem;
            }
          }
        }

        &:not(:last-child) {
          &::after {
            content: "";
            height: 0.1rem;
            background-color: #000;
            width: 4.5rem;
            position: absolute;
            left: 100%;
            top: 50%;
            transform: translate(23%, 50%);

            @media (max-width: 991.98px) {
              width: 2.5rem;
              transform: translate(17%, 50%);
            }
          }
        }

        .status_icon {
          width: 3.2rem;
          height: 3.2rem;
          border-radius: 50%;
          background-color: #868686;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          position: relative;
          color: #fff;

          &::after {
            content: none;
          }

          @media (max-width: 575.98px) {
            width: 2.7rem;
            height: 2.7rem;
          }
        }

        .status_text {
          color: #000;

          @media (max-width: 767.98px) {
            display: none;
          }
        }
      }
    }
  }

  &_wrapper {
    display: flex;
    column-gap: 3.4rem;

    @media (max-width: 991.98px) {
      flex-direction: column;
    }
  }

  &_left {
    width: 65%;
    column-gap: 2.6rem;

    @media (max-width: 991.98px) {
      width: 100%;
    }

    h4 {
      margin-top: 6.4rem;
      color: #000;
      font-size: 2.4rem;
      font-weight: 500;

      @media (max-width: 575.98px) {
        margin-top: 2.7rem;
        font-size: 2rem;
      }
    }

    .new-address {
      color: #000;
      font-size: 1.4rem;
      font-weight: 400;
      text-decoration-line: underline;
      background-color: transparent;
      border: none;
      margin-top: 2rem;
      margin-left: auto;
      display: flex;

      @media (max-width: 575.98px) {
        margin-top: 2.5rem;
      }
    }

    &-flex {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      margin-top: 1.2rem;
      gap: 2.6rem;

      @media (max-width: 767.98px) {
        grid-template-columns: repeat(1, 1fr);
      }

      @media (max-width: 575.98px) {
        gap: 2rem;
      }
    }

    .continue-delivery-btn {
      width: 26.6rem;
      height: 5.5rem;
      border-radius: 6rem;
      background: #000;
      color: #fff;
      font-size: 1.5rem;
      font-weight: 500;
      border: none;
      margin-top: 7.3rem;

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      &:hover {
        background-color: transparent;
        color: #000;
        border: 1px solid;
      }

      @media (max-width: 575.98px) {
        margin: auto;
        margin-top: 5.3rem;
        width: 100%;
      }
    }

    label {
      display: flex;
      align-items: center;
      column-gap: 1rem;
      color: #000;
      font-size: 1.4rem;
      font-weight: 300;
      margin-top: 2.1rem;

      input {
        width: 2rem;
        height: 2rem;
      }
    }
  }

  &_card {
    border-radius: 1.2rem;
    border: 1px solid #edeef2;
    background: #fff;
    padding: 2.3rem 1rem 2.5rem 2.3rem;
    color: #000;
    cursor: pointer;

    &.selected {
      background-color: #f2f4f9;
    }

    @media (max-width: 575.98px) {
      padding: 2.5rem 1.4rem 2.5rem 2.3rem;
    }

    &.isActive {
      background: #f2f4f9;

      ul li.btns a {
        background-color: #000000;
        color: #fff;

        &:hover {
          background-color: transparent;
          color: #000;
        }
      }
    }

    h5 {
      font-size: 2rem;
      font-style: normal;
      font-weight: 400;
    }

    ul {
      margin-top: 1.9rem;

      li {
        font-size: 1.6rem;
        font-weight: 400;
        line-height: 2.2596rem;
        max-width: 36.4rem;
        width: 100%;

        &:not(:last-child) {
          margin-bottom: 2rem;

          @media (max-width: 575.98px) {
            margin-bottom: 1.9rem;
          }
        }

        a {
          color: #000000;
        }

        &.cta {
          margin: 2.5rem 0;
          display: flex;
          column-gap: 1.9rem;

          @media (max-width: 575.98px) {
            column-gap: 1.2rem;
          }

          a,
          button {
            padding: 0.7rem 1.9rem;
            border-radius: 0.8rem;
            border: 1px solid #000;
            font-size: 1.6rem;
            font-weight: 400;
            transition: 0.3s all ease;
            color: #000000 !important;
            background-color: transparent;

            &:hover,
            &.activeBtn {
              color: #ffffff !important;
              transition: 0.3s all ease;
              background-color: #000;
            }

            &[disabled] {
              pointer-events: none;
            }
          }
        }

        &.btns {
          display: flex;

          button {
            border: none;
            background-color: transparent;
            padding: 0;
            font-size: 1.6rem;
            font-weight: 400;
            line-height: 2rem;
            color: #000000 !important;

            &:not(:last-child) {
              border-right: 1px solid #d9d9d9;
              padding-right: 1rem;
              margin-right: 1rem;
            }
          }
        }
      }
    }
  }

  &_right {
    width: 35%;
    background: #f2f4f9;
    padding: 3.5rem 11.6rem 4rem 4.4rem;
    position: absolute;
    right: 0;
    top: -64px;

    @media (max-width: 991.98px) {
      position: unset;
      width: 100%;
      margin-top: 10.5rem;
    }

    @media (max-width: 575.98px) {
      padding: 2.5rem 2rem 5.6rem 2.2rem;
      margin-bottom: 1.9rem;
    }

    h5 {
      color: #000;
      font-size: 2.4rem;
      font-weight: 500;
      margin-bottom: 2rem;

      @media (max-width: 575.98px) {
        font-size: 2rem;
        line-height: 2.5rem;
        margin-bottom: 2.5rem;
      }
    }
  }

  &_order {
    display: flex;
    column-gap: 2.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.2);
    padding-bottom: 1.5rem;

    @media (max-width: 575.98px) {
      column-gap: 2.1rem;
      padding-bottom: 2.5rem;
      align-items: center;
    }

    &:not(:last-child) {
      margin-bottom: 1.5rem;

      @media (max-width: 575.98px) {
        margin-bottom: 2rem;
      }
    }

    a {
      border-radius: 1.5rem;

      &.pd-img {
        background-color: #ffffff;
      }

      img {
        width: 12rem;
        height: 11rem;
        border-radius: 1.2rem;
        object-fit: contain;
        mix-blend-mode: multiply;

        @media (max-width: 575.98px) {
          width: 9.6rem;
          height: 9.6rem;
        }
      }
    }



    ul {
      li {
        color: #807d7e;
        font-size: 1.4rem;
        font-weight: 400;
        line-height: 1.7rem;

        &:nth-of-type(1) {
          color: #000;
          font-size: 1.6rem;
          font-weight: 600;
          letter-spacing: 0.032rem;
          margin-bottom: 1.1rem;
          line-height: 2rem;

          @media (max-width: 575.98px) {
            margin-bottom: 0.5rem;
          }

          span {
            color: rgba(0, 0, 0, 0.5);
          }
        }

        &:nth-of-type(2) {
          margin-bottom: 0.5rem;
        }

        &:last-child {
          color: #000;
          font-size: 1.8rem;
          font-weight: 600;
          line-height: 2.2rem;
          margin-top: 1rem;

          @media (max-width: 575.98px) {
            margin-top: 1.8rem;
            font-size: 1.6rem;
          }
        }
      }
    }
  }

  &_total {
    ul {
      margin-top: 3.9rem;

      li {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #000;
        font-size: 1.5rem;
        font-weight: 600;
        padding-right: 3.4rem;

        @media (max-width: 575.98px) {
          padding-right: 1.5rem;
        }

        &:not(:last-child) {
          margin-bottom: 1.4rem;
          padding-bottom: 1.4rem;
          border-bottom: 1px solid rgba(0, 0, 0, 0.2);
        }

        h6 {
          color: #374957;
          font-size: 1.5rem;
          font-weight: 400;
        }

        span {
          color: #000;
          font-size: 1.8rem;
          font-weight: 600;
        }
      }
    }
  }
}

.heightfix {
  @media screen and (min-width: 992px) {
    min-height: max-content;
  }
}

.checkout_order-coupon {
  background-color: rgba(242, 244, 249, 0.5);
  padding: 2rem 0;

  button {
    width: 100%;
    border: none;
    padding: 0;
    background-color: transparent;
    display: flex;
    border-radius: 0.8rem;
    border: 1px solid #e4e4e4;
    background: #fff;
    padding: 1.3rem 1.3rem 1.3rem 1.5rem;
    align-items: center;
    column-gap: 1.5rem;

    @media (max-width: 991.98px) {
      margin: 0 auto;
    }

    @media (max-width: 575.98px) {
      width: 100%;
    }
  }

  span {
    color: #000;
    text-align: center;
    font-size: 1.6rem;
    font-weight: 500;
  }

  img {
    width: 2.4rem;
    height: 2.4rem;
  }

  .arrow {
    width: 1.4rem;
    height: 1.4rem;
    margin-left: auto;
  }
}

.app.rtl {

  .checkout_right {
    left: 0;
    right: auto;
    padding: 3.5rem 4.4rem 4rem 11.6rem;

    @media (max-width: 575.98px) {
      padding: 2.5rem 2.2rem 5.6rem 2rem;
    }
  }

  .checkout_order-coupon .arrow {
    margin-left: 0;
    margin-right: auto;
    transform: rotate(180deg);
  }

  .checkout .status_bar li:not(:last-child)::after {
    right: 100%;
    left: auto;
    transform: translate(-30%, 50%);
  }

  .checkout_card ul li.btns button:not(:last-child) {
    border-left: 1px solid #d9d9d9;
    padding-left: 1rem;
    margin-left: 1rem;
    margin-right: 0;
    padding-right: 0;
    border-right: 0;
  }
}