"use client";
import OverLayLoader from "@/components/LogoAnimation/OverLayLoader";
import { useState, lazy, Suspense } from "react";
const ReviewPopup = lazy(() => import("@/components/review-popup/ReviewPopup"));
import Rating from "../product-detail/rating/Rating";
import { endpoints } from "@/config/apiEndpoints";
import api from "@/config/axios.interceptor";
import { useQuery } from "@tanstack/react-query";

function ProductReview({ product }: any) {
  const [reviewshow, setReviewShow] = useState(false);
  const handleReviewClose = () => setReviewShow(false);
  const handleReviewShow = () => setReviewShow(true);

  const {
    data: reviews,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["reviews", product?.mainProduct || product?._id],
    queryFn: () => {
      return api
        .get(`${endpoints.reviews}/${product?.mainProduct || product?._id}`)
        .then((res) => {
          return res.data.result;
        });
    },
  });

  return (
    <>
      <button
        onClick={handleReviewShow}
        className="p-0 bg-transparent border-0 text-start"
      >
        <Rating rating={product?.rating} count={reviews?.reviewsCount} />
      </button>
      <Suspense fallback={<OverLayLoader />}>
        {reviewshow && (
          <ReviewPopup
            reviews={reviews?.reviews}
            rating={reviews?.rating}
            count={reviews?.reviewsCount}
            show={reviewshow}
            handleClose={handleReviewClose}
            handleShow={handleReviewShow}
          />
        )}
      </Suspense>
    </>
  );
}

export default ProductReview;
