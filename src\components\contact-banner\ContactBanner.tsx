import Link from "next/link";
import Image from "next/image";

export default async function ContactBanner({banner}:any) {
  console.log(banner)
  return (
    <section className="connect" key={banner?._id}>
      <div className="container">
        <div className="connect_wrapper">
          <div className="connect_content">
            <h2>{banner?.title}</h2>
            <p>{banner?.description}</p>

            <Link href={banner?.link} target="_blank" className="primary-btn">
              {banner?.buttonText}
            </Link>
          </div>

          <div className="connect_image">
            <Image quality={100} priority
              className="d-sm-block d-none"
              // src="/images/common/rgjnbgr.png"
              src={banner?.image}
              width={535}
              height={600}
              alt="images"
            />

            <Image quality={100} priority
              className="d-sm-none d-block"
              // src="/images/common/rgjnbgr-mob.png"
              src={banner?.image}
              width={314}
              height={336}
              alt="images"
            />
          </div>
        </div>
      </div>
    </section>
  );
}
