"use client";

import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Pagination, Navigation } from "swiper/modules";
import "./shapecategories.scss";
import Image from "next/image";
import api from "@/config/axios.interceptor";
import { endpoints } from "@/config/apiEndpoints";
import Link from "next/link";
import { useQuery } from "@tanstack/react-query";
import FallbackImage from "@/components/fallback-image/FallbackImage";

const shapesData = [
  {
    image: "/images/home/<USER>",
    shapes: "Clubmaster",
  },

  {
    image: "/images/home/<USER>",
    shapes: "Round Glasses",
  },

  {
    image: "/images/home/<USER>",
    shapes: "Blend Edit",
  },

  {
    image: "/images/home/<USER>",
    shapes: "Square Glasses",
  },

  {
    image: "/images/home/<USER>",
    shapes: "Retro Aviator",
  },

  {
    image: "/images/home/<USER>",
    shapes: "Clubmaster",
  },
];

export default function ShapeCategories({ id, item }: any) {
  // const shapesData: any = await api.get(endpoints.frameShapes).then((res) => res.data);
  // const shapes = shapesData.result;

  const {
    data: shapes,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["frameShapes"],
    queryFn: () => {
      return api.get(endpoints.frameShapes).then((res) => res.data?.result);
    },
  });

  if (shapes?.length > 0) {
    return (
      <section className="shape" id={id}>
        <div className="shape_wrapper">
          <div className="container-custem px-md-5">
            <div className="shape_title">
              <h2>{item?.title}</h2>
            </div>
            <div style={{ width: "100%" }}>
              <Swiper
                pagination={{
                  type: "progressbar",
                }}
                loop={true}
                modules={[Autoplay, Pagination, Navigation]}
                className="mySwiper"
                speed={500}
                autoplay={{
                  delay: 2000,
                  disableOnInteraction: false,
                  pauseOnMouseEnter: true,
                }}
                breakpoints={{
                  320: {
                    slidesPerView: 2,
                    spaceBetween: 15,
                  },

                  600: {
                    slidesPerView: 3,
                    spaceBetween: 15,
                  },

                  900: {
                    slidesPerView: 4,
                    spaceBetween: 15,
                  },

                  1280: {
                    slidesPerView: 5,
                    spaceBetween: 20,
                  },
                }}
              >
                {shapes.map((items: any, index: number) => (
                  <SwiperSlide key={items?._id}>
                    <Link
                      href={`/products?frameShape=${items?._id}`}
                      key={index}
                    >
                      <div>
                        {/* <Image
                          quality={100}
                          priority
                          src={items?.image ?? ""}
                          width={110}
                          height={40}
                          alt="image"
                        /> */}
                        <FallbackImage
                          src={items?.image ?? ""}
                          width={110}
                          height={40}
                          alt="image"
                          fallbackSrc="/images/product/noImage.jpg"
                        />
                        <h5>{items.name}</h5>
                      </div>
                    </Link>
                  </SwiperSlide>
                ))}
              </Swiper>
            </div>
          </div>
        </div>
      </section>
    );
  } else {
    return null;
  }
}
