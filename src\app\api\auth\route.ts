import { headers, cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'


export async function GET(request: Request) {
    const headersList = headers()
    const referer = headersList.get('referer')

    return new Response('Hello, Next.js!', {
        status: 200,
        headers: { referer: 'no-referer' },
    })
}

export async function POST(request: NextRequest) {
    const data = request.cookies.get('access_token')
    const cookieStore = cookies()
    cookieStore.delete('access_token')
    return NextResponse.json({ success: true })
}