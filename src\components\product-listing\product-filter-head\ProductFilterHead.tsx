"use client";
import { useContext, useState } from "react";
import "./product-filter-head.scss";
import Image from "next/image";
import { usePathname, useRouter } from "next/navigation";
import { TranslationContext } from "@/contexts/Translation";

function ProductFilterHead({ onButtonClick }: any) {
  const [isFilterVisible, setIsFilterVisible] = useState(false);
  const pathname = usePathname();
  const router = useRouter();

  const { translation: { productListing: translation } }: any = useContext(TranslationContext)

  const handleClick = () => {
    setIsFilterVisible(!isFilterVisible);
    onButtonClick();
  };

  const clearFliters = () => {
    router.push(pathname, { scroll: false });
  };

  return (
    <div className={`filter-head ${isFilterVisible ? "active" : ""}`}>
      <div className="filter-head_flex">
        <button disabled>
          <Image quality={100} priority src="/images/common/fi_sliders.png" width={24} height={24} alt="filter image" />
          {translation?.filterBy || "Filter by"}
        </button>
        <span className="cursor-pointer" onClick={clearFliters}>
          {translation?.clearAll || "Clear All"}
        </span>
        <button onClick={handleClick} className="toggle-btn">
          <Image quality={100} priority src="/images/common/toggler.png" width={24} height={24} alt="filter image" />
        </button>
      </div>
    </div>
  );
}

export default ProductFilterHead;
