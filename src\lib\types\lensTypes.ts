export type MultiPrescriptionType = {
    left: number;
    right: number;
}

export type PrescriptionType = {
    sph: number | MultiPrescriptionType;
    cyl: number | MultiPrescriptionType;
    axis: number | MultiPrescriptionType;
    add?: number | MultiPrescriptionType;
    pd: number | MultiPrescriptionType;
}


export type LensDataType = {
    product: string;
    quantity: number;
    vision: 'single' | 'progressive' | "";
    prescription: File | PrescriptionType | null;
    lensType: string;
    brand: string;
    photocromic: string;
    size: string | undefined;
    index: string;
    userDetails: {
        name: string;
        phone: string;
        message: string;
    }
    coating: string;
    sum: number;
}
export const stepFlow = {
    // single: ["type", "prescription", "preference", "material", "index", "coating",],
    single: ["type", "prescription", "userDetails", "success"],
    progressive: ["type", "prescription", "userDetails", "success"],
};