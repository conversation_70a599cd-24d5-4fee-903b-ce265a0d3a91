"use client";
import { Autoplay, Pagination, Navigation } from "swiper/modules";
import { Swiper, SwiperSlide, useSwiper } from "swiper/react";
import WithBlur from "./WithBlur";
import Image from "next/image";
import { useEffect, useState } from "react";

export const WithWithoutSlider = ({ data }: any) => {
  
  return (
    <>
      <Swiper
        allowTouchMove={false}
        slidesPerView={2}
        spaceBetween={30}
        pagination={{
          type: "progressbar",
        }}
        modules={[Autoplay, Pagination, Navigation]}
        breakpoints={{
          320: {
            slidesPerView: 1,
            spaceBetween: 30,
          },

          768: {
            slidesPerView: 2,
            spaceBetween: 30,
          },
        }}
        className="mySwiper"
      >
        {data?.map((item: any, index: number) => (
          <SwiperSlide key={index}>
            <WithBlur data={item} />
          </SwiperSlide>
        ))}

        {/* <SwiperSlide>
              <WithoutBlur />
            </SwiperSlide> */}
      <Buttons />
      </Swiper>

    </>
  );
};

function Buttons(){
  const swiper = useSwiper();
  const [swiperStart, setSwiperStart] = useState(true);
  const [swiperEnd, setSwiperEnd] = useState(false);

  swiper.on('slideChange', ()=>{
    setSwiperStart(swiper.isBeginning)
    setSwiperEnd(swiper.isEnd)
  })
  
  return (
    <div className="swiper-paginaion d-md-none d-flex">
    <div style={{opacity: swiperStart? 0.5 : 1}} onClick={() => swiper?.slidePrev()} className="swiper-button-next">
      <Image
        src="/images/home/<USER>/left-chevron.png"
        width={500}
        height={500}
        alt="arrow"
      />
    </div>
    <div style={{opacity: swiperEnd? 0.5 : 1}} onClick={() => swiper?.slideNext()}  className="swiper-button-prev">
      <Image
        src="/images/home/<USER>/right-chevron.png"
        width={500}
        height={500}
        alt="arrow"
      />
    </div>
  </div>
  )
}
