"use client";
import { addToCart } from "@/lib/methods/cart";
import "./add-to-cart.scss";
import { CountContext } from "@/contexts/AddBtnContext";
import { useContext } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { useSearchParams } from "next/navigation";
import { sendGTMEvent } from "@next/third-parties/google";
import { calculateSavings } from "@/app/[locale]/product/[slug]/ProductVariantSection";
import { AuthContext } from "@/contexts/AuthProvider";
import { createHmac } from "crypto";
import { useLocaleContext } from "@/contexts/LocaleProvider";

interface AddToCartProps {
  showSvg?: boolean;
  useContextValue?: boolean;
  product?: any;
  qty?: number;
  translation: any;
}

function AddToCart({ showSvg, useContextValue, product, qty, translation }: AddToCartProps) {
  const { subscriptionType, multi } = useContext(CountContext);
  const context = useContext(CountContext);
  const count = useContextValue ? context?.count : qty;
  const varient = context?.varient;
  const queryClient = useQueryClient();
  const searchParams = useSearchParams();
  const size = searchParams?.get("size") || product?.sizes?.[0]?.size?._id || null;
  const { userProfile } = useContext(AuthContext);
  const { currencyCode } = useLocaleContext()

  const handleClick = () => {

    const price = product?.price?.aed;
    const offerPrice = product?.offerPrice?.aed;
    let savings = calculateSavings(price, offerPrice);
    if (!offerPrice) savings = 0;
    sendGTMEvent({ ecommerce: null })
    let eventData: any = {
      event: "add_to_cart",
      ecommerce: {
        currency: currencyCode,
        value: price - savings,
        items: [
          {
            item_id: product?.sku,
            item_name: product?.name,
            discount: savings,
            index: 0,
            item_brand: product?.brand,
            item_category: product?.category?.[0]?.name,
            item_category2: product?.category?.[1]?.name,
            item_variant: product?.color?.name,
            price,
            quantity: count * (multi ? 2 : 1),
          }
        ]
      }
    }
    if (userProfile) {
      const mobile = createHmac('sha256', '123').update(userProfile?.mobile).digest('hex')
      let email = null;
      if (userProfile?.email) email = createHmac('sha256', '123').update(userProfile?.email).digest('hex')
      eventData.user = {
        mobile,
        email,
        user_id: userProfile?._id
      }
    }
    sendGTMEvent(eventData)

    if (product?.productType === "contactLens") {
      context.formRef.current?.requestSubmit();
      return;
    }

    addToCart(product?._id, count, size).finally(() => {
      queryClient.invalidateQueries({ queryKey: ["cart"] });
    });
  };

  return (
    subscriptionType !== "subscription" ?
      <>
        <button
          className="add-to-cart"
          disabled={varient?.stock === 0}
          onClick={handleClick}>
          {showSvg === true ? (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="21"
              height="22"
              viewBox="0 0 21 22"
              fill="none">
              <path
                d="M13.7813 9.68652V5.74902C13.7813 4.87878 13.4356 4.04418 12.8203 3.42883C12.2049 2.81348 11.3703 2.46777 10.5001 2.46777C9.62984 2.46777 8.79524 2.81348 8.17988 3.42883C7.56453 4.04418 7.21883 4.87878 7.21883 5.74902V9.68652M17.1553 7.94265L18.2605 18.4426C18.3217 19.0245 17.8667 19.5303 17.2813 19.5303H3.71883C3.58076 19.5304 3.44421 19.5015 3.31803 19.4455C3.19186 19.3894 3.0789 19.3074 2.98647 19.2048C2.89405 19.1023 2.82424 18.9814 2.78158 18.8501C2.73891 18.7188 2.72435 18.58 2.73883 18.4426L3.84483 7.94265C3.87034 7.70076 3.9845 7.47689 4.1653 7.31419C4.34609 7.15148 4.58073 7.06148 4.82395 7.06152H16.1762C16.6802 7.06152 17.1028 7.44215 17.1553 7.94265ZM7.54695 9.68652C7.54695 9.77355 7.51238 9.85701 7.45085 9.91854C7.38931 9.98008 7.30585 10.0146 7.21883 10.0146C7.1318 10.0146 7.04834 9.98008 6.98681 9.91854C6.92527 9.85701 6.8907 9.77355 6.8907 9.68652C6.8907 9.5995 6.92527 9.51604 6.98681 9.4545C7.04834 9.39297 7.1318 9.3584 7.21883 9.3584C7.30585 9.3584 7.38931 9.39297 7.45085 9.4545C7.51238 9.51604 7.54695 9.5995 7.54695 9.68652ZM14.1095 9.68652C14.1095 9.77355 14.0749 9.85701 14.0133 9.91854C13.9518 9.98008 13.8684 10.0146 13.7813 10.0146C13.6943 10.0146 13.6108 9.98008 13.5493 9.91854C13.4878 9.85701 13.4532 9.77355 13.4532 9.68652C13.4532 9.5995 13.4878 9.51604 13.5493 9.4545C13.6108 9.39297 13.6943 9.3584 13.7813 9.3584C13.8684 9.3584 13.9518 9.39297 14.0133 9.4545C14.0749 9.51604 14.1095 9.5995 14.1095 9.68652Z"
                stroke="white"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          ) : (
            <></>
          )}
          {varient?.stock === 0 ?
            (translation?.outOfStock || "Out of Stock")
            : (translation?.addToCart || "Add To Cart")}
        </button>
      </> :
      <div></div>
  );
}

export default AddToCart;
