.search {
  &_wrapper {
    display: flex;
    margin-top: 2.5rem;
    padding-bottom: 5rem;

    @media (max-width: 991.98px) {
      flex-direction: column;
    }

    @media (max-width: 575.98px) {
      margin-top: 4.6rem;
    }
  }

  &_left {
    width: 50%;

    @media (max-width: 991.98px) {
      width: 100%;
    }

    h5 {
      color: #1d364d;
      font-size: 1.5rem;
      font-weight: 700;
    }
  }

  &_right {
    width: 50%;

    @media (max-width: 991.98px) {
      width: 100%;
      margin-top: 3rem;
    }

    h5 {
      color: #000;
      font-size: 1.3rem;
      font-weight: 500;
    }
  }

  &_tags {
    display: flex;
    column-gap: 1.9rem;
    margin-top: 1.2rem;
    row-gap: 2.2rem;
    flex-wrap: wrap;

    span {
      color: #374151;
      font-size: 1.4rem;
      font-weight: 600;
      border-radius: 2.7rem;
      background: #f3f3f3;
      padding: 1rem 2.9rem;
    }
  }

  &_row {
    margin-top: 2.5rem;

    @media (max-width: 575.98px) {
      margin-top: 3rem;
    }
  }

  &_suggested {
    display: flex;
    column-gap: 2.5rem;
    margin-top: 2.5rem;

    @media (max-width: 575.98px) {
      margin-top: 1.5rem;
      column-gap: 1.6rem;
    }

    img {
      border-radius: 1rem;
    }

    h6 {
      margin-top: 1.5rem;
      color: #000;
      font-size: 1.3rem;
      font-weight: 400;
      text-align: center;

      @media (max-width: 575.98px) {
        margin-top: 0.6rem;
      }
    }
  }

  &_products {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.9rem;
    margin-top: 1.9rem;

    @media (max-width: 575.98px) {
      grid-template-columns: repeat(2, 1fr);
      gap: 1.2rem;
      margin-top: 1.5rem;
    }

    .product-card {
      background: #f3f3f3;
      padding: 0.6rem 0 0.9rem 0;

      label {
        font-size: 1.2rem;
        padding: 0rem 0.8rem;
      }

      img {
        aspect-ratio: auto;
        margin: 0;
        height: 7rem;
        width: 15rem;
        border-radius: 1.5rem;
      }

      svg {
        width: 2rem;
        height: 2rem;
      }

      .brand-name {
        font-size: 1.2rem;
        font-weight: 400;
        line-height: 1.5rem;
      }

      .product-modal {
        font-size: 1.3rem;
        font-weight: 400;
        line-height: 1.6rem;
        margin-top: 0;
        padding: 10px 20px 0;
      }

      .product-price-detail {
        font-size: 1.2rem;
        line-height: 1.5rem;
        margin: 0 1rem;
        margin-top: 1rem;
        flex-wrap: wrap;

      }
    }
  }
}