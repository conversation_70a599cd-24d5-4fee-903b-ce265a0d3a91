'use client';
import { useState } from 'react';
import Image from 'next/image';

type Props = {
    src: string;
    alt: string;
    width?: number;
    height?: number;
    fallbackSrc: string;
    quality?: number;
    fill?: boolean;
}

const FallbackImage = ({ width, quality, height, src, alt, fallbackSrc, fill, ...props }: Props) => {
    const [imgSrc, setImgSrc] = useState(src);

    const handleError = () => {
        setImgSrc(fallbackSrc);
    };

    return <Image fill={fill} quality={quality || 100} width={width} height={height} src={imgSrc} alt={alt} {...props} onError={handleError} />;
}

export default FallbackImage