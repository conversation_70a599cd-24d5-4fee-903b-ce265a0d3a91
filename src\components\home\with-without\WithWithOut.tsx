import "./withwithout.scss";
import { WithWithoutSlider } from "./WithWithoutSlider";

export default async function WithWithOut({ id, locale, item, data }: any) {
  try {
    const resData = data.result;
    return (
      <section className="image-comparison" id={id}>
        <div className="container-fluid">
          <div className="image-comparison_title">
            <h2>{item?.title}</h2>
          </div>
          <div>
            <WithWithoutSlider data={resData} />
          </div>
        </div>
      </section>
    );
  } catch (e) {
    return <></>;
  }
}
