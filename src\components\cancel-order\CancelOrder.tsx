import { useState } from "react";
import "./cancel-order.scss";
import GenericModal from "../generic-modal/GenericModal";
import api from "@/config/axios.interceptor";
import { endpoints } from "@/config/apiEndpoints";
import { toast } from "sonner";
import { useQueryClient } from "@tanstack/react-query";

function CancelOrder({ order }: any) {
  const [confirmModal, setConfirmModal] = useState(false);
  const queryClient = useQueryClient();

  const handleCancel = () => {
    api
      .put(endpoints.cancelOrder, {
        order: order,
      })
      .then((res) => {
        if (res.status === 200) {
          queryClient.invalidateQueries({ queryKey: ["orders"] });
          setConfirmModal(false);
          toast.success(res.data.message);
        }
      })
      .catch((err) => {
        toast.error(err.response.data.message);
      });
  };
  return (
    <>
      <button
        onClick={() => setConfirmModal(true)}
        type="button"
        data-testid="cancel-order-button"
        id="cancel-order"
        className="cancel-order d-print-none">
        Cancel Order
      </button>

      <GenericModal
        callBack={handleCancel}
        show={confirmModal}
        primary="Yes"
        secondary="No"
        closeFn={() => setConfirmModal(false)}
        title="Are you sure you want to cancel this order?"
      />
    </>
  );
}

export default CancelOrder;
