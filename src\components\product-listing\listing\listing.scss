// PRODUCT ITEMS STYELE
.list-items {
  width: 70%;

  @media (max-width: 991.98px) {
    width: 100%;
  }

  &.filter-hide {
    width: 100%;
  }

  &.filter-show {
    &.threeCols {
      .list-items_product {
        grid-template-columns: repeat(3, 1fr);
        column-gap: 1.3rem;
        row-gap: 1.2rem;

        .product-card {
          img {
            margin: 0;
            height: 11.5rem;
            aspect-ratio: 1;
          }
        }
      }
    }
  }

  &.filter-show {
    &.fourCols {
      .list-items_product {
        grid-template-columns: repeat(4, 1fr);
        column-gap: 0.92rem;
        row-gap: 0.8rem;

        .product-card {
          padding: 0.5rem 0 0 0;

          img {
            height: 8rem;
            margin: 0;
          }

          button svg {
            width: 2rem;
            height: 2rem;
          }

          label {
            padding: 0.1rem 1rem;
          }

          .brand-name {
            font-size: 1.3rem;
            line-height: 1.6rem;

            @media (max-width: 1199.98px) {
              font-size: 1.1rem;
              line-height: 1.5rem;
            }
          }

          .product-modal {
            font-size: 1.5rem;
            line-height: 1.8rem;
            padding: 0px 2px;
            min-height: 38px;

            @media (max-width: 1199.98px) {
              font-size: 1.3rem;
              line-height: 1.7rem;
            }
          }

          .product-price-detail {
            font-size: 1.3rem;
            column-gap: 0.5rem;

            @media (max-width: 1199.98px) {
              font-size: 1.1rem;
              line-height: 1.5rem;
            }
          }
        }
      }
    }
  }

  &.filter-hide {
    &.twoCols {
      .product-card {
        @media (max-width: 575.98px) {
          padding: 0.1rem 0 0rem 0;

          img {
            height: 7.8rem;
            margin: 0;
            padding: 0 2rem;
          }
          label {
            padding: 0rem 0.4rem;
            font-size: 1.2rem;
            margin-top: 1rem;
          }
          .brand-name {
            font-size: 1.2rem;
            line-height: 1.5rem;
          }

          .product-modal {
            font-size: 1.3rem;
            line-height: 1.6rem;
          }

          .product-price-detail {
            font-size: 1.2rem;
            line-height: 1.5rem;
            column-gap: 0.5rem;
          }
        }
      }

      .list-items_product {
        grid-template-columns: repeat(2, 1fr);

        @media (max-width: 575.98px) {
          gap: 1.2rem;
        }
      }
    }
  }

  &.filter-hide {
    &.threeCols {
      .list-items_product {
        grid-template-columns: repeat(3, 1fr);
      }
    }
  }

  &.filter-hide {
    &.oneCol {
      .list-items_product {
        grid-template-columns: 1fr;
      }
    }
  }

  &.filter-hide {
    &.fourCols {
      .list-items_product {
        grid-template-columns: repeat(4, 1fr);
        column-gap: 1.4rem;
        row-gap: 1.6rem;

        .product-card {
          img {
            margin: 0;
          }
        }
      }
    }
  }

  &_product {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    margin-top: 3rem;

    @media (max-width: 767.98px) {
      margin-top: 2rem;
    }
  }
}
