import { useLocaleContext } from "@/contexts/LocaleProvider";
import { useRouter, useSearchParams } from "next/navigation";
import { useState, useEffect, useRef } from "react";
import RangeSlider from "react-range-slider-input";
import "react-range-slider-input/dist/style.css";

export default function RangeFilter({ priceRange }: any) {
  const params = useSearchParams();
  const current = new URLSearchParams(Array.from(params.entries()));
  const {currencyCode} = useLocaleContext()
  const router = useRouter();
  const sliderRef = useRef<HTMLDivElement>(null);
  
  const minRange = priceRange && priceRange.minPrice? priceRange.minPrice : 0;
  const maxRange = priceRange && priceRange.maxPrice? priceRange.maxPrice : 5000;
  
  const [range, setRange] = useState<number[]>([
    parseInt(params.get("priceFrom") ?? `${minRange}`, 10),
    parseInt(params.get("priceTo") ?? `${maxRange}`, 10),
  ]);

  useEffect(() => {
    const minPrice = parseInt(params.get("priceFrom") ?? `${minRange}`, 10);
    const maxPrice = parseInt(params.get("priceTo") ?? `${maxRange}`, 10);
    setRange([minPrice, maxPrice]);
  }, [params]);

  const priceFilter = (newRange: number[]) => {
    current.set("priceFrom", newRange[0]?.toString());
    current.set("priceTo", newRange[1]?.toString());
    window.history.pushState(null, '', `?${current.toString()}`)
  };

  return (
    <>
      <div ref={sliderRef}>
        <RangeSlider
          onThumbDragEnd={() => {
            priceFilter(range);
          }}
          onInput={(values: number[]) => setRange([Math.ceil(values[0]), Math.ceil(values[1])])}
          value={range}
          min={minRange}
          max={maxRange}
          rangeSlideDisabled={true}
        />
      </div>
      <div className="range-slider__flex">
        <span>{currencyCode + " "} {range[0]}</span>
        <span>{currencyCode + " "} {range[1]}</span>
      </div>
    </>
  );
}
