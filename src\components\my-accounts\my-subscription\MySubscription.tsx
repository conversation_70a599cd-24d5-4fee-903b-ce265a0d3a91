"use client";

import "./my-subscription.scss";
import Image from "next/image";
import api from "@/config/axios.interceptor";
import { endpoints } from "@/config/apiEndpoints";
import { subscription } from "@/lib/subscription";
import { HistoryContext } from "@/contexts/HistoryProvider";
import { useContext, useEffect, useState } from "react";
import { getSubscription } from "@/lib/methods/user";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { TranslationContext } from "@/contexts/Translation";
import { useLocaleContext } from "@/contexts/LocaleProvider";

const options = [
  { value: 3, label: "3 Weeks" },
  { value: 4, label: "4 Weeks" },
  { value: 5, label: "5 Weeks" },
];

function MySubscription() {
  const { data: subscription, isLoading } = useQuery({
    queryKey: ["my-subscriptions"],
    queryFn: getSubscription,
  });

  const queryClient = useQueryClient();
  const { translation }: any = useContext(TranslationContext)
  const [deleteModal, setDeleteModal] = useState(false);
  const {currencyCode} = useLocaleContext()

  const { setPrevPage } = useContext(HistoryContext);
  useEffect(() => {
    setPrevPage({
      title: "My Subscriptions",
      url: "/my-accounts/my-subscriptions",
    });
  }, []);

  const handleUnsubscribe = (subscriptionId: any) => {
    api
      .post(`${endpoints.unsubscribe}/${subscriptionId}`)
      .then((res) => {
        toast.success(res.data?.result);
        return;
      })
      .catch((err) => {
        toast.error(err.response.data.message);
      });
  };

  return (
    <section className="my-subscription">
      <h2>{translation?.myAccount?.mySubscription ?? "My Subcription"}</h2>
      <div className="my-subscription_grids">
        {subscription?.map((items: any) => (
          <div className="my-subscription_items" key={items?._id}>
            <Image
              quality={100}
              priority
              src={items?.image ?? ""}
              width={383}
              height={118}
              alt="subscription image"
            />
            <div style={{ padding: "0rem 1.5rem" }}>
              <h5>
                {items?.title}
              </h5>
              <div className="product-price-detail">
                <span className="real-price">{currencyCode + " "} {items?.offer_price}</span>
                <span className="offer-price">{currencyCode + " "} {items?.price}</span>
                <span className="percentage">
                  {items?.percentage ||
                    ` ${((items?.price - items?.offer_price) / items?.price) * 100
                    }%`}
                </span>
              </div>
              <h6>
                Subscribed Date :
                <span>
                  {new Date(items.subscribed_date).toLocaleDateString()}
                </span>
              </h6>
              <label>Subscription Plan</label>

              <strong>{items?.label}</strong>

              <button
                onClick={() => handleUnsubscribe(items?._id)}
                className={items?.unsubscribed === true ? "disabled" : ""}
                disabled={items?.unsubscribed === true && true}
              >
                {items?.unsubscribed === true ? "Unsubscribed" : "Unsubscribe"}
              </button>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
}

export default MySubscription;
