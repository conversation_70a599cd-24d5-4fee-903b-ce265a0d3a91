@keyframes fade {
  0% {
    opacity: 0;
    pointer-events: none;
  }

  30% {
    opacity: 0;
    pointer-events: none;
  }

  31% {
    opacity: 1;
    pointer-events: auto;
  }
}

.product-detail {
  margin-top: 0;
  position: relative;

  @media (max-width: 767.98px) {
    margin-top: 2.4rem;
  }

  &_sticky {
    position: sticky;
    top: 85px;
    z-index: 3;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    padding: calc(1rem - 3px) 0;
    animation: auto cubic-bezier(0.075, 0.82, 0.165, 1) fade both;
    animation-timeline: view();
    animation-range: entry 20% cover 100%;

    @media (max-width: 767.98px) {
      display: none;
    }

    .buy-with-lens {
      background-color: transparent;
      color: #000;
      border: 1px solid #000;
      font-size: 1.6rem;
      font-weight: 600;
      height: 5rem;
      border-radius: 2.95rem;
      width: 17.4rem;
      line-height: 1.7rem;
      transition: all 500ms cubic-bezier(0.075, 0.82, 0.165, 1);

      &:hover {
        background-color: #000;
        color: #fff;
      }
    }

    .add-try-cart {
      background-color: transparent;
      color: #000;
      border: 1px solid #000;
      font-size: 1.6rem;
      font-weight: 600;
      height: 5rem;
      border-radius: 2.95rem;
      width: 20.4rem;
      line-height: 1.7rem;
      transition: all 500ms cubic-bezier(0.075, 0.82, 0.165, 1);
      display: flex;
      align-items: center;
      justify-content: center;
      column-gap: 1rem;

      &:hover {
        background-color: #000;
        color: #fff;

        img {
          filter: brightness(0) invert(1);
        }
      }

      img {
        width: 3rem;
        height: auto;
        transition: all 500ms cubic-bezier(0.075, 0.82, 0.165, 1);
      }
    }

    &-image {
      display: flex;
      align-items: center;
      column-gap: 1rem;

      h5 {
        font-size: 1.6rem;
        font-weight: 500;
      }

      img {
        width: 7rem;
        height: 4rem;
        object-fit: contain;
      }
    }

    &-btns {
      display: flex;
      align-items: center;
      column-gap: 3rem;

      .count {
        width: 17.2rem;
        height: 4.5rem;
        justify-content: space-between;
        border-radius: 3rem;
        padding: 0 2rem;
        align-items: center;

        span {
          font-size: 2.2rem;
          color: #111827;
          font-weight: 700;
          line-height: 1rem;
        }
      }

      .add-to-cart {
        margin-top: 0;
        width: 17.2rem;
        height: 4.5rem;
        border-radius: 3rem;
        display: inline-flex;
        justify-content: center;
        align-items: center;
        column-gap: 1rem;
        padding: 1.2rem 2.3rem;

        &:hover {
          svg {
            filter: invert(1);
          }
        }
      }
    }
  }

  &_wrapper {
    display: flex;
    column-gap: 2.1rem;
    position: relative;

    @media (max-width: 991.98px) {
      flex-direction: column;
    }
  }

  &_left {
    width: 65%;
    // height: 60rem;
    // overflow-y: auto;

    &.no-image {
      height: auto;
    }

    &::-webkit-scrollbar {
      display: none;
    }

    @media (max-width: 991.98px) {
      width: 100%;
    }

    @media (max-width: 575.98px) {
      height: 100%;

      h6 {
        color: #000;
        font-size: 1.3rem;
        font-weight: 400;
        letter-spacing: 0.026rem;
        display: flex;
        align-items: center;

        span {
          font-weight: 600;
        }

        img {
          width: 2.4rem;
          height: 2.4rem;
          margin-right: 1.2rem;
        }
      }
    }

    .gallery {
      &_slider {
        @media (max-width: 575.98px) {
          margin-top: 1.2rem;
        }

        .swiper-slide {
          &:has(img.opacity-0) {
            background-color: #fdfdfd;
            background-image: url(/images/common/logo.png);
            animation: skeleton 1s linear infinite alternate;
            background-repeat: no-repeat;
            background-position: center;
            background-size: 40%;
          }
        }

        img {
          border-radius: 2rem;
          object-fit: contain;
        }

        button {
          position: absolute;
          right: 0;
          border: none;
          border-radius: 2.7rem;
          background: #000;
          color: #fff;
          font-size: 1.2rem;
          font-weight: 600;
          width: 12.2rem;
          height: 3.7114rem;
          top: 1.7rem;
          right: 1.05rem;
          display: flex;
          align-items: center;
          justify-content: center;

          img {
            width: auto;
            height: auto;
            margin-left: 0.7rem;
          }
        }

        .swiper-pagination {
          position: unset;

          .swiper-pagination-bullet {
            width: 0.8rem;
            height: 0.8rem;
            background-color: rgba(0, 0, 0, 0.6);

            &-active {
              width: 1rem;
              height: 1rem;
              background-color: #000;
            }
          }
        }
      }
    }
  }

  &_right {
    flex: 1;
    position: sticky;
    height: 100%;
    top: 20%;

    .cashback-tag {
      margin-top: 1rem;
      padding: 1rem 1.5rem;
      border-radius: .8rem;
      background: linear-gradient(to right, #000, #66666600);

      p {
        color: white;
        text-shadow: 0px 0px 3px #000;
      }
    }

    @media (max-width: 991.98px) {
      width: 100%;
      margin-top: 1rem;
    }

    .block-one {
      // border-bottom: 1px solid #e4e4e4;
      padding-bottom: 2rem;
      
      a{
        display: flex;
        height: fit-content;
      }

      .label{
        background-color: #000;
        padding: .5rem 1rem;
        border-radius: 1rem;
        color: white;
        width: fit-content;
      }

      .rating {
        padding: 0.7rem 1rem;
        color: #d48d3b;
        font-size: 1.4rem;
        font-weight: 600;
        border-radius: 2.7rem;
        background: #fbf3ea;
        display: flex;
        column-gap: 0.7rem;
        align-items: center;

        svg {
          margin-top: -2px;
        }
      }

      &_span {
        border-radius: 2.7rem;
        background: #fbf3ea;
        padding: 0.7rem 1rem;
        color: #d48d3b;
        font-size: 1.4rem;
        font-weight: 600;
      }

      h3 {
        color: #000;
        font-size: 2.3rem;
        font-style: normal;
        font-weight: 600;
        line-height: 3.1rem;

        @media (max-width: 575.98px) {
          font-size: 2.2rem;
          line-height: 2.7rem;
        }
      }

      h5 {
        color: #374151;
        font-size: 1.6rem;
        font-weight: 400;
        line-height: 2rem;
        margin-top: 0.5rem;

        @media (max-width: 575.98px) {
          margin-top: 2.5rem;
        }

        span {
          font-weight: 600;
        }
      }

      .fav {
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #ededed;
        color: #000;
        font-size: 1.6rem;
        font-weight: 600;
        column-gap: 0.7rem;
        width: 3.4rem;
        height: 3.4rem;
        border-radius: 2rem;
        box-sizing: content-box;
        padding: 0;

        // svg {
        //   width: 2rem;
        //   height: 2rem;
        // }
      }

      .share {
        display: flex;
        align-items: center;
        justify-content: center;
        background: #ededed;
        border: none;
        border-radius: 50%;
        width: 3.4rem;
        height: 3.4rem;
        padding: 0 !important;

        svg {
          width: 2rem;
          height: 2rem;
        }
      }

      .compare {
        color: #374151;
        font-size: 1.4rem;
        font-weight: 400;
        border-radius: 2.7rem;
        background: #f3f3f3;
        display: flex;
        justify-content: center;
        align-items: center;
        column-gap: 0.7rem;
        height: 3.2rem;
        max-width: 13.2rem;
        width: 100%;
        align-items: center;
        border: none;


        &:hover {
          background-color: #f8f8f8;
          border: 1px solid #e3e3e3;
        }
      }
    }

    .block-two {
      // display: flex;
      // justify-content: space-between;
      // padding-bottom: 1rem;
      // border-bottom: 1px solid #e4e4e4;

      h2 {
        color: #000;
        font-size: 2.5rem;
        font-weight: 700;
        line-height: 3.1rem;

        @media (max-width: 575.98px) {
          font-size: 2.4rem;
        }
      }

      .savings {
        font-size: 1.5rem;
        font-weight: 600;
        line-height: 1.8rem;
        color: #000000;
        margin-top: 0.8rem;

        h5 {
          margin-left: 0;
        }

        span {
          font-weight: 600;
        }

        .off {
          color: red;
          font-size: 1.5rem;
          font-weight: 600;
          line-height: 1.8rem;
          margin-left: 1rem;
          display: inline-block;
        }
      }

      .vat {
        color: rgba(114, 108, 108, 1);
        font-size: 1.4rem;
        font-weight: 400;
        line-height: 1.7rem;
      }

      h5 {
        color: rgba(0, 0, 0, 0.5);
        font-size: 1.5rem;
        font-weight: 400;
        text-decoration: line-through;
        line-height: 1.8rem;
      }

      .rating {
        padding: 0.7rem 1rem;
        color: #d48d3b;
        font-size: 1.4rem;
        font-weight: 600;
        border-radius: 2.7rem;
        background: #fbf3ea;
        display: flex;
        column-gap: 0.7rem;
        align-items: center;
      }

      .review {
        border-radius: 2.7rem;
        background: #f3f3f3;
        color: #374151;
        font-size: 1.4rem;
        font-weight: 600;
        padding: 0.7rem 1rem;
        display: flex;
        align-items: center;
        column-gap: 0.7rem;
        justify-content: center;
        border: none;

        &:hover {
          background-color: #f8f8f8;
          outline: 1px solid #e3e3e3;
        }
      }

      h6 {
        color: #374151;
        font-size: 1.4rem;
        font-weight: 400;
        margin-top: 1.4rem;

        @media (max-width: 575.98px) {
          font-size: 1.3rem;
          line-height: 1.6rem;
        }

        span {
          color: #000;
          font-weight: 600;
        }
      }
    }

    .block-three {
      margin-top: 2rem;
      // padding-bottom: 1.3rem;
      // border-bottom: 1px solid #e4e4e4;

      span {
        color: rgba(55, 65, 81, 0.5);
        font-size: 1.6rem;
        font-weight: 400;
      }

      &_colors {
        margin-top: 0.5rem;

        .ul {
          // display: flex;
          // column-gap: 1.2rem;
          // align-items: center;

          .li {
            width: 9.4rem;
            height: 9.4rem;
            border-radius: 1rem;
            cursor: pointer;
            transition: all 0.2s ease-in-out;
            position: relative;
            outline: 1px dashed rgb(218, 218, 218);

            a{
              display: block;
              position: absolute;
              inset: 0;
            }

            &.isActive {
              width: 9.2rem;
              height: 9.2rem;
              transition: all 0.2s ease-in-out;
              outline: 2px solid rgba(255, 135, 104, 1);
              border: 2px solid #ffffff;

              // &::after {
              //   content: "";
              //   background-image: url(../../../../../public/images/product-detail/tick.svg);
              //   background-repeat: no-repeat;
              //   background-position: center;
              //   height: 10px;
              //   width: 12px;
              //   position: absolute;
              //   top: 50%;
              //   left: 50%;
              //   transform: translate(-50%, -50%);
              // }

              &::before {
                content: "";
                position: absolute;
                width: 36px;
                height: 36px;

                border-radius: 50%;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
              }
            }

            &.isActiveImg {
              border: 2px solid #ffffff;
              outline: 2px solid rgba(255, 135, 104, 1);
              img{
                border-radius: 50%;
              }
            }
          }
        }
      }
    }

    .block-four {
      margin-top: 3.1rem;

      .count {
        width: 16.9rem;
        height: 5.6rem;
        border-radius: 2.95rem;
        justify-content: space-between;
        padding: 0 2.6rem;
        background-color: #f3f3f3;

        button {
          padding-bottom: 7px;
        }

        span {
          color: #000;
          font-size: 2.2rem;
          font-style: normal;
          font-weight: 700;
        }
      }

      .add-to-cart {
        margin-top: 0;
        display: flex;
        align-items: center;
        height: 5.6rem;
        width: 24.3rem;
        column-gap: 1rem;
        justify-content: center;

        &:hover {
          svg {
            filter: invert(1);
          }

          // background-color: #000;
          // color: #fff;
        }
      }

      &_btns {
        display: flex;
        column-gap: 3rem;

        .add-to-cart {
          background-color: transparent;
          color: #000;
          border: 1px solid #000;
          font-size: 1.6rem;
          font-weight: 600;
          height: 5rem;
          border-radius: 2.95rem;
          line-height: 1.7rem;
          width: max-content;

          &:hover {
            background-color: #000;
            color: #fff;
          }

          @media (max-width: 567.98px) {
            width: 100%;
          }

          svg {
            filter: invert(1);
          }

          &:hover {
            svg {
              filter: none;
            }
          }
        }

        @media (max-width: 991.98px) {
          display: flex;
          align-items: center;
          column-gap: 2rem;
        }

        @media (max-width: 575.98px) {
          flex-direction: column;
          row-gap: 2rem;
        }

        button {
          background-color: transparent;
          color: #000;
          border: 1px solid #000;
          font-size: 1.6rem;
          font-weight: 600;
          height: 5.6rem;
          border-radius: 2.95rem;
          transition: all 500ms cubic-bezier(0.075, 0.82, 0.165, 1);

          &:hover {
            background-color: #000;
            color: #fff;

            img {
              filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(7456%) hue-rotate(347deg) brightness(103%) contrast(101%);
            }
          }

          &.add-try-cart {
            background-color: transparent;
            color: #000;
            border: 1px solid #000;
            font-size: 1.6rem;
            font-weight: 600;
            height: 5rem;
            border-radius: 2.95rem;
            width: 20.4rem;
            line-height: 1.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
            column-gap: 1rem;

            @media (max-width: 991.98px) {
              margin-top: 0;
            }

            @media (max-width: 567.98px) {
              width: 100%;
            }

            img {
              width: 3rem;
              height: auto;
            }

            &:hover {
              background-color: #000000;
              color: #ffffff;

              img {
                filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(7456%) hue-rotate(347deg) brightness(103%) contrast(101%);
              }
            }
          }
        }

        .buy-with-lens {
          background-color: #000000;
          color: #ffffff;
          border: 1px solid #000;
          font-size: 1.6rem;
          font-weight: 600;
          height: 5rem;
          border-radius: 2.95rem;
          width: 17.4rem;
          line-height: 1.7rem;

          &:hover {
            background-color: transparent;
            color: #000000;
          }

          @media (max-width: 567.98px) {
            width: 100%;
          }
        }


      }
    }

    .block-five {
      .subscriptionLink {
        font-size: 1.3rem;
        color: #726c6c;
        font-size: 1.4rem;
        font-weight: 400;
        -webkit-text-decoration-line: underline;
        text-decoration-line: underline;
        line-height: 1rem;
        margin-top: 1.5rem;
        display: inline-block;
      }

      &_tamara {
        display: flex;
        column-gap: 1.7rem;
        border-radius: 1.4rem;
        border: 1px solid #e4e4e4;
        padding: 1.3rem 1.8rem 1.3rem 1.5rem;
        margin-top: 2rem;

        @media (max-width: 575.98px) {
          margin-top: 3rem;
        }

        img {
          width: 7.4rem;
          height: 3rem;
        }

        h6 {
          color: #000;
          font-size: 1.4rem;
          font-weight: 400;
          line-height: 1rem;

          @media (max-width: 575.98px) {
            font-size: 1.3rem;
          }
        }

        a {
          color: #726c6c;
          font-size: 1.4rem;
          font-weight: 400;
          text-decoration-line: underline;
          line-height: 1rem;

          @media (max-width: 575.98px) {
            font-size: 1.3rem;
          }
        }
      }

      &_border {
        border: 1px solid #e4e4e4;
        border-radius: 1.4rem;
        padding: 1.7rem;
        margin-top: 1.7rem;

        @media (max-width: 575.98px) {
          margin-top: 2rem;
        }
      }

      &_free-delivery {
        display: flex;
        column-gap: 1.4rem;
        border-bottom: 1px solid #e4e4e4;
        margin-bottom: 2.1rem;
        padding-bottom: 2.1rem;
        width: 100%;

        img {
          width: 2.4rem;
          height: 2.4rem;
        }

        h5 {
          color: #1d364d;
          font-size: 1.7rem;
          font-weight: 700;
          line-height: 2.1rem;

          @media (max-width: 575.98px) {
            font-size: 1.4rem;
            line-height: 1.7rem;
          }
        }

        h6 {
          color: #726c6c;
          font-size: 1.4rem;
          font-weight: 400;
          line-height: 1.7rem;
          text-decoration-line: underline;
          margin-top: 0.7rem;

          @media (max-width: 575.98px) {
            font-size: 1.3rem;
            margin-top: 0;
          }
        }

        .postal-code {
          margin-top: 2.5rem;
          position: relative;

          @media (max-width: 575.98px) {
            display: none;
          }

          label {
            color: #808080;
            font-size: 1.3rem;
            font-weight: 400;
            line-height: 2rem;
          }

          input {
            border: none;
            border-bottom: 1px solid #e2e4e5;
            width: 100%;
            padding: 0.8rem 0;
            color: #242426;
            font-size: 1.8rem;
            font-weight: 400;
            line-height: 2.8rem;

            &::placeholder {
              color: #dbdbdb;
              font-size: 1.8rem;
              font-weight: 400;
              line-height: 2.8rem;
            }

            &:focus-within {
              outline: none;
              border: none;
              border-bottom: 1px solid #e2e4e5;
            }
          }

          input[type="number"]::-webkit-inner-spin-button,
          input[type="number"]::-webkit-outer-spin-button {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
          }

          button {
            color: #000;
            font-size: 1.7rem;
            font-weight: 600;
            text-decoration-line: underline;
            background: none;
            border: none;
            position: absolute;
            right: 0;
            padding: 0;
            top: 28px;
          }

          span {
            margin-top: 1rem;
            display: block;
            color: #1d364d;
            font-size: 1.4rem;
            font-weight: 400;
            text-decoration-line: underline;
          }
        }
      }

      &_return-delivery {
        display: flex;
        column-gap: 1.4rem;

        img {
          width: 2.4rem;
          height: 2.4rem;
        }

        h5 {
          color: #1d364d;
          font-size: 1.7rem;
          font-weight: 700;
          line-height: 2.1rem;

          @media (max-width: 575.98px) {
            font-size: 1.3rem;
          }
        }

        span {
          color: #726c6c;
          font-size: 1.4rem;
          font-weight: 400;
          margin-top: 0.7rem;
          display: block;

          @media (max-width: 575.98px) {
            font-size: 1.3rem;
            margin-top: 0;
          }

          a {
            color: #726c6c;
          }
        }
      }
    }

    .block-six {
      margin-top: 2rem;

      @media (max-width: 575.98px) {
        margin-top: 3rem;
      }

      h4 {
        color: #000;
        font-size: 2.4rem;
        font-weight: 700;
        margin-bottom: 2rem;

        @media (max-width: 575.98px) {
          margin-bottom: 0;
          font-size: 2rem;
        }
      }

      &_flex {
        display: flex;
        column-gap: 1.9rem;
        margin-top: 3rem;

        &:not(:last-child) {
          @media (max-width: 991.98px) {
            border-bottom: 1px solid #bebcbd;
            padding-bottom: 2.5rem;
            margin-top: 2rem;
          }
        }

        @media (max-width: 575.98px) {
          margin-top: 1.4rem;
        }

        img {
          width: 14.7rem;
          height: 9.6rem;
          border-radius: 1.2rem;
          object-fit: contain;

          @media (max-width: 575.98px) {
            width: 10.0324rem;
            height: 12.5rem;
            object-fit: contain;
          }
        }
      }

      &_info {
        display: flex;
        flex-direction: column;
        column-gap: 1rem;
        width: 100%;

        @media (max-width: 575.98px) {
          flex-direction: column;
        }

        p {
          color: #000;
          font-size: 1.6rem;
          font-weight: 600;
          letter-spacing: 0.032rem;
          margin-bottom: 1.1rem;
          line-height: 2rem;

          @media (max-width: 575.98px) {
            margin-bottom: 0.4rem;
          }
        }

        ul {
          li {
            color: #807d7e;
            font-size: 1.4rem;
            font-weight: 400;
            line-height: 1.7rem;
            margin-bottom: 0.4rem;

            &:last-child {
              color: #000;
              font-size: 1.8rem;
              font-weight: 600;
              line-height: 2.2rem;
              margin-top: 0.3rem;

              @media (max-width: 575.98px) {
                font-size: 1.6rem;
                line-height: 2rem;
              }
            }
          }
        }
      }

      &_btns {
        gap: 1rem;
        display: flex;
        flex-direction: column;
        align-items: flex-start;

        @media (max-width: 575.98px) {
          display: flex;
          flex-direction: row;
          column-gap: 1.1rem;
        }

        @media (max-width: 372px) {
          flex-direction: column;
        }

        .count {
          width: 12.1rem;
          height: 3.6rem;
          justify-content: space-between;
          padding: 0.8rem 1.9rem;

          @media (max-width: 575.98px) {
            width: 10rem;
          }

          button {
            line-height: 1.5rem;
          }

          span {
            line-height: 1.5rem;
          }
        }

        .add-to-cart {
          color: #fff;
          font-size: 1.2rem;
          font-weight: 500;
          display: flex;
          column-gap: 0.69rem;
          padding: 0.8rem 1.9rem;
          margin-top: 0;
          gap: 1rem;

          @media (max-width: 575.98px) {
            margin-top: 0;
          }

          svg {
            width: 1.35rem;
            height: 1.35rem;

            @media (max-width: 575.98px) {
              display: none;
            }
          }

          &:hover {
            background-color: transparent;
            color: #000;

            svg {
              filter: invert(1);
            }
          }
        }
      }
    }
  }
}

.product-desc {
  margin-top: 6rem;

  @media (max-width: 575.98px) {
    margin-top: 2rem;
  }

  h4 {
    color: #000;
    font-size: 2.4rem;
    font-weight: 700;

    @media (max-width: 575.98px) {
      font-size: 2rem;
    }
  }

  p {
    margin-top: 2rem;

    @media (max-width: 575.98px) {
      margin-top: 1rem;
      line-height: 2rem;
      font-size: 1.3rem;
    }
  }

  video {
    width: 100%;
    height: 480px;
    object-fit: cover;
    overflow: hidden;
  }

  img,
  video {
    margin-top: 3.4rem;
    border-radius: 2rem;

    @media (max-width: 575.98px) {
      margin-top: 1.7rem;
      height: 20.4rem;
    }
  }
}

.tech-info {
  margin-top: 3.1rem;

  @media (max-width: 575.98px) {
    margin-top: 2rem;
  }

  &-flex {
    @media (max-width: 768px) {
      max-width: unset;
    }

    max-width: 60rem;
    width: 100%;
    position: relative;
    display: grid;
    gap: 1rem;
    grid-template-columns: 1fr auto 1fr;

    // &::after {
    //   content: ":";
    //   position: absolute;
    //   font-weight: 600;
    //   color: #000000;
    //   left: 12rem;

    // }
  }

  h5 {
    color: #000;
    font-size: 2.1rem;
    font-weight: 600;
  }

  ul {
    margin-top: 2.3rem;

    li {
      width: 100%;
      display: flex;
      padding: 0.5rem 0;
      padding-left: 3.2rem;

      &:not(:last-child) {
        margin-bottom: 1.4rem;
      }

      &:nth-child(even) {
        background: #f2f4f9;
      }

      &:nth-child(odd) {
        padding: 0;
        padding-left: 3.2rem;
      }

      span {
        color: #000;
        font-size: 1.4rem;
        line-height: 2.72rem;
        display: inline-block;

        @media (min-width:768px) {
          white-space: nowrap;
        }

        &:first-child {
          font-weight: 600;
        }

        &:last-child {
          font-weight: 400;
        }
      }
    }
  }
}

.product-images {
  display: flex;
  justify-content: space-between;
  margin-top: 4.3rem;
  flex-wrap: wrap;
  gap: 2rem;

  @media (max-width: 575.98px) {
    margin-top: 2rem;
  }

  @media (max-width: 767.98px) {
    flex-direction: column;
    row-gap: 2rem;
  }

  img {
    width: calc(50% - 5.9rem / 2);
    border-radius: 2rem;
    mix-blend-mode: darken;
    object-fit: contain;

    @media (max-width: 767.98px) {
      width: 100%;
    }
  }
}

.product-detail-slider {
  background: #f2f4f9;
  margin-top: 5.2rem;
  padding-top: 4.9rem;
  padding-bottom: 9.1rem;

  @media (max-width: 575.98px) {
    margin-top: 2.2rem;
    padding-top: 2.4rem;
    padding-bottom: 2.8rem;
  }
}

@keyframes fade-in {
  30% {
    opacity: 0%;
    height: 0;
    scale: 0;
  }

  31% {
    opacity: 100%;
    height: auto;
    scale: 1;
  }
}