.address-book-popup {
  &.modal {
    .modal-dialog {
      max-width: 123.9rem;
      width: 100%;
      margin: 0 auto;
    }

    .modal-header {
      padding: 0;
      border: none;

      .btn-close {
        background-image: url(../../../public/images/common/close.png);
        width: 4.8rem;
        height: 4.8rem;
        background-position: center;
        background-repeat: no-repeat;
        background-size: contain;
        opacity: 1;
        padding: 0;

        @media (max-width: 575.98px) {
          width: 2.8rem;
          height: 2.8rem;
          transform: translateY(-36px);
        }

        &:focus {
          box-shadow: none;
          outline: none;
          border: none;
        }
      }

      h2 {
        line-height: 4rem;

        @media (max-width: 575.98px) {
          line-height: 3rem;
          text-align: center;
          width: 100%;
        }
      }
    }

    .modal-content {
      border: none;
      border-radius: 3rem;
      padding: 3.6rem 4.6rem 5.2rem 5.3rem;
      max-width: 123.9rem;

      @media (max-width: 575.98px) {
        padding: 8.3rem 1.7rem 4.2rem 1.7rem;
        border-radius: 0;
      }
    }

    .modal-body {
      padding: 0;
    }
  }

  .contact-form {
    &_wrapper {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      margin-top: 2rem;

      @media (max-width: 575.98px) {
        flex-direction: column;
        margin-top: 5.6rem;
      }
    }

    &_inputs {
      width: calc(50% - 4.3rem);
      position: relative;

      @media (max-width: 767.98px) {
        width: calc(50% - 2rem);
      }

      @media (max-width: 575.98px) {
        width: 100%;
      }

      &:not(:last-child) {
        margin-bottom: 3.6rem;

        @media (max-width: 575.98px) {
          margin-bottom: 3rem;
        }
      }

      &:nth-of-type(1) {
        width: 100%;
      }

      label {
        margin-bottom: 0.8rem;
        color: #808080;
        font-size: 1.3rem;
        font-weight: 400;

        @media (max-width: 575.98px) {
          font-size: 1.4rem;
          font-size: 1.5rem;
        }
      }

      input {
        width: 100%;
        border: none;
        border-bottom: 1px solid #e2e4e5;
        padding-left: 1.5rem;
        padding-bottom: 0.8rem;
        position: relative;
        color: #242426;
        font-size: 1.8rem;
        font-weight: 400;

        &::placeholder {
          color: #b7b7b7;
          font-size: 1.8rem;
          font-weight: 400;
          line-height: 2.8rem;

          @media (max-width: 575.98px) {
            font-size: 1.5rem;
          }
        }

        &:focus-visible {
          border-bottom: 1px solid #e2e4e5;
          outline: none;
          box-shadow: none;
        }
      }

      input[type="number"]::-webkit-inner-spin-button,
      input[type="number"]::-webkit-outer-spin-button {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
      }

      select {
        width: 100%;
        border: none;
        border-bottom: 1px solid #e2e4e5;
        padding-left: 1.5rem;
        padding-bottom: 0.8rem;
        appearance: none;

        &::placeholder {
          color: #a3a3a3;
          font-size: 1.8rem;
          font-weight: 400;
        }

        &:focus-visible {
          border-bottom: 1px solid #e2e4e5;
          outline: none;
          box-shadow: none;
        }
      }

      &-radio {
        display: flex;
        column-gap: 2rem;

        input {
          appearance: none;
          border: none;
          cursor: pointer;
          position: absolute;
          padding: 0.7rem 1.9rem;
        }

        input[type="radio"]:checked+span {
          background: #000;
          color: #fff;
        }

        label {
          margin-bottom: 0;
          position: relative;

          span {
            height: 3.4rem;
            border-radius: 0.8rem;
            border: 1px solid #000;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 1rem;
            cursor: pointer;
          }
        }
      }

      .select {
        position: relative;

        .select-container {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          z-index: 100;

          .drop-item {
            z-index: 10;

            img {
              width: 30px;
              margin-right: 10px;
            }
          }
        }

        .css-lkh0o5-menu {
          margin-top: 29px !important;
        }

        label {
          color: #242426;
          font-size: 1.4rem;
          font-weight: 400;
          line-height: 2rem;
          padding-bottom: 0.8rem;
        }

        .countrycode {
          display: flex;
          column-gap: 1.6rem;
          align-items: baseline;

          .countrycode-icon {
            position: relative;
            max-width: 100px;

            &::after {
              content: "";
              background-image: url("../../../public/images/common/Icon.png");
              width: 2.4rem;
              height: 2.4rem;
              position: absolute;
              right: -7px;
              z-index: 50;
            }
          }
        }

        .react-select {
          width: 100%;
          max-width: 100px;
          top: 36px;
          left: 0;
          z-index: 10;
          opacity: 0;
        }

        input[type="number"]::-webkit-inner-spin-button,
        input[type="number"]::-webkit-outer-spin-button {
          -webkit-appearance: none;
          -moz-appearance: none;
          appearance: none;
        }

        input {
          width: 100%;
          color: #242426;
          font-size: 1.8rem;
          font-weight: 400;
          line-height: 2.8rem;
          border: none;
          border-bottom: 1px solid #e2e4e5;
          padding: 0 1.5rem;
          padding-bottom: 0.8rem;

          &:focus-within {
            outline: none;
            border-bottom: 1px solid #e2e4e5;
          }

          &::placeholder {
            color: #cacaca;
            font-size: 1.8rem;
            font-weight: 400;
          }
        }

      }

      .inp {
        display: none;
      }

      p {
        font-size: 1.3rem;
        font-weight: 400;
        line-height: 1.9rem;
        letter-spacing: -0.011em;
        color: rgba(0, 0, 0, 0.8);
        margin-top: 1.5rem;
        opacity: 50%;
      }

      .css-1jqq78o-placeholder {
        font-size: 1.3rem;
        font-weight: 400;
        color: #999999;
        line-height: 1.6rem;
      }

      .css-13cymwt-control {
        border: none;
        border-bottom: 1px solid rgba(0, 0, 0, 0.15) !important;
        border-radius: 0;
        width: 100%;

        .css-1fdsijx-ValueContainer {
          padding: 0 !important;
          margin: 0 !important;

          .css-qbdosj-Input {
            padding: 0;
            margin: 0;
          }
        }

        .css-1u9des2-indicatorSeparator {
          display: none;
        }

        .css-1xc3v61-indicatorContainer {
          padding-right: 0;
        }

        .css-13cymwt-control {
          background-color: red;

          &:hover {
            border: none;
            background-color: blue;
          }
        }
      }

      .css-1dimb5e-singleValue {
        color: #000000;
        font-size: 1.3rem;
        font-weight: 400;
      }

      .css-t3ipsp-control {
        border: none !important;
        border-bottom: 1px solid rgba(0, 0, 0, 0.15) !important;
        outline: none;
        box-shadow: none;
        padding: 0;
        width: 100%;

        .css-1fdsijx-ValueContainer {
          padding: 0;
          margin: 0;
        }

        .css-1xc3v61-indicatorContainer {
          padding-right: 0;
        }
      }

      .css-1nmdiq5-menu {
        color: #000000;
        font-size: 1.3rem;
        font-weight: 400;
        z-index: 10;

        div {
          &>div {
            transition: .2s;

            &[aria-selected="true"] {
              background-color: black;
              color: white;
            }

            &[aria-selected="false"] {
              background-color: white;
              color: black;
            }

            &:hover {
              background-color: black;
              color: white;
            }
          }
        }

      }

      .css-1u9des2-indicatorSeparator {
        display: none;
      }

    }

    &_btns {
      width: 100%;
      display: flex;
      column-gap: 3.1rem;
      margin-top: 7.5rem;

      @media (max-width: 575.98px) {
        margin-top: 5.6rem;
        column-gap: 1.6rem;
      }

      .button {
        color: #fff;
        text-align: center;
        font-size: 1.5rem;
        font-weight: 500;
        background-color: #000;
        border-radius: 6rem;
        width: 13.8rem;
        height: 5.6rem;
        border: 1px solid #000;

        @media (max-width: 575.98px) {
          height: 4.5rem;
          width: 15.9rem;
        }

        &.cancel {
          color: #000;
          background-color: transparent;
        }
      }
    }

    &_checkbox {
      margin-top: 4.1rem;



      label {
        display: flex;
        column-gap: 1rem;
        color: #000;
        font-size: 1.4rem;
        font-weight: 300;
      }

      input {
        border-radius: 0.4rem;
        border: 1px solid #adadad;
        background: #fff;
        width: 2rem;
        height: 2rem;
      }
    }
  }
}


.app.rtl {
  .address-book-popup.modal .modal-content {
    padding: 3.6rem 5.3rem 5.2rem 4.6rem;

    @media (max-width: 575.98px) {
      padding: 8.3rem 1.7rem 4.2rem 1.7rem;
    }
  }

  .address-book-popup.modal .modal-header .btn-close {
    margin-left: 0;
  }

  .address-book-popup .contact-form_inputs input {
    padding-left: 0;
    padding-right: 1.5rem;
    text-align: right;
  }

  .address-book-popup .contact-form_inputs .select .countrycode .countrycode-icon::after {
    left: 7px;
  }
}