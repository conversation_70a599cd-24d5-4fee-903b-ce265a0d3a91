// .wrap {
//   @media (max-width: 575.98px) {
//     display: flex;
//     flex-direction: column;
//   }
// }

.insurance-info {
  margin-top: 4rem;


  h3 {
    font-size: 2rem;
    font-weight: 700;
    line-height: 2.6rem;
    color: #000;
    margin-bottom: 1.6rem;
  }

  h6 {
    color: #000;
    font-size: 2.4rem;
    line-height: 3.12rem;
    margin-top: 3.4rem;

    @media (max-width: 575.98px) {
      font-size: 2rem;
      line-height: 2.6rem;
    }
  }

  p {
    line-height: 2.72rem;
  }

  &_logos {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    align-items: center;
    margin: 3.4rem 0;
    column-gap: 5rem;
    row-gap: 3rem;

    @media (max-width: 991.98px) {
      grid-template-columns: repeat(4, 1fr);
      column-gap: 5rem;
    }

    @media (max-width: 767.98px) {
      grid-template-columns: repeat(3, 1fr);
    }

    @media (max-width: 575.98px) {
      flex-direction: column;
      row-gap: 3rem;
      margin: 7.8rem 0 4.1rem 0;
      grid-template-columns: repeat(2, 1fr);
    }

    img {
      height: auto;
      object-fit: contain;
    }
  }

  .list {
    margin-top: 1.5rem;
    margin-left: 3rem;

    @media (max-width: 575.98px) {
      margin-top: 7.5rem;
    }

    li {
      font-size: 1.5rem;
      font-weight: 400;
      line-height: 2.4rem;
      color: #0f0f0f;
      list-style: disc;
    }
  }
}

.default_style_ul {
  ul {
    padding-left: 2rem;

    li {
      list-style: disc;
    }
  }
}

.app.rtl {
  .insurance-form_inputs input {
    padding-left: 0;
    padding-right: 1.5rem;
    text-align: right;
  }

  .insurance-form_inputs .select .countrycode .countrycode-icon::after {
    left: 0;
    right: auto;
  }

  .insurance-form_inputs:nth-of-type(7)::after {
    right: auto;
    left: 0;
  }

  .insurance-form_inputs .css-13cymwt-control .css-1xc3v61-indicatorContainer {
    padding-left: 0;
  }

  .default_style_ul ul {
    padding-right: 2rem;
    padding-left: 0;
  }

}