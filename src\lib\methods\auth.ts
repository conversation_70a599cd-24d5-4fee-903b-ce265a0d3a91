import { endpoints } from "@/config/apiEndpoints";
import api from "@/config/axios.interceptor";
import { redirect } from "next/navigation";
import { toast } from "sonner";

export const logout = (translation: any) => {
    toast.loading(translation?.popup?.loggingOut ?? "Logging out...");
    fetch("/api/auth", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify({ logout: true }),
    }).then((res) => {
        toast.success(translation?.popup?.logoutSuccess ?? "Logout Successfully");
        window.location.href = "/";
    });
};

export const getUser = async () => {
    return api.get(endpoints.profile).then((res) => {
        if (res.data.errorCode === 0) {
            return res.data.result;
        } else {

            return null;
        }
    }).catch(() => {
        return null;
    });
};
