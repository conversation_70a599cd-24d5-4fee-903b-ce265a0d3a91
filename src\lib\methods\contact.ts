import { endpoints } from "@/config/apiEndpoints";
import { cookies } from "next/headers";

export const getContactBanner = async () => {
    const locale = cookies().get("Next-Locale")?.value || "sa-en";
    const [storeId, language] = locale.split("-");
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}${endpoints.contactBanner}`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            language: language || "en",
            storeid: storeId || "sa"
        },
        next: {
            tags: ['contact-banner']
        }
    });
    const data = await res.json();
    const banner = data.result;
    return banner;
}
