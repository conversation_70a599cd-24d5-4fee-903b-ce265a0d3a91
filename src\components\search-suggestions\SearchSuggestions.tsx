import Image from "next/image";
import Link from "next/link";
import FallbackImage from "../fallback-image/FallbackImage";

export default function SearchSuggestions({ data, translation }:any) {
  return (
    <div className="search_left">
      <h5>{translation?.popular ?? "Popular Searches"}</h5>
      <div className="search_tags">
        {data?.popularSearches?.map((item: any, index: number) => (
          <Link
            href={`/search?q=${item.key}`}
            key={item?._id + index}
            data-disable-nprogress={true}
          >
            <span>{item.key}</span>
          </Link>
        ))}
      </div>

      <div className="search_row">
        <h5>{translation?.topCategory ?? "Top Category for you"}</h5>
        <div className="search_suggested">
          {data?.categories?.map((item: any) => (
            <Link href={`/products/${item?.slug}`} key={item?._id}>
              <div className="search_suggested-items" key={item?._id}>
                {/* <Image quality={100} priority
                  src={item?.image ?? ""}
                  width={128}
                  height={102}
                  alt="search images"
                /> */}
                <FallbackImage
                  width={128}
                  height={102}
                  alt="Search Images"
                  src={item?.image}
                  fallbackSrc={"/images/product/noImage.jpg"} />
                <h6>{item?.name}</h6>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
}
