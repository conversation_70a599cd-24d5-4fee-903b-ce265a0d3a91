"use client";
import Image from "next/image";
import "./product-gallery.scss";
import { useState } from "react";
import OverLayLoader from "@/components/LogoAnimation/OverLayLoader";
import dynamic from "next/dynamic";
const VirtualTryPopUp = dynamic(
  () => import("../virtual-try-popup/VirtualTryPopUp"),
  {
    loading: () => (
      <div className="spinner-border text-light mt-4" role="status"></div>
    ),
  }
);
const VirtualSliderPopUp = dynamic(
  () => import("@/components/virtual-slider-popup/VirtualSliderPopUp"),
  {
    loading: () => <OverLayLoader />,
  }
);

function ProductGallery({ data, isVirtualTry, upc, vmPolicy }: any) {
  const [show, setShow] = useState(false);
  const handleClose = () => setShow(false);
  const handleShow = () => setShow(true);
  
  return (
    <div
      className="gallery d-sm-block d-none"
      style={{ backgroundColor: "white" }}
    >
      <div className="gallery_single-row">
        <Image
          quality={100}
          priority
          onClick={handleShow}
          src={data?.[0] || "/images/product/noImage-2x.jpg"}
          width={707}
          height={557}
          alt="gallery image"
          className="opacity-0"
          onLoadingComplete={(image) => image.classList.remove("opacity-0")}
        />
        {isVirtualTry && <VirtualTryPopUp upc={upc} vmPolicy={vmPolicy} />}

        {show && (
          <VirtualSliderPopUp
            data={data}
            show={show}
            handleClose={handleClose}
          />
        )}
      </div>

      <div className="gallery_multi-row">
        {data?.length > 1 &&
          data?.slice(1)?.map((item: any, index: number) => {
            return (
              <Image
                quality={100}
                priority
                onClick={handleShow}
                src={item || "/images/product/noImage.jpg"}
                width={707}
                height={557}
                alt="gallery image"
                key={index}
                className="opacity-0"
                onLoadingComplete={(image) =>
                  image.classList.remove("opacity-0")
                }
              />
            );
          })}
      </div>
    </div>
  );
}

export default ProductGallery;
