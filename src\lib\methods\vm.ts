import { endpoints } from "@/config/apiEndpoints";
import { cookies } from "next/headers";

export const getVmPolicy = async () => {
    const locale = cookies().get("Next-Locale")?.value || "ae-en";
    const [storeId, language] = locale.split("-")
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}${endpoints.getVmPolicy}`, {
        method: "GET",
        headers: {
            "Content-Type": "application/json",
            language: language || "en",
            storeid: storeId || "ae",
        },
        next: {
            tags: ["vm-policy"]
        }
    });
    const data = await response.json();
    return data;
}