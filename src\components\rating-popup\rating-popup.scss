.rating-popup {
  &.modal {
    .modal-header {
      padding: 0;
      border: none;

      .btn-close {
        background-image: url(../../../public/images/common/close.png);
        width: 4.8rem;
        height: 4.8rem;
        background-position: center;
        background-repeat: no-repeat;
        background-size: contain;
        opacity: 1;
        padding: 0;

        @media (max-width: 575.98px) {
          width: 2.8rem;
          height: 2.8rem;
        }

        &:focus {
          box-shadow: none;
          outline: none;
          border: none;
        }
      }
    }

    .modal-content {
      border: none;
      border-radius: 3rem;
      padding: 1.8rem 5.3rem 4.2rem 5.3rem;

      @media (max-width: 575.98px) {
        padding: 2.1rem 1.7rem 4.2rem 1.7rem;
        border-radius: 2rem;
      }
    }

    .modal-body {
      padding: 0;
      display: flex;
      flex-direction: column;
      align-items: center;

      h2 {
        text-align: center;
        line-height: 4rem;

        @media (max-width: 575.98px) {
          line-height: 3.6rem;
          font-size: 2.2rem;
          font-weight: 700;
        }
      }

      .start-rating {
        margin-top: 2rem;

        @media (max-width: 575.98px) {
          margin-top: 2.9rem;
        }
      }
      .image-upload-review {
        width: 100%;
        display: flex;
        align-items: center;
        column-gap: 14px;
        margin-top: 3rem;
        .preview {
          width: 66px;
          height: 56px;
          position: relative;
          &-delete-btn {
            position: absolute;
            top: 0;
            right: 0;
            padding: 0 4px;
            transform: translate(25%, -25%);
            background-color: #000;
            border-radius: 50%;
            width: 22px;
            height: 22px;
            display: grid;
            place-items: center;
            color: #fff;
            cursor: pointer;
            &:hover {
              background-color: #aa3131;
            }
          }
          img {
            height: 56px;
            width: 66px;
            width: 100%;
            object-fit: cover;
            border-radius: 4px;
          }
        }
        &-label {
          padding: 15px 20px;
          border: 1px dashed rgb(82, 82, 82);
          border-radius: 4px;
          width: auto !important;
          margin: 0;
          cursor: pointer;
        }
      }
      label {
        margin-top: 3.4rem;
        color: #242426;
        font-size: 1.4rem;
        font-weight: 400;
        line-height: 2rem;
        width: 100%;
        padding-bottom: 0.8rem;

        @media (max-width: 575.98px) {
          margin-top: 7.7rem;
        }
      }

      textarea {
        border: none;
        border-bottom: 1px solid #e2e4e5;
        width: 100%;
        resize: none;
        color: #242426;
        font-size: 1.5rem;
        font-weight: 400;
        line-height: 2.8rem;
        height: 4.4rem;
        padding-left: 1.6rem;

        &::placeholder {
          font-size: 1.5rem;
          color: #242426;
        }

        &:focus-visible {
          border: none;
          outline: none;
          border-bottom: 1px solid #e2e4e5;
        }
      }

      button {
        margin-top: 4rem;
        color: #fff;
        text-align: center;
        font-size: 1.5rem;
        font-weight: 500;
        border: none;
        background-color: #000;
        height: 5.6rem;
        width: 40rem;
        border-radius: 6rem;

        @media (max-width: 575.98px) {
          width: 16.9rem;
          height: 4.5rem;
          margin-top: 7.7rem;
        }
      }
    }
  }
}

.reviewBtn-pop {
  color: #000;
  font-size: 14px;
  font-weight: 700;
  text-decoration-line: underline;
  background-color: transparent;
  border: none;
  // margin-left: -6px;
}
