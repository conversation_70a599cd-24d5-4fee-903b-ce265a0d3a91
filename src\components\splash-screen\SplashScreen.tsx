"use client";
import Image from "next/image";
import "./splash-screen.scss";
import { useContext, useEffect, useState } from "react";
import { HistoryContext } from "@/contexts/HistoryProvider";
import { AnimatePresence, motion } from "framer-motion";
import { useRouter } from "next-nprogress-bar";
import { useSearchParams } from "next/navigation";

const ball = {
  width: 100,
  height: 100,
  backgroundColor: "#dd00ee",
  zIndex: 9999,
}

export default function SplashScreen() {
  const { isVisible, setIsVisible } = useContext(HistoryContext);
  const searchParams = useSearchParams();

  useEffect(() => {
    const fromPath = searchParams.get("from");
    if (fromPath) {
      setIsVisible(false);
    }
  }, [searchParams]);
  const hideOnScroll = () => {
    if (typeof window === "undefined") return;
    if (typeof document === "undefined") return;
    if (document.cookie.includes("yateem-splash=true")) {
      setIsVisible(false);
      return;
    }
    if (sessionStorage.getItem("splash") === "true") {
      setIsVisible(false);
      return;
    }
    const winScroll = document.body.scrollTop || document.documentElement.scrollTop;
    if (winScroll > 0) {
      setIsVisible(false);
      sessionStorage.setItem("splash", "true");
      document.cookie = "yateem-splash=true; path=/;";
    }
  };

  useEffect(() => {
    // window.addEventListener("scroll", hideOnScroll);
    // return () => {
    //   window.removeEventListener("scroll", hideOnScroll);
    // };
    setIsVisible(false)
  }, []);

  // useEffect(() => {
  //   if (document.cookie.includes("yateem-splash=true")) {
  //     setIsVisible(false);
  //     return;
  //   }
  // }, []);

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 1 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="splash">
          <div className="scroll_container">
            <div className="splash__glass">
              <Image quality={100} priority src="/images/splash/yateem-glass.svg" width={200} height={200} alt="logo" />
            </div>

            {/* <div className="splash__scroll">
              <Image quality={100} priority src="/images/splash/scroll.gif" width={106} height={60} alt="logo" />

              <span>SCROLL DOWN</span>
            </div> */}

            <div className="stand__container">
              <div className="splash__logo__container">
                <Image quality={100} priority
                  className="splash__logo"
                  src="/Yateem_Optician_Logo-white.svg"
                  width={200}
                  height={200}
                  alt="logo"
                />
              </div>

              <Image quality={100} priority
                className="splash__stand"
                src="/images/splash/stand.svg"
                width={200}
                height={200}
                alt="logo"
              />
            </div>
          </div>
        </motion.div>
      )}

    </AnimatePresence>
  );
}
