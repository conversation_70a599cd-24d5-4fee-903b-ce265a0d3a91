"use clinet";

import {
  ReactCompareSlider,
  ReactCompareSliderImage,
} from "react-compare-slider";

function WithBlur({ data }: any) {
  return (
    <ReactCompareSlider
      role="slider"
      itemOne={
        <ReactCompareSliderImage
          srcSet={data?.beforeImage ?? ""}
          alt={data?.title || "Before"}
          loading="lazy"
        />
      }
      itemTwo={
        <ReactCompareSliderImage
          role="slider"
          srcSet={data?.afterImage ?? ""}
          alt={data?.title || "After"}
          loading="lazy"
        />
      }
    />
  );
}

export default WithBlur;
