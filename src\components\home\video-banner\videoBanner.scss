.videosec {
  margin-top: 5rem;
  &_video {
    position: relative;

    .gif {
      width: 100%;
      object-fit: contain;
      // height: 50rem;
      height: auto;

      @media (max-width: 575.98px) {
        // height: 20rem;
        height: auto;
        width: 100%;
        object-fit: cover;
      }
    }
  }

  &_content {
    right: 0;
    position: absolute;
    top: 0;
    background: rgba(0, 0, 0, 0.15);
    height: 100%;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0 5.8rem 0 6.5rem;

    @media (max-width: 575.98px) {
      position: relative;
      background: rgba(0, 0, 0, 0.03);
      padding: 0;
      padding-top: 2rem;
      padding-bottom: 2rem;
    }

    img {
      width: auto;

      @media (max-width: 575.98px) {
        filter: brightness(0) saturate(100%) invert(0%) sepia(90%) saturate(0%) hue-rotate(264deg) brightness(93%) contrast(101%);
      }
    }

    p {
      max-width: 32.8rem;
      width: 100%;
      font-size: 1.7rem;
      font-weight: 500;
      line-height: 2.6rem;
      margin-top: 2rem;
      color: #fff;

      @media (max-width: 575.98px) {
        color: #000;
        margin-top: 1.5rem;
      }
    }
  }
}

.app.rtl {
  .videosec_content {
    left: 0;
    right: auto;
  }
}