"use client";

import { createContext, useEffect, useState } from "react";

export const HistoryContext = createContext({
  history: {},
  prevPage: { title: "", url: "" },
} as any);

export const HistoryProvider = ({ children }: any) => {
  const [history, setHistory] = useState({});
  const [prevPage, setPrevPage] = useState({ title: "", url: "" });
  const [isVisible, setIsVisible] = useState(true);
  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);

  return (
    <HistoryContext.Provider
      value={{
        history,
        setHistory,
        prevPage,
        setPrevPage,
        isVisible,
        setIsVisible,
        isProfileMenuOpen,
        setIsProfileMenuOpen,
      }}>
      {children}
    </HistoryContext.Provider>
  );
};
