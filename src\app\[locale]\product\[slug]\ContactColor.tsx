"use client";

import { endpoints } from "@/config/apiEndpoints";
import api from "@/config/axios.interceptor";
import { useQuery } from "@tanstack/react-query";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";

export default function ContactColor({
    currentVariant,
    mainProduct,
    product,
    translation,
    colors,
    combinations,
    variant,
    sizes
}: any) {
    // const {
    //     data: variants,
    //     isLoading,
    //     error,
    // } = useQuery({
    //     queryKey: ["variants", mainProduct],
    //     queryFn: async () => {
    //         const res = await api.get(`${endpoints.variants}/${mainProduct}`);
    //         return res.data.result;
    //     },
    // });

    const [activeColor, setActiveColor] = useState(null);
    const router = useRouter();

    // const handleColorClick = (color: any) => {
    //     router.replace(`/product/${color?.slug}`);
    //     setActiveColor(color);
    // };

    const handleColorClick = (color: any) => {
        if (combinations[color?._id][variant?.size]) {
            router.replace(`/product/${combinations[color?._id][variant?.size]}`, {
                scroll: false
            });
        } else {
            for (let size of sizes) {
                if (combinations[color?._id][size?._id]) {
                    return router.replace(`/product/${combinations[color?._id][size?._id]}`, {
                        scroll: false
                    });
                }
            }
        }
        // setActiveColor(color);
    };

    return (
        <>
            {colors?.length > 0 ? (
                <div className="block-three">
                    <span>{translation.color || "Color"}</span>
                    <div className="block-three_colors" style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}>
                        {colors?.map((option: any, index: number) => {
                            let link = "";
                            if (combinations[option?._id][variant?.size]) {
                                link = `/product/${combinations[option?._id][variant?.size]}`;
                            } else {
                                if (sizes.length > 0) {
                                    for (let size of sizes) {
                                        if (combinations[option?._id][size?._id]) {
                                            link = `/product/${combinations[option?._id][size?._id]}`
                                            break;
                                        }
                                    }
                                } else {
                                    link = `/product/${combinations[option?._id][0]}`
                                }
                            }
                            return (
                                <Link
                                key={option?._id}
                                    replace={true} scroll={false} href={link}
                                    style={{ padding: ".5rem", outline: product?.color?._id === option._id ? "1px solid #a0a0a0" : "", borderRadius: ".5rem", width: "min-content" }}
                                >
                                    <div
                                        title={option?.name}
                                        className={product?.color?._id === option._id  ? "isActive" : ""}
                                        style={{
                                            backgroundImage: `linear-gradient(180deg, ${option?.color?.[0]
                                                } 46%, ${option?.color?.[1]
                                                    ? option?.color?.[1]
                                                    : option?.color?.[0]
                                                } 46%)`,
                                            width: "40px",
                                            height: "40px",
                                            borderRadius: "100%"
                                            // outline: currentVariant === option.slug ? `2px solid ${option.color[0]}` : "none",

                                        }}
                                        onClick={() => handleColorClick(option)}
                                    >
                                        <Image src="/contact.webp" style={{ width: "100%", height: "100%", }} width={50} height={50} alt={"contact"} />
                                    </div>
                                </Link>
                            )
                        }
                        )}
                    </div>
                </div>
            ) : (
                <div className="block-three">
                    <span>{translation.color || "Color"}</span>
                    <div className="block-three_colors">
                        <div style={{ padding: "1rem", outline: "1px solid #a0a0a0", borderRadius: ".5rem", width: "min-content" }}>
                            <div
                                title={product?.color?.name}
                                className={"isActive"}
                                style={{
                                    backgroundImage: `linear-gradient(180deg, ${product?.color?.color?.[0]
                                        } 46%, ${product?.color?.color?.[1]
                                            ? product?.color?.color?.[1]
                                            : product?.color?.color?.[0]
                                        } 46%)`,
                                    width: "50px",
                                    height: "50px",
                                    borderRadius: "100%"
                                    // outline: currentVariant === option.slug ? `2px solid ${option.color[0]}` : "none",

                                }}
                            >
                                <Image src="/contact.webp" style={{ width: "100%", height: "100%", }} width={40} height={40} alt={"contact"} />
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
}
