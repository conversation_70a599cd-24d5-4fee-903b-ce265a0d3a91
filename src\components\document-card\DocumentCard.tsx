"use client"

import Link from "next/link";
import Image from "next/image";
import "./document-card.scss";
import { useContext } from "react";
import { TranslationContext } from "@/contexts/Translation";
import { useLocaleContext } from "@/contexts/LocaleProvider";

const isPDFFile = (fileName: string) => {
  const extension = fileName?.split(".").pop(); // Extract the file extension
  return extension?.toLowerCase() === "pdf"; // Check if the extension is 'pdf'
};
const options: any = {
  year: "numeric",
  month: "long",
  day: "numeric",
};

export default function DocumentCard({ items, confirmDelete }: { items: any; confirmDelete: any }) {
  const {translation: {other}} = useContext(TranslationContext)
  const { currentLocale:locale } = useLocaleContext()
  return (
    <div className="document_view_cards" key={items?._id}>
      <button
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          confirmDelete(items?.refid);
        }}
        className="delete-icon">
        <Image quality={100} priority src="/images/common/deletecon1.png" width={11} height={14} alt="delete icon" />
      </button>
      {isPDFFile(items?.file) ? (
        <object
          data={items?.file}
          className="document_view_pdf"
          type="application/pdf"
          width="100%"
          height="150px"
          style={{ objectFit: "cover", overflow: "hidden" }}>
          <p>
            {locale.includes("en")? "Unable to Load the Document Preview.": "تعذر تحميل معاينة المستند"} <br /> {locale.includes("en")? "Please": "لو سمحت"}
            <a href={items?.file} target="_blank">
              {" "}
              {locale.includes("en")? "Download to view the PDF!": "تنزيل لعرض ملف PDF"}
            </a>
          </p>
        </object>
      ) : (
        <Image quality={100} priority src={items?.file} width={336} height={137} alt="document" />
      )}
      <div className="d-flex justify-content-between align-items-center mt-3">
        <div className="document_view_cards-info">
          <h4>{items.title}</h4>
          <h5>{new Date(items.createdAt)?.toLocaleDateString("en-US", options)}</h5>
        </div>
        <Link href={items?.file} target="_blank">
          <button type="button">
            {other.download ?? "Download"}
            <Image quality={100} priority
              src="/images/common/fi_download-cloud.png"
              width={16}
              height={16}
              alt="Picture of the author"
            />
          </button>
        </Link>
      </div>
    </div>
  );
}
