"use client";

import "./mobile-menu.scss";
import Image from "next/image";
import Link from "next/link";
import { useState, useContext } from "react";
import Sheet from "react-modal-sheet";
import { usePathname } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import api from "@/config/axios.interceptor";
import { endpoints } from "@/config/apiEndpoints";
import { HistoryContext } from "@/contexts/HistoryProvider";
import { AuthContext } from "@/contexts/AuthProvider";

function MobileMenu({translation}:any) {
  const pathname = usePathname();
  const [homeOpen, SetHomeOpen] = useState(false);
  const [categoryOpen, SetCategoryOpen] = useState(false);
  const [cartOpen, SetCartOpen] = useState(false);
  const [accountOpen, SetAccountOpen] = useState(false);
  const { userProfile } = useContext(AuthContext);

  const { setIsProfileMenuOpen, isProfileMenuOpen } =
    useContext(HistoryContext);

  const isActive = (route: string) => {
    if (route === "/") {
      return pathname?.split("/")?.length === 2;
    }
    return pathname.includes(route);
  };

  const {
    data: counts,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ["cart", "counts"],
    queryFn: () => {
      return api.get(endpoints.counts).then((res) => {
        if (res.status === 200) {
          return res.data?.result;
        } else throw error;
      });
    },
  });

  const {other} = translation;

  return (
    <div className="bottom-navigation d-xl-none d-block">
      <div className="container">
        <ul>
          <li className={isActive("/") ? "isActive" : ""}>
            <Link href="/">
              <Image
                quality={100}
                priority
                src="/images/mobile/Home.svg"
                width={24}
                height={24}
                alt="logo"
              />
            </Link>
            <Link href="/">{other?.home ?? "Home"}</Link>
            <Sheet isOpen={homeOpen} onClose={() => SetHomeOpen(false)}>
              <Sheet.Container>
                <Sheet.Header />
                <Sheet.Content>
                  <ul>
                    <li>
                      <Link href="/my-address-book">Sun Glasses</Link>
                    </li>
                    <li>Contact Lenses</li>
                    <li>Ophthalmic lense</li>
                    <li>Eyeglasses</li>
                    <li>Insurance</li>
                    <li>Insurance</li>
                    <li>Brands</li>
                  </ul>
                </Sheet.Content>
              </Sheet.Container>
              <Sheet.Backdrop />
            </Sheet>
          </li>

          <li className={isActive("/categories") ? "isActive" : ""}>
            <Link href="/categories">
              <Image
                quality={100}
                priority
                src="/images/mobile/note-2.svg"
                width={24}
                height={24}
                alt="logo"
              />
            </Link>
            <Link href="/categories">{other?.category ?? "Category"}</Link>
          </li>

          <li className={isActive("/cart") ? "isActive" : ""}>
            <Link href="/cart">
              <Image
                quality={100}
                priority
                src="/images/mobile/shopping-basket.svg"
                width={24}
                height={24}
                alt="logo"
              />
            </Link>
            <Link href="/cart">{other?.cart ?? "Cart"}</Link>
            {counts?.cart > 0 && (
              <span className="cart-count">
                {counts?.cart ? `${counts.cart}` : ""}
              </span>
            )}
          </li>

          <li
            className={isActive("/my-accounts") ? "isActive" : ""}
          >
            <Link style={{ display: 'flex', justifyContent: "center" }} href={!userProfile ? "/login" : "/my-accounts"}>
              <Image
                quality={100}
                priority
                src="/images/mobile/Person.svg"
                width={24}
                height={24}
                alt="logo"
              />
            </Link>
            <Link style={{ display: 'flex', justifyContent: "center" }} href={!userProfile ? "/login" : "/my-accounts"}>
              {other?.account ?? "Account"}
            </Link>
          </li>
        </ul>
      </div>
    </div>
  );
}

export default MobileMenu;
