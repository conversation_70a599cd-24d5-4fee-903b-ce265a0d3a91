"use client";
import Image from "next/image";
import React, { useState } from "react";

export const ImageViewer = ({ product }: any) => {
    const [imageSrc, setImageSrc] = useState<any>({});
    return (
        <Image
            quality={100}
            priority
            src={imageSrc?.[product?.productid] ? imageSrc?.[product?.productid] : product?.thumbnail}
            width={120}
            height={120}
            style={{ filter: product?.stock === 0 ? "grayscale(1)" : "" }}
            alt={product?.name}
            onError={() =>
                setImageSrc({
                    [product?.productid]: "/images/product/noImage.jpg",
                })
            }
        />
    );
};
