import { useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';

interface UseUrlStateReturn {
    active: string | null;
    setActive: (newValue: string) => void;
}

function useUrlState(paramName: string): UseUrlStateReturn {
    const [state, setState] = useState<string | null>(null);
    const router = useRouter();


    useEffect(() => {
        const urlParams = new URLSearchParams(window.location.search);
        const paramValue = urlParams.get(paramName);
        setState(paramValue || null);
    }, [paramName]);

    const setActive = (newValue: string) => {
        const urlParams = new URLSearchParams(window.location.search);
        urlParams.set(paramName, newValue);
        // window.history.pushState({}, '', `${window.location.pathname}?${urlParams}`);
        window.history.replaceState(null, '', `?${urlParams.toString()}`);
        // router.push(`${window.location.pathname}?${urlParams}`, {
        //     scroll: false,
        // });
        setState(newValue);
    };

    return { active: state, setActive };
}

export default useUrlState;
