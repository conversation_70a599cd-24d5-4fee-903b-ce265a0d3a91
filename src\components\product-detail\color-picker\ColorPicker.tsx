"use client";

import { endpoints } from "@/config/apiEndpoints";
import api from "@/config/axios.interceptor";
import { useQuery } from "@tanstack/react-query";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";

export default function ColorPicker({
  currentVariant,
  mainProduct,
  product,
  translation,
  colors,
  combinations,
  variant,
  sizes,
  type
}: any) {
  console.log(colors)
  console.log(type)
  // const {
  //   data: variants,
  //   isLoading,
  //   error,
  // } = useQuery({
  //   queryKey: ["variants", mainProduct],
  //   queryFn: async () => {
  //     const res = await api.get(`${endpoints.variants}/${mainProduct}`);
  //     return res.data.result;
  //   },
  // });

  const [activeColor, setActiveColor] = useState(null);
  const router = useRouter();

  const handleColorClick = (color: any) => {
    if (combinations[color?._id][variant?.size]) {
      router.replace(`/product/${combinations[color?._id][variant?.size]}`, {
        scroll: false
      });
    } else {
      for (let size of sizes) {
        if (combinations[color?._id][size?._id]) {
          return router.replace(`/product/${combinations[color?._id][size?._id]}`, {
            scroll: false
          });
        }
      }
    }
    // setActiveColor(color);
  };

  return (
    <>
      {colors?.length > 0 ? (
        <div className="block-three">
          <span>{translation.color || "Color"}</span>
          <div className="block-three_colors">
            <div className="ul" style={{display: "grid", gridTemplateColumns: "repeat(auto-fill, minmax(90px, 1fr))", gap: "1rem",}}>
              {colors?.map((option: any, index: number) => {
                let link = "";
                if (combinations[option?._id][variant?.size]) {
                  link = `/product/${combinations[option?._id][variant?.size]}`;
                } else {
                  if (sizes.length > 0) {
                    for (let size of sizes) {
                      if (combinations[option?._id][size?._id]) {
                        link = `/product/${combinations[option?._id][size?._id]}`
                        break;
                      }
                    }
                  } else {
                    link = `/product/${combinations[option?._id][0]}`
                  }
                }
                return (
                  <div
                    key={option?._id}
                    title={option?.name}
                    className={`${product?.color?._id === option._id ? "isActive" : ""} li`}
                    // style={{
                    //   backgroundImage: `linear-gradient(180deg, ${option?.color?.[0]
                    //     } 46%, ${option?.color?.[1]
                    //       ? option?.color?.[1]
                    //       : option?.color?.[0]
                    //     } 46%)`,
                    //   // outline: currentVariant === option.slug ? `2px solid ${option.color[0]}` : "none",
                    // }}
                    onClick={() => handleColorClick(option)}
                    style={{ display: "flex", alignItems: "center", justifyContent: "center" }}
                  >
                    <Link style={{ display: "flex", alignItems: "center", justifyContent: "center" }} replace={true} scroll={false} href={link}>
                      <Image style={{ width: "100%", height: "100%", objectFit: "contain" }} src={type === "contactLens" ? (option?.lensImage ?? option?.thumbnail) : option?.thumbnail} width={50} height={50} alt={option?.name} />
                    </Link>
                  </div>
                )
              })}
            </div>
          </div>
        </div>
      ) : (
        <div className="block-three">
          <span>{translation.color || "Color"}</span>
          <div className="block-three_colors">
            <div className="ul">
              <div
                title={product?.color?.name}
                className={"li isActive"}
                style={{ display: "flex", alignItems: "center", justifyContent: "center" }}
              // style={{
              //   backgroundImage: `linear-gradient(180deg, ${product?.color?.color?.[0]
              //     } 46%, ${product?.color?.color?.[1]
              //       ? product?.color?.color?.[1]
              //       : product?.color?.color?.[0]
              //     } 46%)`,
              //   // outline: currentVariant === option.slug ? `2px solid ${option.color[0]}` : "none",
              // }}
              >
                <Image style={{ width: "100%", height: "100%", objectFit: "contain" }} src={type === "contactLens" ? (product?.lensImage ?? product?.thumbnail) : product?.thumbnail} width={50} height={50} alt={product?.name?.en} />
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
