.product-card {
  text-align: center;
  padding: 1rem 0 0rem;
  background: #fff;
  border-radius: 1.5rem;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;

  .mySwiper {
    width: 100%;

    .swiper-wrapper {
      display: flex;
      width: 100%;

      .swiper-slide {
        width: 100%;
      }
    }

    .swiper-button-prev {
      transition: transform 0.3s ease;
      transform: translateX(-50px);
      &::after {
        filter: invert(1);
        background-image: url(../../../../public/images/home/<USER>
        background-repeat: no-repeat;
        font-size: 0;
        width: 2rem;
        height: 2rem;
        background-size: contain;
      }
    }

    .swiper-button-next {
      transition: transform 0.3s ease;
      transform: translateX(50px);
      &::after {
        background-image: url(../../../../public/images/home/<USER>
        background-repeat: no-repeat;
        font-size: 0;
        width: 2rem;
        height: 2rem;
        background-size: contain;
        filter: invert(1);
      }
    }

  }

  &:hover {
    img {
      scale: 1.03;
    }

    .vm {
      img {
        scale: 1;
      }
    }

    .swiper-button-prev {
      transform: translateX(0);
    }

    .swiper-button-next {
      transform: translateX(0);
    }
  }

  div:last-child.colors {
    &.colors {}

    margin-top: 2rem !important;
  }

  .product-card-link {
    display: inline-block
  }

  .isActive {
    transition: scale 450ms cubic-bezier(0.175, 0.885, 0.32, 1.275);

    &:hover {
      scale: 1.1;
    }
  }

  label {
    border-radius: 0rem 0.5rem 0.5rem 0rem;
    background: #000;
    color: #fff;
    font-size: 1.3rem;
    font-weight: 500;
    text-transform: uppercase;
    padding: 0.6rem 1.4rem;
    position: relative;
    left: 0;
    transform: translateX(-100%);
    transition-delay: 0;
    transition: transform 2s;
    z-index: 2;
  }

  label.show {
    transform: translateX(0);
  }

  label.show.delay {
    transition-delay: 2s;
  }

  button {
    border: none;
    background: none;
    padding: 0;
    padding-right: 0.8rem;
    position: relative;
    // z-index: 12;

    svg {
      width: 2.4rem;
      height: 2.4rem;

      // @media (max-width: 575.98px) {
      //   width: 2rem;
      //   height: 2rem;
      // }
    }
  }

  .vm {
    border-radius: 0 0 1.5rem 1.5rem;
    width: 100%;
    background-color: #ececec;
    color: #000;
    font-size: 1.5rem;
    font-weight: 400;
    line-height: 1;
    padding: .5rem 0;
    display: flex;
    align-items: center;
    justify-content: center;

    @media (max-width: 575.98px) {
      font-size: 1.2rem;
    }

    img {
      width: 2rem !important;
      height: 2rem !important;
      margin: 0 !important;
      margin-right: .7rem !important;
      padding: 0 !important;


      @media (max-width: 575.98px) {
        width: 1.8rem !important;
        height: 1.8rem !important;
      }
    }
  }

  img {
    border-radius: 1.5rem 1.5rem 0rem 0rem;
    object-fit: contain;
    margin: 2rem 0;
    transition: scale 500ms cubic-bezier(0.075, 0.82, 0.165, 1);
  }

  .brand-name {
    color: #808080;
    font-size: 1.5rem;
    font-weight: 600;
    line-height: 1.9rem;
  }

  .product-modal {
    color: #000;
    font-size: 1.7rem;
    font-weight: 400;
    margin-top: 0.5rem;
    line-height: 2.1rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    overflow: hidden;
    padding: 5px 20px;
    padding-bottom: 0;
  }

  .product-price-detail {
    display: flex;
    align-items: center;
    column-gap: 1rem;
    margin-top: 1rem;
    justify-content: center;
    font-size: 1.5rem;

    .real-price {
      color: #000;
      font-weight: 600;
      display: flex;
      gap: .5rem;
    }

    .offer-price {
      color: #878787;
      font-weight: 400;
      text-decoration-line: line-through;
      text-decoration-color: #878787;
      display: flex;
      gap: .5rem;
    }

    .percentage {
      color: red;
      font-weight: 600;
    }
  }
}

.add-to-cart {
  padding: 1.2rem 3.3rem;
  border-radius: 9.8rem;
  color: #fff;
  display: inline-block;
  background-color: #000;
  transition: all 0.3s ease;
  border: 1px solid #000;

  @media (max-width: 575.98px) {
    padding: 0.5rem 2.1rem;
    font-size: 1.3rem;
  }

  &:hover {
    background-color: transparent;
    transition: all 0.3s ease;
    color: #000;
  }
}

.add-to-cart-btn {
  padding: 1.2rem 3.3rem !important;
  border-radius: 9.8rem !important;
  color: #fff !important;
  display: inline-block !important;
  background-color: #000 !important;
  ;
  transition: all 0.3s ease !important;
  ;
  border: 1px solid #000 !important;
  ;

  @media (max-width: 575.98px) {
    padding: 0.5rem 2.1rem !important;
    ;
    font-size: 1.3rem !important;
  }

  &:hover {
    background-color: transparent !important;
    transition: all 0.3s ease !important;
    color: #000 !important;
  }
}

.app.rtl {
  .product-card button {
    padding-right: 0rem;
    padding-left: 0.8rem;

  }

  .product-card .vm img {
    margin-left: 0.7rem !important;
    margin: 0 !important;
    margin-right: 0rem !important;
  }

  .product-card label {
    border-radius: 0.5rem 0rem 0rem 0.5rem;
  }
}

.colors {
  margin: 1rem 1.5rem;
  display: flex;
  margin-top: auto;
  justify-content: center;

  @media (max-width: 575.98px) {
    margin: auto .8rem;
  }
}

.colors .color-li {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  transition: all 0.2s ease-in-out;
  position: relative;

  &.isActive {
    width: 1.3rem;
    height: 1.3rem;
    transition: all .2s ease-in-out;
    outline: 2px solid #ff8768;
    border: 2px solid #fff;

    // &::after {
    //   content: "";
    //   background-image: url(../../../../public/images/product-detail/tick.svg);
    //   background-repeat: no-repeat;
    //   background-position: center;
    //   height: 8px;
    //   width: 10px;
    //   position: absolute;
    //   top: 50%;
    //   left: 50%;
    //   transform: translate(-50%, -50%);
    // }
  }

  @media (max-width: 575.98px) {
    width: 1.5rem;
    height: 1.5rem;
  }

}

.colors .color-ul {
  display: flex;
  gap: 1rem;
  align-items: center;
  height: 3.4rem;

  p {
    color: #000;
  }
}

.app.rtl .product-card {
  label {
    right: 0;
    left: unset;
    transform: translateX(100%);
    transition-delay: 0;
    transition: transform 2s;
    z-index: 2;
    border-radius: 0.5rem 0rem 0rem 0.5rem;
  }

  label.show {
    transform: translateX(0);
  }

  label.show.delay {
    transition-delay: 2s;
  }
}