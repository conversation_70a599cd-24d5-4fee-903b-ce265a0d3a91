.my-subscription {
  h2 {
    margin-bottom: 3rem;

    @media (max-width: 1199.98px) {
      margin-top: 4.4rem;
    }

    @media (max-width: 575.98px) {
      margin-bottom: 2.8rem;
      margin-top: 1.5rem;
    }
  }

  &_grids {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 3rem;

    @media (max-width: 767.98px) {
      grid-template-columns: repeat(2, 1fr);
      gap: 1.5rem;
    }

    @media (max-width: 575.98px) {
      grid-template-columns: repeat(1, 1fr);
    }
  }

  &_items {
    border-radius: 2rem;
    border: 1px solid #ebebeb;
    background: #fff;
    padding-bottom: 3.5rem;

    img {
      border-radius: 2rem;
      height: auto;
    }

    h5 {
      font-size: 1.6rem;
      font-weight: 500;
      margin-top: 2.8rem;
      max-width: 30rem;
      width: 100%;
      line-height: 2rem;
    }

    .product-price-detail {
      display: flex;
      align-items: center;
      column-gap: 1rem;
      margin-top: 1rem;
      font-size: 1.5rem;

      .real-price {
        color: #000;
        font-weight: 600;
      }

      .offer-price {
        color: #878787;
        font-weight: 400;
        text-decoration-line: line-through;
        text-decoration-color: #878787;
      }

      .percentage {
        color: #f5b7b3;
        font-weight: 600;
      }
    }

    h6 {
      margin-top: 2.1rem;
      color: #374151;
      font-size: 1.4rem;
      font-weight: 600;
      margin-bottom: 1.4rem;

      span {
        font-weight: 400;
      }
    }
    strong {
      border-radius: 2.95rem;
      background: #ededed;
      border: none;
      font-weight: 600;
      margin-left: 1rem;
      appearance: none;
      padding: 0.8rem 1.6rem;
    }

    &-select {
      position: relative;
      display: inline-block;

      &::after {
        content: "";
        background-image: url(../../../../public/images/common/Icon.png);
        background-position: right;
        background-repeat: no-repeat;
        background-size: cover;
        top: 50%;
        transform: translateY(-50%);
        position: absolute;
        width: 20px;
        height: 20px;
        right: 15px;
      }
      strong {
        border-radius: 2.95rem;
        background: #ededed;
        border: none;
        width: 12.1875rem;
        height: 4.6875rem;
        margin-left: 1rem;
        appearance: none;
        padding-left: 1.6rem;

        &:focus-visible {
          outline: none;
        }
      }
    }

    label {
      color: #000;
      font-size: 1.3rem;
      font-weight: 400;
    }

    button {
      color: #000;
      font-size: 1.4rem;
      font-weight: 500;
      background-color: transparent;
      border: 1px solid #000;
      border-radius: 2.95rem;
      display: block;
      margin: 0 auto;
      margin-top: 3.2rem;
      width: calc(100% - 3.2rem);
      padding: 1.3rem 0;
      transition: 0.3s all ease;

      &:hover {
        background-color: #000;
        color: #fff;
        transition: 0.3s all ease;
      }
    }

   
  }
}
