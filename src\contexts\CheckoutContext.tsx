"use client";

import { getAddress } from "@/lib/methods/user";
import { useQuery } from "@tanstack/react-query";
import { useSearchParams } from "next/navigation";
import { createContext, useEffect, useState } from "react";
import { useLocaleContext } from "./LocaleProvider";

export const CheckoutContext = createContext({
  selectedAddress: "",
  paymentMethod: "COD",
} as any);

export const CheckoutProvider = ({ children }: any) => {
  const [selectedAddress, setSelectedAddress] = useState("");
  const [paymentMethod, setPaymentMethod] = useState("");
  const [shippingMethod, setShippingMethod] = useState("door");
  const [storeId, setStoreId] = useState("");

  const [boundingRect, setBoundingRect] = useState({});

  const searchParams = useSearchParams();
  const subscriptionId = searchParams.get("sub") || "";
  const { currentLocale } = useLocaleContext()
  const { data: addressData, isLoading } = useQuery({
    queryKey: ["address", currentLocale?.split("-")[0]],
    queryFn: getAddress,
  });

  useEffect(() => {
    if (addressData && addressData?.length > 0) {
      const defaultAddress = addressData.find((item: any) => item.isDefault);

      if (!selectedAddress && defaultAddress) {
        setSelectedAddress(defaultAddress?._id);
      }
    }
    else{
      setSelectedAddress("")
    }
  }, [addressData]);

  return (
    <CheckoutContext.Provider
      value={{
        selectedAddress,
        setSelectedAddress,
        paymentMethod,
        setPaymentMethod,
        shippingMethod,
        setShippingMethod,
        storeId,
        setStoreId,
        boundingRect,
        setBoundingRect,
        subscriptionId,
      }}>
      {children}
    </CheckoutContext.Provider>
  );
};
