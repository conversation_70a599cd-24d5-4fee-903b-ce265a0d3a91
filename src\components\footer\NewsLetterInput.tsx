"use client";

import { endpoints } from "@/config/apiEndpoints";
import api from "@/config/axios.interceptor";
import Image from "next/image";
import { useState } from "react";
import SuccessPopup from "../success-popup/SuccessPopup";

const pattern = /^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/g


export default function NewsLetterInput({translation}:any) {
  const [email, setEmail] = useState("");
  const [success, setSuccess] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const successData = {
    title: translation?.popup?.newsLetterSubscribedTitle ?? "Thank you for subscribing!",
    description: translation?.popup?.newsLetterSubscribedDescription ?? "You will be receiving our latest products and news shortly.",
    primaryBtnTxt: translation?.popup?.newsLetterSubscribedButton ?? "Done",
  };

  const subscribeNewsLetter = async (e: any) => {
    e.preventDefault();
    setErrorMessage(null);
    try {
      if(!pattern.test(email)){
        setErrorMessage(translation?.formFields?.emailAddressInvalidError ?? "Invalid Email");
        return;
      }
      const res = await api.post(endpoints.newsLetterSubscription, { email });
      setEmail("");
      setSuccess(true);
    } catch (error: any) {
      if (error.response) {
        if (error.response.status === 422) {
          console.error(error.response?.data?.message);
          setErrorMessage(error.response?.data?.message);
          // Clear the error message after 3 seconds
          setTimeout(() => {
            setErrorMessage(null);
            setEmail("");
          }, 3000);
        }
      }
    }
  };

  return (
    <>
      <form className="footer_input" onSubmit={subscribeNewsLetter}>
        <p>{translation?.footer?.newsLetter?.text ?? "Stay up to date with our latest products and news"}</p>
        <span>
          <input
            className="rounded-0"
            type="email"
            placeholder={translation?.footer?.newsLetter?.placeholder ?? 'EMAIL ADDRESS'}
            name="email"
            required
            value={email}
            onChange={(e) => setEmail(e.target.value)}
          />
          <button type="submit">
            <Image
              className="object-fit-contain"
              quality={100}
              priority
              src="/images/footer/plus-fill.png"
              width={24}
              height={21}
              alt="Picture of the author"
            />
          </button>
        </span>
        {errorMessage && <p style={{ color: "red" }}>{errorMessage}</p>}
      </form>

      <SuccessPopup
        show={success}
        handleClose={() => setSuccess(false)}
        data={successData}
      />
    </>
  );
}
