import OverLayLoader from "@/components/LogoAnimation/OverLayLoader";
import { useState, lazy, Suspense } from "react";
const ReviewPopup = lazy(() => import("@/components/review-popup/ReviewPopup"));

function Review({ reviews, rating, count }: any) {
  const [reviewshow, setReviewShow] = useState(false);
  const handleReviewClose = () => setReviewShow(false);
  const handleReviewShow = () => setReviewShow(true);
  return (
    <>
      <button className="review" onClick={handleReviewShow}>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="17"
          viewBox="0 0 16 17"
          fill="none">
          <path
            d="M5.75 7C5.75 7.06631 5.72366 7.12989 5.67678 7.17678C5.62989 7.22366 5.5663 7.25 5.5 7.25C5.4337 7.25 5.37011 7.22366 5.32322 7.17678C5.27634 7.12989 5.25 7.06631 5.25 7C5.25 6.9337 5.27634 6.87011 5.32322 6.82322C5.37011 6.77634 5.4337 6.75 5.5 6.75C5.5663 6.75 5.62989 6.77634 5.67678 6.82322C5.72366 6.87011 5.75 6.9337 5.75 7ZM5.75 7H5.5M8.25 7C8.25 7.06631 8.22366 7.12989 8.17678 7.17678C8.12989 7.22366 8.0663 7.25 8 7.25C7.9337 7.25 7.87011 7.22366 7.82322 7.17678C7.77634 7.12989 7.75 7.06631 7.75 7C7.75 6.9337 7.77634 6.87011 7.82322 6.82322C7.87011 6.77634 7.9337 6.75 8 6.75C8.0663 6.75 8.12989 6.77634 8.17678 6.82322C8.22366 6.87011 8.25 6.9337 8.25 7ZM8.25 7H8M10.75 7C10.75 7.06631 10.7237 7.12989 10.6768 7.17678C10.6299 7.22366 10.5663 7.25 10.5 7.25C10.4337 7.25 10.3701 7.22366 10.3232 7.17678C10.2763 7.12989 10.25 7.06631 10.25 7C10.25 6.9337 10.2763 6.87011 10.3232 6.82322C10.3701 6.77634 10.4337 6.75 10.5 6.75C10.5663 6.75 10.6299 6.77634 10.6768 6.82322C10.7237 6.87011 10.75 6.9337 10.75 7ZM10.75 7H10.5M1.5 9.00667C1.5 10.0733 2.24867 11.0027 3.30467 11.158C4.02933 11.2647 4.76133 11.3467 5.5 11.404V14.5L8.28933 11.7113C8.42744 11.5738 8.61312 11.4945 8.808 11.49C10.1091 11.458 11.407 11.3471 12.6947 11.158C13.7513 11.0027 14.5 10.074 14.5 9.006V4.994C14.5 3.926 13.7513 2.99733 12.6953 2.842C11.1406 2.61381 9.57135 2.49951 8 2.5C6.40533 2.5 4.83733 2.61667 3.30467 2.842C2.24867 2.99733 1.5 3.92667 1.5 4.994V9.006V9.00667Z"
            stroke="#374151"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
        {count || 0} Reviews
      </button>
      <Suspense fallback={<OverLayLoader />}>
        {reviewshow && (
          <ReviewPopup
            reviews={reviews}
            rating={rating}
            count={count}
            show={reviewshow}
            handleClose={handleReviewClose}
            handleShow={handleReviewShow}
          />
        )}
      </Suspense>
    </>
  );
}

export default Review;
