.splash {
  padding: 100px 0;
  height: 300vh;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 99999;
  background: rgb(28, 63, 70);
  background: linear-gradient(
    0deg,
    rgba(28, 63, 70, 1) 21%,
    rgba(80, 126, 146, 1) 25%
  );

  img {
    object-fit: unset;
  }

  &__glass {
    z-index: 10;
    animation-name: myanimation;
    animation-timing-function: ease-in;
    animation-duration: 7s;
    animation-fill-mode: forwards;
  }

  &__logo {
    width: 263px;
    height: 53px;
    animation-name: reduceLogoWidth;
    animation-duration: 7s;
    animation-timing-function: ease-in-out;
    animation-fill-mode: forwards;
  }

  &__logo__container {
    position: absolute;
    top: 58%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 99;
    text-align: center;

  }

  &__stand {
    will-change: transform;
    mix-blend-mode: multiply;
    animation-name: flipAndMoveStand;
    animation-duration: 7s;
    animation-timing-function: ease-in-out;
    animation-fill-mode: forwards;
    margin: 3rem auto;
    display: flex;
  }

  .stand__container {
    position: relative;
  }

  &__scroll {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 12%;
    opacity: 0;
    animation: fadeIn 0.3s ease-in-out 1s forwards;

    img {
      width: 10.6rem;
      height: 6rem;
    }

    span {
      color: #fff;
      font-size: 1.3rem;
      font-weight: 300;
      letter-spacing: 0.13rem;
      display: block;
    }
  }
}


@keyframes myanimation {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.5);
  }

  100% {
    transform: scale(1.5);
  }
}

@keyframes flipAndMoveStand {
  0% {
    transform: translateY(0) scaleX(1);
    width: 100%;
  }

  50% {
    transform: translateY(50%) scaleX(-1);
    width: 90%;
  }

  100% {
    transform: translateY(50%) scaleX(-1);
    width: 80%;
  }
}

@keyframes reduceLogoWidth {
  0% {
    width: 263px;
  }

  25% {
    width: 230px;
  }

  50% {
    width: 210px;
    height: 43px;
  }

  100% {
    width: 210px;
    height: 43px;
  }
}

@keyframes fadeIn {
  to {
    display: block;
    opacity: 1;
  }
}
