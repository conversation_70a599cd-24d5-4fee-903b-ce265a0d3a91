"use client";

const SuccessPopup = dynamic(
  () => import("@/components/success-popup/SuccessPopup"),
  {
    ssr: false,
    loading: () => <OverLayLoader />,
  }
);
import OverLayLoader from "@/components/LogoAnimation/OverLayLoader";
import { AuthContext } from "@/contexts/AuthProvider";
import { useLocaleContext } from "@/contexts/LocaleProvider";
import { sendGTMEvent } from "@next/third-parties/google";
import { useQueryClient } from "@tanstack/react-query";
import { createHmac } from "crypto";
import dynamic from "next/dynamic";
import { useRouter } from "next/navigation";
import { useContext, useEffect } from "react";

export default function SuccessPopupWrapper({
  isSuccess,
  cart,
  resp,
  rejectionMessage
}: {
  isSuccess: boolean;
  cart: string;
  resp?: any;
  rejectionMessage?: string
}) {
  const router = useRouter();
  const queryClient = useQueryClient();
  const userProfile = useContext(AuthContext).user
  const { currencyCode, currentLocale } = useLocaleContext()

  const sendPurchaseDataLayer = (order: any) => {
    sendGTMEvent({ ecommerce: null })
    let eventData: any = {
      event: "purchase",
      ecommerce: {
        currency: currencyCode,
        transaction_id: order?.orderNo,
        value: order?.total,
        tax: order?.vatAmount,
        shipping: order?.shippingCharge || "",
        coupon: order?.couponCode || "",
        items: order?.products?.map((item: any, i: number) => {
          return {
            item_id: item?.product?.sku,
            item_name: item?.product?.name?.en,
            index: i,
            item_brand: item?.product?.brand?.name?.en,
            item_category: item?.product?.category?.[0]?.name?.en,
            item_category2: item?.product?.category?.[1]?.name?.en,
            item_variant: item?.product?.color?.name?.en,
            price: item?.price,
            quantity: item.quantity,
          }
        })
      }
    }
    if (userProfile) {
      const mobile = createHmac('sha256', '123').update(userProfile?.mobile).digest('hex')
      let email = null;
      if (userProfile?.email) email = createHmac('sha256', '123').update(userProfile?.email).digest('hex')
      eventData.user = {
        mobile,
        email,
        user_id: userProfile?._id
      }
    }
    sendGTMEvent(eventData)
  }

  useEffect(() => {
    if (isSuccess) {
      sendPurchaseDataLayer(resp?.order)
    }
  }, [])

  return (
    <div>
      <SuccessPopup
        isFail={!isSuccess}
        data={{
          title: isSuccess ? (currentLocale?.split("-")[1] == "en" ? "Order Placed Successfully" : "تم تقديم الطلب بنجاح") : (currentLocale?.split("-")[1] == "en" ? "Payment Failed" : "فشل الدفع"),
          description: isSuccess
            ? (currentLocale?.split("-")[1] == "en" ? "Your order has been placed successfully!" : "لقد تم تقديم طلبك بنجاح!")
            : (rejectionMessage || "Payment could not verified, please try again!"),
          primaryBtnTxt: (currentLocale?.split("-")[1] == "en" ? "Close" : "يغلق"),
          primaryBtnLink: isSuccess ? "/" : `/checkout/${cart}/payment-method`,
          secondaryBtnTxt: userProfile?.isGuest ? null : isSuccess ? (currentLocale?.split("-")[1] == "en" ? "View Orders" : "عرض الطلبات") : null,
          secondaryBtnLink: isSuccess ? "/my-accounts/my-orders" : null,
        }}
        show={true}
        handleClose={() => {
          router.push(isSuccess ? "/" : `/checkout/${cart}/payment-method`);
          if (isSuccess) {
            queryClient.invalidateQueries({ queryKey: ["cart", "counts"] });
            queryClient.invalidateQueries({ queryKey: ["cart"] });
            queryClient.invalidateQueries({ queryKey: ["tryCart"] });
            queryClient.invalidateQueries({ queryKey: ["user"] });
          }
        }}
      />
    </div>
  );
}
