"use client";

import Form from "react-bootstrap/Form";
import Image from "next/image";
import "./product-listing-head.scss";
import Dropdown from "react-bootstrap/Dropdown";
import { useContext, useEffect, useState } from "react";
import useFilter from "@/hooks/useFilter";
import { usePathname, useSearchParams } from "next/navigation";
import { TranslationContext } from "@/contexts/Translation";
import { FilterContext } from "@/contexts/FilterContaxt";
import Link from "next/link";

type SortType = {
  value: string;
  label: string;
};

function ProductListingHead({ onViewChange, filterVisibility, matches, data, view, cats, mainCats, brand }: any) {

  const { translation: { productListing: translation } }: any = useContext(TranslationContext)

  const sortOptions: SortType[] = [
    { value: "0", label: translation.newArrivals ?? "New Arrivals" },
    { value: "1", label: translation.sale ?? "Sale" },
    { value: "2", label: translation.exclusive ?? "Exclusive" },
    { value: "3", label: translation.priceLowToHigh ?? "Price: Low to High" },
    { value: "4", label: translation.priceHighToLow ?? "Price: High to Low" },
  ];

  const [sort, setSort] = useState<SortType>(sortOptions[0]);
  const { active, setActive } = useFilter("sort");
  const { active: tryHomeActive, setActive: setTryHomeActive } = useFilter("tryHome");
  const [activeCats, setActiveCats]: any = useState([])
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const handleFilter = (filter: SortType) => {
    let value = filter.value;
    setActive([value]);
  };

  useEffect(() => {
    if (active.length > 0) {
      setSort(sortOptions.filter((item) => active.includes(item.value))[0]);
    }
  }, [active]);

  useEffect(() => {
    const newParams = new URLSearchParams(searchParams.toString());
    const values = newParams.getAll("subCategory")
    setActiveCats(values)
  }, [searchParams])

  const handleCats = (e: any) => {
    const value = e.target.dataset.value;
    const newParams = new URLSearchParams(searchParams.toString());
    const values = newParams.getAll("subCategory")
    newParams.delete("subCategory")
    let newValues: any = []
    if (values.includes(value)) {
      newValues = values.filter((val) => val !== value)
    } else {
      newValues = [...values, value]
    }
    newValues.forEach((selectedOption: any) => {
      newParams.append("subCategory", selectedOption);
    });
    window.history.pushState(null, '', `${pathname}?${newParams.toString()}`)
  }



  return (
    <div className="listing-head">
      <div className="listing-head_flex">
        <h6>
          <span>{translation?.showing || "Showing"} </span>
          {data} {translation?.products || "Products"}
        </h6>
        <ul>
          {/* {!pathname.includes("contact") && (<li className="listing-head_toggler">
            <Form>
              Try at home
              <Form.Check
                defaultChecked={tryHomeActive.length > 0 ? true : false}
                onChange={(e) => {
                  setTryHomeActive((prevActive) => {
                    if (prevActive.includes("true")) {
                      return prevActive.filter((item) => item !== "true");
                    } else {
                      return [...prevActive, "true"];
                    }
                  });
                }}
                type="switch"
                id="custom-switch"
              />
            </Form>
          </li>)} */}

          <li
            onClick={() => {
              onViewChange("oneCol");
            }}
            className={`listing-head_one-grid ${view === "oneCol" ? "active" : ""
              }`}>
            {/* <Image quality={100} priority src="/images/common/elements1.png" width={24} height={24} alt="image" /> */}
            <div className="bars">
              <span className="bar" ></span>
            </div>
          </li>
          <li
            onClick={() => {
              onViewChange("twoCols");
            }}
            className={`listing-head_two-grids ${filterVisibility ? "" : ""} ${view === "twoCols" ? "active" : ""
              }`}>
            {/* <Image quality={100} priority src="/images/common/bar2.png" width={48} height={48} alt="image" /> */}
            <div className="bars">
              <span className="bar" ></span>
              <span className="bar" ></span>
            </div>
          </li>

          <li
            onClick={() => {
              onViewChange("threeCols");
            }}
            className={`listing-head_three-grids  ${view === "threeCols" ? "active" : ""}`}>
            {/* <Image quality={100} priority src="/images/common/elements3.png" width={48} height={48} alt="image" /> */}
            <div className="bars">
              <span className="bar" ></span>
              <span className="bar" ></span>
              <span className="bar" ></span>
            </div>
          </li>
          {!matches && (
            <li
              onClick={() => {
                onViewChange("fourCols");
              }}
              className={`listing-head_four-grid  ${view === "fourCols" ? "active" : ""}`}>
              {/* <Image quality={100} priority src="/images/common/elements4.png" width={48} height={48} alt="image" /> */}
              <div className="bars">
                <span className="bar" ></span>
                <span className="bar" ></span>
                <span className="bar" ></span>
                <span className="bar" ></span>
              </div>
            </li>
          )}

          <li className="listing-head_sort">
            {translation?.sortBy || "Sort by"}:
            <Dropdown>
              <Dropdown.Toggle id="dropdown-basic">{sort?.label || "New Arrivals"}</Dropdown.Toggle>

              <Dropdown.Menu>
                {sortOptions.map((option) => (
                  <Dropdown.Item
                    key={option.value}
                    onClick={() => {
                      setSort(option);
                      handleFilter(option);
                    }}>
                    {option.label}
                  </Dropdown.Item>
                ))}
              </Dropdown.Menu>
            </Dropdown>
          </li>
        </ul>
      </div>
      <div className="hide-scroll cat-chips" style={{ display: "flex", gap: "1rem", padding: ".5rem 0", overflowX: "auto" }}>
        {mainCats?.length > 1 ? mainCats.map((cat: any) => (
          <Link href={brand? `/products/${cat.slug}?brand=${brand}`: `/products/${cat.slug}`} data-value={cat?._id} key={cat?._id} style={{ backgroundColor: activeCats?.includes(cat?._id) ? "black" : "white", color: activeCats?.includes(cat?._id) ? "white" : "black", border: "1px solid black", padding: ".3rem 1rem", borderRadius: ".6rem", whiteSpace: "nowrap" }}>
            {cat.name}
          </Link>
        )) :
          cats?.map((cat: any) => (
            <button onClick={handleCats} data-value={cat?._id} key={cat?._id} style={{ backgroundColor: activeCats?.includes(cat?._id) ? "black" : "white", color: activeCats?.includes(cat?._id) ? "white" : "black", border: "1px solid black", padding: ".3rem 1rem", borderRadius: ".6rem", whiteSpace: "nowrap" }}>
              {cat.name}
            </button>
          ))
        }
      </div>
    </div>
  );
}

export default ProductListingHead;
