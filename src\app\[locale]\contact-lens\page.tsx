import Image from "next/image";
import "./contact-lens-subscription.scss";
import Faq from "@/components/faq/Faq";
import BreadCrumbs from "@/components/breadcrumbs/BreadCrumbs";
import React, { useRef, useState } from "react";

import { endpoints } from "@/config/apiEndpoints";
import StepsSlider from "@/components/contact-lens/StepsSlider";
import { cookies } from "next/headers";
import { Metadata } from "next";
import GenericBackButton from "@/components/GenericBackButton/GenericBackButton";

export async function generateMetadata({
  params,
}: {
  params: any;
}): Promise<Metadata> {
  const baseURL = process.env.NEXT_PUBLIC_API_URL || "";
  const res = await fetch(`${baseURL}${endpoints.contactLensPage}`, {
    headers: {
      language: params.locale,
    },
  });
  const data = await res.json();
  const aboutData = data.result;
  const lang = params.locale;
  return {
    title: lang === "ar" ? aboutData?.seoDetails?.title?.ar : aboutData?.seoDetails?.title?.en || "Contact Lens  | Yateem Optician",
    description: lang === "ar" ? aboutData?.seoDetails?.description?.ar : aboutData?.seoDetails?.description?.en || "Yateem Optician",

    openGraph: {
      title: lang === "ar" ? aboutData?.seoDetails?.title?.ar : aboutData?.seoDetails?.title?.en || "Contact Lens  | Yateem Optician",
      description: lang === "ar" ? aboutData?.seoDetails?.description?.ar : aboutData?.seoDetails?.description?.en || "Yateem Optician",
      type: "website",
      images: [
        {
          url: aboutData?.seoDetails?.ogImage,
          width: 1200,
          height: 630,
        },
      ],
    },
    alternates: {
      canonical: lang === "ar" ? aboutData?.seoDetails?.canonical?.ar : aboutData?.seoDetails?.canonical?.en,
    },
  };
}

async function getInitialProps(): Promise<{
  contactLensData: any;
  faqData: any;
}> {
  const locale = cookies().get("Next-Locale")?.value || "sa-en";
  const [storeId, language] = locale.split("-");
  
  const [contactLensData, faqData] = await Promise.all([
    fetch(process.env.NEXT_PUBLIC_API_URL + endpoints.contactLensPage, {
      headers: {
        "Content-Type": "application/json; charset=ISO-8859-1'",
        language: language || "en",
        storeid: storeId || "sa"
      },
      next: { tags: ["contact-lens-page"], revalidate: 60 * 60 * 24 },
      method: "GET",
    })
      .then((r) => r.json())
      .catch((error) => "Error"),
    fetch(process.env.NEXT_PUBLIC_API_URL + endpoints.contactLensFaq, {
      method: "GET",
      headers: {
        "Content-Type": "application/json; charset=ISO-8859-1'",
        language: language || "en",
        storeid: storeId || "sa"
      },
      next: { tags: ["contact-lens-page", "contact-faq"] },
    })
      .then((r) => r.json())
      .catch((error) => "Error"),
    ,
  ]);

  return { contactLensData, faqData };
}

async function ContactLensSubscription({
    props
}: {
  props: { locale: string };
}) {
  const { contactLensData, faqData } = await getInitialProps();

  // const res = await fetch(process.env.NEXT_PUBLIC_API_URL + endpoints.contactLensPage, {
  //   headers: {
  //     "Content-Type": "application/json",
  //     language: cookies().get("Next-Locale")?.value || "en",
  //   },
  //   next: { tags: ["contact-lens-page"], revalidate: 60 * 60 * 24 },
  //   method: "GET",
  // });

  // const pageData = await res.json();

  const { result: contactLens } = contactLensData;
  const locale = cookies().get("Next-Locale")?.value || "en"

  return (
    <main>
      <BreadCrumbs
        backHome="Home"
        currentPage={[
          {
            title: `${locale.includes("en")? "Contact Lens": "العدسات اللاصقة"}`,
            link: "/products/contact-lens-2",
          },
          { title: contactLens?.title, link: "" },
        ]}
        image="/images/common/banner2.png"
      />
      <section className="subscription">
        <div className="container">
          <div className="subscription_wrapper">
            <div className="subscription_head position-relative">
              <GenericBackButton style={{ top: "5px" }} />
              <h2>{contactLens?.title}</h2>
              <p>{contactLens?.description}</p>

              <div className="subscription_flex">
                <div className="subscription_content">
                  <h4>{contactLens?.sectionOne?.title}</h4>
                  <p
                    dangerouslySetInnerHTML={{
                      __html: contactLens?.sectionOne?.description,
                    }}
                    style={{ marginTop: "5px" }}
                  ></p>
                  {/* <p style={{ marginTop: "5px" }}>
                    Your deliveries will arrive at your chosen location every three months. Home
                    delivery with Complete Contact Care is free and all of our packages will fit
                    easily through your letterbox. Contact lens solution can also be included in
                    your delivery if needed.
                  </p> */}
                </div>
                <div className="subscription_image">
                  <Image
                    src={
                      contactLens?.sectionOne?.image
                        ?? ""
                    }
                    width={560}
                    height={308}
                    alt="Picture of contact lens subscription"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="guide">
        <div className="container">
          <h2>{contactLens?.sectionTwo?.title} </h2>
          <p>{contactLens?.sectionTwo?.description}</p>
        </div>

        <StepsSlider data={contactLens?.sectionTwo?.steps} />
      </section>

      <section className="insurance-faq">
        <Faq
          data={faqData?.result?.[0]?.faq}
          title={faqData?.result?.[0]?.title}
          description={faqData?.result?.[0]?.description}
        />
      </section>
    </main>
  );
}

export default ContactLensSubscription;
