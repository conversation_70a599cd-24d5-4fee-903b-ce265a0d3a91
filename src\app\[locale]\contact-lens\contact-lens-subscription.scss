.subscription {
  margin-top: 3.5rem;

  @media (max-width: 575.98px) {
    margin-top: 1.5rem;
  }

  &_head {
    p {
      margin-top: 1.3rem;
      line-height: 2.72rem;
    }
  }

  &_flex {
    display: flex;
    column-gap: 5.1rem;
    margin-top: 2.8rem;

    @media (max-width: 767.98px) {
      flex-direction: column;
      margin-top: 2.2rem;
    }
  }

  &_content {
    width: 50%;

    @media (max-width: 767.98px) {
      width: 100%;
    }

    h4 {
      color: #000;
      font-size: 2.4rem;
      font-weight: 700;
      line-height: 3.2rem;

      @media (max-width: 575.98px) {
        font-size: 2rem;
      }
    }
  }

  &_image {
    width: 50%;

    @media (max-width: 767.98px) {
      width: 100%;
      margin-top: 2.5rem;
    }

    img {
      border-radius: 2rem;
    }
  }
}

// GUIDE SECTION STYLE

.guide {
  margin-top: 6.4rem;
  position: relative;
  padding-top: 4.1rem;
  position: relative;

  @media (max-width: 575.98px) {
    padding-top: 0rem;
    margin-top: 4.2rem;
  }

  &::after {
    content: "";
    background: #f2f4f9;
    width: 100%;
    height: 53%;
    position: absolute;
    top: 0;
    z-index: -1;

    @media (max-width: 575.98px) {
      height: 100%;
      background: #fff;
    }
  }

  &::before {
    content: "";
    background: rgb(255, 255, 255);
    width: 100%;
    height: 53%;
    position: absolute;
    bottom: 0;
    z-index: 0;
  }

  h2 {
    line-height: 4.2rem;

    @media (max-width: 575.98px) {
      font-size: 2rem;
      line-height: 2.6rem;
    }
  }

  p {
    text-align: center;
    font-size: 1.8rem;
    font-weight: 400;
    line-height: 3rem;
    max-width: 86rem;
    width: 100%;
    margin: 0 auto;
    margin-top: 1rem;
    color: #52525b;

    @media (max-width: 575.98px) {
      font-size: 1.4rem;
      line-height: 2.4rem;
      margin-top: 1.5rem;
    }
  }

  img {
    width: auto;
    height: auto;
  }

  &_flex {
    display: flex;
  }

  &_slider {
    position: relative;

    &::after {
      content: "";
      height: 0.3rem;
      background-color: #000;
      width: 100%;
      position: absolute;
      top: 148px;
      transform: translateY(-50%);

      @media (max-width: 575.98px) {
        content: none;
      }
    }

    .swiper-wrapper {
      padding-top: 7rem;

      @media (max-width: 575.98px) {
        padding-top: 5rem;
      }

      .swiper-slide {
        text-align: center;
      }
    }

    .swiper-pagination {
      position: unset;
      background-color: #f2f4f9;
      margin-top: 4.1rem;

      @media (min-width: 576px) {
        display: none;
      }

      .swiper-pagination-progressbar-fill {
        background-color: #000;
      }
    }
  }

  &_downarrow {
    width: 10.3279rem;
    height: 10.3356rem;
    border-radius: 50%;
    margin: 0px auto;
    transform: translateY(-30px);
    position: relative;

    &::after {
      content: "";
      background-image: url(../../../../public/images/common/ele.png);
      background-position: center;
      width: 1.9968rem;
      height: 2.1632rem;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background-repeat: no-repeat;
    }

    @media (min-width: 576px) {
      display: none;
    }
  }

  &_round {
    width: 16.5385rem;
    height: 16.5326rem;
    color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    position: relative;
    margin: 0 auto;

    @media (max-width: 575.98px) {
      width: 13.7601rem;
      height: 13.7552rem;
    }

    &::after {
      content: "";
      position: absolute;
      width: 23.8816rem;
      height: 23.8644rem;
      background-color: rgba(255, 255, 255, 0.64);
      z-index: -1;
      border-radius: 50%;

      @media (max-width: 575.98px) {
        width: 19.8697rem;
        height: 19.8553rem;
      }
    }

    &::before {
      content: "";
      position: absolute;
      width: 32rem;
      height: 16rem;
      background-color: #a4c5c6;
      border-radius: 18rem 18rem 0 0;
      top: -77px;
      z-index: -1;

      @media (min-width: 576px) {
        content: none;
      }

      @media (max-width: 575.98px) {
        top: -84px;
      }
    }

    span {
      color: #fff;
      font-size: 1.1878rem;
      font-weight: 400;
    }

    h2 {
      color: #fff;
      font-size: 3.3811rem;
      font-weight: 700;
      line-height: 4.2rem;
    }
  }

  &_content {
    padding-top: 4rem;

    @media (max-width: 575.98px) {
      padding-top: 0.73rem;
    }

    h5 {
      color: #000;
      font-size: 1.7rem;
      font-weight: 600;
      line-height: 2.1rem;

      @media (max-width: 575.98px) {
        max-width: 19.2rem;
        margin: 0 auto;
        font-size: 1.5rem;
      }
    }

    p {
      margin: 0 auto;
      margin-top: 1.3rem;
      color: #343434;
      font-size: 1.6rem;
      font-weight: 400;
      line-height: 2rem;
      max-width: 23.3rem;
      width: 100%;

      @media (max-width: 575.98px) {
        font-size: 1.4rem;
      }
    }
  }
}

.insurance-faq {
  .faq {
    margin-top: 8.8rem;

    @media (max-width: 575.98px) {
      margin-top: 2.5rem;
    }
  }
}
