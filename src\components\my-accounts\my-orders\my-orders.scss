.orders {
  margin-bottom: 12.6rem;

  @media (max-width: 575.98px) {
    margin-bottom: 5rem;
  }

  h2 {
    margin-bottom: 3rem;

    @media (max-width: 1199.98px) {
      margin-top: 4.4rem;
    }

    @media (max-width: 575.98px) {
      margin-bottom: 2.8rem;
      margin-top: 1.5rem;
    }
  }

  &_body {
    border-radius: 0.8rem;
    background: fff;
    padding: 2.8rem 5.5rem;
    border: 2px solid #f2f4f9;

    @media (max-width: 767.98px) {
      padding: 1.8rem 1.9rem 3.1rem 1.9rem;
    }

    &:not(:last-child) {
      margin-bottom: 4.1rem;
    }

    &:first-of-type {
      background-color: #f2f4f9;
    }
  }

  &_top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    row-gap: 1.7rem;

    @media (max-width: 575.98px) {
      row-gap: 0;
      flex-direction: column;
      align-items: flex-start;
    }
  }

  &_id {
    h5 {
      color: #000;
      font-size: 2rem;
      font-weight: 400;
      line-height: 2.5rem;

      @media (max-width: 575.98px) {
        font-size: 1.7rem;
        line-height: 2.1rem;
      }
    }

    h6 {
      color: #000;
      font-size: 1.4rem;
      font-weight: 400;
      margin-top: 1.2rem;

      @media (max-width: 575.98px) {
        margin-top: 0.5rem;
        font-size: 1.3rem;
        line-height: 1.63rem;
      }

      &:last-child {
        margin-top: 0.6rem;

        @media (max-width: 575.98px) {
          margin-top: 1.6rem;
        }
      }

      span {
        color: #595959;
      }
    }
  }

  &_status {
    align-self: flex-end;

    @media (max-width: 575.98px) {
      align-self: flex-start;
    }

    h6 {
      color: #000;
      font-size: 1.4rem;
      font-weight: 400;
      margin-top: 1.2rem;

      @media (max-width: 575.98px) {
        margin-top: 0.2rem;
        font-size: 1.3rem;
        line-height: 1.63rem;
      }

      &:last-child {
        margin-top: 0.6rem;

        @media (max-width: 575.98px) {
          margin-top: 0.2rem;
        }
      }

      span {
        color: #595959;
      }
    }
  }

  &_btn {
    @media (max-width: 575.98px) {
      margin-top: 1.7rem;
    }

    .view-detail {
      margin-right: 4.4rem;

      @media (max-width: 991.98px) {
        margin-right: 1.6rem;
      }
    }
  }

  &_bottom {
    display: flex;
    margin-top: 2.3rem;
    padding-top: 2.3rem;
    border-top: 1px solid #bebcbd;
    align-items: center;

    @media (max-width: 991.98px) {
      flex-direction: column;
      align-items: flex-start;
    }

    @media (max-width: 575.98px) {
      border: none;
    }

  }

  &_detail {
    display: flex;
    column-gap: 2.5rem;
    align-items: center;

    @media (max-width: 991.98px) {
      margin-bottom: 3rem;
    }

    @media (max-width: 575.98px) {
      padding-bottom: 2.5rem;
      margin-bottom: 2rem;
      column-gap: 2.1rem;
      border-bottom: 1px solid #bebcbd;
      width: 100%;
    }

    &:nth-of-type(2) {
      margin-left: 10.3rem;

      @media (max-width: 991.98px) {
        margin-left: 0;
      }
    }

    img {
      width: 12rem;
      height: 12rem;
      border-radius: 1.2rem;
      object-fit: contain;

      @media (max-width: 575.98px) {
        width: 9.6rem;
        height: 12.5rem;
      }
    }

    &-modal {
      color: #000;
      font-size: 1.6rem;
      font-weight: 600;
      line-height: 2rem;
      word-wrap: break-word;
      max-width: 300px;
    }

    &-color {
      color: #807d7e;
      font-size: 1.4rem;
      font-weight: 400;
      margin-top: 1.1rem;
      line-height: 1.7rem;

      @media (max-width: 575.98px) {
        margin-top: 0.5rem;
      }
    }

    &-size {
      color: #807d7e;
      font-size: 1.4rem;
      font-weight: 400;
      margin-top: 4px;
      line-height: 1.7rem;
    }

    &-price {
      color: #000;
      font-size: 1.8rem;
      font-weight: 600;
      margin-top: 1rem;
      line-height: 2.2rem;

      @media (max-width: 575.98px) {
        margin-top: 1.8rem;
        font-size: 1.6rem;
      }
    }
  }

  &_products {
    flex: 0 0 auto;

    @media (max-width: 991.98px) {
      margin: 0 auto;
      margin-top: 3rem;
    }

    a {
      color: #000;
      font-size: 1.8rem;
      text-decoration-line: underline;
      font-weight: 400;
    }
  }
}