.singup-popup {
  &.modal {
    .modal-dialog {
      max-width: 68.3rem;
      width: 100%;
      margin: 0;
      margin-left: auto;
    }

    .modal-header {
      padding: 0;
      border: none;

      .btn-close {
        background-image: url(../../../../public/images/common/close.png);
        width: 4.8rem;
        height: 4.8rem;
        background-position: center;
        background-repeat: no-repeat;
        background-size: contain;
        opacity: 1;
        padding: 0;

        @media (max-width: 575.98px) {
          width: 2.8rem;
          height: 2.8rem;
        }

        &:focus {
          box-shadow: none;
          outline: none;
          border: none;
        }
      }
    }

    .modal-content {
      padding: 4rem 4.5rem 5rem 16.7rem;
      border: none;
      background: #fff;
      border-radius: 0;
      height: 100vh;
      overflow-y: auto;

      @media (max-width: 575.98px) {
        padding: 5.4rem 2rem 4.2rem 2rem;
      }
    }

    .modal-body {
      padding: 0;
      display: flex;
      justify-content: center;
      flex-direction: column;

      h2 {
        line-height: 3.6rem;
        color: #242731;
        font-weight: 700;
        max-width: 40.4rem;
        width: 100%;
        text-align: left;
      }

      p {
        margin-top: 1.6rem;
        color: #575f6e;
        font-size: 1.6rem;
        font-weight: 300;
        line-height: 2.2rem;
        max-width: 35rem;
        width: 100%;
      }

      .ingup-popup-fill {
        margin-top: 2.8rem;

        .ingup-popup-inputs {
          max-width: 35rem;
          position: relative;

          &:not(:last-child) {
            margin-bottom: 2.8rem;

            @media (max-width: 575.98px) {
              margin-bottom: 3.5rem;
            }
          }

          label {
            color: #242426;
            font-size: 1.4rem;
            font-weight: 400;
            line-height: 2rem;
            padding-bottom: 0.8rem;
          }

          input {
            display: block;
            border: none;
            border-bottom: 1px solid #e2e4e5;
            color: #242426;
            font-size: 1.8rem;
            font-weight: 400;
            line-height: 2.8rem;
            width: 100%;
            padding-left: 1.6rem;
            padding-bottom: 0.8rem;

            &::placeholder {
              color: #808080;
              font-size: 1.8rem;
              font-weight: 400;
            }

            &:focus-within {
              outline: none;
              border-bottom: 1px solid #e2e4e5;
            }
          }

          select {
            display: block;
            border: none;
            border-bottom: 1px solid #e2e4e5;
            color: #242426;
            font-size: 1.8rem;
            font-weight: 400;
            line-height: 2.8rem;
            width: 100%;
            padding-left: 1.6rem;
            padding-bottom: 0.8rem;
            appearance: none;

            &::placeholder {
              color: #808080;
              font-size: 1.8rem;
              font-weight: 400;
            }

            &:focus-within {
              outline: none;
              border-bottom: 1px solid #e2e4e5;
            }
          }

          &.select {
            position: relative;

            &::placeholder {
              color: #808080;
              font-size: 1.8rem;
              font-weight: 400;
            }

            &::after {
              content: "";
              background-image: url(../../../../public/images/common/Icon.png);
              width: 2.4rem;
              height: 2.4rem;
              position: absolute;
              top: 29px;
              right: 0;
            }
          }

          &.radio {
            display: flex;
            align-items: center;
            justify-content: space-between;

            span {
              margin-right: auto;
            }

            label {
              padding-bottom: 0;
              display: flex;
              column-gap: 0.8rem;
            }

            input {
              appearance: none;
              width: 2rem;
              height: 2rem;
              border-radius: 50%;
              border: 1px solid #000;
              position: relative;

              &:checked {
                &::after {
                  content: "";
                  width: 1.2rem;
                  height: 1.2rem;
                  background-color: #000;
                  position: absolute;
                  top: 50%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                  position: absolute;
                  border-radius: 50%;
                }
              }
            }
          }
        }

        .ingup-popup-btn {
          display: flex;
          column-gap: 1.6rem;
          margin-top: 0.5rem;

          .button {
            width: 15.9rem;
            height: 4.5rem;
            font-size: 1.5rem;
            font-weight: 500;
            border-radius: 6rem;
            border: 0;
          }

          .save {
            background-color: #000;
            color: #fff;
          }

          .skip {
            background-color: transparent;
            border: 1px solid #000;
            color: #000000;
          }
        }
      }
    }
  }
}