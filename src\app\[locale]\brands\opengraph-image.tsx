import OpengraphImage from "@/components/opengraph-image";
import { ImageResponse } from "next/og";
export const runtime = "edge";
export default async function Image() {
  const baseURL = process.env.NEXT_PUBLIC_API_URL;
  const res = await fetch(`${baseURL}brands`);
  const data = await res.json();
  const brands = data.result;
  return await OpengraphImage({ title: `${brands?.length} Brands available in Yateem Optician` });
}
