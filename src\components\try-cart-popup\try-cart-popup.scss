.try-cart-popup {
  &.modal {
    &.show .modal-dialog {
      transform: none !important;
    }

    .modal-dialog {
      margin: 0;
      position: fixed;
      right: 0;
      width: 100%;
      max-width: 68.3rem;
      background: #f2f4f9;
      height: 100%;
    }

    .modal-content {
      border: none;
      border-radius: 0;
      padding: 4rem 4rem 4.8rem 3rem;

      @media (max-width: 575.98px) {
        padding: 4rem 2.2rem 3.2rem 2.2rem;
      }

      h2 {
        color: #000;
        font-size: 3.2rem;
        font-weight: 500;
        line-height: 4rem;
        text-align: left;
        margin-top: 4rem;

        @media (max-width: 575.98px) {
          line-height: 2.6rem;
          font-size: 2.4rem;
          margin-top: 0;
        }
      }
    }

    .modal-header {
      padding: 0;
      border: none;

      .btn-close {
        background-image: url(../../../public/images/common/close.png);
        width: 4.8rem;
        height: 4.8rem;
        background-position: center;
        background-repeat: no-repeat;
        background-size: contain;
        opacity: 1;
        padding: 0;

        @media (max-width: 575.98px) {
          width: 2.8rem;
          height: 2.8rem;
          transform: translateY(-20px);
        }

        &:focus {
          box-shadow: none;
          outline: none;
          border: none;
        }
      }
    }

    .modal-body {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 1.5rem;
      border: none;
      padding: 0;
      margin-top: 3.2rem;

      @media (max-width: 575.98px) {
        grid-template-columns: repeat(1, 1fr);
        margin-top: 2.5rem;
      }

      .try-cart-flex {
        display: flex;
        column-gap: 1.2rem;
        border-radius: 1.3rem;
        background: #fff;
        padding: 1rem;
        border: 1px solid #E4E4E4;

        @media (max-width: 575.98px) {
          width: 100%;
        }

        &.add-new {
          column-gap: 3.3rem;
          align-items: center;
          padding: 1.3rem 1rem 2.6rem 3.2rem;

          img {
            width: 3.2rem;
            height: 3.2rem;
            border-radius: 0;
          }

          .try-cart-add {
            h5 {
              color: #807d7e;
              font-size: 1.6rem;
              font-weight: 500;
              letter-spacing: 0.032rem;
              line-height: 2rem;
            }

            button {
              color: #000;
              font-size: 1.4rem;
              font-weight: 600;
              text-decoration-line: underline;
              background: none;
              margin-top: 1.5rem;
            }
          }
        }

        .try-cart-items {
          width: 100%;

          li {
            display: flex;
            align-items: center;
            justify-content: space-between;
            color: #807d7e;
            font-size: 1.4rem;
            font-weight: 400;
            line-height: 1.7rem;

            span {
              color: #000;
              font-size: 1.6rem;
              font-weight: 600;
            }

            &:nth-of-type(1) {
              color: #000;
              font-size: 1.6rem;
              font-weight: 600;
              letter-spacing: 0.032rem;
              line-height: 2rem;
              margin-bottom: 1.1rem;
            }

            &:nth-of-type(2) {
              margin-bottom: 4px;
            }
          }
        }

        img {
          width: 7.5rem;
          height: 7.5rem;
          border-radius: 1.2rem;
          object-fit: contain;
        }

        button {
          border: none;
          background-color: transparent;
          padding: 0;
        }
      }
    }

    .modal-footer {
      border: none;
      padding: 0;
      padding-bottom: 1rem;
      margin-top: 3rem;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: flex-start;

      @media (max-width: 575.98px) {
        margin-top: 1.6rem;
      }

      .try-cart-footer {
        margin: 0;

        @media (max-width: 575.98px) {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;
          padding: 1rem 0.8rem 1rem 1.2rem;
          border-radius: 1.3rem;
          background-color: #fff;

        }
      }

      p {
        font-size: 1.4rem;
        font-weight: 400;
        line-height: 1.7rem;
        color: #1F2738;
      }

      h5 {
        color: #1f2738;
        font-size: 1.4rem;
        font-weight: 400;
        line-height: 1.7rem;
        margin-top: 1px;
      }

      h6 {
        color: #000;
        font-size: 1rem;
        font-weight: 400;
        line-height: 1.2rem;
      }

      button {
        border-radius: 6rem;
        background: #000;
        color: #fff;
        font-size: 1.5rem;
        font-weight: 500;
        border: none;
        height: 5rem;
        margin: 0;
        padding: 0 3.2rem;
        margin-top: 4rem;

        &.disabled {
          background-color: #797A7D;
        }

        @media (max-width: 575.98px) {
          margin: 0 auto;
          margin-top: 2rem;
          padding: 1.2rem 6.7rem;
        }
      }
    }
  }
}

.add-try-cart{
  img{
    filter: invert(1);
    &:hover{
      filter: none;
    }
  }
}