"use client";
import Nav from "react-bootstrap/Nav";
import Navbar from "react-bootstrap/Navbar";
import Offcanvas from "react-bootstrap/Offcanvas";
import Select from "react-select";
import "./header.scss";
import Image from "next/image";
import Link from "next/link";
import { useContext, useRef } from "react";
import { useParams, usePathname, useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import TryCartPopup from "../try-cart-popup/TryCartPopup";
import Dropdown from "react-bootstrap/Dropdown";
import { motion } from "framer-motion";
import Form from "react-bootstrap/Form";
import { AuthContext } from "@/contexts/AuthProvider";
import { logout } from "@/lib/methods/auth";
import { AppProgressBar as ProgressBar } from "next-nprogress-bar";
import ImageCreator from "../ImageCreator";
import { useOnClickOutside } from "usehooks-ts";
import { HistoryContext } from "@/contexts/HistoryProvider";
import { useQuery } from "@tanstack/react-query";
import api from "@/config/axios.interceptor";
import { endpoints } from "@/config/apiEndpoints";
import { OverlayTrigger, Tooltip } from "react-bootstrap";
import Search from "./Search";
import { toast } from "sonner";
import Accordion from "react-bootstrap/Accordion";
import { TranslationContext } from "@/contexts/Translation";
import { CircleFlag } from "react-circle-flags";
import { LocaleContext, useLocaleContext } from "@/contexts/LocaleProvider";

const redirectionTypes = {
  listing: "/products/",
  category: "/products/",
  page: "",
  product: "/product/",
};
const subMenuAnimate = {
  enter: {
    opacity: 1,
    height: 294,
    //scaleY: 1,
    transition: {
      duration: 1,
    },
    display: "block",
  },
  exit: {
    opacity: 0,
    //scaleY: 0,
    rotateX: -15,
    height: 0,
    transition: {
      duration: 1,
      delay: 0.3,
    },
    transitionEnd: {
      display: "none",
    },
  },
};

const jsonData = [
  {
    image: "/images/common/mm1.png",
    hover_image: "/images/common/mm1.png",
    title: "Eyeglasses",
  },

  {
    image: "/images/common/mm2.png",
    hover_image: "/images/common/mm3.png",
    title: "Contact lenses",
  },

  {
    image: "/images/common/mm3.png",
    hover_image: "/images/common/mm3.png",
    title: "Sunglasses",
  },

  {
    image: "/images/common/mm4.png",
    hover_image: "/images/common/mm4.png",
    title: "Computer Glasses",
  },
];

const tooltip = (
  <Tooltip id="tooltip" className="lead">
    Hit <kbd>Enter</kbd> to Search!.
  </Tooltip>
);

function Header({ navData, stores }: any) {
  const cartRef = useRef<any>(null);
  const wishListRef = useRef<any>(null);
  const subMenuRef = useRef(null);
  const { userProfile } = useContext(AuthContext);
  const bsToggleRef = useRef(null);
  const searchParams = useSearchParams();
  const router = useRouter();
  const { translation }: any = useContext(TranslationContext)

  const [cartDropShow, setCartDropShow] = useState(false);

  const countries = stores?.map((store: any) => {
    return {
      value: store?.storeId,
      label: (
        <div className="drop-item">
          <CircleFlag countryCode={store?.storeId} />
          {store?.name}
        </div>
      ),
    };
  })

  useEffect(() => {
    const fromPath = searchParams.get("from");
    if (fromPath) {
      router.replace("/login");
    }
  }, [searchParams]);

  const pathname = usePathname();
  const [isSticky, setSticky] = useState(false);
  const [query, setQuery] = useState("");
  const { history, setHistory, isProfileMenuOpen, setIsProfileMenuOpen } =
    useContext(HistoryContext);
  const [menuOpen, setMenuOpen] = useState(false);
  const closeMenu = () => {
    setMenuOpen(!menuOpen);
    setIsProfileMenuOpen(false);
  };
  const [show, setShow] = useState(false);
  const handleClose = () => setShow(false);
  const handleShow = () => {
    // router.push("/coming-soon");
    if (!userProfile) {
      router.push("/login");
      toast.error("You need to login first!");
      return;
    }
    if (userProfile?.isGuest) {
      toast.error("Try cart is not available for guest users!");
      router.push("/login");
      return;
    }
    if (userProfile?.isTryCart) {
      router.push("/my-accounts/try-cart-order");
      return;
    } else {
      setShow(true);
    }
  };

  const [isHover, toggleHover] = useState(false);
  const [subMenuData, setSubMenuData] = useState<any>(null);

  const hideHoverMenu = () => {
    toggleHover(false);
  };

  const [searchVisible, setSearchVisible] = useState(false);

  const setCurrentPage = () => {
    setHistory({
      ...history,
      beforeSearch: { pathname, title: document?.title },
    });
    // router.push("/search");
  };


  const handleSearch = (e: any) => {
    e?.preventDefault();
    // router.push(`/search?q=${query}`);
    window.history.pushState(null, '', `/search?q=${query}`)
    window.scrollTo(0, 0);
  };

  const {
    data: counts,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ["cart", "counts"],
    queryFn: () => {
      return api.get(endpoints.counts).then((res) => {
        if (res.status === 200) {
          return res.data?.result;
        } else throw error;
      });
    },
  });

  useEffect(() => {
    if (pathname === "/ar" || pathname === "/en") {
      document?.body.classList.add("home-body");
    } else {
      document?.body.classList.remove("home-body");
    }
    if (pathname.includes("/search")) {
      setSearchVisible(true);
    } else {
      setSearchVisible(false);
    }
  }, [pathname]);

  useEffect(() => {
    if (typeof window === "undefined") return;
    const handleScroll = () => {
      const offset = window.scrollY;
      const header = document.getElementById('header');

      if (offset > 100 && !pathname.includes("search")) {
        // setSticky(true);
        header?.classList.add("sticky")
      } else {
        header?.classList.remove("sticky")
        // setSticky(false);
      }
    };
    handleScroll();

    const value = cartRef?.current?.getBoundingClientRect();

    window.addEventListener("scroll", handleScroll);

    // Cleanup event listener on component unmount
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  const handleHover = (menuItem: any) => {
    return () => {
      toggleHover(false);
      if (menuItem?.isSubmenu && menuItem?.submenus?.length > 0) {
        setSubMenuData(menuItem?.submenus);
        toggleHover(true);
      }
    };
  };

  const closeSearch = () => {
    setQuery("");
    router.push(history?.beforeSearch?.pathname || "/");
    setSearchVisible(false);
  };
  useOnClickOutside(subMenuRef, hideHoverMenu);

  // const countries = [
  //   {
  //     value: "oman",
  //     label: (
  //       <div className="drop-item">
  //         <img src="/icons/flags/om.svg" alt="" />
  //         Oman
  //       </div>
  //     ),
  //   },
  //   {
  //     value: "uae",
  //     label: (
  //       <div className="drop-item">
  //         <img src="/icons/flags/ae.svg" alt="" />
  //         UAE
  //       </div>
  //     ),
  //   },
  // ];
  const languages = [
    {
      value: "ar",
      label: <p className="drop-item">العربية</p>,
    },
    {
      value: "en",
      label: <p className="drop-item">English</p>,
    },
  ];

  const { changeLocale, currentLocale } = useLocaleContext()

  const [selectedCountry, setSelectedCountry] = useState<any>(() => {
    const country = currentLocale.split("-")[0]
    return countries?.find((e: any) => e.value === country)
  });
  const [selectedLanguage, setSelectedLanguage] = useState<any>(
    languages?.filter((e) => e.value === currentLocale.split("-")[1])[0]
  );

  const toggleLanguage = (e: any) => {
    let locale: any = currentLocale.split("-")[0]
    locale = `${locale}-${e.value}`
    changeLocale(locale);
    setSelectedLanguage(e);
  };

  const toggleCountry = (e: any) => {
    let locale: any = currentLocale.split("-")[1]
    locale = `${e.value}-${locale}`
    changeLocale(locale);
    setSelectedCountry(e);
  };

  return (
    <>
      <header
        id="header"
        className={`${
          // isSticky && !pathname.includes("search") ? "sticky" : ""
          ""
          } ${pathname === "/" ? "home-nav header" : "header"}`}
      >
        <div className={`header_wrapper  ${searchVisible ? "hidden" : ""}`}>
          <div className="container">
            {["xl"].map((expand) => (
              <Navbar className="p-0" key={expand} expand={expand}>
                <div className="header_navbar">
                  <div className="d-flex" style={{ columnGap: "16px" }}>
                    <Navbar.Toggle
                      className="d-block d-sm-none"
                      aria-controls={`offcanvasNavbar-expand-${expand}`}
                      onClick={() => setIsProfileMenuOpen(true)}
                    />

                    <Link data-disable-nprogress={true} href={"/"}>
                      <Navbar.Brand className="m-0 p-0">
                        <Image
                          quality={100}
                          priority
                          className="header_navbar-logo"
                          // src={"/images/logos/yateem-white-logo.svg"}
                          src={navData?.logo ?? "/images/logos/yateem-white-logo.svg"}
                          width={170}
                          height={35}
                          alt="logo"
                        />
                      </Navbar.Brand>
                    </Link>
                  </div>

                  {!userProfile && (
                    <Link href={"/login"} style={currentLocale.includes("en") ? { marginLeft: "auto" } : { marginRight: "auto" }} className="header_log d-block d-sm-none">
                      <svg fill="#ffffff" width="30" height="30" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" xmlSpace="preserve"><g id="SVGRepo_bgCarrier" strokeWidth="0"></g><g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round"></g><g id="SVGRepo_iconCarrier"> <g> <g> <path d="M80,71.2V74c0,3.3-2.7,6-6,6H26c-3.3,0-6-2.7-6-6v-2.8c0-7.3,8.5-11.7,16.5-15.2c0.3-0.1,0.5-0.2,0.8-0.4 c0.6-0.3,1.3-0.3,1.9,0.1C42.4,57.8,46.1,59,50,59c3.9,0,7.6-1.2,10.8-3.2c0.6-0.4,1.3-0.4,1.9-0.1c0.3,0.1,0.5,0.2,0.8,0.4 C71.5,59.5,80,63.9,80,71.2z"></path> </g> <g> <ellipse cx="50" cy="36.5" rx="14.9" ry="16.5"></ellipse> </g> </g> </g></svg>
                      {/* <span>
                        {translation?.myAccount?.login ?? "Login"}
                      </span> */}
                    </Link>
                  )}
                  {userProfile && <Link
                    style={currentLocale.includes("en") ? { marginLeft: "auto" } : { marginRight: "auto" }}
                    // data-disable-nprogress={true}
                    href={"/my-accounts"}
                    className="d-block d-sm-none"
                  >
                    <div className="avatar">
                      {userProfile?.image && (
                        <Image
                          quality={100}
                          priority
                          src={userProfile?.image}
                          fill
                          alt="user"
                        />
                      )}
                      {!userProfile?.image && (
                        <ImageCreator
                          text={userProfile?.name}
                          phone={userProfile?.mobile}
                          font="16px"
                        />
                      )}
                    </div>
                  </Link>}
                  <Link

                    data-disable-nprogress={true}
                    className="d-block d-xl-none"
                    onClick={() => {
                      setCurrentPage();
                      setMenuOpen(false);
                      setIsProfileMenuOpen(false);
                    }}
                    href="/search"
                  >
                    <Image
                      quality={100}
                      priority
                      className="searchIcon"
                      src={"/images/home/<USER>/search.svg"}
                      width={24}
                      height={24}
                      alt="logo"
                    />
                  </Link>
                  <Navbar.Offcanvas
                    show={isProfileMenuOpen}
                    onHide={closeMenu}
                    id={`offcanvasNavbar-expand-${expand}`}
                    aria-labelledby={`offcanvasNavbarLabel-expand-${expand}`}
                    placement="start"
                  >
                    <Offcanvas.Header closeButton>
                      <Offcanvas.Title
                        id={`offcanvasNavbarLabel-expand-${expand}`}
                      >
                        <Image
                          quality={100}
                          priority
                          className="header_navbar-logo"
                          // src={"/images/logos/yateem-white-logo.svg"}
                          src={navData?.logo ?? "/images/logos/yateem-white-logo.svg"}
                          width={170}
                          height={35}
                          alt="logo"
                        />
                      </Offcanvas.Title>
                    </Offcanvas.Header>
                    <Offcanvas.Body>
                      <Nav className="header_links align-items-md-center">
                        {/* <Link data-disable-nprogress={true}  onMouseEnter={showHoverMenu} href="/products/sunglasses">
                          Sunglasses
                        </Link> */}
                        {navData?.header?.map((item: any) => (
                          <Link
                            data-disable-nprogress={true}
                            onMouseEnter={handleHover(item)}
                            key={item?._id}
                            // href={
                            //   item?.isSubmenu
                            //     ? ""
                            //     : `${
                            //         redirectionTypes?.[
                            //           item?.menuType as keyof typeof redirectionTypes
                            //         ]
                            //       }${item?.redirection}`
                            // }
                            href={item?.link || ""}
                          >
                            {item?.title}
                          </Link>
                        ))}
                        {/* <Link data-disable-nprogress={true}  href="/products/sunglasses">Eyeglasses</Link>
                        <Link data-disable-nprogress={true}  href="/products/sunglasses">Contact Lens</Link>
                        <Link data-disable-nprogress={true}  href="/products/sunglasses">Ophthalmic lense</Link>
                        <Link data-disable-nprogress={true}  href="/insurance">Insurance</Link>
                        <Link data-disable-nprogress={true}  href="/brands">Brands</Link> */}
                      </Nav>

                      <div className="header_icons">
                        <div
                          style={{ padding: "0 1.4rem", position: "relative" }}
                          className="d-flex justify-content-between align-items-center d-xl-none"
                        >
                          <div className="header_intro">
                            <h4 style={{ direction: currentLocale.includes("en") ? "ltr" : "rtl" }}>{currentLocale.includes("en") ? "Hi" : "أهلاً"},{" "}{userProfile?.name && userProfile?.name?.trim()?.toLowerCase() != "guest" ? `${userProfile?.name}` : (currentLocale.includes("en") ? "Guest" : "ضيف")} !</h4>
                            <Link data-disable-nprogress={true} href="">
                              {userProfile?.email}
                            </Link>
                          </div>

                          {userProfile && (
                            <div onClick={() => logout(translation)} className="header_log btn">
                              <Image
                                quality={100}
                                priority
                                src="/images/common/login.svg"
                                width={24}
                                height={24}
                                alt="logo"
                              />
                              <span>
                                {translation?.myAccount?.logout ?? "Logout"}
                              </span>
                            </div>
                          )}
                          {!userProfile && (
                            <Link href={"/login"} className="header_log btn">
                              <Image
                                quality={100}
                                priority
                                src="/images/common/login.svg"
                                width={24}
                                height={24}
                                alt="logo"
                              />
                              <span>
                                {translation?.myAccount?.login ?? "Login"}
                              </span>
                            </Link>
                          )}
                        </div>

                        <ul className="header_iconlist">
                          <li className="d-xl-none d-flex" style={{ gap: "1rem", justifyContent: "space-between", padding: "1rem 0" }} >
                            <Select
                              isSearchable={false}
                              components={{
                                IndicatorSeparator: () => null,
                              }}
                              classNames={{
                                control: (state) => (state.isFocused ? "select" : "select"),
                                menu: (state) => "menu",
                                indicatorsContainer: () => "indicators-container",
                              }}
                              defaultValue={selectedCountry}
                              onChange={toggleCountry}
                              options={countries}
                            />
                            <Select
                              isDisabled
                              isSearchable={false}
                              styles={{ container: (base) => ({ ...base, opacity: 0.5 }) }}
                              components={{
                                IndicatorSeparator: () => null,
                              }}
                              classNames={{
                                control: (state) => (state.isFocused ? "select" : "select"),
                                menu: (state) => "menu",
                                indicatorsContainer: () => "indicators-container",
                              }}
                              defaultValue={selectedLanguage}
                              onChange={toggleLanguage}
                              options={languages}
                            />
                          </li>
                          <li>
                            <Link
                              data-disable-nprogress={true}
                              onClick={() => {
                                setCurrentPage();
                                setMenuOpen(false);
                                setIsProfileMenuOpen(false);
                              }}
                              href="/search"
                            >
                              <Image
                                quality={100}
                                priority
                                src={"/images/home/<USER>/search.svg"}
                                width={24}
                                height={24}
                                alt="logo"
                              />
                            </Link>
                            <Link
                              data-disable-nprogress={true}
                              className="header_iconlist-link"
                              onClick={() => {
                                setCurrentPage();
                                setMenuOpen(false);
                                setIsProfileMenuOpen(false);
                              }}
                              href="/search"
                            >
                              {translation?.myAccount?.search ?? "Search"}
                            </Link>
                          </li>

                          <li className="d-xl-none d-flex">
                            <Link onClick={closeMenu} href="/my-accounts">
                              <Image
                                quality={100}
                                priority
                                src={"/images/home/<USER>/user-edit-01.svg"}
                                width={24}
                                height={24}
                                alt="logo"
                              />
                            </Link>
                            <Link
                              onClick={closeMenu}
                              className="header_iconlist-link"
                              href="/my-accounts"
                            >
                              {translation?.myAccount?.myProfile ?? "My Profile"}
                            </Link>
                          </li>

                          <li className="d-xl-none d-flex">
                            <Link onClick={closeMenu} href="/my-accounts/my-cashbacks">
                              <Image
                                quality={100}
                                priority
                                src={"/images/home/<USER>/myOrders.svg"}
                                width={24}
                                height={24}
                                alt="logo"
                              />
                            </Link>
                            <Link
                              onClick={closeMenu}
                              className="header_iconlist-link"
                              href="/my-accounts/my-cashbacks"
                            >
                              {translation?.myAccount?.myCashbacks ?? "My Cashbacks"}
                            </Link>
                          </li>

                          <li>
                            <Dropdown ref={wishListRef} onClick={closeMenu}>
                              <Link
                                href={"/my-accounts/my-wishlist"}
                                data-disable-nprogress={true}
                              >
                                <Dropdown.Toggle id="dropdown-wishlist">
                                  <Image
                                    quality={100}
                                    priority
                                    src={"/images/home/<USER>/Heart.svg"}
                                    width={24}
                                    height={24}
                                    alt="logo"
                                  />
                                  {counts?.wishlist > 0 && (
                                    <span className="white-dot">
                                      {" "}
                                      {counts?.wishlist
                                        ? `${counts.wishlist}`
                                        : ""}
                                    </span>
                                  )}
                                </Dropdown.Toggle>
                              </Link>
                            </Dropdown>
                            <Link
                              onClick={closeMenu}
                              data-disable-nprogress={true}
                              className="header_iconlist-link"
                              href="/my-accounts/my-wishlist"
                            >
                              {translation?.myAccount?.myWishlist ?? "My Wishlist"}
                              {counts?.wishlist ? `(${counts.wishlist})` : ""}
                            </Link>
                          </li>

                          <li>
                            <Dropdown
                              ref={cartRef}
                              onClick={closeMenu}
                            // onMouseEnter={() => setCartDropShow(true)}
                            // onMouseLeave={() => setCartDropShow(false)}
                            >
                              <Link
                                href={"/cart"}
                                data-disable-nprogress={true}
                              >
                                <Dropdown.Toggle id="dropdown-cart">
                                  <Image
                                    quality={100}
                                    priority
                                    src={"/images/common/shopping-basket.svg"}
                                    width={24}
                                    height={24}
                                    alt="logo"
                                  />
                                  {counts?.cart > 0 && (
                                    <span className="white-dot">
                                      {" "}
                                      {counts?.cart ? `${counts.cart}` : ""}
                                    </span>
                                  )}
                                </Dropdown.Toggle>
                              </Link>

                              {/* <Dropdown.Menu
                                show={false}
                                // onMouseLeave={() => setCartDropShow(false)}
                              >
                                <Dropdown.Item>
                                  <Link
                                    data-disable-nprogress={true}
                                    href={"/cart"}
                                  >
                                    <Image  quality={100}  priority
                                      src={"/images/home/<USER>/cart.svg"}
                                      width={22}
                                      height={20}
                                      alt="logo"
                                    />
                                    My Cart{" "}
                                    {counts?.cart ? `(${counts.cart})` : ""}
                                  </Link>
                                </Dropdown.Item>
                                <Dropdown.Item onClick={handleShow}>
                                  <Image  quality={100}  priority
                                    src={"/images/home/<USER>/tryCart.svg"}
                                    width={22}
                                    height={20}
                                    alt="logo"
                                  />
                                  Try Cart{" "}
                                  {counts?.tryCart ? `(${counts.tryCart})` : ""}
                                </Dropdown.Item>
                              </Dropdown.Menu> */}
                            </Dropdown>

                            <Link
                              data-disable-nprogress={true}
                              className="header_iconlist-link"
                              href="/cart"
                              onClick={() => setIsProfileMenuOpen(false)}
                            >
                              {translation?.myAccount?.myCart ?? "My Cart"}{counts?.cart ? `(${counts.cart})` : ""}
                            </Link>
                          </li>
                          {/* desktop */}
                          <li>
                            {userProfile && (
                              <Dropdown className="d-none d-xl-block">
                                <Dropdown.Toggle
                                  id="dropdown-profile"
                                  title={userProfile?.name || (translation?.myAccount?.login ?? "Login")}
                                >
                                  <div className="avatar">
                                    {userProfile?.image && (
                                      <Image
                                        quality={100}
                                        priority
                                        src={userProfile?.image}
                                        fill
                                        alt="user"
                                      />
                                    )}
                                    {!userProfile?.image && (
                                      <ImageCreator
                                        text={userProfile?.name}
                                        phone={userProfile?.mobile}
                                        font="16px"
                                      />
                                    )}
                                  </div>
                                </Dropdown.Toggle>

                                <Dropdown.Menu>
                                  <Dropdown.Item href="/my-accounts">
                                    <Link
                                      data-disable-nprogress={true}
                                      href="/my-accounts"
                                    >
                                      <Image
                                        quality={100}
                                        priority
                                        src={
                                          "/images/home/<USER>/user-edit-01-black.svg"
                                        }
                                        width={22}
                                        height={20}
                                        alt="logo"
                                      />
                                      {translation?.myAccount?.myProfile ?? "My Profile"}
                                    </Link>
                                  </Dropdown.Item>
                                  <Dropdown.Item>
                                    <Link
                                      data-disable-nprogress={true}
                                      href={"/my-accounts/my-cashbacks"}
                                    >
                                      <Image
                                        quality={100}
                                        priority
                                        src={"/images/home/<USER>/myOrders_dark.svg"}
                                        width={22}
                                        height={20}
                                        alt="logo"
                                      />
                                      {translation?.myAccount?.myCashbacks ?? "My Cashbacks"}

                                    </Link>
                                  </Dropdown.Item>
                                  <Dropdown.Item>
                                    <Link
                                      data-disable-nprogress={true}
                                      href="/cart"
                                      onClick={() =>
                                        setIsProfileMenuOpen(false)
                                      }
                                    >
                                      <Image
                                        quality={100}
                                        priority
                                        src={"/images/home/<USER>/cart.svg"}
                                        width={22}
                                        height={20}
                                        alt="logo"
                                      />
                                      {translation?.myAccount?.myCart ?? "My Cart"}
                                      {counts?.cart ? `(${counts.cart})` : ""}
                                    </Link>
                                  </Dropdown.Item>
                                  <Dropdown.Item onClick={handleShow}>
                                    <Image
                                      quality={100}
                                      priority
                                      src={"/images/home/<USER>/tryCart.svg"}
                                      width={22}
                                      height={20}
                                      alt="logo"
                                    />
                                    {translation?.myAccount?.tryCart ?? "Try Cart"}
                                    {counts?.tryCart
                                      ? `(${counts.tryCart})`
                                      : ""}
                                  </Dropdown.Item>
                                  <Dropdown.Item>
                                    <Link
                                      data-disable-nprogress={true}
                                      href={"/my-accounts/my-orders"}
                                    >
                                      <Image
                                        quality={100}
                                        priority
                                        src={"/images/home/<USER>/myOrders_dark.svg"}
                                        width={22}
                                        height={20}
                                        alt="logo"
                                      />
                                      {translation?.myAccount?.myOrders ?? "My Orders"}
                                    </Link>
                                  </Dropdown.Item>
                                  <Dropdown.Item>
                                    <Link
                                      data-disable-nprogress={true}
                                      href={"/my-accounts/my-wishlist"}
                                      onClick={() =>
                                        setIsProfileMenuOpen(false)
                                      }
                                    >
                                      <Image
                                        quality={100}
                                        priority
                                        src={
                                          "/images/home/<USER>/myWishlist.svg"
                                        }
                                        width={22}
                                        height={20}
                                        alt="logo"
                                      />
                                      {translation?.myAccount?.myWishlist ?? "My Wishlist"}
                                      {counts?.wishlist
                                        ? `(${counts.wishlist})`
                                        : ""}
                                    </Link>
                                  </Dropdown.Item>
                                  <Dropdown.Item>
                                    <Link
                                      data-disable-nprogress={true}
                                      href={"/my-accounts/my-address-book"}
                                    >
                                      <Image
                                        quality={100}
                                        priority
                                        src={
                                          "/images/home/<USER>/myAddressBook_dark.svg"
                                        }
                                        width={22}
                                        height={20}
                                        alt="logo"
                                      />

                                      {translation?.myAccount?.myAddressBook ?? "My Address Book"}
                                    </Link>
                                  </Dropdown.Item>
                                  <Dropdown.Item>
                                    <Link
                                      data-disable-nprogress={true}
                                      href={"/my-accounts/my-subscription"}
                                    >
                                      <Image
                                        quality={100}
                                        priority
                                        src={
                                          "/images/home/<USER>/mySubscriptions_dark.svg"
                                        }
                                        width={22}
                                        height={20}
                                        alt="logo"
                                      />
                                      {translation?.myAccount?.mySubscription ?? "My Subscription"}
                                    </Link>
                                  </Dropdown.Item>
                                  <Dropdown.Item>
                                    <Link
                                      data-disable-nprogress={true}
                                      href={"/my-accounts/my-prescription"}
                                    >
                                      <Image
                                        quality={100}
                                        priority
                                        src={
                                          "/images/home/<USER>/myPrescriptions_dark.svg"
                                        }
                                        width={22}
                                        height={20}
                                        alt="logo"
                                      />
                                      {translation?.myAccount?.myPrescription ?? "My Prescription"}
                                    </Link>
                                  </Dropdown.Item>
                                  <Dropdown.Item onClick={() => logout(translation)}>
                                    <Image
                                      quality={100}
                                      priority
                                      src={"/images/home/<USER>/logOut.svg"}
                                      width={22}
                                      height={20}
                                      alt="logo"
                                    />
                                    {translation?.myAccount?.logout ?? "Logout"}
                                  </Dropdown.Item>
                                </Dropdown.Menu>
                              </Dropdown>
                            )}
                            {!userProfile && (
                              <Link className="d-none d-xl-block" data-disable-nprogress={true} href="/login">
                                <Image
                                  quality={100}
                                  priority
                                  src={"/images/home/<USER>/Profile.svg"}
                                  width={24}
                                  height={24}
                                  alt="logo"
                                />
                              </Link>
                            )}

                            <Link
                              data-disable-nprogress={true}
                              className="header_iconlist-link"
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                handleShow();
                                setIsProfileMenuOpen(false);
                              }}
                              href="#"
                            >
                              <Image
                                quality={100}
                                priority
                                src={"/images/common/try-cart-white.svg"}
                                className="me-2"
                                // style={{ filter: "invert(1)" }}
                                width={24}
                                height={24}
                                alt="logo"
                              />
                              {translation?.myAccount?.tryCart ?? "Try Cart"} ({counts?.tryCart})
                            </Link>
                          </li>

                          <li className="d-xl-none d-flex">
                            <Link onClick={closeMenu} href="/my-accounts/my-orders">
                              <Image
                                quality={100}
                                priority
                                src={"/images/home/<USER>/myOrders.svg"}
                                width={24}
                                height={24}
                                alt="logo"
                              />
                            </Link>
                            <Link
                              onClick={closeMenu}
                              className="header_iconlist-link"
                              href="/my-accounts/my-orders"
                            >
                              {translation?.myAccount?.myOrders ?? "My Orders"}
                            </Link>
                          </li>

                          <li className="d-xl-none d-flex">
                            <Link onClick={closeMenu} href="/my-accounts/my-address-book">
                              <Image
                                quality={100}
                                priority
                                src={"/images/home/<USER>/myAddressBook.svg"}
                                width={24}
                                height={24}
                                alt="logo"
                              />
                            </Link>
                            <Link
                              onClick={closeMenu}
                              className="header_iconlist-link"
                              href="/my-accounts/my-address-book"
                            >
                              {translation?.myAccount?.myAddressBook ?? "My Address Book"}
                            </Link>
                          </li>

                          <li className="d-xl-none d-flex">
                            <Link onClick={closeMenu} href="/my-accounts/my-subscription">
                              <Image
                                quality={100}
                                priority
                                src={"/images/home/<USER>/mySubscriptions.svg"}
                                width={24}
                                height={24}
                                alt="logo"
                              />
                            </Link>
                            <Link
                              onClick={closeMenu}
                              className="header_iconlist-link"
                              href="/my-accounts/my-subscription"
                            >
                              {translation?.myAccount?.mySubscription ?? "My Subscription"}
                            </Link>
                          </li>

                          <li className="d-xl-none d-flex">
                            <Link onClick={closeMenu} href="/my-accounts/my-prescription">
                              <Image
                                quality={100}
                                priority
                                src={"/images/home/<USER>/myPrescriptions.svg"}
                                width={24}
                                height={24}
                                alt="logo"
                              />
                            </Link>
                            <Link
                              onClick={closeMenu}
                              className="header_iconlist-link"
                              href="/my-accounts/my-prescription"
                            >
                              {translation?.myAccount?.myPrescription ?? "My Prescription"}
                            </Link>
                          </li>

                          <li className="d-xl-none d-flex">
                            <Link onClick={closeMenu} href="/store-locator">
                              <Image
                                quality={100}
                                priority
                                src={"/images/home/<USER>/store.svg"}
                                width={24}
                                height={24}
                                alt="logo"
                              />
                            </Link>
                            <Link
                              onClick={closeMenu}
                              className="header_iconlist-link"
                              href="/store-locator"
                            >
                              {translation?.myAccount?.storeLocator ?? "Store Locator"}
                            </Link>
                          </li>

                          <li className="try-cart d-none d-xl-block">
                            <button
                              onClick={handleShow}
                              style={{
                                width: counts?.tryCart ? "13rem" : "10rem",
                              }}
                            >
                              <Image
                                quality={100}
                                priority
                                src={"/images/common/try-cart.svg"}
                                width={25}
                                height={19}
                                alt="logo"
                              />
                              {translation?.myAccount?.tryCart ?? "Try Cart"}
                              {counts?.tryCart ? `(${counts.tryCart})` : ""}
                            </button>
                            <TryCartPopup
                              show={show}
                              handleClose={handleClose}
                            />
                          </li>
                        </ul>
                      </div>
                    </Offcanvas.Body>
                    <div className="bg-light d-flex d-xl-none bottom-sheet-profile">
                      <div className="d-flex justify-content-center align-items-center py-3">
                        <span></span>
                      </div>
                      {/* <ul>
                        <li>
                          <Link
                            data-disable-nprogress={true}
                            href="/my-accounts/my-orders"
                            onClick={() => setIsProfileMenuOpen(false)}
                          >
                            My Orders
                          </Link>
                        </li>

                        <li>
                          <Link
                            data-disable-nprogress={true}
                            href="/my-accounts/my-address-book"
                            onClick={() => setIsProfileMenuOpen(false)}
                          >
                            My Address Book
                          </Link>
                        </li>

                        <li>
                          <Link
                            data-disable-nprogress={true}
                            href="/my-accounts/my-subscription"
                            onClick={() => setIsProfileMenuOpen(false)}
                          >
                            My Subscription
                          </Link>
                        </li>

                        <li>
                          <Link
                            data-disable-nprogress={true}
                            href="/my-accounts/my-prescription"
                            onClick={() => setIsProfileMenuOpen(false)}
                          >
                            My Prescription
                          </Link>
                        </li>

                        <li>
                          <Link
                            data-disable-nprogress={true}
                            href="/contact-us"
                            onClick={() => setIsProfileMenuOpen(false)}
                          >
                            Contact Us
                          </Link>
                        </li>

                        <li>
                          <Link
                            data-disable-nprogress={true}
                            href="/store-locator"
                            onClick={() => setIsProfileMenuOpen(false)}
                          >
                            Store Locator
                          </Link>
                        </li>
                      </ul> */}

                      <Accordion defaultActiveKey="0">
                        {navData?.header
                          ?.filter((item: any) => item?.submenus.length > 0)
                          ?.map((item: any, index: any) => (
                            <Accordion.Item eventKey={index} key={item?._id}>
                              <Accordion.Header>{item?.title}</Accordion.Header>
                              <Accordion.Body>
                                <ul>
                                  {item?.submenus?.map((submenu: any) => (
                                    <li key={submenu?._id}>
                                      <Link
                                        data-disable-nprogress={true}
                                        href={submenu?.redirection}
                                        onClick={() =>
                                          setIsProfileMenuOpen(false)
                                        }
                                      >
                                        {submenu?.title}
                                      </Link>
                                    </li>
                                  ))}
                                </ul>
                              </Accordion.Body>
                            </Accordion.Item>
                          ))}
                        {navData?.header
                          ?.filter((item: any) => !(item?.submenus.length > 0))
                          ?.map((item: any, index: any) => (
                            <div key={item?._id} className="accordion-item">
                              <Link
                                onClick={() => setIsProfileMenuOpen(false)}
                                className="accordion-button no-after"
                                href={item?.link}
                              >
                                {item?.title}
                              </Link>
                            </div>
                          ))}
                        <div className="accordion-item">
                          <Link
                            onClick={() => setIsProfileMenuOpen(false)}
                            className="accordion-button no-after"
                            href="/contact-us"
                          >
                            {translation?.breadCrump?.contactUs ?? "Contact Us"}
                          </Link>
                        </div>
                      </Accordion>

                      <div className="text-center">
                        © Yateem {new Date().getFullYear()} | All right
                        reserved.
                      </div>
                    </div>
                  </Navbar.Offcanvas>
                </div>
              </Navbar>
            ))}
          </div>
          {isHover && (
            <motion.div
              ref={subMenuRef}
              onMouseLeave={hideHoverMenu}
              className="sub-menu"
              initial="exit"
              animate={isHover ? "enter" : "exit"}
              variants={subMenuAnimate}
            >
              <div className="w-100 h-100 d-flex justify-content-start">
                {subMenuData.map((items: any, index: number) => (
                  <Link
                    data-disable-nprogress={true}
                    style={{
                      width: `${100 / subMenuData.length}%`,
                      position: "relative",
                    }}
                    onClick={hideHoverMenu}
                    href={`${items?.redirection}`}
                    key={index}
                  >
                    <Image
                      quality={100}
                      priority
                      style={{
                        width: "100%",
                        height: "100%",
                        objectFit: "cover",
                        aspectRatio: "2",
                      }}
                      src={items.image ?? ""}
                      alt="image"
                      width={500}
                      height={500}
                    />
                    <span>{items?.title}</span>
                  </Link>
                ))}
              </div>
            </motion.div>
          )}
        </div>

        <div className={`search-input ${searchVisible ? "visible" : ""}`}>
          <Search
            handleSearch={handleSearch}
            query={query}
            setQuery={setQuery}
            searchParams={searchParams}
            closeSearch={closeSearch}
            translation={translation}
            tooltip={tooltip}
          />
        </div>
      </header>
      <ProgressBar
        height="3px"
        color="#fff"
        options={{ showSpinner: false }}
        shallowRouting
      />
    </>
  );
}

export default Header;
