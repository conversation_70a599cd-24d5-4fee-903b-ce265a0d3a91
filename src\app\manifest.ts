import { MetadataRoute } from 'next'

export default function manifest(): MetadataRoute.Manifest {
    return {
        name: '<PERSON><PERSON><PERSON> Optician',
        short_name: '<PERSON><PERSON><PERSON>',
        description: 'Yateem Optician | Shop for the best contact lenses, spectacles and more',
        start_url: '/',
        display: 'standalone',
        background_color: '#fff',
        theme_color: '#14252c',
        icons: [
            {
                src: '/favicon.ico',
                sizes: 'any',
                type: 'image/x-icon',
            },
            {
                "purpose": "maskable",
                "sizes": "512x512",
                "src": "/pwa/icon512_maskable.png",
                "type": "image/png"
            },
            { "purpose": "any", "sizes": "512x512", "src": "/pwa/icon512_rounded.png", "type": "image/png" },
            { "purpose": "any", "sizes": "144x144", "src": "/pwa/icon-144x144.png", "type": "image/png" },
            {
                "src": "/pwa/icon-192x192.png",
                "sizes": "192x192",
                "type": "image/png"
            },
            {
                "src": "/pwa/icon-256x256.png",
                "sizes": "256x256",
                "type": "image/png"
            },
            {
                "src": "/pwa/icon-384x384.png",
                "sizes": "384x384",
                "type": "image/png"
            },
            {
                "src": "/pwa/icon-512x512.png",
                "sizes": "512x512",
                "type": "image/png"
            }

        ],
    }
}