import { endpoints } from "@/config/apiEndpoints";

export async function getMultiStores() {
  // const locale = cookies().get("Next-Locale")?.value || "ae-en";
  // const [storeId, language] = locale.split("-")
  const res = await fetch(process.env.NEXT_PUBLIC_API_URL + endpoints.multiStores, {
    method: "GET",
    // headers: {
    //   "Content-Type": "application/json",
    //   language: "en",
    //   // storeid: storeId || "ae"
    // },
    next: { tags: ["country"] },
    cache: "force-cache"
  });

  const data = await res.json();

  if (data?.errorCode !== 0) {
    throw Error("Something went wrong");
  }
  return data
}