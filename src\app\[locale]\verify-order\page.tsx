import LogoAnimation from "@/components/LogoAnimation/LogoAnimation";
import SuccessPopupWrapper from "./SuccessPopup";
import api from "@/config/axios.interceptor";
import { endpoints } from "@/config/apiEndpoints";
import { AuthProvider } from "@/contexts/AuthProvider";
import { sendGTMEvent } from "@next/third-parties/google";
import { createHmac } from "crypto";

const failerMessage = {
  en: "Sorry, <PERSON><PERSON> is unable to approve this purchase. Please use an alternative payment method for your order",
  ar: "نأسف، تابي غير قادرة على الموافقة على هذه العملية. الرجاء استخدام طريقة دفع أخرى."
}

const cancelledMessage = {
  en: "You aborted the payment. Please retry or choose another payment method.",
  ar: "لقد ألغيت الدفعة. فضلاً حاول مجددًا أو اختر طريقة دفع أخرى."
}

export default async function OrderPlaced({ searchParams, params }: { searchParams: any, params: any }) {


  try {
    let rejectionMessage = ""
    let language: "en" | "ar" = params.locale.split("-")[1]

    const isTabby = searchParams.paymentMethod == "TABBY";
    if (isTabby) {
      if (searchParams?.status == "cancelled") {
        rejectionMessage = cancelledMessage[language]
      } else {
        rejectionMessage = failerMessage[language]
      }

      return (
        <AuthProvider>
          <SuccessPopupWrapper cart={searchParams?.cartType} isSuccess={false} rejectionMessage={rejectionMessage} />;
        </AuthProvider>
      )
    }

    const res = await api.post(endpoints.verifyPayment, searchParams)
    const data = res.data

    return (
      <>
        <div style={{ display: "grid", placeItems: "center", padding: "50px 0" }}>
          <LogoAnimation />
        </div>
        <AuthProvider>
          <SuccessPopupWrapper resp={data.result} cart={searchParams?.cartType} isSuccess={true} />
        </AuthProvider>
      </>
    );
  } catch (err) {
    return <AuthProvider>
      <SuccessPopupWrapper cart={searchParams?.cartType} isSuccess={false} />;
    </AuthProvider>
  }
}
