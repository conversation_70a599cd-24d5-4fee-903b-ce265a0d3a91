import { endpoints } from "@/config/apiEndpoints";
import { cookies } from "next/headers";
export const getCategories = async (type: 'all' | 'home') => {
    const locale = cookies().get("Next-Locale")?.value || "sa-en";
    const [storeId, language] = locale.split("-");
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}${endpoints.categories}/${type}`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            language: language || "en",
            storeid: storeId || "sa"
        },
        next: {
            tags: ['categories']
        },
        cache: "force-cache"
    });
    const data = await res.json();
    const categories = data.result;
    return categories;
}
