"use client";

import ProductCard from "@/components/product/product-card/ProductCard";
import { spectacleProducts } from "@/lib/products";
import "./wishlist.scss";
import { getWishlist } from "@/lib/methods/user";
import { useQuery } from "@tanstack/react-query";
import LogoAnimation from "@/components/LogoAnimation/LogoAnimation";
import EmptyState from "@/components/empty-states/EmptyState";
import { useContext, useEffect } from "react";
import { HistoryContext } from "@/contexts/HistoryProvider";
import { TranslationContext } from "@/contexts/Translation";

function WishList({ data }: any) {
  const { data: wishList, isLoading } = useQuery({
    queryKey: ["wishList"],
    queryFn: getWishlist,
  });
  const { setPrevPage } = useContext(HistoryContext);
  const {translation}:any = useContext(TranslationContext)
  useEffect(() => {
    setPrevPage({
      title: "My WishList",
      url: "/my-accounts/my-wishlist",
    });
  }, []);

  return (
    <>
      <section className="wishlist">
        <h2>{translation?.myAccount?.myWishlist ?? "My Wishlist"}</h2>
        {!isLoading && wishList?.length > 0 && (
          <div className="wishlist_grids" key={data?._id}>
            {wishList.map((card: any, index: number) => (
              <ProductCard product={card} addToCart={(Object.values(card) as any)?.[0]?.isAddToCart} key={card?._id} />
            ))}
          </div>
        )}
      </section>
      {!isLoading && wishList?.length === 0 && (
        <EmptyState icon="sad" title="Your wishlist is empty" />
      )}
      {isLoading && <LogoAnimation className="d-flex justify-content-center py-5" />}
    </>
  );
}

export default WishList;
