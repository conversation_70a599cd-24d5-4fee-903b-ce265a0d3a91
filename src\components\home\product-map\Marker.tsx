"use client";
import Link from "next/link";
import Image from "next/image";
import { useRef, useState } from "react";
import { useOnClickOutside } from "usehooks-ts";
import { AnimatePresence, motion } from "framer-motion";

export default function Marker({ items }: any) {
  const [show, setShow] = useState(false);
  const ref = useRef(null);
  useOnClickOutside(ref, () => setShow(false));
  return (
    <div
      className="team_box"
      style={{
        top: items?.positionY + "%",
        left: items?.positionX + "%",
        zIndex: show ? 60 : 50,
      }}
    >
      <button
        className="hover_btn"
        onTouchStart={() => setShow(true)}
        onTouchEnd={() => setShow(false)}
        onMouseEnter={() => setShow(true)}
        onMouseLeave={() => setShow(false)}
        onClick={()=> true }
      >
        <div className="button-wrapper">
          <div className="floating-btn">
            <div className="pulse-item" />
            <div className="pulse-item" />
            <span className="material-symbols-outlined icons">
              <svg
                width="9"
                height="9"
                viewBox="0 0 9 9"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M8.04167 3.79167H5.20833V0.958333C5.20833 0.770472 5.13371 0.590304 5.00087 0.457466C4.86803 0.324628 4.68786 0.25 4.5 0.25C4.31214 0.25 4.13197 0.324628 3.99913 0.457466C3.86629 0.590304 3.79167 0.770472 3.79167 0.958333V3.79167H0.958333C0.770472 3.79167 0.590304 3.86629 0.457466 3.99913C0.324628 4.13197 0.25 4.31214 0.25 4.5C0.25 4.68786 0.324628 4.86803 0.457466 5.00087C0.590304 5.13371 0.770472 5.20833 0.958333 5.20833H3.79167V8.04167C3.79167 8.22953 3.86629 8.4097 3.99913 8.54253C4.13197 8.67537 4.31214 8.75 4.5 8.75C4.68786 8.75 4.86803 8.67537 5.00087 8.54253C5.13371 8.4097 5.20833 8.22953 5.20833 8.04167V5.20833H8.04167C8.22953 5.20833 8.4097 5.13371 8.54253 5.00087C8.67537 4.86803 8.75 4.68786 8.75 4.5C8.75 4.31214 8.67537 4.13197 8.54253 3.99913C8.4097 3.86629 8.22953 3.79167 8.04167 3.79167Z"
                  fill="black"
                />
              </svg>
            </span>
          </div>
        </div>
      </button>
      <AnimatePresence>
        {show && (
          <motion.div
            onMouseEnter={() => setShow(true)}
            onMouseLeave={() => setShow(false)}
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0 }}
            transition={{ duration: 0.5 }}
            className={`team_image ${show && "show"} ${
              items?.positionX > 80 ? "right-0" : ""
            }`}
            ref={ref}
          >
            <Image
              quality={100}
              priority
              src={items.image ?? ""}
              width={246}
              height={281}
              alt="images"
            />
            <div className="team_title">
              <h5>{items.title}</h5>
              <Link href={items.link}>{items.buttonText}</Link>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
