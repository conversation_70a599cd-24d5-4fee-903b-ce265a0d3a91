.toaster {
  border-radius: 2.95rem;
  background: #000;
  height: 5.6rem;
  width: 29.8rem;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.4rem 2.6rem;
  position: relative;

  &::after {
    content: "";
    background-image: url(../../../public/images/common/Polygon.png);
    background-repeat: no-repeat;
    position: absolute;
    top: -10px;
    right: 25px;
    width: 1.7rem;
    height: 1.5rem;
    filter: brightness(0) saturate(100%) invert(0%) sepia(0%) saturate(0%)
      hue-rotate(324deg) brightness(96%) contrast(104%);
  }

  &_text {
    line-height: 0;
  }

  h5 {
    color: #fff;
    font-size: 1.4rem;
    font-weight: 600;
    line-height: 1.7rem;
  }

  span {
    color: #fff;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.2rem;
    display: inline-block;
    margin-top: 2px;
  }

  a {
    color: #cecece;
    font-size: 1rem;
    font-weight: 400;
    text-decoration-line: underline;

    &:hover{
      color: #cecece !important;
    }
  }
}

#toasterPos {
  @media screen and (min-width: 1200px) {
    position: absolute;
    left: 281px;
    top: 20px;
  }
  @media screen and (min-width: 1400px) {
    left: 365px;
  }
}
body:has(header.sticky) #toasterPos {
  @media screen and (min-width: 1200px) {
    top: 0px;
  }
}
