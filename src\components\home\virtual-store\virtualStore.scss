.virtualtry {
  margin-top: 5rem;

  @media (max-width: 767.98px) {
    margin-top: 2.4rem;
  }

  &_wrapper {
    display: flex;
    border-radius: 2rem;

    @media (max-width: 767.98px) {
      flex-direction: column;
    }
  }

  &_left {
    width: 65%;

    @media (max-width: 1199.98px) {
      width: 100%;
    }

    img {
      border-radius: 2rem 0rem 0rem 2rem;

      @media (max-width: 767.98px) {
        border-radius: 1.6rem 1.6rem 0rem 0rem;
      }

      @media (max-width: 575.98px) {
        max-height: 25rem;
      }
    }
  }

  &_right {
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    width: 35%;
    border-radius: 0rem 2rem 2rem 0rem;
    position: relative;

    @media (max-width: 1199.98px) {
      width: 100%;
    }

    @media (max-width: 767.98px) {
      border-radius: 0rem 0rem 1.5rem 1.5rem;
      z-index: 3;
    }

    &::after {
      content: "";
      background: rgba(0, 0, 0, 0.6);
      height: 100%;
      width: 100%;
      top: 0;
      position: absolute;
      border-radius: 0rem 2rem 2rem 0rem;

      @media (max-width: 767.98px) {
        border-radius: 0rem 0rem 1.5rem 1.5rem;
      }
    }
  }

  &_content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
    width: 100%;
    text-align: center;
    padding: 0 6rem;

    @media (max-width: 991.98px) {
      padding: 0 3rem;
    }

    @media (max-width: 767.98px) {
      position: relative;
      left: 0;
      top: 0;
      transform: none;
      padding: 3rem 1.6rem 3.6rem 1.4rem;
    }

    h4 {
      font-size: 2.4rem;
      font-style: normal;
      font-weight: 500;
      line-height: 2.6rem;
      color: #fff;

      @media (max-width: 575.98px) {
        font-size: 2rem;
      }
    }

    p {
      margin-top: 2rem;
      color: #fff;
      font-size: 1.7rem;
      font-weight: 500;
      line-height: 2.6rem;

      @media (max-width: 575.98px) {
        margin-top: 1rem;
        line-height: 2.4rem;
        font-size: 1.5rem;
      }
    }

    .primary-btn {
      margin-top: 3rem;

      @media (max-width: 575.98px) {
        margin-top: 2.4rem;
      }
    }
  }
}


.app.rtl {
  .virtualtry_right {
    border-radius: 2rem 0rem 0rem 2rem;

    &::after {
      border-radius: 2rem 0rem 0rem 2rem;
    }
  }

  .virtualtry_left img {
    border-radius: 0rem 2rem 2rem 0rem;
  }
}