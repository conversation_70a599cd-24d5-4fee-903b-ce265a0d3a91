{"name": "yateem-optician-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start -p 1347", "lint": "next lint", "lux": "next dev -p 1347"}, "dependencies": {"@luxottica/virtual-mirror": "^9.10.3", "@next/third-parties": "^15.1.4", "@react-google-maps/api": "^2.20.6", "@react-oauth/google": "^0.12.1", "@tanstack/react-query": "^5.8.4", "@tanstack/react-query-devtools": "^5.8.4", "axios": "^1.6.2", "country-state-city": "^3.2.1", "firebase": "^10.10.0", "framer-motion": "^10.16.5", "jwt-decode": "^4.0.0", "mongoose": "^8.1.1", "next": "^14.1.0", "next-international": "^1.1.4", "next-nprogress-bar": "^2.3.9", "react": "^18.2.0", "react-bootstrap": "^2.9.1", "react-circle-flags": "^0.0.23", "react-compare-slider": "^3.0.0", "react-countup": "^6.5.3", "react-dom": "^18.2.0", "react-fast-marquee": "^1.6.2", "react-hook-form": "^7.48.2", "react-icons": "^5.5.0", "react-intersection-observer": "^9.5.3", "react-modal-sheet": "^2.2.0", "react-otp-input": "^3.1.0", "react-range-slider-input": "^3.0.7", "react-select": "^5.8.0", "react-simple-star-rating": "^5.1.7", "react-to-print": "^2.14.15", "sharp": "^0.32.6", "sonner": "^1.2.4", "swiper": "^11.2.6", "usehooks-ts": "^2.9.1"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "^14.1.0", "sass": "^1.69.5", "typescript": "^5"}}