# Yateem Optician Frontend Documentation

## Project Overview
- **Name**: Yateem Optician Frontend
- **Description**: A React and Next.js based application for an optician e-commerce platform, providing services for browsing and purchasing optical products.
- **Version**: 0.1.0
- **Business Context**: This platform enables users to browse a wide selection of optical products, manage prescriptions, and place orders online. It supports multi-store operations, loyalty programs, and insurance integration. The frontend is optimized for both customer experience and admin workflows.
- **Target Users**: End customers, optician store staff, and administrators.

## Setup and Installation
- **Requirements**:
  - Node.js
  - npm/yarn
- **Installation Instructions**:
  ```bash
  # Clone the repository
  git clone <repository-url>
  
  # Navigate to the project directory
  cd yateem-optician-frontend

  # Install dependencies
  npm install
  # or
  yarn install
  ```
  
- **Environment Configuration**:
  - `.env` file should contain:
    - `NEXT_PUBLIC_API_URL='https://yateemapi.s423.previewbay.com/api/web/'`  
      > **Description:** Base URL for all API requests. Change this to point to staging/production as needed.
    - `NEXT_PUBLIC_IMAGE_DOMAIN='yateemapi.s423.previewbay.com'`  
      > **Description:** Domain for serving images (used for image optimization and CORS).
    - `PAGESIZE=15`  
      > **Description:** Default page size for paginated endpoints (optional, can be overridden in code).
  - **Security:** Never commit sensitive keys or production URLs to public repos.
  - **Deployment Note:** All environment variables must be set in your deployment platform (e.g., Vercel dashboard).

## Development and Build Scripts
- **Dev Server**:
  ```bash
  npm run dev
  ```
  Access at: [http://localhost:3000](http://localhost:3000)
  
- **Production Build**:
  ```bash
  npm run build
  ```
  
- **Start Server**:
  ```bash
  npm run start
  ```
  
- **Linting**:
  ```bash
  npm run lint
  ```

## Key Features
- **Internationalization (i18n):**
  - Supports English and Arabic. Locale switching handled via `LocaleProvider` context.
  - Translation files live in `src/locales/`. To add a new language, add a new JSON file and update the provider.
- **API Interactions:**
  - Uses Axios for HTTP requests and React Query for data fetching/caching.
  - All endpoints are managed in `src/config/apiEndpoints.ts`.
  - Example usage:
    ```ts
    import axios from 'axios';
    const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/products`);
    ```
- **Authentication:**
  - Managed via `AuthProvider` context and cookies for session persistence.
  - Login, logout, and session validation handled centrally.
- **State Management:**
  - Context API for auth and locale; React Query for server state.
- **SEO and Metadata:**
  - Uses `next/head` and `next/metadata` for dynamic SEO tags.
- **Responsive Design:**
  - SCSS and Bootstrap for layouts; custom styles for branding.

## Authentication Logic
- User credentials are sent to the API via Axios.
- On success, a session token is stored in cookies and context is updated.
- Protected routes/components check context and redirect if not authenticated.

## API Integration
- All API requests go through Axios instances, with a custom interceptor in `src/config/axios.interceptor.ts` for authentication and error handling.
- Error handling is centralized; API errors are shown via toast notifications or error boundaries.
- Example API call:
  ```ts
  import { login } from 'src/lib/methods/auth';
  await login(username, password);
  ```
- **Endpoints:** All endpoints are defined in `src/config/apiEndpoints.ts` as a single object. Example endpoints:
  - `login`: User authentication
  - `products`: Product listing
  - `productDetail`: Product detail by slug
  - `cart`, `addToCart`, `removeFromCart`: Cart management
  - `orders`, `orderDetail`: Order management
  - `register`: User registration
  - `about`: About page content
  - `translation`: Fetch translation resources
  - ...and many more (see file for full list)
- **Usage:**
  ```ts
  import api from '@/config/axios.interceptor';
  import { endpoints } from '@/config/apiEndpoints';
  // Example: Fetch products
  api.post(endpoints.products, { page: 1, limit: 15 });
  ```
- **Authentication:**
  - Most user endpoints require a valid session token (stored in cookies).
  - The Axios interceptor attaches tokens from cookies to requests automatically.
- **Error Handling:**
  - API errors are caught and surfaced to the UI via toast notifications.
  - 404s and other HTTP errors may trigger redirects or error components.
- **Advanced:**
  - Some endpoints accept locale/language headers for i18n.
  - Pagination, filtering, and search are supported on many endpoints (see API docs or code comments).

### API Request/Response Examples

**Login**
- Request:
  ```json
  POST /login
  {
    "email": "<EMAIL>",
    "password": "your_password"
  }
  ```
- Response:
  ```json
  {
    "errorCode": 0,
    "result": {
      "token": "jwt_token_string",
      "user": { "id": 123, "name": "John Doe" }
    }
  }
  ```

**Fetch Products**
- Request:
  ```json
  POST /products
  {
    "page": 1,
    "limit": 15,
    "keyword": "glasses"
  }
  ```
- Response:
  ```json
  {
    "errorCode": 0,
    "result": [
      { "id": 1, "name": "Ray-Ban RX1234", "price": 120 },
      { "id": 2, "name": "Oakley OX5678", "price": 150 }
    ]
  }
  ```

**Add to Cart**
- Request:
  ```json
  POST /add-to-cart
  {
    "productId": 1,
    "quantity": 2
  }
  ```
- Response:
  ```json
  {
    "errorCode": 0,
    "result": { "cartId": "abc123", "items": [ ... ] }
  }
  ```

**Error Example**
- Response:
  ```json
  {
    "errorCode": 401,
    "message": "Unauthorized. Please log in."
  }
  ```

## Internationalization (i18n)
- Uses `next-intl` or similar for locale management.
- Language files in `src/locales/`.
- To add a language: add a new JSON file, update provider, and add a language option to the UI.

## Styling & Theming
- SCSS modules for component-level styles.
- Bootstrap for grid/layout and base components.
- Theming can be customized via SCSS variables in `src/styles/`.

## Testing
- (Add details if tests exist. Example: Jest and React Testing Library are recommended.)
- To run tests:
  ```bash
  npm run test
  ```

## Application Structure

- **src/app/**: Next.js app entry and routing.
- **src/components/**: UI components (Header, Footer, Home, Auth, etc.)
- **src/contexts/**: Context providers for auth and locale.
- **src/lib/methods/**: API call methods.
- **src/styles/**: SCSS and Bootstrap overrides.
- **src/config/**: API endpoints and constants.

## Deployment
- **Recommended Platform:** Vercel or any Next.js-compatible host.
- **Steps:**
  1. Set environment variables in the deployment platform.
  2. Push to the main branch or trigger deployment via CI/CD.
  3. Monitor build and deployment logs for errors.
- **Troubleshooting:**
  - Ensure all environment variables are set.
  - Check API connectivity from the deployed environment.

## FAQ / Troubleshooting
- **Q:** App fails to fetch API data?
  - **A:** Check `NEXT_PUBLIC_API_URL` and CORS settings on backend. Ensure your token is valid and the backend is reachable from your environment.
- **Q:** Styles not loading?
  - **A:** Ensure SCSS and Bootstrap imports are not commented out. Check for build errors related to SCSS modules.
- **Q:** Locale not switching?
  - **A:** Check context provider and translation files. Ensure the correct locale is being passed in headers.
- **Q:** Images not loading?
  - **A:** Check `NEXT_PUBLIC_IMAGE_DOMAIN` and verify image URLs are correct and accessible.
- **Q:** Build fails on deploy?
  - **A:** Ensure all required environment variables are set. Check build logs for missing dependencies or misconfigured scripts.
- **Q:** API returns 401/403?
  - **A:** User session may have expired. Try logging in again. If persistent, check token logic in the Axios interceptor.

## Developer Workflow
- **Branching:** Use feature branches (e.g., `feature/checkout-flow`) for new features. Use `bugfix/` or `hotfix/` prefixes for fixes.
- **Commits:** Write clear, descriptive commit messages. Use `--to build` in commit messages to trigger deployment.
- **Pull Requests:** Ensure your PR passes all CI checks and includes tests where relevant. Tag reviewers and provide context for your changes.
- **Code Style:** Follow the existing code style (TypeScript, SCSS modules, functional React components). Use Prettier and ESLint for formatting and linting.
- **Testing:** Add/maintain tests for new features and bug fixes. Use Jest and React Testing Library if present.

## Onboarding Checklist
- [ ] Clone the repository and install dependencies
- [ ] Create your `.env` file (see Setup section)
- [ ] Run the dev server and verify it works locally
- [ ] Test API connectivity (login, fetch products, etc.)
- [ ] Review code structure and key files (see Project Structure)
- [ ] Set up your code editor with Prettier and ESLint
- [ ] Run linter and fix any issues
- [ ] Run tests (if present)
- [ ] Read and follow the Developer Workflow section

## Workflow Diagram
Below is a simplified workflow for a typical feature development:

```mermaid
flowchart TD
    A[Create Feature Branch] --> B[Develop Feature]
    B --> C[Write/Update Tests]
    C --> D[Commit with --to build]
    D --> E[Open Pull Request]
    E --> F[CI/CD: Lint, Test, Build]
    F --> G[Code Review]
    G --> H[Merge to Main]
    H --> I[Deployment Triggered]
```

## Contributing
- Fork the repo and create a feature branch.
- Follow code style and naming conventions.
- Open a pull request with a clear description.
- Ensure all checks pass before requesting review.

## License
- [Specify license here, e.g., MIT, Apache 2.0, etc.]
- See `LICENSE` file for details.

## Appendices
- **Dependencies:** React, Next.js, Axios, React Query, Bootstrap, SCSS, next-intl, and more.
- **Common Commands:** See Available Scripts section above.
- **Contact:** For support, contact the maintainers or open an issue.

## Application Structure

- **`src/app`** 
  - Main entry point and contains routing for locales.

- **`src/components`** 
  - **Header**: Navbar and search functionalities.
  - **Footer**: Contact information and links.
  - **Home**: Various user-interactive components for the landing page.
  - **Authentication**: Context and components for login/logout.

- **`src/contexts`**: Contains context providers such as `AuthProvider` and `LocaleProvider`.

- **`src/lib/methods`**: API call methods, including authentication and user management.

## Deployment
- **Platform**: Deploy on Vercel or similar platforms supporting Next.js.

## Appendices
- **Dependencies**: Key libraries include React, Next.js, Axios, React Query, and various Bootstrap components.
- **Common Commands**: Additional scripts or common commands being utilized.
- **License Info**: To be detailed if required.

