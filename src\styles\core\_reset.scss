html {
  /* 62.5% of 16px base font size is 10px */
  font-size: 62.5%;
  height: 100%;
  // @media (max-width: 768px) {
  //     font-size: 50%;
  // }

  // @media (max-width: 576px) {
  //     font-size: 40%;
  // }
}

body {
  margin: 0;
  padding: 0;
  position: relative;
  font-family: "Outfit";
  overflow-x: hidden;
  font-size: 1.6rem;
  height: 100%;
}

a {
  text-decoration: none;

  &:hover {
    text-decoration: none;
    color: black;
  }
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  padding: 0;
  line-height: inherit;
}

section {
  max-width: 100vw;
}

p {
  margin: 0;
  padding: 0;
}

label {
  margin: 0;
  padding: 0;
}

button {
  &:focus {
    outline: none;
  }
}

.btn {
  &:focus {
    outline: none;
    box-shadow: none;
  }
}

ul,
li {
  padding: 0;
  list-style: none;
  margin: 0px;
}

li[data-sonner-toast]{
padding: 16px;
}

.form-control {
  &:focus {
    outline: none;
    box-shadow: none;
  }
}

img {
  width: 100%;
  object-fit: cover;
}

.nav-link {
  &:hover {
    color: #fff;
  }
}

$grid-breakpoints: (xs: 370px,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1400px,
  xxxl: 1600px,
  xxxxl: 1800px,
);

.container {
  @include media-breakpoint-up(xxxl) {
    max-width: 152rem;
  }

  @include media-breakpoint-up(xxxxl) {
    max-width: 164rem;
  }

  @include media-breakpoint-down(xl) {
    max-width: 100rem;
    width: 100%;
    margin: 0 auto;
  }

  @include media-breakpoint-down(sm) {
    padding: 0 1.5rem;
  }
}

.container-fluid {

  @include media-breakpoint-down(xxl) {
    max-width: 125.4rem;
    width: 93%;
    margin: 0 auto;
  }

  @include media-breakpoint-down(md) {
    max-width: 124.4rem;
    width: 100%;
    margin: 0 auto;
  }
}

.container-custem {
  padding: 0 0.75rem;

  @include media-breakpoint-down(xxl) {
    max-width: 1200rem;
    width: 89%;
    margin: 0 auto;
  }

  @include media-breakpoint-down(md) {
    max-width: 124.4rem;
    width: 100%;
    margin: 0 auto;
  }
}