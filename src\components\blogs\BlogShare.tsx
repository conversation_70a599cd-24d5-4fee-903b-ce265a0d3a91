"use client";
import Link from "next/link";
import Image from "next/image";
import { useContext, useState } from "react";
import { useEffectOnce } from "usehooks-ts";
import { TranslationContext } from "@/contexts/Translation";

export default function BlogShare() {
  const { translation } = useContext(TranslationContext)
  const [url, setUrl] = useState("");
  useEffectOnce(() => {
    if (typeof window !== "undefined") setUrl(window.location.href);
  });

  const nativeShare = () => {
    const shareData = {
      title: "Hey Checkout this blog on Yateem Optician",
      text: `Hey Checkout ${document?.title} on Yateem Optician`,
      url: url,
    };
    if (navigator.share) {
      navigator
        .share(shareData)
        .then(() => {})
        .catch((error) => {});
    }
  };
  return (
    <>
      <ul>
        <li>{translation?.other?.share ?? "Share"}<span>{":"}</span></li>

        <li>
          <Link
            href={"https://www.facebook.com/sharer/sharer.php?u=" + encodeURIComponent(url)}
            target={"_blank"}>
            <Image quality={100} priority src="/images/common/facebook.png" width={18} height={18} alt="user" />
          </Link>
        </li>

        <li>
          <Link
            href={`http://twitter.com/share?text=Hey Checkout this Blog from Yateem Optician&url=${encodeURIComponent(
              url
            )}&hashtags=yateemOptician,sunglass,eyecare`}
            target="_blank">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="17"
              height="15"
              viewBox="0 0 17 15"
              fill="none">
              <path
                d="M0.562336 0.901367L6.72212 8.5165L0.523438 14.7079H1.91851L7.34545 9.28724L11.7303 14.7079H16.4778L9.9714 6.66445L15.7411 0.901367H14.346L9.34807 5.89371L5.30983 0.901367H0.562336ZM2.6139 1.85149H4.79491L14.4259 13.7576H12.2449L2.6139 1.85149Z"
                fill="black"
              />
            </svg>
          </Link>
        </li>

        <li>
          <Link href={""} onClick={nativeShare}>
            <Image quality={100} priority src="/images/common/instagram.png" width={18} height={18} alt="user" />
          </Link>
        </li>

        <li>
          <Link
            href={`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`}
            target={"_blank"}>
            <Image quality={100} priority src="/images/common/linkedin.png" width={18} height={18} alt="user" />
          </Link>
        </li>
      </ul>
    </>
  );
}
