"use client";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination, Navigation, Autoplay } from "swiper/modules";
import Marquee from "react-fast-marquee";

function MarqueeSlider(data: any) {
  return (
    <div>
      <Marquee autoFill>
        {data.data.map((items: any, index: any) => (
          <SwiperSlide className="me-4 text-uppercase" key={index}>
            <h5>{items.name}</h5>
          </SwiperSlide>
        ))}
      </Marquee>
    </div>
  );
}

export default MarqueeSlider;
