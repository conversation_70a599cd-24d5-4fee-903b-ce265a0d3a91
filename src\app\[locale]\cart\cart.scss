.cart {
  position: relative;

  &_loyality_wrapper {
    padding-bottom: 5rem !important;
  }

  &_wrapper {
    display: flex;
    column-gap: 3rem;

    @media (max-width: 991.98px) {
      flex-direction: column;
    }

    @media (max-width: 575.98px) {
      padding-bottom: 0 !important;
    }

    .table {
      width: 60%;
      margin-top: 3.7rem;
      min-height: 560px;

      @media (max-width: 575.98px) {
        margin-top: 1.5rem;
        margin-bottom: 2rem;
        min-height: auto;
      }

      @media (max-width: 991.98px) {
        width: 100%;
        min-height: auto;
      }

      h2 {
        text-align: left;
        margin-bottom: 2.1rem;
        line-height: 4rem;

        @media (max-width: 575.98px) {
          text-align: center;
        }
      }

      &_item {
        display: flex;
        justify-content: space-between;
        // align-items: flex-start;

        @media (max-width: 575.98px) {
          flex-direction: column;
          width: 100%;
        }

        &:not(:last-child) {
          padding-bottom: 4rem;
          margin-bottom: 4rem;
          border-bottom: 1px solid #bebcbd;

          @media (max-width: 991.98px) {
            padding-bottom: 2.5rem;
            margin-bottom: 2rem;
          }
        }
      }

      &_info {
        display: flex;
        column-gap: 2.5rem;
        position: relative;

        .btn {
          display: none;
        }

        @media (max-width: 575.98px) {
          column-gap: 2.1rem;
          width: 100%;

          .btn {
            display: block;
            position: absolute;
            right: 10px;
            top: 10px;
          }
        }

        @media (max-width: 355px) {
          column-gap: 1rem;
          .table_description{
            .title{
              font-size: 1.4rem;
            }
          }
          a{
            img{
              width: 7rem;
            }
          }
        }

        img {
          padding: 0;
          width: 12rem;
          height: 12rem;
          border-radius: 1.2rem;
          object-fit: contain;

          @media (max-width: 575.98px) {
            width: 9.6rem;
            height: 12.5rem;
          }
        }

        ul {
          padding: 0;

          @media (max-width: 575.98px) {
            width: 100%;
          }

          li {
            color: #807d7e;
            font-size: 1.4rem;
            font-weight: 400;
            line-height: 1.7rem;
            margin-bottom: 5px;

            &.title {
              color: #000;
              font-size: 1.6rem;
              font-weight: 600;
              letter-spacing: 0.032rem;
              margin-bottom: 1.1rem;
              line-height: 2rem;
              max-width: 17rem;
              width: 100%;

              @media (min-width: 1400px) {
                max-width: 34rem;
              }

              @media (max-width: 575.98px) {
                margin-bottom: 0.5rem;
              }
            }

            button {
              border: none;
              padding: 0;
              background-color: transparent;
            }
          }
        }

        &-mobile {
          display: flex;
          align-items: center;
          margin-top: 1rem;
          column-gap: 2.1rem;
          justify-content: space-between;

          span {
            color: #000;
            font-size: 1.6rem;
            font-weight: 600;
            padding: 0;
            line-height: 2rem;
          }

          @media (min-width: 576px) {
            display: none;
          }
        }
      }

      &_infosm {
        @media (max-width: 575.98px) {
          display: none;
        }
      }

      &_description {
        padding: 0;

        ul {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          column-gap: 2rem;

          @media (max-width: 575.98px) {
            grid-template-columns: repeat(1, 1fr);
          }
        }
      }

      &_size {
        color: #807d7e;
        font-weight: 400;
        font-size: 1.4rem;
        line-height: 1.7rem;
      }

      &_count {
        .count {
          padding: 4px 0;
          width: 100%;
          margin-top: 1.2rem;
          padding: 4px 18px;
          justify-content: space-between;
        }
      }

      &_price-delete {
        display: flex;
        align-items: center;
        column-gap: 4.9rem;

        span {
          color: #000;
          font-size: 1.8rem;
          font-weight: 600;
          padding: 0;
          line-height: 2.2rem;
          min-width: max-content;
        }

        button {
          border: none;
          padding: 0;
          background-color: transparent;
        }
      }
    }
  }

  &_order {
    &-summary {
      // position: absolute;
      // right: 0;
      // top: 0;
      width: 40%;

      @media (max-width: 991.98px) {
        position: unset;
        width: 100%;
      }
    }

    &-width {
      background-color: #f2f4f9;
      // padding: 12.5rem 11.8rem 5.4rem 3.5rem;
      padding: 9.5rem 2rem 5.4rem 3.5rem;

      @media (max-width: 1199.98px) {
        // padding: 12.5rem 3.5rem 5.4rem 3.5rem;
        padding: 9.5rem 2rem 5.4rem 3.5rem;
      }

      @media (max-width: 991.98px) {
        position: unset;
        width: 100%;
        padding: 3rem;
      }

      @media (max-width: 575.98px) {
        padding: 2.8rem 2rem;
      }

      h5 {
        color: #000;
        font-size: 2.2rem;
        font-weight: 500;
      }

      ul {
        margin-top: 3.9rem;

        li {
          display: flex;
          justify-content: space-between;
          align-items: center;
          color: #000;
          font-size: 1.5rem;
          font-weight: 600;
          padding-right: 3.4rem;

          @media (max-width: 575.98px) {
            padding-right: 1.5rem;
          }

          &:not(:last-child) {
            margin-bottom: 1.4rem;
            padding-bottom: 1.4rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.2);
          }

          h6 {
            color: #374957;
            font-size: 1.5rem;
            font-weight: 400;
          }

          .price-text {
            font-size: 1.4rem;
          }

          span {
            color: #000;
            font-size: 1.8rem;
            font-weight: 600;
          }
        }
      }
    }

    &-coupon {
      background-color: rgba(242, 244, 249, 0.5);
      padding: 2rem 3.5rem 2rem 2rem;

      @media (max-width: 991.98px) {
        padding: 2rem;

      }

      button {
        width: 100%;
        border: none;
        padding: 0;
        background-color: transparent;
        display: flex;
        border-radius: 0.8rem;
        border: 1px solid #e4e4e4;
        background: #fff;
        padding: 1.3rem 1.3rem 1.3rem 1.5rem;
        align-items: center;
        column-gap: 1.5rem;
      }

      span {
        color: #000;
        text-align: center;
        font-size: 1.6rem;
        font-weight: 500;
      }

      img {
        width: 2.4rem;
        height: 2.4rem;
      }

      .arrow {
        width: 1.4rem;
        height: 1.4rem;
        margin-left: auto;
      }
    }

    &-code {
      // padding: 4.2rem 12rem 6.5rem 4.6rem;
      padding: 4.2rem 2rem 6.5rem 4.6rem;
      background-color: rgba(242, 244, 249, 0.5);

      @media (max-width: 1199.98px) {
        padding: 3rem;
      }

      @media (max-width: 575.98px) {
        padding: 1rem 2rem 4.5rem 2.2rem;
      }

      h5 {
        color: #000;
        font-size: 2.2rem;
        font-weight: 500;
        line-height: 2.7rem;

        @media (max-width: 575.98px) {
          font-size: 2rem;
        }
      }

      p {
        color: #949494;
        font-size: 1.6rem;
        font-weight: 500;
        margin-top: 0.3rem;
        line-height: 2rem;

        @media (max-width: 575.98px) {
          font-size: 1.3rem;
          line-height: 1.6rem;
        }
      }

      label {
        color: #808080;
        font-size: 1.3rem;
        font-weight: 400;
        line-height: 2rem;
        padding-bottom: 0.8rem;
        margin-top: 5rem;

        @media (max-width: 575.98px) {
          margin-top: 2.5rem;
        }
      }

      button {
        color: #000;
        font-size: 1.7rem;
        font-weight: 600;
        padding: 0;
        border: 0;
        background-color: transparent;
        text-decoration-line: underline;
        min-width: fit-content;
        margin-left: 2.9rem;

        &:disabled {
          cursor: not-allowed;
        }

        @media (max-width: 575.98px) {
          font-size: 1.5rem;
          text-align: end;
        }
      }

      input {
        color: #242426;
        text-transform: uppercase;
        font-size: 1.8rem;
        font-weight: 400;
        line-height: 2.8rem;
        border: none;
        background-color: transparent;
        border-bottom: 1px solid #e2e4e5;
        padding-left: 1.5rem;
        padding-bottom: 0.8rem;
        width: 100%;

        @media (max-width: 575.98px) {
          font-size: 1.4rem;
        }

        &:focus-within {
          outline: none;
          border-bottom: 1px solid #e2e4e5;
        }

        &::placeholder {
          color: #e0e0e0;
          font-size: 1.8rem;
          font-weight: 400;

          @media (max-width: 575.98px) {
            font-size: 1.4rem;
          }
        }
      }

      .checkout-btn {
        border-radius: 6rem;
        background: #000;
        padding: 1.7rem 0 1.7rem 0;
        width: 100%;
        color: #fff;
        font-size: 1.5rem;
        font-weight: 500;
        display: inline-block;
        text-align: center;
        margin-top: 7.3rem;
        text-decoration: none;
        transition: all 500ms cubic-bezier(0.19, 1, 0.22, 1);
        margin-left: 0;

        &:hover {
          background-color: transparent;
          color: #000;
          border: 1px solid;
        }

        @media (max-width: 575.98px) {
          margin-top: 4.6rem;
        }
      }
    }
  }
}


.app.rtl {
  .cart_order-width {
    background-color: #f2f4f9;
    padding: 9.5rem 3.5rem 5.4rem 2rem;

    @media (max-width: 991.98px) {
      padding: 3rem;
    }

    @media (max-width: 575.98px) {
      padding: 2.8rem 2rem;
    }
  }

  .cart_order-width ul li {
    padding-right: 0rem;
    padding-left: 3.4rem;

    @media (max-width: 575.98px) {
      padding-left: 1.5rem;
    }
  }

  .cart_order-coupon {
    padding: 2rem 2rem 2rem 3.5rem;
  }

  .cart_order-code button {
    margin-left: 0rem;
    margin-right: 2.9rem;
  }

  .cart_order-coupon .arrow {
    margin-left: 0;
    margin-right: auto;
    transform: rotate(180deg);
  }

  @media (max-width: 575.98px) {
    .cart_order-code .checkout-btn {
      margin: 0 auto !important;
      margin-top: 4.6rem !important;
    }
  }

  .cart_wrapper{
    .table_info{
      @media (max-width: 575.98px) {
    
        .btn {
          right: unset;
          left: 10px;
        }
      }

    } 

  } 
}