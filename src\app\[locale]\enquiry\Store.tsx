"use client"

import "./enquity.scss";
import Link from "next/link";
import { useContext } from "react";
import { TranslationContext } from "@/contexts/Translation";


function Store({ stores }: any) {

    const { translation: { other, formFields}} = useContext(TranslationContext)

    return (
        <div className="enquiry_left">
            <h5>{other?.availableStores ?? "Available Stores"}</h5>
            {stores?.map((store: any) => (
                <div className="enquiry_box" key={store?._id}>
                    <h6>{store?.name}</h6>
                    <ul>
                        <li>{store?.address}</li>
                        <li>
                            <Link
                                href={"tel:" + store?.countryCode + store?.mobile}
                            >
                                {formFields?.phoneNumber ?? "Phone"} : {store?.countryCode} {store?.mobile}
                            </Link>
                        </li>
                        <li>
                            <Link href={"mailto:" + store?.email}>
                                {formFields?.emailAddress ?? "Email"} : {store?.email}
                            </Link>
                        </li>
                    </ul>
                </div>
            ))}
        </div>
    );
}

export default Store;
