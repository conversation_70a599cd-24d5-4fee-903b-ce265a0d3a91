import { useLocaleContext } from "@/contexts/LocaleProvider";
import "./loyalty.scss";

export default function LoyaltyPoints() {
  const { currencyCode } = useLocaleContext()
  return (
    <div className="loyalty">
      <div>
        <h5>Apply Loyalty Points</h5>
        <p>Use your loyalty points to earn extra discount.</p>
        <p>
          Points <strong>0</strong>
        </p>
      </div>
      <div className="points">
        <h6>Discount</h6>
        <p>{currencyCode + " "} 0</p>
      </div>
    </div>
  );
}
