import "./signuptype.scss";
import Modal from "react-bootstrap/Modal";
import { toast } from "sonner";
import { useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { endpoints } from "@/config/apiEndpoints";
import api from "@/config/axios.interceptor";
import { useRouter, useSearchParams } from "next/navigation";

function SignUpType({ show, handleClose, redirectToCheckout, stepHandler, translation, lang }: any) {
  const queryClient = useQueryClient();
  const [isGuest, setIsGuest] = useState(false);
  const searchParams = useSearchParams()
  const router = useRouter();

  const onSubmit = async (e: any) => {
    e.preventDefault();
    if (isGuest) {
      switch (searchParams.get("type")) {
        case 'checkout':
          router.back();
          setTimeout(() => {
            router.push("/checkout/cart");
          }, 10)
          break;
        case 'wishlist':
          try {
            const res = await api.post(endpoints.manageWishlist, { product: searchParams.get('id') })
            if (res.data.errorCode === 0) {
              queryClient.invalidateQueries({ queryKey: ["wishList"] });
              queryClient.invalidateQueries({ queryKey: ["cart", "counts"] });
              queryClient.invalidateQueries({ queryKey: ["products"] });
              toast.success(res.data.message);
            }
            setTimeout(() => {
              window && window.location.reload()
            }, 10)
            router.back();
          } catch (error: any) {
            toast.error(error.response.data.message);
            router.back();
          }
          break;
        default:
          handleClose()
          break;
      }

      toast.success(lang.includes("en")? `Welcome Guest`: 'مرحبا بالضيف', {});
      queryClient.invalidateQueries({ queryKey: ["user"] });
    } else {
      stepHandler("signup");
    }
  };

  return (
    <>
      <Modal
        className="singup-popup"
        show={show}
        onHide={handleClose}
        backdrop="static"
        keyboard={false}>
        <Modal.Header closeButton></Modal.Header>
        <Modal.Body>
          <h2>{translation?.login?.signup ?? "Sign Up"}</h2>
          <p>{translation?.login?.accountTxt ?? "Choose any of the option to continue."}</p>
          <form className="ingup-popup-fill" onSubmit={onSubmit} style={{ marginTop: "1rem" }}>
            <div className="ingup-popup-inputs radio flex-column align-items-start">
              <label htmlFor="">
                <input
                  type="radio"
                  value="false"
                  onChange={() => setIsGuest(false)}
                  checked={!isGuest}
                />
                {translation?.login?.signupToYateem ?? "Signup to Yateem"}
              </label>
              <label htmlFor="">
                <input
                  value="true"
                  checked={isGuest}
                  onChange={() => setIsGuest(true)}
                  type="radio"
                />
                {translation?.login?.continueAsGuest ?? "Continue as Guest"}
              </label>
            </div>
            <button className="save button">{translation?.productPage?.continue ?? "Continue"}</button>
          </form>
        </Modal.Body>
      </Modal>
    </>
  );
}

export default SignUpType;
