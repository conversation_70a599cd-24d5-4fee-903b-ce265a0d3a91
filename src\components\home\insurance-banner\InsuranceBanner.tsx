import Link from "next/link";
import "./insuranceBanner.scss";
import Image from "next/image";

export default function InsuranceBanner({ data, id }: any) {
  return (
    <section className="insurance" id={id}>
      <div className="insurance_bg">
        <Image quality={100} priority
          className="d-sm-block d-none"
          src={data?.image ?? "/images/home/<USER>"}
          width={1366}
          height={371}
          alt="image"

        />

        <Image quality={100} priority
          className="d-sm-none d-block"
          src={data?.image ?? "/images/home/<USER>"}
          width={1366}
          height={570}
          alt="image"
        />
      </div>
      <div className="container">
        <div className="insurance_content">
          <h2>{data?.title}</h2>
          <p>{data?.description}</p>
          <Link href={data?.redirection || "/insurance"} className="primary-btn">
            {data?.buttonText || "Check Your Insurance"}
          </Link>
        </div>
      </div>
    </section>
  );
}
