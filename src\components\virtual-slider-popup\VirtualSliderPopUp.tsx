"use client";

import Modal from "react-bootstrap/Modal";
import Image from "next/image";
import "./virtual-slider-popup.scss";
import React, { useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { EffectFade, FreeMode, Keyboard, Thumbs } from "swiper/modules";

function VirtualSliderPopUp({ show, handleClose, data }: any) {
  const [thumbsSwiper, setThumbsSwiper] = useState<any>(null);
  return (
    <>
      <Modal
        show={show}
        fullscreen
        onHide={handleClose}
        className="virtual-slider-modal"
      >
        <Modal.Header closeButton></Modal.Header>
        <Modal.Body className="p-0">
          {show ? (
            <div className="virtual-slider">
              <Swiper
                spaceBetween={10}
                keyboard
                navigation={true}
                thumbs={{ swiper: thumbsSwiper }}
                modules={[EffectFade, FreeMode, Thumbs, Keyboard]}
                className="swiper-thumb"
                effect={"fade"}
                speed={500}
              >
                {data.map((items: string, index: number) => (
                  <SwiperSlide key={index + "thumb"}>
                    <Image quality={100} priority
                      src={items ?? ""}
                      width={1366}
                      height={777}
                      alt="virtual slider"
                      className="opacity-0"
                      style={{ maxHeight: "75vh" }}
                      onLoadingComplete={(image) =>
                        image.classList.remove("opacity-0")
                      }
                    />
                  </SwiperSlide>
                ))}
              </Swiper>
              <Swiper
                onSwiper={setThumbsSwiper}
                spaceBetween={10}
                slidesPerView={4}
                breakpoints={{
                  640: {
                    slidesPerView: "auto"
                  }
                }}
                freeMode={true}
                keyboard
                watchSlidesProgress={true}
                modules={[FreeMode, Thumbs, Keyboard]}
                // allowTouchMove={false}
                watchOverflow={true}

                className="swiper-gallery"
              >
                <div className="container">
                  {data.map((items: string, index: number) => (
                    <SwiperSlide key={index + "slider"}>
                      <Image quality={100} priority
                        src={items ?? ""}
                        width={108}
                        height={108}
                        alt="virtual slider"
                        className="opacity-0"
                        onLoadingComplete={(image) =>
                          image.classList.remove("opacity-0")
                        }
                      />
                    </SwiperSlide>
                  ))}
                </div>
              </Swiper>
            </div>
          ) : null}
        </Modal.Body>
      </Modal >
    </>
  );
}

export default VirtualSliderPopUp;
