// 'use client'

// import { createI18nClient } from 'next-international/client';
// // import en from './en';

// const locales = [
//     "sa-en",
//     "sa-ar",
//     "ae-en",
//     "ae-ar",
//     "qa-en",
//     "qa-ar",
//     "om-en",
//     "om-ar",
//     "bh-en",
//     "bh-ar"
// ]

// export const { useI18n, useScopedI18n, I18nProviderClient, useChangeLocale, defineLocale, useCurrentLocale } =
//     createI18nClient(
//         {
//             ["sa-en"]: async () => {
//                 await new Promise(resolve => setTimeout(resolve, 100));
//                 return import('./en');
//             },
//             ["sa-ar"]: async () => {
//                 await new Promise(resolve => setTimeout(resolve, 100));
//                 return import('./ar');
//             },
//             ["ae-en"]: async () => {
//                 await new Promise(resolve => setTimeout(resolve, 100));
//                 return import('./en');
//             },
//             ["ae-ar"]: async () => {
//                 await new Promise(resolve => setTimeout(resolve, 100));
//                 return import('./ar');
//             },
//             ["qa-en"]: async () => {
//                 await new Promise(resolve => setTimeout(resolve, 100));
//                 return import('./en');
//             },
//             ["qa-ar"]: async () => {
//                 await new Promise(resolve => setTimeout(resolve, 100));
//                 return import('./ar');
//             },
//             ["om-en"]: async () => {
//                 await new Promise(resolve => setTimeout(resolve, 100));
//                 return import('./en');
//             },
//             ["om-ar"]: async () => {
//                 await new Promise(resolve => setTimeout(resolve, 100));
//                 return import('./ar');
//             },
//             ["bh-en"]: async () => {
//                 await new Promise(resolve => setTimeout(resolve, 100));
//                 return import('./en');
//             },
//             ["bh-ar"]: async () => {
//                 await new Promise(resolve => setTimeout(resolve, 100));
//                 return import('./ar');
//             },
//         },
//         {
//             // Uncomment to set base path
//             // basePath: '/base',
//             // Uncomment to use custom segment name
//             // segmentName: 'locale',
//             // Uncomment to set fallback locale
//             // fallbackLocale: en,
//         },
//     );