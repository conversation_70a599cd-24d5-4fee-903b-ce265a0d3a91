"use-client";

import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay } from "swiper/modules";
import Image from "next/image";
import Link from "next/link";

function LogoSlider({ brands }: any) {
  return (
    <section>
      <Swiper
        modules={[Autoplay]}
        className="mySwiper"
        speed={500}
        slidesPerView={6}
        loop={true}
        autoplay={{
          delay: 2000,
          disableOnInteraction: false,
          pauseOnMouseEnter: true,
        }}
        breakpoints={{
          320: {
            slidesPerView: 2,
            slidesOffsetBefore: 10,
          },

          600: {
            slidesPerView: 3,
            slidesOffsetBefore: 10,
          },

          768: {
            slidesPerView: 4,
            slidesOffsetBefore: 10,
          },

          1024: {
            slidesPerView: 5,
            spaceBetween: 10,
          },

          1280: {
            slidesPerView: 6,
            spaceBetween: 20,
          },
        }}
        spaceBetween={20}
      >
        {brands?.map((items: any, index: any) => (
          <>
            {items?.image && (
              <SwiperSlide key={index}>
                <Link href={`/brands/${items.slug}`}>
                  <Image
                    quality={100}
                    priority
                    src={items?.image ?? ""}
                    width={250}
                    height={90}
                    alt="image"
                  />
                </Link>
              </SwiperSlide>
            )}
          </>
        ))}
      </Swiper>
    </section>
  );
}

export default LogoSlider;
