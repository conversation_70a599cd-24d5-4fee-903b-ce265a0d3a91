.my-perscription {
  margin-top: 3rem;
  padding-bottom: 18.6rem;
  position: relative;

  @media (max-width: 575.98px) {
    margin-top: 2.8rem;
    margin-bottom: 4rem;
    padding-bottom: 16rem;
  }

  &_head {
    display: flex;
    align-items: center;
    justify-content: space-between;

    button {
      padding: 1.1rem 3.4rem;
      color: #fff;
      border-radius: 6rem;
      background: #000;
      font-size: 1.5rem;
      font-weight: 500;
      border: 1px solid #000;
      transition: 0.3s all ease;

      @media (max-width: 575.98px) {
        position: absolute;
        bottom: 0;
        width: 100%;
        text-align: center;
      }

      &:hover {
        background-color: transparent;
        color: #000;
        transition: 0.3s all ease;
      }
    }
  }

  &_wrapper {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    margin-top: 3rem;
    gap: 2rem;

    @media (max-width: 991.98px) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media (max-width: 575.98px) {
      grid-template-columns: repeat(1, 1fr);
      margin-top: 2.8rem;
    }
  }
}
