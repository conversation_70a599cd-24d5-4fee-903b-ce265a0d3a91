import React, { useState, useEffect } from "react";
import "./lens-index.scss";
import Image from "next/image";
import { endpoints } from "@/config/apiEndpoints";
import { useQuery } from "@tanstack/react-query";
import api from "@/config/axios.interceptor";

function LensIndex({
  lensData,
  handleLensData,
  sum,
  setSum,
  setSelected,
}: any) {
  const [activeIndex, setActiveIndex] = useState(-1);
  const [localSum, setLocalSum] = useState(sum);

  const handleDivClick = (index: number) => {
    setActiveIndex(index);
  };

  const getPrice = (id: string | number) => {
    return indexData?.find((power: any) => power._id === id)?.price;
  };

  const {
    data: indexData,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["lens-index", lensData?.brand],
    queryFn: () => {
      return api
        .get(`${endpoints.lensIndex}/${lensData?.brand}`)
        .then((res) => {
          if (res.status === 200) {
            return res.data?.result?.index;
          } else {
            return [];
          }
        });
    },
  });

  useEffect(() => {
    // if (indexData && indexData?.length > 0) {
    //   handleLensData({ index: lensData?.index || indexData[0]?._id });
    //   setSum("index", getPrice(lensData?.index) || getPrice(indexData[0]?._id));
    // }
    if (indexData?.length && lensData?.index) {
      setActiveIndex(
        indexData?.findIndex((item: any) => item._id === lensData?.index)
      );
      setSum("index", getPrice(indexData[indexData?.findIndex((item: any) => item._id === lensData?.index)]?._id)||0);
      setSelected(true)
    }

    indexData?.length === 0 && setSelected(true);
  }, [indexData,lensData]);

  

  return (
    <div className="lens-index">
      {indexData?.map((item: any, index: number) => {
        return (
          <div
            className={`lens-index_box cursor-pointer ${activeIndex === index ? "isActive" : ""
              }`}
            onClick={() => {
              handleDivClick(index);
              handleLensData({ index: item?._id });
              setSum("index", getPrice(item?._id));
              setSelected(true);
            }}
            key={index}
          >
            <Image quality={100} priority
              src="/images/modal/i1.svg"
              width={62}
              height={53}
              alt="images"
            />
            <h5>{item.name}</h5>
          </div>
        );
      })}
    </div>
  );
}

export default LensIndex;
