"use client";
import { Outfit } from "next/font/google";
import Image from "next/image";
const outfit = Outfit({ subsets: ["latin"] });

// import ErrorPage from "@/components/404/ErrorPage";
// import Error from "next/error";
// import Link from "next/link";
// import { redirect } from "next/navigation";
import { useEffect } from "react";

// Render the default Next.js 404 page when a route
// is requested that doesn't match the middleware and
// therefore doesn't have a locale associated with it.

export default function NotFound() {
  useEffect(() => {
    if (typeof window === "undefined") return;
    window.location.href = "/";
  }, []);

  return (
    <html lang="en">
      <body
        className={`${outfit.className} main-body`}
        style={{ background: "linear-gradient(0deg, rgb(28, 63, 70) 21%, rgb(80, 126, 146) 25%)" }}>
        <div
          style={{
            display: "grid",
            placeItems: "center",
            height: "100dvh",
          }}>
          <div style={{ textAlign: "center", color: "white" }}>
            <Image
              src="/images/splash/yateem-glass.svg"
              style={{ maxWidth: "90%" }}
              width={600}
              height={200}
              alt=""
            />
            <h2>Please Wait</h2>
            <p style={{ paddingBottom: 40 }}>We are redirecting you...</p>
            <a
              style={{
                textDecoration: "none",
                color: "white",
                background: "black",
                padding: "10px 20px",
                borderRadius: 10,
              }}
              href="/">
              Return Home
            </a>
          </div>
        </div>
        {/* <Error statusCode={404} /> */}
        {/* <ErrorPage errorcode="404" /> */}
      </body>
    </html>
  );
}
