import BreadCrumbs from "@/components/breadcrumbs/BreadCrumbs";
import BrandsDetail from "@/components/brand-detail/BrandsDetail";
import ListingPage from "@/components/listing-page/ListingPage";
import { endpoints } from "@/config/apiEndpoints";
import api from "@/config/axios.interceptor";
import {
  dehydrate,
  HydrationBoundary,
  QueryClient,
} from "@tanstack/react-query";
import { Metadata } from "next";
import NotFound from "@/app/not-found";
import BrandWidgets from "./BrandWidgets";

export async function generateMetadata({
  params,
}: {
  params: any;
}): Promise<Metadata> {
  const res = await api.post(endpoints.products, {
    keyword: params.slug,
    page: 1,
    limit: 10,
    brandPage: true,
  });
  const data = await res.data.result;
  const locale = params.locale;

  return {
    title: locale === "ar" ? data?.seoDetails?.title?.ar : data?.seoDetails?.title?.en || "Yateem Optician",
    description: locale === "ar" ? data?.seoDetails?.description?.ar : data?.seoDetails?.description?.en || "Yateem Optician description",
    keywords: locale === "ar" ? data?.seoDetails?.keywords?.ar : data?.seoDetails?.keywords?.en || "Yateem Optician keywords",
    openGraph: {
      title: locale === "ar" ? data?.seoDetails?.title?.ar : data?.seoDetails?.title?.en || "Yateem Optician",
      description: locale === "ar" ? data?.seoDetails?.description?.ar : data?.seoDetails?.description?.en || "Yateem Optician description",
      type: "website",
      images: [
        {
          url: data?.seoDetails?.ogImage,
          width: 742,
          height: 396,
          alt: locale === "ar" ? data?.seoDetails?.title?.ar : data?.seoDetails?.title?.en || "Yateem Optician",
          type: "image/jpeg",
        },
      ],
    },
    alternates: {
      canonical: locale === "ar" ? data?.seoDetails?.canonical?.ar : data?.seoDetails?.canonical?.en,
    },
  };
}

async function Page({ params }: any) {
  const queryClient = new QueryClient();

  try {
    const filterData = await api.get(endpoints.filters + `?brand=${params.slug}`);
    const filters = (await filterData.data?.result) || {};
    const pageLimit = process.env.PAGESIZE || 15;
    const getProducts = async ({ pageParam = 1 }: { pageParam: number }) => {
      const res = await api.post(endpoints.products, {
        keyword: params.slug,
        brandPage: true,
        page: pageParam,
        limit: pageLimit,
      });
      return res.data.result;
    };
    // const UrlParams = useSearchParams();
    // const paramsObject: any = {};
    // for (const [key, value] of UrlParams.entries()) {
    //   if (!paramsObject[key]) {
    //     paramsObject[key] = [value];
    //   } else {
    //     paramsObject[key].push(value);
    //   }
    // }

    await queryClient.prefetchInfiniteQuery({
      queryKey: ["products", params.slug, {}],
      queryFn: getProducts,
      initialPageParam: 1,
      getNextPageParam: (lastPage, pages) => lastPage.nextPage,
      pages: 2,
    });

    return (
      <main>
        <BreadCrumbs
          backHome="Home"
          currentPage={[
            { link: "/brands", title: "Brands" },
            { title: params.slug?.replaceAll("-", " "), link: "" },
          ]}
          image="/images/common/banner4.png"
        />
        {/* <BrandsDetail params={params.slug} /> */}
        <BrandWidgets params={params.slug} />
        <HydrationBoundary state={dehydrate(queryClient)}>
          <ListingPage
            slug={params.slug}
            pageLimit={pageLimit}
            allFilters={filters}
            brandPage={true}
            breadCrump={false}
          />
        </HydrationBoundary>
      </main>
    );
  } catch (error) {
    console.log(error);
    return <NotFound />;
  }
}

export default Page;
