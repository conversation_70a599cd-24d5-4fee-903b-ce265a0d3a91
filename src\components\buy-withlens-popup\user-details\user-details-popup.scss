.user-details-popup {


  .checkout-popup-input {
    margin-top: 3.2rem;

    .countrycode {
      display: flex;
      column-gap: 1.6rem;
      align-items: baseline;
      margin-bottom: 1rem;

      .countrycode-icon {
        position: relative;

        &::after {
          content: "";
          background-image: url("../../../../public/images/common/Icon.png");
          width: 2.4rem;
          height: 2.4rem;
          position: absolute;
          right: -13px;
        }
      }
    }

    .css-lkh0o5-menu {
      margin-top: 29px !important;
    }

    label {
      color: #242426;
      font-size: 1.4rem;
      font-weight: 400;
      line-height: 2rem;
      padding-bottom: 0.8rem;
    }

    #countryCode {
      border: none;
      border-bottom: 1px solid #e2e4e5;
      padding: 0 0.6rem .8rem;
      padding-bottom: 0.8rem;
      appearance: none;
      line-height: 2.8rem;
      max-width: 90px;

      &:focus-visible {
        outline: none;
        border-bottom: 1px solid #e2e4e5;
      }
    }

    .select-container {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;

      .drop-item {
        img {
          width: 30px;
          margin-right: 10px;
        }
      }
    }

    .react-select {
      width: 100%;
      max-width: 90px;
      top: 25px;
      left: 0;
      z-index: 10;
      opacity: 0;
    }

    input {
      width: 100%;
      color: #242426;
      font-size: 1.8rem;
      font-weight: 400;
      line-height: 2.8rem;
      border: none;
      border-bottom: 1px solid #e2e4e5;
      padding: 0 1.5rem;
      padding-bottom: 0.8rem;

      &:focus-within {
        outline: none;
        border-bottom: 1px solid #e2e4e5;
      }

      &::placeholder {
        color: #cacaca;
        font-size: 1.8rem;
        font-weight: 400;
      }
    }

    input[type="number"]::-webkit-inner-spin-button,
    input[type="number"]::-webkit-outer-spin-button {
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
    }
  }

  &.modal {

    &.show .modal-dialog {
      transform: none !important;
    }

    .modal-dialog {
      margin: 0;
      position: fixed;
      right: 0;
      max-width: 68.3rem;
      width: 100%;
      height: 100%;
    }

    .modal-header {
      padding: 0;
      border: none;

      h2 {
        text-align: left;
        line-height: 3.1rem;
        display: flex;
        align-items: center;
        column-gap: 1.4rem;
        font-size: 2.5rem;
        font-weight: 500;

        img {
          width: 4rem;
          height: 4rem;
          object-fit: contain;
        }
      }

      .btn-close {
        background-image: url(../../../../public/images/common/close.png);
        width: 4.8rem;
        height: 4.8rem;
        background-position: center;
        background-repeat: no-repeat;
        background-size: contain;
        opacity: 1;
        padding: 0;

        @media (max-width: 575.98px) {
          width: 2.8rem;
          height: 2.8rem;
          transform: none;
        }

        &:focus {
          box-shadow: none;
          outline: none;
          border: none;
        }
      }
    }

    .modal-content {
      border: none;
      background: #fff;
      height: 100%;
      border-radius: 0;
      overflow: auto;
    }

    .modal-wrap {
      padding: 4rem 5rem 0rem 6.2rem;

      @media (max-width: 575.98px) {
        padding: 1.5rem;
        padding-bottom: 0;
      }
    }

    .modal-body {
      padding: 0;
      margin-top: 3.9rem;
      margin-left: 5.5rem;

      @media (max-width: 575.98px) {
        margin-left: 0;
      }

      h2 {
        text-align: left;
        line-height: 4rem;
        display: flex;
        align-items: center;
        column-gap: 2rem;
        margin-bottom: 3rem;

        @media (max-width: 575.98px) {
          line-height: 3rem;
        }

        img {
          width: 4rem;
          height: 4rem;
          object-fit: contain;

          @media (max-width: 575.98px) {
            display: none;
          }
        }
      }

      button {
        border-radius: 6rem;
        background: #000;
        color: #fff;
        font-size: 1.5rem;
        font-weight: 500;
        height: 5.6rem;
        width: 100%;
        border: none;
      }
    }

    .modal-footer {
      padding: 0;
      border: none;
      margin-top: auto;
      background-color: #F2F4F9;

      .user-details-popup-btn {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        padding-left: 6.2rem;
        padding-right: 4.5rem;
        padding-top: 1rem;
        padding-bottom: 1rem;

        @media (max-width: 575.98px) {
          padding-left: 1.5rem;
          padding-right: 1.5rem;
        }

        h6 {
          font-size: 15px;
          font-weight: 600;
          color: #000000;

          span {
            font-size: 14px;
            font-weight: 600;

            b {
              font-size: 18px;
            }
          }
        }
      }

    }
  }

  &_select {
    display: flex;
    column-gap: 3rem;

    .icon {
      position: relative;
      display: inline-block;
      margin-right: 1.5rem;

      &::after {
        content: "";
        position: absolute;
        background-image: url(../../../../public/images/common/Icon.png);
        background-repeat: no-repeat;
        width: 2rem;
        height: 2rem;
        top: 10px;
        right: 8px;
      }
    }
  }

  &-input {
    &:not(:last-child) {
      margin-bottom: 2.8rem;

      @media (max-width: 575.98px) {
        margin-bottom: 2rem;
      }
    }

    label {
      margin-bottom: 0.8rem;
      color: #242426;
      font-size: 1.4rem;
      font-weight: 400;

      @media (max-width: 575.98px) {
        font-size: 1.3rem;
      }
    }

    input {
      width: 100%;
      border: none;
      border-bottom: 1px solid #e2e4e5;
      padding-left: 1.5rem;
      padding-bottom: 0.8rem;
      position: relative;

      &[type="number"] {
        -moz-appearance: none;
        appearance: none;
      }

      &::-webkit-inner-spin-button {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
      }

      &::placeholder {
        color: #a2a2a3;
        font-size: 1.8rem;
        font-weight: 400;
        line-height: 2.8rem;

        @media (max-width: 575.98px) {
          font-size: 1.5rem;
        }
      }

      &:focus-visible {
        border-bottom: 1px solid #e2e4e5;
        outline: none;
        box-shadow: none;
      }
    }

    textarea {
      width: 100%;
      border: none;
      border-bottom: 1px solid #e2e4e5;
      padding-left: 1.5rem;
      padding-bottom: 6.8rem;
      resize: none;

      @media (max-width: 575.98px) {
        font-size: 1.5rem;
      }

      &:focus-visible {
        border-bottom: 1px solid #e2e4e5;
        outline: none;
        box-shadow: none;
      }

      &::placeholder {
        color: #242426;
        font-size: 1.8rem;
        font-weight: 400;

        @media (max-width: 575.98px) {
          font-size: 1.5rem;
        }
      }
    }
  }

  &-select {
    .icon {
      position: relative;

      &::after {
        content: "";
        background-image: url("../../../../public/images/common/Icon.png");
        width: 2.4rem;
        height: 2.4rem;
        position: absolute;
        right: -8px;
      }
    }

    select {
      width: 100%;
      border: none;
      border-bottom: 1px solid #e2e4e5;
      padding-left: 1.5rem;
      padding-bottom: 0.8rem;
      appearance: none;

      &::placeholder {
        color: #242426;
        font-size: 1.8rem;
        font-weight: 400;
        line-height: 2.8rem;
      }

      &:focus-visible {
        border-bottom: 1px solid #e2e4e5;
        outline: none;
        box-shadow: none;
      }
    }
  }

  &-btn {
    .primary-btn {
      background-color: #000000;
      color: #fff;
      border: 1px solid #000;
      padding: 1.5rem 3.9rem;
      margin-top: 0;

      @media (max-width: 575.98px) {
        padding: 1.1rem 3.9rem;
      }

      &:hover {
        background-color: #ffffff;
        color: #000;
      }
    }
  }
}

.app.rtl {
  .user-details-popup.modal .modal-header .btn-close {
    margin-left: 0;
  }

  .user-details-popup.modal .modal-header h2 img {
    transform: rotate(180deg);
  }

  .user-details-popup.modal .modal-wrap {
    padding: 4rem 6.2rem 0rem 5rem;

    @media (max-width: 575.98px) {
      padding: 1.5rem;
    }
  }

  .user-details-popup .checkout-popup-input input {
    text-align: right;
  }

  .user-details-popup .checkout-popup-input .countrycode .countrycode-icon::after {
    right: auto;
    left: -9px;
  }

  .user-details-popup-select .icon::after {
    right: auto;
    left: 8px;
  }

  .user-details-popup-input input {
    padding-right: 1.5rem;
    padding-left: 0;
  }

  .user-details-popup-select select {
    padding-right: 1.5rem;
    padding-left: 0;
  }

  .user-details-popup-select textarea {
    padding-right: 1.5rem;
    padding-left: 0;
  }
}