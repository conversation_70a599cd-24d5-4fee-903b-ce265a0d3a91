"use client";
import { useQuery } from "@tanstack/react-query";
import LogoSlider from "./LogoSlider";
import "./brandlogos.scss";
import api from "@/config/axios.interceptor";
import { endpoints } from "@/config/apiEndpoints";

export default function BrandsLogos({ id, item, locale }: any) {
  const { data: brands } = useQuery({
    queryKey: ["brands", locale],
    queryFn: () => {
      return api.get(endpoints.brands).then((res) => {
        if (res.status === 200) {
          return res.data?.result;
        }
      });
    },
  });
  return (
    <section className="brands" id={id}>
      <div className="brands_title">
        <h2>{item?.items?.[0]?.title}</h2>
      </div>

      <div className="brands_slider">
        <div dir="rtl">
          <LogoSlider brands={brands} />
        </div>

        <div style={{ marginTop: "20px" }}>
          <LogoSlider brands={brands} />
        </div>
      </div>
    </section>
  );
}
