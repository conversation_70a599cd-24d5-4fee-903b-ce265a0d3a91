import { useContext, useEffect, useRef, useState } from "react";
import "./user-details-popup.scss";
import Modal from "react-bootstrap/Modal";
import Image from "next/image";
import { AnimatePresence, motion } from "framer-motion";
import { axis, pdValues, powers } from "@/lib/constants/lens";
import { SubmitHandler, useForm } from "react-hook-form";
import { toast } from "sonner";
import { endpoints } from "@/config/apiEndpoints";
import api from "@/config/axios.interceptor";
import { getUser } from "@/lib/methods/auth";
import Select from "react-select";
import { countryCodeWithFlags } from "@/lib/countryCodeWithFlag";
import { TranslationContext } from "@/contexts/Translation";
import { useLocaleContext } from "@/contexts/LocaleProvider";

type UserDetails = {
  name: string;
  email: string;
  phone: string;
  countryCode: string;
  country: string;
  message: string;
};

function UserDetailsPopup({
  show,
  handleClose,
  lensData,
  handleLensData,
  setStep,
  back,
  successData,
  handleSuccessData,
  sum
}: any) {
  const {
    register,
    handleSubmit,
    setFocus,
    setValue,
    formState: { errors },
  } = useForm<UserDetails>({});
  const { currentLocale:locale, countryCodes, currencyCode } = useLocaleContext()
  const emiratesOptions = [
    {
      label: locale.includes("en") ? "Abu Dhabi" : "أبو ظبي",
      value: "Abu Dhabi",
    },
    {
      label: locale.includes("en") ? "Ajman" : "عجمان",
      value: "Ajman",
    },
    {
      label: locale.includes("en") ? "Dubai" : "دبي",
      value: "Dubai",
    },
    {
      label: locale.includes("en") ? "Fujairah" : "الفجيرة",
      value: "Fujairah",
    },
    {
      label: locale.includes("en") ? "Ras Al Khaimah" : "رَأْس ٱلْخَيْمَة",
      value: "Ras Al Khaimah",
    },
    {
      label: locale.includes("en") ? "Sharjah" : "الشارقة",
      value: "Sharjah",
    },
    {
      label: locale.includes("en") ? "Umm Al Quwain" : "أم القيوين",
      value: "Umm Al Quwain",
    },
  ];

  const {translation: {productPage, popup, formFields}} = useContext(TranslationContext)

  const onSubmit: SubmitHandler<UserDetails> = (data) => {
    api
      .post(endpoints.buyWithLens, { ...lensData, userDetails: data })
      .then((res) => {
        console.log(res.data)
        handleSuccessData({
          ...successData,
          title:
            // res?.data?.result?.message || "Our experts will contact you soon.",
            productPage?.enquirySuccess ?? "Our experts will contact you soon.",
            primaryBtnTxt: productPage?.continue ?? "Continue"
        });
        setStep("userDetails");
      })
      .catch((err) => {
        toast.error(err.response.data.message);
      });
  };

  useEffect(() => {
    const fetchUser = async () => {
      const user = await getUser();
      setFocus("name");
      if (user) {
        setValue("name", user?.name);
        setValue("phone", user?.mobile);
        setValue("email", user?.email);
        setValue("countryCode", user?.countryCode || countryCodes[locale.split("-")[0]]);
        setValue("country", user?.emirates);
        setFocus("message");
      } else {
        setValue("countryCode", countryCodes[locale.split("-")[0]]);
      }
    };
    fetchUser();
  }, []);

  return (
    <>
      <Modal
        show={show}
        onHide={handleClose}
        backdrop="static"
        keyboard={false}
        centered
        className="user-details-popup"
      >
        <div className="modal-wrap">
          <Modal.Header closeButton>
            <h2>
              <Image
                quality={100}
                priority
                onClick={() => back("userDetails")}
                src="/images/common/backarrow.svg"
                width={40}
                height={40}
                alt="back arrow"
              />
              {productPage?.userDetails ?? "User Details"}
            </h2>
          </Modal.Header>
          <Modal.Body>
            <div className="user-details-popup-form">
              <div className="user-details-popup-input">
                <label htmlFor="">{formFields?.fullName ?? "Full name"}</label>
                <input
                  {...register("name", { required: (formFields?.fullNameRequiredError ?? "Name is required") })}
                  type="text"
                  placeholder="Alex Smith"
                />
                {errors.name && (
                  <small className="error text-danger">
                    {errors.name.message}
                  </small>
                )}
              </div>
              <div className="user-details-popup-input">
                <label htmlFor="email" className="email">{formFields?.emailAddress ?? "Email Address"}</label>
                <input
                  {...register("email", {
                    required: (formFields?.emailAddressRequiredError ?? "Please enter email!"),
                    pattern: {
                      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                      message: (formFields?.emailAddressInvalidError ?? "Invalid email address"),
                    },
                    validate: (value) => {
                      if (value.includes("@yahoo.com")) {
                        return (formFields?.emailAddressYahooError ?? "We don't accept Yahoo accounts");
                      }
                    },
                  })}
                  type="email"
                  name="email"
                  placeholder="<EMAIL>"
                />
                {errors.email && (
                  <small className="error text-danger">
                    {errors.email.message}
                  </small>
                )}
              </div>

              {/* <div className="user-details-popup-input">
                  <label htmlFor="">Phone Number</label>
                  <input
                    {...register("phone", {
                      required: "Phone Number is required",
                    })}
                    type="text"
                    placeholder="Phone Number"
                  />
                  {errors.phone && (
                    <small className="error text-danger">
                      {errors.phone.message}
                    </small>
                  )}
                </div> */}

              <div className="checkout-popup-input position-relative">
                <label htmlFor="mobile">{formFields?.phoneNumber ?? "Enter your phone number"}</label>

                <Select
                  className="select-container"
                  onChange={(e: any) => setValue("countryCode", e.value)}
                  styles={
                    {
                      // option:(state)=>{}
                    }
                  }
                  theme={(theme) => ({
                    ...theme,
                    borderRadius: 0,
                    colors: {
                      ...theme.colors,
                      primary25: "#ccc",
                      primary: "black",
                    },
                  })}
                  classNames={{
                    control: (state) => "react-select",

                    dropdownIndicator: () => "d-none",
                    option: (state) =>
                      state.isSelected ? "option selected" : "option",
                    menu: () => "menu",
                  }}
                  formatOptionLabel={(country) => (
                    <div className="drop-item">
                      <img src={country.image} alt="" />
                      {country.label}
                    </div>
                  )}
                  options={countryCodeWithFlags?.map((country) => ({
                    label: country.name,
                    value: country.dial_code,
                    image: country.image,
                  }))}
                />

                <div className="countrycode">
                  <div className="countrycode-icon">
                    <input
                      {...register("countryCode")}
                      id="countryCode"
                      name="countryCode"
                    />
                  </div>

                  <input
                    {...register("phone",
                      {
                        required: (formFields?.phoneNumberRequiredError ?? "Phone Number is Required"),
                        pattern: {
                          value: /^[0-9]*$/,
                          message: (formFields?.phoneNumberInvalidError ?? "Invalid Phone Number"),
                        },
                      }
                    )}
                    tabIndex={1}
                    type="tel"
                    id="mobile"
                    placeholder="************"
                  />
                </div>
                {errors.phone && (
                  <small className="error text-danger">
                    {errors.phone.message}
                  </small>
                )}
              </div>

              <div className="user-details-popup-input">
                <label htmlFor="">{formFields?.emirates ?? "Emirates"}</label>

                <div className="user-details-popup-select">
                  <div className="icon">
                    <select
                      {...register("country", {
                        required: "Emirates is required",
                      })}
                    >
                      <option selected disabled value="">
                        {formFields?.selectEmirates ?? "Select Emirates"}
                      </option>
                      {emiratesOptions.map((item:any, key:any)=>(
                        <option key={key} value={item.value}>{item.label}</option>
                      ))}
                      {/* <option value="Abu Dhabi">Abu Dhabi</option>
                      <option value="Ajman">Ajman</option>
                      <option value="Dubai">Dubai</option>
                      <option value="Fujairah">Fujairah</option>
                      <option value="Ras Al Khaimah">Ras Al Khaimah</option>
                      <option value="Sharjah">Sharjah</option>
                      <option value="Umm Al Quwain">Umm Al Quwain</option> */}
                    </select>
                  </div>
                </div>
                {errors.country && (
                  <small className="error text-danger">
                    {errors.country.message}
                  </small>
                )}
              </div>

              <div className="user-details-popup-input">
                <label htmlFor="">{formFields?.address ?? "Address"}</label>
                <textarea
                  {...register("message", {
                    required: (formFields?.addressRequired ?? "Address is required"),
                  })}
                  placeholder="Enter Address"
                ></textarea>
                {errors.message && (
                  <small className="error text-danger">
                    {errors.message.message}
                  </small>
                )}
              </div>
            </div>
          </Modal.Body>
        </div>
        <Modal.Footer>
          <div className="user-details-popup-btn">
            <h6>
              Subtotal :
              <span>
                {currencyCode + " "} <b>{sum}</b>
              </span>
            </h6>
            <button onClick={handleSubmit(onSubmit)} type="submit" className="primary-btn">
              {productPage?.continue ?? "Continue"}
            </button>
          </div>
        </Modal.Footer>
      </Modal>
    </>
  );
}

export default UserDetailsPopup;
