.my-cashbacks{
    display: flex;
    flex-direction: column;
    gap: 1rem;

    h2{
        @media screen and (max-width: 1200px){
            margin-top: 3rem;
        }
    }
    
    .banner{
        display: flex;
        gap: 1rem;

        background-color: #F2F4F9;
        padding: 2.5rem 4.5rem;
        margin-top: 2rem;
        border-radius: 20px;
        justify-content: space-between;
        
        @media screen and (max-width: 650px)  {
            flex-direction: column;
        }

        @media screen and (max-width: 425px)  {
            padding: 1rem 1.5rem;
        }

        &-item{
            display: grid;
            grid-template-columns: auto 1fr auto;
            gap: 2rem;
            align-items: center;
            justify-content: space-evenly;
            width: 100%;

            img{
                object-fit: contain;
                width: 45px;
            }

            p{
                color: #797A7D;
                font-size: 1.5rem;
                font-weight: 400;
            }

            @media screen and (min-width: 650px)  {
                display: flex;
                flex-direction: column;
                width: fit-content;
                align-items: baseline;
                gap: .5rem;

                img{
                    width: fit-content;
                }
            }

            @media screen and (max-width: 425px)  {
                h4{
                    font-size: 1.5rem;
                }
                p{
                    font-size: 1.3rem
                }
            }
        }
    }

    .activity{
        margin-top: 2rem;
        display: flex;
        flex-direction: column;
        padding: 2.5rem 3rem;
        background: #fff;
        border-radius: 15px;
        box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
        gap: 1.5rem;

        &-item{

            &:not(:last-child){
                border-bottom: 1px solid #E6E6E6;
                padding-bottom: 1rem;
            }

            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 1rem;

            &-info{
                display: flex;
                flex-direction: column;

                h6{
                    font-size: 1.6rem;
                    font-weight: 600;
                }
                p{
                    font-size: 1.4rem;
                    color: #797A7D;
                }

                @media screen and (max-width: 650px){
                    h6{
                        font-size: 1.4rem;
                    }
                    p{
                        font-size: 1.2rem;
                    }
                }
            }

            &-amount{
                text-align: right;

                h6{
                    font-size: 1.5rem;
                    font-weight: 600;
                }
                p{
                    font-size: 1.3rem;
                    color: #797A7D;
                }
                @media screen and (max-width: 650px){
                    h6{
                        font-size: 1.3rem;
                    }
                    p{
                        font-size: 1.1rem;
                    }
                }
            }
        }

        @media screen and (max-width: 650px){
            padding: 1.8rem 1.5rem;
        }
    }

    .notes{
        margin-top: 2rem;
        ul{
            padding: unset;
            margin: unset;
            margin-top: .5rem;
            margin-left: 1rem;
            li{
                list-style-type: disc;
                list-style-position: inside;
                padding: unset;
                margin: unset;
                color: #2D3736;
                font-weight: 300;
            }
        }
    }

}
