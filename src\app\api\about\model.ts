import mongoose from "mongoose";

const aboutSchema = new mongoose.Schema({
    refid: { type: String, required: true },
    sectionOne: {
        title: {
            en: { type: String, required: true },
            ar: { type: String, }
        },
        description: {
            en: { type: String, required: true },
            ar: { type: String, }
        },
        image: { type: String, required: true },
    },
    sectionTwo: [
        {
            title: {
                en: { type: String, required: true },
                ar: { type: String, }
            },
            count: { type: String, required: true },
        }
    ],
    sectionThree: {
        title: {
            en: { type: String, required: true },
            ar: { type: String, }
        },
        description: {
            en: { type: String, required: true },
            ar: { type: String, }
        },
        timeline: [{
            title: {
                en: { type: String, required: true },
                ar: { type: String, }
            },
            description: {
                en: { type: String, required: true },
                ar: { type: String, }
            },
            image: { type: String, required: true },
            year: { type: String, required: true }
        }]
    },
    sectionFour: {
        title: {
            en: { type: String, required: true },
            ar: { type: String, }
        },
        data: [{
            title: {
                en: { type: String, required: true },
                ar: { type: String, }
            },
            description: {
                en: { type: String, required: true },
                ar: { type: String, }
            },
            image: { type: String, required: true },
        }]
    },
    createdBy: { type: mongoose.Schema.Types.ObjectId, ref: "admin.users" },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
}, { timestamps: true });

export default mongoose.models.About || mongoose.model("about", aboutSchema);