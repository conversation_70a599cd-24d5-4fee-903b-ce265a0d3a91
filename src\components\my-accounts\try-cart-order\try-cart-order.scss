.print-Padding {

  .trycart-order,
  .trycart-detail,
  .trycart-invoice {
    padding: 0 20px;
  }
}

.printSection {
  .printHeader {
    padding: 30px 20px;
    margin-bottom: 20px;
    background-color: #000;
    text-align: center;

    img {
      width: 200px;
    }
  }
}

.trycart-order {
  .tryCartButton {
    padding: 0.7rem 1.9rem;
    border-radius: 0.8rem;
    border: 1px solid #000;
    font-size: 1.6rem;
    font-weight: 400;
    transition: 0.3s all ease;
    color: #000000;

    &:hover {
      color: #ffffff;
      transition: 0.3s all ease;
      background-color: #000;
    }
  }

  h2 {
    text-align: left;

    @media (max-width: 575.98px) {
      margin-top: 1.5rem;
    }
  }

  &_table {
    .table {
      margin-top: 3.6rem;

      @media (max-width: 575.98px) {
        margin-top: 2.8rem;
      }

      &_body {
        width: 100%;
      }

      &_row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-right: 2.5rem;

        @media (max-width: 575.98px) {
          flex-direction: column;
          align-items: flex-start;
        }

        &:not(:last-child) {
          border-bottom: 1px solid #bebcbd;
          padding-bottom: 2.3rem;
          margin-bottom: 2rem;

          @media (max-width: 575.98px) {
            padding-bottom: 2rem;
          }
        }
      }

      &_col-one {
        display: flex;
        align-items: center;
        column-gap: 2.5rem;
        padding: 0;
        max-width: -51rem;
        width: 100%;

        @media (max-width: 767.98px) {
          column-gap: 2.1rem;
          max-width: 100%;
        }

        img {
          width: 12rem;
          height: 12rem;
          padding: 0;
          border-radius: 1.2rem;
          object-fit: contain;

          @media (max-width: 575.98px) {
            width: 9.6rem;
            height: 12.5rem;
          }
        }

        ul {
          li {
            color: #807d7e;
            font-size: 1.4rem;
            line-height: 1.7rem;
            font-weight: 400;
            margin-bottom: 0.4rem;

            &:nth-of-type(1) {
              color: #000;
              font-size: 1.6rem;
              font-weight: 600;
              letter-spacing: 0.032rem;
              margin-bottom: 1.1rem;
              line-height: 2rem;

              @media (max-width: 575.98px) {
                margin-bottom: 0.5rem;
              }
            }

            &:last-child {
              margin-top: 1.8rem;
              color: #000;
              font-size: 1.6rem;
              font-weight: 600;
            }
          }
        }
      }

      &_col-two {
        padding: 0;
        display: flex;
        align-items: center;

        @media (max-width: 767.98px) {
          margin-top: 2rem;
        }

        @media (max-width: 575.98px) {
          display: flex;
          justify-content: space-between;
          width: 100%;
        }

        h5 {
          color: #000;
          font-size: 1.8rem;
          font-weight: 600;

          @media (max-width: 767.98px) {
            display: none;
          }
        }

        button {
          border: none;
          background-color: transparent;
        }

        .move-cart-btns {
          color: #fff;
          font-size: 1.5rem;
          font-weight: 500;
          border-radius: 6rem;
          background: #000;
          margin-left: 11.4rem;
          width: 13.2rem;
          height: 5.6rem;

          @media (max-width: 991.98px) {
            margin-left: 4.8rem;
          }

          @media (max-width: 767.98px) {
            margin-left: 0;
          }
        }

        .not-btn {
          color: #ff9c9c;
          font-size: 1.5rem;
          font-weight: 400;
          text-decoration-line: underline;
          margin-left: 4.8rem;

          @media (max-width: 575.98px) {
            margin-left: 0;
          }
        }
      }
    }
  }
}

.trycart-detail {
  margin-top: 3.5rem;
  display: flex;
  column-gap: 11.5rem;

  @media (max-width: 991.98px) {
    column-gap: 2.5rem;
  }

  @media (max-width: 767.98px) {
    flex-direction: column;
    margin-top: 3rem;
  }

  &_left {
    width: 60%;

    @media (max-width: 767.98px) {
      width: 100%;
      order: 2;
    }
  }

  &_right {
    width: 40%;
    border-radius: 2rem;
    background: #f2f4f9;
    padding: 5.1rem 2rem 3.8rem 2rem;

    @media (max-width: 767.98px) {
      width: 100%;
    }

    @media (max-width: 575.98px) {
      padding: 2.3rem 1.4rem 2.3rem 2.3rem;
    }

    h5 {
      color: #000;
      font-size: 2.2rem;
      font-weight: 500;

      @media (max-width: 575.98px) {
        font-size: 1.6rem;
      }
    }

    p {
      color: #949494;
      font-size: 1.6rem;
      font-weight: 500;
      margin-top: 1.2rem;
      line-height: 2rem;
      max-width: 38.3rem;
      width: 100%;

      @media (max-width: 575.98px) {
        font-size: 1.3rem;
        font-weight: 400;
        line-height: 1.6rem;
        margin-top: 0.9rem;
        color: #000;
      }
    }

    ul {
      margin-top: 3.2rem;

      @media (max-width: 575.98px) {
        margin-top: 1.9rem;
      }

      li {
        display: flex;
        align-items: center;
        justify-content: space-between;

        &:not(:last-child) {
          border-bottom: 1px solid rgba(0, 0, 0, 0.2);
          padding-bottom: 1.8rem;
          margin-bottom: 2.4rem;

          @media (max-width: 575.98px) {
            padding-bottom: 1.1rem;
            margin-bottom: 0.9rem;
          }
        }

        h6 {
          color: #000;
          font-size: 1.5rem;
          font-weight: 600;
        }

        .left {
          span {
            color: #374957;
            font-size: 1.5rem;
            font-weight: 400;
          }
        }

        .right {
          font-size: 1.4rem;

          span {
            font-size: 1.8rem;
          }
        }
      }
    }

    button {
      border-radius: 9.8rem;
      border: 1px solid #000;
      background-color: transparent;
      height: 6.574rem;
      width: 100%;
      margin-top: 4rem;

      @media (max-width: 575.98px) {
        margin-top: 2.8rem;
        height: 4.5rem;
      }
    }
  }

  &_cutemer-details {
    @media (max-width: 767.98px) {
      margin-top: 2rem;
    }

    h4 {
      color: #000;
      font-size: 2.4rem;
      font-weight: 500;

      @media (max-width: 575.98px) {
        font-size: 2rem;
      }
    }

    .box {
      background: #f2f4f9;
      padding: 2rem 2rem 2rem 2.3rem;
      border-radius: 2rem;
      margin-top: 2rem;

      @media (max-width: 575.98px) {
        margin-top: 1.5rem;
        padding: 2.1rem 2rem 4rem 1.5rem;
      }

      ul {
        li {
          color: #000;
          font-weight: 400;
          font-size: 1.6rem;
          max-width: 40.2rem;
          width: 100%;
          line-height: 2.2rem;

          @media (max-width: 575.98px) {
            max-width: 26rem;
          }

          &:first-child {
            font-size: 2rem;
          }

          a {
            color: #000;
          }

          &:not(:last-child) {
            margin-bottom: 1.58rem;
          }
        }
      }
    }
  }

  &_shipping-details {
    margin-top: 4rem;

    @media (max-width: 767.98px) {
      margin-top: 2rem;
    }

    h4 {
      color: #000;
      font-size: 2.4rem;
      font-weight: 500;

      @media (max-width: 575.98px) {
        font-size: 2rem;
      }
    }

    .box {
      margin-top: 2rem;
      border-radius: 2rem;
      border: 1px solid #ebebeb;
      background: #fdfdfe;
      display: flex;
      justify-content: space-between;
      padding: 3rem 3.9rem 2.4rem 2.6rem;

      @media (max-width: 575.98px) {
        flex-direction: column;
        margin-top: 1.5rem;
        padding: 3rem 3.9rem 1rem 2.6rem;
        align-items: flex-start;
      }

      &_content {
        width: 70%;

        @media (max-width: 575.98px) {
          width: 100%;
        }
      }

      &_title {
        h5 {
          color: #242426;
          font-size: 1.6rem;
          font-weight: 600;
          line-height: 2.8rem;
        }
      }

      &_logo {
        padding-top: 2.1rem;
        margin-top: 1.4rem;
        border-top: 1px solid #e4e4e4;
        display: flex;
        align-items: center;

        img {
          width: 8.7rem;
          height: 3.9rem;
          object-fit: contain;
        }

        span {
          font-size: 1.5rem;
          font-weight: 400;
          color: #000;
          margin-left: 1.8rem;
          display: inline-block;
        }
      }

      &_barcode {
        @media (max-width: 575.98px) {
          margin-top: 1.4rem;
        }

        img {
          object-fit: contain;
        }
      }
    }
  }
}

.trycart-invoice {
  padding-bottom: 2.5rem;

  .invoice {
    display: flex;
    justify-content: space-between;
    margin-top: 4rem;
    padding: 3.7rem 3rem 4.2rem 2.9rem;
    border-radius: 2rem;
    background: #f2f4f9;
    flex-wrap: wrap;
    row-gap: 2rem;

    @media (max-width: 575.98px) {
      flex-direction: column;
      padding: 2.8rem 1.8rem 2.8rem 1.8rem;
      row-gap: 0.7rem;
      margin-top: 2rem;
    }

    ul {
      li {
        font-size: 1.4rem;
        font-weight: 400;
        line-height: 1.7rem;
        color: #000;

        @media (max-width: 575.98px) {
          font-size: 1.3rem;
          line-height: 1.6rem;
        }

        &:not(:last-child) {
          margin-bottom: 2rem;

          @media (max-width: 575.98px) {
            margin-bottom: 0.7rem;
          }
        }

        span {
          color: #595959;
        }
      }
    }

    button {
      padding: 1.4rem 2.8rem;
      border-radius: 9.8rem;
      border: 1px solid #000;
      background-color: transparent;
      color: #000;
      font-size: 1.8rem;
      font-weight: 400;
      padding: 0.4rem 2.8rem;
      height: 5.1rem;

      @media (max-width: 575.98px) {
        margin-top: 1.7rem;
        font-size: 1.4rem;
        height: 4.5rem;
      }
    }
  }
}