h2 {
  font-size: 3.2rem;
  font-weight: 500;
  color: #000000;
  text-align: center;

  @include media-breakpoint-down(xl) {
    font-size: 2.6rem;
  }
}

h4 {
  font-size: 2rem;

  @include media-breakpoint-down(sm) {
    font-size: 1.7rem;
  }
}

p {
  font-size: 1.6rem;
  font-weight: 400;
  line-height: 2.72rem;

  @media (max-width: 575.98px) {
    font-size: 1.4rem;
    line-height: 2.02rem;
  }
}


.primary-btn {
  margin-top: 2.5rem;
  display: inline-block;
  color: #000000;
  font-size: 1.5rem;
  font-weight: 500;
  padding: 1.5rem 3.5rem;
  background-color: #ffffff;
  border-radius: 30px;
  border: 1px solid #fff;
  transition: 0.3s all ease;

  @media (max-width: 575.98px) {
    margin-top: 1.6rem;
    padding: 1.3rem 3.5rem;
  }

  &:hover {
    background-color: transparent;
    color: #fff;
    transition: 0.3s all ease;
  }
}

.disabled{
  background-color: #797A7D !important;
  color: #fff !important;
  border: none !important;

  &:hover{
    background-color: #797A7D !important;
    color: #fff !important;
    border: none !important;
  }
}