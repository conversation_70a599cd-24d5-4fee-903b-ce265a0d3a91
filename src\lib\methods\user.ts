import { endpoints } from "@/config/apiEndpoints";
import api from "@/config/axios.interceptor";

export const getAddress = async () => {
    const res = await api.get(endpoints.address);
    if (res.data.errorCode === 0) return res.data.result;
    else return null;
}

export const getCashbacks = async () => {
    const res = await api.get(endpoints.cashbacks);
    if (res.data.errorCode === 0) return res.data.result;
    else return null;
}

export const getWishlist = async () => {
    const res = await api.get(endpoints.wishlist);
    if (res.data.errorCode === 0) return res.data.result;
    else return null;
}

export const getPrescription = async () => {
    const res = await api.get(endpoints.prescriptions);
    if (res.data.errorCode === 0) return res.data.result;
    else return null;
}

export const getSubscription = async () => {
    const res = await api.get(endpoints.subscriptionList);
    if (res.data.errorCode === 0) return res.data.result;
    else return null;
}