"use client"

import { TranslationContext } from "@/contexts/Translation";
import Image from "next/image";
import Link from "next/link";
import React, { useContext } from "react";

function Page() {

  const { translation: { homeTry } }: any = useContext(TranslationContext);

  return (
    <section className="coming-soon">
      <div className="container">
      <div className="coming-soon-div">
          <Image
            src="/images/common/coming-soon.svg"
            width={1920}
            height={1080}
            alt="coming soon"
          />
          <h5>{homeTry?.comingSoonTitle ?? "Try Cart Coming Soon...!"}</h5>
          <p>{homeTry?.comingSoonText ?? "Trying products at home, soon available"}</p>
          <Link href="/" className="primary-btn">{homeTry?.comingSoonBtn ?? "Homepage"}</Link>
      </div>
      </div>
    </section>
  );
}

export default Page;
