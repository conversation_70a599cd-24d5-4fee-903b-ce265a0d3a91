import "./product-detail.scss";
import { CountProvider } from "@/contexts/AddBtnContext";
import { getProduct } from "@/lib/methods/products";
import NotFound from "../../not-found";
import { Metadata } from "next/types";
import { getVmPolicy } from "@/lib/methods/vm";
import { dehydrate, HydrationBoundary, QueryClient, useQuery } from "@tanstack/react-query";
import Details from "./Details";

export async function generateMetadata({
  params,
}: {
  params: any;
}): Promise<Metadata> {
  const product = await getProduct(params?.slug);
  const locale = params.locale;
  return {
    title: locale === "ar" ? product?.seoDetails?.title?.ar : product?.seoDetails?.title?.en || "Yateem Optician",
    description: locale === "ar" ? product?.seoDetails?.description?.ar : product?.seoDetails?.description?.en || "Yateem Optician",
    keywords: locale === "ar" ? product?.seoDetails?.keywords?.ar : product?.seoDetails?.keywords?.en || "Yateem Optician",

    openGraph: {
      title: locale === "ar" ? product?.seoDetails?.title?.ar : product?.seoDetails?.title?.en || "Yateem Optician",
      description: locale === "ar" ? product?.seoDetails?.description?.ar : product?.seoDetails?.description?.en || "Yateem Optician",
      type: "website",
      images: [
        {
          url: product?.seoDetails?.ogImage,
          width: 742,
          height: 396,
          alt: "Yateem",
        },
      ],
    },
    alternates: {
      canonical: locale === "ar" ? product?.seoDetails?.canonical?.ar : product?.seoDetails?.canonical?.en,
    },
  };
}

async function Page({
  params,
  searchParams,
}: {
  params: { slug: any };
  searchParams: any;
}) {
  try {
    // const product = await getProduct(params?.slug);

    const queryClient = new QueryClient()


    const [_, { value: vmPolicy }]: any = await Promise.allSettled([
      queryClient.prefetchQuery({
        queryKey: ['product', params?.slug],
        queryFn: () => getProduct(params?.slug),
      })
      , getVmPolicy()])

    return (
      <main style={{ backgroundColor: "#fff", position: "relative", zIndex: 1 }} >
        <HydrationBoundary state={dehydrate(queryClient)}>
          <CountProvider>
            
            <Details slug={params?.slug} vmPolicy={vmPolicy}  />
            {/* <ProductStickyBar /> */}
          </CountProvider>
        </HydrationBoundary>
      </main>
    );
  } catch (error) {
    return <NotFound />;
  }
}

export default Page;
