"use client"

import { Modal } from "react-bootstrap";
import "./Terms.scss"
import { useLocaleContext } from "@/contexts/LocaleProvider";

export default function TermsAndConditions({show, handleClose, content}: any) {
  const { currentLocale:locale } = useLocaleContext()
    return (
      <Modal
          className={`terms-pop-up ${locale == "ar"? "app rtl": ""}`}
          show={show}
          onHide={handleClose}
          backdrop="static"
          keyboard={false}
          centered
        >
          <Modal.Header closeButton>
            <h2 style={{margin: "0 auto"}}>{content?.terms?.title}</h2>
          </Modal.Header>
          <Modal.Body>
            <div dangerouslySetInnerHTML={{ __html: content?.terms?.content }}>
            </div>
          </Modal.Body>
        </Modal>
    )
  }
  