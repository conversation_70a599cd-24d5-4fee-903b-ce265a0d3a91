"use client";

import React, { useContext, useEffect } from "react";
import { useState } from "react";
import "./EnterManuallyModal.scss";
import Image from "next/image";
import { useRef } from "react";
import Select from "react-select";
import { TranslationContext } from "@/contexts/Translation";

function EnterManuallyModal({
  show,
  pdValue,
  onClick,
  handleCloseEnterManually,
  powerData,
  errors,
  register,
  onSubmit,
  handleSubmit,
  defaultFormRef,
  setValue,
  lensData
}: any) {
  const ref = useRef(null);
  const [isPdKnown, setIsPdKnown] = useState(pdValue ? "known" : "unknown");
  console.log(powerData)

  const {translation: {productPage}} = useContext(TranslationContext)

  const onRadioChange = (event: any) => {
    setIsPdKnown(event.target.value);
    if (event.target.value === "unknown") {
      setValue('pd', '')
    }
  };

  const leftSph = powerData?.sph?.find((power: any) => power._id === lensData?.prescription?.leftSph)
  const rightSph = powerData?.sph?.find((power: any) => power._id === lensData?.prescription?.rightSph)
  const leftCyl = powerData?.cyl?.find((power: any) => power._id === lensData?.prescription?.leftCyl)
  const rightCyl = powerData?.cyl?.find((power: any) => power._id === lensData?.prescription?.rightCyl)
  const leftAxis = powerData?.axis?.find((power: any) => power._id === lensData?.prescription?.leftAxis)
  const rightAxis = powerData?.axis?.find((power: any) => power._id === lensData?.prescription?.rightAxis)
  const leftAdd = powerData?.add?.find((power: any) => power._id === lensData?.prescription?.leftAdd)
  const rightAdd = powerData?.add?.find((power: any) => power._id === lensData?.prescription?.rightAdd)

  return (
    <div className="enter-manually">
      <div className="enter-manually_header">
        <h2>
          <Image
            quality={100}
            priority
            src="/images/common/backarrow.svg"
            width={40}
            height={40}
            alt="back arrow"
            className="cursor-pointer"
            onClick={handleCloseEnterManually}
          />
          {productPage?.enterManually ?? "Enter Manually"}
        </h2>
        <button>
          <Image
            quality={100}
            priority
            src="/images/common/close.svg"
            width={40}
            height={40}
            alt="close"
            onClick={onClick}
          />
        </button>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} ref={defaultFormRef}>
        <div className="enter-manually_box">
          <span>{productPage?.leftEye ?? "OD (Left Eye)"}</span>

          <div className="enter-manually_boxflex">
            <div className="enter-manually_select">
              <label htmlFor="sphere">{productPage?.sphere ?? "Sphere"}</label>
              <Select
                defaultValue={leftSph && { label: leftSph?.name, value: leftSph?._id }}
                options={powerData?.sph.map((power: any) => ({ label: power?.name, value: power?._id }))}
                onChange={(e: any) => setValue("leftSph", e.value)}
                placeholder={productPage?.select ?? "Select"}
              />
              <input
                className="inp"
                {...register("leftSph", { required: true })}
                id="leftSph"
                name="leftSph"
              />
              {errors.leftSph && <p className="error">{errors.leftSph.type}</p>}
            </div>

            <div className="enter-manually_select">
              <label htmlFor="cylinder">{productPage?.cyl ?? "Cylinder"}</label>
              <Select
                defaultValue={leftCyl && { label: leftCyl?.name, value: leftCyl?._id }}
                options={powerData?.cyl.map((power: any) => ({ label: power?.name, value: power?._id }))}
                onChange={(e: any) => setValue("leftCyl", e.value)}
                placeholder={productPage?.select ?? "Select"}
              />
              <input
              className="inp"
                {...register("leftCyl", { required: true })}
                id="leftCyl"
                name="leftCyl"
              />
              {errors.leftCyl && <p className="error">{errors.leftCyl.type}</p>}
            </div>

            <div className="enter-manually_select">
              <label htmlFor="cars">{productPage?.axis ?? "Axis"}</label>
              <Select
                defaultValue={leftAxis && { label: leftAxis?.name, value: leftAxis?._id }}
                options={powerData?.axis.map((power: any) => ({ label: power?.name, value: power?._id }))}
                onChange={(e: any) => setValue("leftAxis", e.value)}
                placeholder={productPage?.select ?? "Select"}
              />
              <input
              className="inp"
                {...register("leftAxis", { required: true })}
                id="leftAxis"
                name="leftAxis"
              />
              {errors.leftAxis && (
                <p className="error">{errors.leftAxis.type}</p>
              )}
            </div>
            {lensData?.vision == "progressive" && <div className="enter-manually_select">
              <label htmlFor="cars">{productPage?.addition ?? "Addition"}</label>
              <Select
                defaultValue={leftAdd && { label: leftAdd?.name, value: leftAdd?._id }}
                options={powerData?.add.map((power: any) => ({ label: power?.name, value: power?._id }))}
                onChange={(e: any) => setValue("leftAdd", e.value)}
                placeholder={productPage?.select ?? "Select"}
              />
              <input
              className="inp"
                {...register("leftAdd", { required: true })}
                id="leftAdd"
                name="leftAdd"
              />
              {errors.leftAdd && (
                <p className="error">{errors.leftAdd.type}</p>
              )}
            </div>}
          </div>
        </div>

        <div className="enter-manually_box">
          <span>{productPage?.rightEye ?? "OD (Right Eye)"}</span>

          <div className="enter-manually_boxflex">
            <div className="enter-manually_select">
              <label htmlFor="rsphere">{productPage?.sphere ?? "Sphere"}</label>
              <Select
                defaultValue={rightSph && { label: rightSph?.name, value: rightSph?._id }}
                options={powerData?.sph.map((power: any) => ({ label: power?.name, value: power?._id }))}
                onChange={(e: any) => setValue("rightSph", e.value)}
                placeholder={productPage?.select ?? "Select"}
              />
              <input
              className="inp"
                {...register("rightSph", { required: true })}
                id="rightSph"
                name="rightSph"
              />
              {errors.rightSph && (
                <p className="error">{errors.rightSph.type}</p>
              )}
            </div>

            <div className="enter-manually_select">
              <label htmlFor="rcylinder">{productPage?.cyl ?? "Cylinder"}</label>
              <Select
              defaultValue={rightCyl && { label: rightCyl?.name, value: rightCyl?._id }}
                options={powerData?.cyl.map((power: any) => ({ label: power?.name, value: power?._id }))}
                onChange={(e: any) => setValue("rightCyl", e.value)}
                placeholder={productPage?.select ?? "Select"}
              />
              <input
              className="inp"
                {...register("rightCyl", { required: true })}
                id="rightCyl"
                name="rightCyl"
              />
              {errors.rightCyl && (
                <p className="error">{errors.rightCyl.type}</p>
              )}
            </div>

            <div className="enter-manually_select">
              <label htmlFor="raxis">{productPage?.axis ?? "Axis"}</label>
              <Select
              defaultValue={rightAxis && { label: rightAxis?.name, value: rightAxis?._id }}
                options={powerData?.axis.map((power: any) => ({ label: power?.name, value: power?._id }))}
                onChange={(e: any) => setValue("rightAxis", e.value)}
                placeholder={productPage?.select ?? "Select"}
              />
              <input
              className="inp"
                {...register("rightAxis", { required: true })}
                id="rightAxis"
                name="rightAxis"
              />
              {errors.rightAxis && (
                <p className="error">{errors.rightAxis.type}</p>
              )}
            </div>

            {lensData?.vision == "progressive" && <div className="enter-manually_select">
              <label htmlFor="raxis">{productPage?.addition ??"Addition"}</label>
              <Select
              defaultValue={rightAdd && { label: rightAdd?.name, value: rightAdd?._id }}
                options={powerData?.add.map((power: any) => ({ label: power?.name, value: power?._id }))}
                onChange={(e: any) => setValue("rightAdd", e.value)}
                placeholder={productPage?.select ?? "Select"}
              />
              <input
              className="inp"
                {...register("rightAdd", { required: true })}
                id="rightAdd"
                name="rightAdd"
              />
              {errors.rightAdd && (
                <p className="error">{errors.rightAdd.type}</p>
              )}
            </div>}
          </div>
        </div>

        <div className="enter-manually_box">
          <span>{productPage?.pd ?? "PD - Pupillary Distance"}</span>

          <div className="enter-manually_boxflex">
            <div className="enter-manually_select">
              <label htmlFor="know" className="enter-manually_max-content cursor-pointer">
                <input
                  id="know"
                  type="radio"
                  name="pdChoice"
                  value="known"
                  checked={isPdKnown === "known"}
                  onChange={onRadioChange}
                />
                {productPage?.iKnowPd ?? "I know my PD"}
              </label>
              {isPdKnown === "known" && (
                <input
                  {...register("pd", { required: "PD is required" })}
                  type="text"
                  placeholder="Enter Eye PD"
                />
              )}
              {errors.pd && (
                <small className="error">{errors.pd.message}</small>
              )}
              <label htmlFor="dknow" className="mt-3 enter-manually_max-content cursor-pointer">
                <input
                  id="dknow"
                  type="radio"
                  name="pdChoice"
                  value="unknown"
                  checked={isPdKnown === "unknown"}
                  onChange={onRadioChange}
                />
                {productPage?.iDontKnowPd ?? "I don’t know my PD"}
              </label>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
}

export default EnterManuallyModal;
