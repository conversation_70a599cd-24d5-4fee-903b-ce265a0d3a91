"use client";

import "./upload-popup.scss";
import Modal from "react-bootstrap/Modal";
import Image from "next/image";
import { useContext, useEffect, useRef, useState } from "react";
import { SubmitHandler, useForm } from "react-hook-form";
import { useQueryClient } from "@tanstack/react-query";
import { endpoints } from "@/config/apiEndpoints";
import api from "@/config/axios.interceptor";
import { toast } from "sonner";
import { TranslationContext } from "@/contexts/Translation";

interface Inputs {
  name: string;
  file: FileList;
}

function UploadPopUp({ }: any) {
  const {
    register,
    handleSubmit,
    reset,
    setFocus,
    watch,
    formState: { errors },
  } = useForm<Inputs>();
  const {translation: {myAccount}}:any = useContext(TranslationContext)

  const queryClient = useQueryClient();
  const file: any = watch("file");

  const [show, setShow] = useState(false);
  const handleClose = () => setShow(false);
  const handleShow = () => {
    reset();
    setShow(true);
  };

  const ref = useRef<any>(null);

  const onSubmit: SubmitHandler<Inputs> = (data:any) => {
    const formData = new FormData();
    const name = data.name || "Untitled";
    formData.append("title", name);
    formData.append("file", data.file?.[0]);

    api.post(endpoints.uploadPrescription, formData).then((res:any) => {
      toast.success(res?.data?.message);
      handleClose();
      queryClient.invalidateQueries({ queryKey: ["prescription"] });
    });
  };
  return (
    <>
      <button onClick={handleShow}>{myAccount?.uploadPrescription ?? "Upload Prescription"}</button>
      {show && (
        <Modal
          show={show}
          onHide={handleClose}
          backdrop="static"
          keyboard={false}
          centered
          className="review-popup">
          <Modal.Header closeButton></Modal.Header>
          <Modal.Body>
            <h2>{myAccount?.prescriptionUploadTitle ?? "Upload Prescription"}</h2>
            <form onSubmit={handleSubmit(onSubmit)} style={{ width: "100%" }}>
              <div className="upload_inputs my-4">
                <label htmlFor="">{myAccount?.prescriptionUploadInputLabel ?? "Title"}</label>
                <input
                  {...register("name")}
                  autoFocus
                  type="text"
                  name="name"
                  placeholder={myAccount?.prescriptionUploadInputPlaceholder ?? "Left Eye Test"}
                />
              </div>
              <div className="file-upload">
                <input
                  id="fileInput"
                  accept="image/*,application/pdf"
                  type="file"
                  {...register("file", { required: (myAccount?.prescriptionUploadError ?? "Please select a file") })}
                />
                <label htmlFor="fileInput" ref={ref}>
                  <>
                    {file?.[0]?.name && <p className="text-center">{file?.[0]?.name}</p>}
                    {!file?.[0]?.name && (
                      <Image quality={100} priority src="/images/common/icons/pin.png" width={24} height={24} alt="pin" />
                    )}
                    {!file?.[0]?.name && <>{myAccount?.prescriptionUploadInputFile ?? "Upload File"}</>}
                  </>
                </label>
                <small className="form-error text-danger">{errors.file?.message}</small>
              </div>
              <p>{myAccount?.prescriptionUploadFileInfo ?? "You can upload jpg, png, pdf file as prescription"}</p>
              <div style={{ display: 'flex', alignItems: 'center', gap: '.5rem' }}>
                <svg width="20px" height="20px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="M12 17.75C12.4142 17.75 12.75 17.4142 12.75 17V11C12.75 10.5858 12.4142 10.25 12 10.25C11.5858 10.25 11.25 10.5858 11.25 11V17C11.25 17.4142 11.5858 17.75 12 17.75Z" fill="#000000"></path> <path d="M12 7C12.5523 7 13 7.44772 13 8C13 8.55228 12.5523 9 12 9C11.4477 9 11 8.55228 11 8C11 7.44772 11.4477 7 12 7Z" fill="#000000"></path> <path fill-rule="evenodd" clip-rule="evenodd" d="M1.25 12C1.25 6.06294 6.06294 1.25 12 1.25C17.9371 1.25 22.75 6.06294 22.75 12C22.75 17.9371 17.9371 22.75 12 22.75C6.06294 22.75 1.25 17.9371 1.25 12ZM12 2.75C6.89137 2.75 2.75 6.89137 2.75 12C2.75 17.1086 6.89137 21.25 12 21.25C17.1086 21.25 21.25 17.1086 21.25 12C21.25 6.89137 17.1086 2.75 12 2.75Z" fill="#000000"></path> </g></svg>
                <p style={{ margin: 0 , fontWeight: 600, }}>{myAccount?.prescriptionUploadInfo ?? "Check if your prescription is not more 6 months old"}</p>
              </div>
              <input type="submit" className="button" value={myAccount?.prescriptionUploadSubmit ?? "Submit"} />
            </form>
          </Modal.Body>
        </Modal>
      )}
    </>
  );
}

export default UploadPopUp;
