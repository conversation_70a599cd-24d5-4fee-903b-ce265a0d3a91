import { NextRequest } from "next/server";
import jsonData from './countries+states.json'



export async function GET(req: NextRequest) {
    const url = req.nextUrl;
    const limit = parseInt(url.searchParams.get('limit') || '10'); // Default limit of 10
    const query = url.searchParams.get('query')?.toLowerCase() || ''; // Lowercase query

    // Input validation for limit
    if (isNaN(limit) || limit < 1 || limit > 100) {
        return new Response(JSON.stringify({ error: 'Invalid limit parameter. Limit must be a number between 1 and 100.' }), {
            status: 400,
        });
    }

    const filteredCountries = jsonData.filter((country) =>
        country.name.toLowerCase().startsWith(query)
    ).slice(0, limit);

    const countries = filteredCountries.map((country) => ({
        name: country.name,
        code: country.phone_code,
    }));

    return new Response(JSON.stringify(countries));

}
