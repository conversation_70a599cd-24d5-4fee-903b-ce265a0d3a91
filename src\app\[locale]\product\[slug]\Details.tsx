"use client"

import React, { useContext, useEffect, useState } from 'react'
import ProductSelelct from "@/components/product-select/ProductSelelct";
import BackButton from "@/components/product-detail/BackButton";
import SizePicker from "@/components/product-detail/size-picker/SizePicker";
import BreadCrumpExtended from "./BreadCrumpExtended";
import ProductVariantSection, { calculateSavings } from "./ProductVariantSection";
import Rating from "@/components/product-detail/rating/Rating";
import ProductReview from "@/components/product-review/ProductReview";
import ProductStickyBar from "@/components/product-sticky-bar/ProductStickyBar";
import Favourite from "@/components/product-detail/favourite/Favourite";
import Share from "@/components/product-detail/share/Share";
import Compare from "@/components/product-detail/compare/Compare";
import Count from "@/components/count/Count";
import AddToCart from "@/components/add-to-cart/AddToCart";
import TryCartPopup from "@/components/try-cart-popup/TryCartPopup";
import Image from "next/image";
import Link from "next/link";
import PostalCode from "@/components/product-detail/postal-code/PostalCode";
import GallerySlider from "@/components/product-detail/gallery-slider/GallerySlider";
import ProductSlider from "@/components/product/product-slider/ProductSlider";
import BuywithLensPopup from "@/components/buy-withlens-popup/BuywithLensPopup";
import ColorPicker from "@/components/product-detail/color-picker/ColorPicker";
import FrequentCard from "@/components/product-detail/FrequentCard";
import ProductGallery from "@/components/product-detail/product-gallery/ProductGallery";
import { useQuery } from '@tanstack/react-query';
import { getProduct } from '@/lib/methods/products';
import { CountContext } from '@/contexts/AddBtnContext';
import { HistoryContext } from '@/contexts/HistoryProvider';
import { TranslationContext } from '@/contexts/Translation';
import { sendGAEvent } from '@next/third-parties/google'
import { sendGTMEvent } from '@next/third-parties/google'
import { AuthContext } from '@/contexts/AuthProvider';
import { createHmac } from 'crypto';
import ContactColor from './ContactColor';
import { useSearchParams } from 'next/navigation';
import { SettingsContext } from '@/contexts/SettingsProvider';
import { useLocaleContext } from '@/contexts/LocaleProvider';
import Script from 'next/script';

const TABBY_PUBLIC_KEY = process.env.NEXT_PUBLIC_TABBY_PUBLIC_KEY ?? "";
const TAMARA_PUBLIC_KEY = process.env.NEXT_PUBLIC_TAMARA_PUBLIC_KEY ?? "";

export default function Details({ slug, vmPolicy }: any) {

  const { data: product, isLoading } = useQuery({
    queryKey: ['product', slug],
    queryFn: () => getProduct(slug),
  })

  const { varient } = useContext(CountContext);
  const { userProfile } = useContext(AuthContext);
  const { translation: { productPage: translation, productListing } }: any = useContext(TranslationContext)
  const { settings } = useContext(SettingsContext)
  const { currentLocale: locale, currencyCode } = useLocaleContext()

  const [variant, setVarient] = useState({
    color: product?.color?._id,
    size: product?.size?._id
  })

  console.log(variant)

  useEffect(() => {
    if (!isLoading) {
      const price = product?.customizable ? product?.productType == "contactLens" ? product?.contactSizes?.[0]?.price : product?.sizes?.[0]?.price : product?.price?.aed;
      const offerPrice = product?.customizable ? product?.productType == "contactLens" ? product?.contactSizes?.[0]?.offerPrice : product?.sizes?.[0]?.offerPrice : product?.offerPrice?.aed;
      let savings = calculateSavings(price, offerPrice);
      if (!offerPrice) savings = 0;

      sendGTMEvent({ ecommerce: null })
      let eventData: any = {
        event: "view_item",
        ecommerce: {
          currency: currencyCode,
          value: price - savings,
          items: [
            {
              item_id: product?.sku,
              item_name: product?.name,
              discount: savings,
              index: 0,
              item_brand: product?.brand,
              item_category: product?.category?.[0]?.name,
              item_category2: product?.category?.[1]?.name,
              item_variant: product?.color?.name,
              price,
              quantity: 1,
            }
          ]
        }
      }
      if (userProfile) {
        const mobile = createHmac('sha256', '123').update(userProfile?.mobile).digest('hex')
        let email = null;
        if (userProfile?.email) email = createHmac('sha256', '123').update(userProfile?.email).digest('hex')
        eventData.user = {
          mobile,
          email,
          user_id: userProfile?._id
        }
      }
      sendGTMEvent(eventData)
    }
  }, [isLoading])

  const { cashback } = product
  let isCashback = false;
  let cashbackPercentage = 0
  if (cashback?.brand?.isEnabled) {
    isCashback = true;
    cashbackPercentage = cashback?.brand?.percentage;
  } else if (cashback?.category?.isEnabled) {
    isCashback = true;
    cashbackPercentage = cashback?.category?.percentage;
  } else if (cashback?.product?.isEnabled) {
    isCashback = true;
    cashbackPercentage = cashback?.product?.percentage;
  }

  return (
    <>
      <BreadCrumpExtended product={product} />
      <div className="container">
        <div className="product-detail_sticky ">
          <div className="product-detail_sticky-image">
            <Image
              src={product?.thumbnail || "/images/product/noImage-2x.jpg"}
              width={70}
              height={40}
              quality={90}
              alt="sunglass"
            />
            <h5>{product?.name}</h5>
          </div>
          {product?.stock > 0 && (
            <div className="product-detail_sticky-btns">
              {product?.productType === "contactLens" && (
                <>
                  <Count useContextValue />
                  <AddToCart
                    product={product}
                    useContextValue
                    showSvg={true}
                    translation={translation}
                  />
                </>
              )}
              {product?.productType !== "contactLens" &&
                product?.stock > 0 && (
                  product?.isChooseLens && (
                    <BuywithLensPopup product={product} translation={translation} />
                  )
                )}
              {product?.isTryCart && (
                <TryCartPopup product={product} showBtn showMsg />
              )}
            </div>
          )}
          {product?.stock === 0 && (
            <div className="product-detail_sticky-btns text-muted">
              {translation?.outOfStock || "OUT OF STOCK"}
            </div>
          )}
        </div>
        <div className="product-detail">
          <div className="product-detail_wrapper">
            <div
              className={`product-detail_left ${product?.images?.length === 0 ? "no-image" : ""
                }`}
            >
              <BackButton />
              <ProductGallery
                upc={product?.upc}
                data={product?.images}
                isVirtualTry={product?.isVirtualTry}
                vmPolicy={vmPolicy?.result}
              />
              <GallerySlider
                upc={product?.upc}
                data={product?.images}
                isVirtualTry={product?.isVirtualTry}
                vmPolicy={vmPolicy?.result}
              />
            </div>

            <div className="product-detail_right">
              <div className="block-one">
                <div style={{ display: 'flex', gap: "1rem", alignItems: "center" }}>
                  <Link href={`/brands/${product?.brandSlug}`} >
                    <span className="block-one_span">{product?.brand}</span>
                  </Link>
                  {product?.label && <div className="">
                    <p className='label'>{product?.label}</p>
                  </div>}
                </div>
                <div className="d-flex justify-content-between align-items-baseline mt-3 gap-4">
                  <div>
                    <h3>{product?.name}</h3>
                    <p>{product?.sku}</p>
                    {/* <h5>
                            <span>SKU :</span>
                            {product?.sku}
                          </h5> */}
                  </div>
                  <div>
                    <div
                      className="d-flex align-items-center"
                      style={{ columnGap: "2.3rem" }}
                    >
                      <Favourite
                        data={{
                          slug: product?.slug,
                          id: product?._id,
                          isWishlisted: product?.isWishlisted,
                          count: product?.likeCount,
                          category: product?.category,
                          brandSlug: product?.brandSlug,
                        }}
                        product={product}
                      />
                      <Share data={product} />
                    </div>
                  </div>
                </div>
                <div className="d-flex align-content-center gap-4 mt-3">
                  {/* <Rating rating={product?.rating} /> */}
                  {/* <ProductReview product={product} /> */}
                  <Compare product={product} />
                </div>
              </div>

              <ProductVariantSection product={product} translation={translation} />
              {isCashback && <div className="cashback-tag">
                <p>You will get {cashbackPercentage}% Cashback on this product</p>
              </div>}
              {product?.productType === "contactLens" && (
                <ProductSelelct product={product} translation={translation} />
              )}
              {/* {product?.productType !== "contactLens" ? ( */}
              {true ? (
                <>
                  <ColorPicker
                    product={product}
                    colors={product?.colors}
                    variant={variant} setVarient={setVarient} combinations={product?.combinations}
                    sizes={product?.sizes}
                    // variants={product?.variants}
                    currentVariant={product?.slug}
                    mainProduct={product?.isDefaultVariant ? product?._id : product?.mainProduct}
                    translation={translation}
                    type={product?.productType}
                  />
                </>
              ) : (
                ""
                // <ContactColor
                //   product={product}
                //   colors={product?.colors}
                //   variant={variant} setVarient={setVarient} combinations={product?.combinations}
                //   sizes={product?.sizes}
                //   // variants={product?.variants}
                //   currentVariant={product?.slug}
                //   // mainProduct={product?.isDefaultVariant ? product?._id: product?.mainProduct}
                //   translation={translation}
                // />
              )}
              {product?.sizes?.length > 0 && (
                <SizePicker variant={variant} setVarient={setVarient} combinations={product?.combinations} product={product} sizes={product?.sizes} translation={translation} />
              )}
              <div className="block-four">
                {(product?.stock > 0 || product?.customizable) &&
                  product?.productType === "contactLens" && (
                    <div className="d-flex align-items-center justify-content-between gap-4 gap-lg-5 ">
                      <Count useContextValue />
                      <AddToCart
                        product={product}
                        useContextValue
                        showSvg={true}
                        translation={translation}
                      />
                    </div>
                  )}

                <div className="block-four_btns">
                  {product?.productType !== "contactLens" ?
                    varient?.stock ?
                      varient?.stock > 0 ?
                        (
                          <>
                            {product?.isChooseLens && (
                              <BuywithLensPopup product={product} translation={translation} />
                            )}
                            {product?.isAddToCart && (
                              <AddToCart
                                product={product}
                                useContextValue
                                showSvg={true}
                                translation={translation}
                              />
                            )}
                          </>
                        )
                        : <div className="product-detail_sticky-btns text-muted">
                          {locale.includes("en") ? "OUT OF STOCK" : "إنتهى من المخزن"}
                        </div>
                      : product?.stock > 0 ? (
                        <>
                          {product?.isChooseLens && (
                            <BuywithLensPopup product={product} />
                          )}
                          {product?.isAddToCart && (
                            <AddToCart
                              product={product}
                              useContextValue
                              showSvg={true}
                              translation={translation}
                            />
                          )}
                        </>
                      ) : (
                        <div className="product-detail_sticky-btns text-muted">
                          {(translation?.outOfStock || "OUT OF STOCK")}
                        </div>
                      )
                    : ""}
                  {!product?.customizable && product?.stock === 0 && (
                    <button
                      className="buy-with-lens"
                      disabled
                      style={{ opacity: 0.5, cursor: "not-allowed" }}
                    >
                      {(translation?.outOfStock || "OUT OF STOCK")}
                    </button>
                  )}
                  {product?.isTryCart && (
                    <TryCartPopup product={product} showBtn showMsg />
                  )}
                </div>
                {(settings?.tamara || settings?.tabby) ? <div style={{ margin: "2rem 0" }}>
                  <Tamara product={product} />
                </div> : ""}
              </div>
              <div className="block-five">
                {product?.productType === "contactLens" && (
                  <Link className="subscriptionLink" href={"/contact-lens"}>
                    {translation?.subscription || "Click to learn more about Subscriptions"}
                  </Link>
                )}
                <div className="block-five_tamara d-none">
                  <Image
                    src="/images/product-detail/tamara.png"
                    width={74}
                    height={30}
                    alt="tamara"
                  />

                  <div>
                    <h6>Pay 4 interest-free payments of AED 1247.00.</h6>
                    <Link href="/policy/terms-and-conditions">
                      Learn More
                    </Link>
                  </div>
                </div>

                {/* <div className="block-five_border">
                        <div className="block-five_free-delivery">
                          <Image
                            src="/images/product-detail/del.png"
                            width={24}
                            height={24}
                            alt="devivery"
                          />
                          <div className="w-100">
                            <h5>Free Delivery</h5>
                          </div>
                        </div>
  
                        <div className="block-five_return-delivery">
                          <Image
                            src="/images/product-detail/del.png"
                            width={24}
                            height={24}
                            alt="devivery"
                          />
  
                          <div>
                            <h5>Return Delivery</h5>
                            <span>
                              Free 30 days Delivery Return.
                              <Link href="/policy/return-policy">Details</Link>
                            </span>
                          </div>
                        </div>
                      </div> */}
              </div>
              {product?.boughtTogether?.length > 0 && (
                <div className="block-six">
                  <h4>{translation?.frequentlyBoughtWith || "Frequently bought with"}</h4>
                  {product?.boughtTogether?.map((item: any) => (
                    <FrequentCard key={item._id} data={item} />
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
        <div className="product-desc">
          <h4>{translation?.productDescription || "Product Description"}</h4>
          <div
            dangerouslySetInnerHTML={{ __html: product?.description }}
          ></div>
          {/* <Image  quality={100}  priority   src="/images/product-detail/gif.gif" width={1133} height={480} alt="gif" /> */}

          {product?.video && (
            <div className={`product-video`}>
              <video
                autoPlay
                loop
                muted
                src={product?.video}
                width={1280}
                height={480}
              />
            </div>
          )}
          {/* {product?.descriptionTwo && (
            <div
              dangerouslySetInnerHTML={{ __html: product?.descriptionTwo }}
            ></div>
          )} */}
          {/* <p>
                  When it&quot;s colder than the far side of the moon and spitting rain too,
                  you&quot;ve still got to look good. From water-repellent leather to a rugged
                  outsole, the Lunar Force 1 adapts AF-1 style, so you can keep your flame burning
                  when the weather hits. Metal lace hardware and extended tongue bring mountain boot
                  toughness, while the star-studded toe design gives your look the edge
                </p> */}
        </div>
        <div className="tech-info" style={{ position: "relative", zIndex: -1 }}>
          <h5>{translation?.technicalInfo ?? "Technical Information"}</h5>
          {/* <ul>
              {product?.technicalInfo?.map((items: any, index: any) => (
                <li key={"tech" + index}>
                  <div className="tech-info-flex">
                    <span>{items?.title}</span>
                    <span>{items?.description}</span>
                  </div>
                </li>
              ))}
            </ul> */}
          <ul>
            {product?.name && (
              <li>
                <div className="tech-info-flex">
                  <span style={{ verticalAlign: "top" }}>{translation?.name ?? "Name"}</span>
                  <span>:</span>
                  <span>{product?.name}</span>
                </div>
              </li>
            )}
            {product?.modelName && (
              <li>
                <div className="tech-info-flex">
                  <span style={{ verticalAlign: "top" }}>{translation?.modelName ?? "Model Name"}</span>
                  <span>:</span>
                  <span>{product?.modelName}</span>
                </div>
              </li>
            )}
            {product?.brand && (
              <li>
                <div className="tech-info-flex">
                  <span>{translation?.brand ?? "Brand"}</span>
                  <span>:</span>
                  <span>{product?.brand}</span>
                </div>
              </li>
            )}
            {product?.frameShape && (
              <li>
                <div className="tech-info-flex">
                  <span>{productListing?.frameShape ?? "Frame Shape"}</span>
                  <span>:</span>
                  <span>{product?.frameShape}</span>
                </div>
              </li>
            )}
            {product?.frameType && (
              <li>
                <div className="tech-info-flex">
                  <span>{productListing?.frameType ?? "Frame Type"}</span>
                  <span>:</span>
                  <span>{product?.frameType}</span>
                </div>
              </li>
            )}
            {product?.color && (
              <li>
                <div className="tech-info-flex">
                  <span>{translation?.color ?? "Color"}</span>
                  <span>:</span>
                  <span>{product?.color?.name}</span>
                </div>
              </li>
            )}
            {product?.frontMaterial?.length > 0 ? (
              <li>
                <div className="tech-info-flex">
                  <span>{productListing?.frontMaterial ?? "Front Material"}</span>
                  <span>:</span>
                  <span>{product?.frontMaterial?.join(", ")}</span>
                </div>
              </li>
            ) : ""}
            {product?.gender?.length > 0 ? (
              <li>
                <div className="tech-info-flex">
                  <span>{translation?.gender ?? "Gender"}</span>
                  <span>:</span>
                  <span>{product?.gender?.map((item: string) => {
                    if (item?.trim().toLowerCase() == "men") return (translation.men ?? "Men");
                    if (item?.trim().toLowerCase() == "women") return (translation.women ?? "Women");
                    if (item?.trim().toLowerCase() == "kids") return (translation.kids ?? "Kids");
                    if (item?.trim().toLowerCase() == "unisex") return (translation.unisex ?? "Unisex");
                    return ""
                  }).join(", ")}</span>
                </div>
              </li>
            ) : ""}
          </ul>
        </div>

        <div className="product-images" style={{ position: "relative", zIndex: -1 }}>
          {product?.descriptionImages?.map((item: any, index: any) => (
            <Image
              key={"desc" + index}
              src={item ?? ""}
              width={526}
              height={526}
              alt="images"
            />
          ))}
        </div>
      </div>

      {product?.recommendedProducts?.length > 0 && (
        <div className="product-detail-slider" style={{ position: "relative", zIndex: -1 }}>
          <h2>{translation?.recommendedProducts || "Recommended Products"}</h2>
          <ProductSlider products={product?.recommendedProducts} />
        </div>
      )}
    </>
  )
}

function Tamara({ product }: any) {
  const searchParams = useSearchParams();
  const active = searchParams?.get("size");
  const { settings } = useContext(SettingsContext)
  const { currentLocale: locale, currencyCode } = useLocaleContext()
  const [show, setShow] = useState(false)
  console.log(TABBY_PUBLIC_KEY)
  const [price, setPrice] = useState({
    price: product?.price?.aed,
    offerPrice: product?.offerPrice?.aed || null,
  });

  const setTabby = () => {
    if (window.TabbyPromo) {
      {/* @ts-ignore */ }
      new TabbyPromo({
        selector: '#tabby', // required, content of tabby Promo Snippet will be placed in element with that selector.
        currency: currencyCode, // required, AED|SAR|KWD only supported, with no spaces or lowercase.
        price: price.offerPrice ? price.offerPrice : price.price, // required, price of the product. 2 decimals max for AED|SAR and 3 decimals max for KWD.
        lang: locale?.split("-")[1], // Optional, en|ar only supported
        source: 'product', // Optional, snippet placement; `product` for product page and `cart` for cart page.
        publicKey: TABBY_PUBLIC_KEY, // required, Public Key
        merchantCode: 'YOUAE',  // required,
      });
    }
  }

  useEffect(() => {
    setTabby()
  }, [product, window.TabbyPromo])

  useEffect(() => {
    setShow(false)
    window.tamaraWidgetConfig = {
      lang: locale?.split("-")[1],
      country: "AE",
      publicKey: TAMARA_PUBLIC_KEY
    }
    setShow(true)
  }, [locale])

  return (
    <>
      {settings?.tabby ? <div id="tabby" style={{ marginBottom: "1rem" }}></div> : ""}
      {settings?.tamara ? <div style={{ border: "1px solid #d6d6d3", borderRadius: "7px", padding: "16px" }}>
        {/* @ts-ignore */}
        {show && <tamara-widget id={locale} inline-variant="text" type="tamara-summary" amount={price.offerPrice ? price.offerPrice : price.price} inline-type='2'></tamara-widget>}
      </div> : ""}
      <Script id='tabby-script' src="https://checkout.tabby.ai/tabby-promo.js" onReady={setTabby}
      >
      </Script >
    </>
  );
}