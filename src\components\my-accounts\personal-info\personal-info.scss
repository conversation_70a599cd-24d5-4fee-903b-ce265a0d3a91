.personal-info {
  margin-top: 3rem;
  padding-bottom: 11rem;

  @media (max-width: 575.98px) {
    margin-top: 2.2rem;
    padding-bottom: 2rem;
  }

  &_head {
    text-align: center;
    position: relative;
    display: grid;
    place-items: center;

    .avatar {
      margin-top: 2rem;
      width: 12rem;
      height: 12rem;
      border-radius: 1.2rem;
      overflow: hidden;
      position: relative;
      width: 120px;
      height: 120px;

      @media (max-width: 575.98px) {
        margin-top: 1.7rem;
      }
    }
  }

  &_profile-btn {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: -21px;
    display: flex;
    column-gap: 0.5rem;

    button,
    label {
      border: none;
      border-radius: 10rem;
      background: #fff;
      box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.05);
      width: 3.5rem;
      height: 3.5rem;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 1.4rem;
        height: 2.8rem;
        object-fit: contain;
        margin: auto;
      }
    }
  }

  &_flex {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-top: 4.4rem;

    @media (max-width: 575.98px) {
      flex-direction: column;
    }
  }

  &_inputs {
    width: calc(50% - 4.3rem);
    position: relative;

    @media (max-width: 767.98px) {
      width: calc(50% - 2rem);
    }

    @media (max-width: 575.98px) {
      width: 100%;
    }

    &:not(:last-child) {
      margin-bottom: 3.6rem;

      @media (max-width: 575.98px) {
        margin-bottom: 3rem;
      }
    }

    label {
      margin-bottom: 0.8rem;
      color: #808080;
      font-size: 1.3rem;
      font-weight: 400;

      @media (max-width: 575.98px) {
        font-size: 1.4rem;
        font-size: 1.5rem;
      }
    }

    input {
      width: 100%;
      border: none;
      border-bottom: 1px solid #e2e4e5;
      padding-left: 1.5rem;
      padding-bottom: 0.8rem;
      position: relative;
      z-index: 2;

      &::placeholder {
        color: #929292;
        font-size: 1.8rem;
        font-weight: 400;

        @media (max-width: 575.98px) {
          font-size: 1.5rem;
        }
      }

      &:focus-visible {
        border-bottom: 1px solid #e2e4e5;
        outline: none;
        box-shadow: none;
      }
    }

    input[type="number"]::-webkit-inner-spin-button,
    input[type="number"]::-webkit-outer-spin-button {
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
    }

    select {
      width: 100%;
      border: none;
      border-bottom: 1px solid #e2e4e5;
      padding-left: 1.5rem;
      padding-bottom: 0.8rem;
      appearance: none;

      &::placeholder {
        color: #727272;
        font-size: 1.8rem;
        font-weight: 400;
      }

      &:focus-visible {
        border-bottom: 1px solid #e2e4e5;
        outline: none;
        box-shadow: none;
      }
    }

    .countrycode {
      display: flex;
      column-gap: 1rem;
      align-items: baseline;

      .countrycode-icon {
        position: relative;

        &::after {
          content: "";
          background-image: url("../../../../public/images/common/Icon.png");
          width: 2.4rem;
          height: 2.4rem;
          position: absolute;
          right: -8px;
        }
      }

      select {
        border: none;
        border-bottom: 1px solid #e2e4e5;
        padding: 0 1.6rem;
        padding-bottom: 0.8rem;
        width: 8.6rem;

        &:focus-visible {
          outline: none;
          border-bottom: 1px solid #e2e4e5;
        }
      }
    }

    .inp {
      display: none;
    }

    p {
      font-size: 1.3rem;
      font-weight: 400;
      line-height: 1.9rem;
      letter-spacing: -0.011em;
      color: rgba(0, 0, 0, 0.8);
      margin-top: 1.5rem;
      opacity: 50%;
    }

    .css-1jqq78o-placeholder {
      font-size: 1.3rem;
      font-weight: 400;
      color: #999999;
      line-height: 1.6rem;
    }

    .css-13cymwt-control {
      border: none;
      border-bottom: 1px solid rgba(0, 0, 0, 0.15) !important;
      border-radius: 0;
      width: 100%;

      .css-1fdsijx-ValueContainer {
        padding: 0 !important;
        margin: 0 !important;

        .css-qbdosj-Input {
          padding: 0;
          margin: 0;
        }
      }

      .css-1u9des2-indicatorSeparator {
        display: none;
      }

      .css-1xc3v61-indicatorContainer {
        padding-right: 0;
      }

      .css-13cymwt-control {
        background-color: red;

        &:hover {
          border: none;
          background-color: blue;
        }
      }
    }

    .css-1dimb5e-singleValue {
      color: #000000;
      font-size: 1.3rem;
      font-weight: 400;
    }

    .css-t3ipsp-control {
      border: none !important;
      border-bottom: 1px solid rgba(0, 0, 0, 0.15) !important;
      outline: none;
      box-shadow: none;
      padding: 0;
      width: 100%;

      .css-1fdsijx-ValueContainer {
        padding: 0;
        margin: 0;
      }

      .css-1xc3v61-indicatorContainer {
        padding-right: 0;
      }
    }

    .css-1nmdiq5-menu {
      color: #000000;
      font-size: 1.3rem;
      font-weight: 400;
      z-index: 10;

      div {
        &>div {
          transition: .2s;

          &[aria-selected="true"] {
            background-color: black;
            color: white;
          }

          &[aria-selected="false"] {
            background-color: white;
            color: black;
          }

          &:hover {
            background-color: black;
            color: white;
          }
        }
      }

    }

    .css-1u9des2-indicatorSeparator {
      display: none;
    }

  }

  &_btn {
    display: flex;
    column-gap: 2rem;
    margin: auto;
    margin-top: 7rem;
    width: 50%;

    @media (max-width: 575.98px) {
      width: 100%;
      margin-top: 4rem;
    }

    .button {
      border: none;
      border-radius: 10rem;
      color: #fff;
      background-color: #000;
      font-size: 1.5rem;
      font-weight: 500;
      height: 5.6rem;
      width: 100%;
      border: none;
      border: 1px solid #000;

      &[disabled] {
        opacity: 0.5;
        cursor: not-allowed;
      }

      @media (max-width: 575.98px) {
        height: 4.5rem;
      }
    }

    .cancel-btn {
      background-color: transparent;
      color: #000;
    }
  }
}

.app.rtl {
  .personal-info_inputs input {
    padding-right: 1.5rem;
    padding-left: 0rem;
  }

  .personal-info_inputs .css-13cymwt-control .css-1xc3v61-indicatorContainer {
    padding-right: 0;
    padding-left: 0;
  }

  .personal-info_inputs .countrycode .countrycode-icon::after {
    right: auto;
    left: 8px;
  }

  .personal-info_inputs .countrycode select {
    padding-right: 1.6rem;
    padding-left: 0;
  }
}