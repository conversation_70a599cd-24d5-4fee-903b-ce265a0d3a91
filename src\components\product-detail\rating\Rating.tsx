function Rating({ rating, count }: { rating: number | undefined, count: number }) {
  return (
    <span className="rating">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="16"
        height="17"
        viewBox="0 0 16 17"
        fill="none"
      >
        <path
          d="M7.65311 2.83314C7.6813 2.76436 7.72932 2.70552 7.79105 2.66411C7.85279 2.6227 7.92544 2.60059 7.99978 2.60059C8.07411 2.60059 8.14676 2.6227 8.2085 2.66411C8.27023 2.70552 8.31825 2.76436 8.34644 2.83314L9.76311 6.24047C9.78963 6.30423 9.83322 6.35943 9.88909 6.40001C9.94496 6.44059 10.0109 6.46497 10.0798 6.47047L13.7584 6.76514C14.0911 6.7918 14.2258 7.20714 13.9724 7.4238L11.1698 9.82514C11.1174 9.86993 11.0784 9.92827 11.057 9.99377C11.0356 10.0593 11.0326 10.1294 11.0484 10.1965L11.9051 13.7865C11.9223 13.8585 11.9178 13.934 11.8921 14.0035C11.8665 14.0729 11.8208 14.1332 11.7609 14.1767C11.7009 14.2203 11.6295 14.245 11.5555 14.248C11.4815 14.2509 11.4083 14.2318 11.3451 14.1931L8.19511 12.2698C8.13627 12.2339 8.06869 12.215 7.99978 12.215C7.93086 12.215 7.86328 12.2339 7.80444 12.2698L4.65444 14.1938C4.59128 14.2325 4.51808 14.2515 4.44408 14.2486C4.37008 14.2457 4.29861 14.2209 4.23869 14.1774C4.17877 14.1339 4.13308 14.0736 4.10741 14.0041C4.08174 13.9347 4.07722 13.8592 4.09444 13.7871L4.95111 10.1965C4.967 10.1294 4.96408 10.0593 4.94267 9.99374C4.92126 9.92822 4.8822 9.86988 4.82978 9.82514L2.02711 7.4238C1.97098 7.37554 1.93038 7.31177 1.91041 7.24049C1.89043 7.16922 1.89198 7.09363 1.91485 7.02323C1.93772 6.95284 1.9809 6.89077 2.03895 6.84485C2.097 6.79893 2.16734 6.77119 2.24111 6.76514L5.91978 6.47047C5.98861 6.46497 6.05459 6.44059 6.11046 6.40001C6.16633 6.35943 6.20992 6.30423 6.23644 6.24047L7.65311 2.83314Z"
          stroke="#D48D3B"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
      {rating} ({count || 0})
    </span>
  );
}

export default Rating;
