"use client";
import Offcanvas from "react-bootstrap/Offcanvas";
import Filter from "../filter/Filter";
import { useRouter, usePathname } from "next/navigation";

function MobileFilter({ show, handleClose, filters, slug, brandPage, isVirtualTry, setIsVirtualTry, translation }: any) {
  const router = useRouter();
  const pathname = usePathname();
  const clearFliters = () => {
    router.push(pathname, { scroll: false });
  };
  return (
    <>
      <Offcanvas className="filter-offcanvas" placement="bottom" show={show} onHide={handleClose}>
        <Offcanvas.Body>
          <div style={{ display: 'flex', justifyContent: 'space-between', gap: '1rem', alignItems: 'end' }}>
            <h4 style={{ marginTop: '2rem' }}>{translation?.filterBy ?? "Filter by"}</h4>
            <button className="close-btn" style={{ margin: '1rem', border: 'none', marginLeft: 'auto', background: 'none' }} onClick={handleClose}>
              <svg fill="#5e5e5e" height="16px" width="16px" version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink" viewBox="0 0 460.775 460.775" xmlSpace="preserve" stroke="#000000"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="M285.08,230.397L456.218,59.27c6.076-6.077,6.076-15.911,0-21.986L423.511,4.565c-2.913-2.911-6.866-4.55-10.992-4.55 c-4.127,0-8.08,1.639-10.993,4.55l-171.138,171.14L59.25,4.565c-2.913-2.911-6.866-4.55-10.993-4.55 c-4.126,0-8.08,1.639-10.992,4.55L4.558,37.284c-6.077,6.075-6.077,15.909,0,21.986l171.138,171.128L4.575,401.505 c-6.074,6.077-6.074,15.911,0,21.986l32.709,32.719c2.911,2.911,6.865,4.55,10.992,4.55c4.127,0,8.08-1.639,10.994-4.55 l171.117-171.12l171.118,171.12c2.913,2.911,6.866,4.55,10.993,4.55c4.128,0,8.081-1.639,10.992-4.55l32.709-32.719 c6.074-6.075,6.074-15.909,0-21.986L285.08,230.397z"></path> </g></svg>
            </button>
          </div>
          <Filter filters={filters} translation={translation} isVirtualTry={isVirtualTry} setIsVirtualTry={setIsVirtualTry} brandPage={brandPage} slug={slug} />
          <div className="filter-offcanvas-btn">
            <button onClick={handleClose} className="apply">
              {translation?.apply ?? "Apply"}
            </button>
            <button onClick={clearFliters} className="reset">
              {translation?.reset ?? "Reset"}
            </button>
          </div>
        </Offcanvas.Body>
      </Offcanvas>
    </>
  );
}

export default MobileFilter;
