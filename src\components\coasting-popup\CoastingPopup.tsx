import Modal from "react-bootstrap/Modal";
import "./coasting-popup.scss";
import { Image } from "react-bootstrap";
import { useEffect, useState } from "react";
import Reading from "../reading-popup/Reading";
import { useQuery } from "@tanstack/react-query";
import { endpoints } from "@/config/apiEndpoints";
import api from "@/config/axios.interceptor";
import LensPrice from "../buy-withlens-popup/lens-price/LensPrice";

const colors = [
  { bg: "#DBF2FF", text: "#045B8D" },
  { bg: "#CEFFED", text: "#00492F" },
  { bg: "#FFF2DB", text: "#8B0000" },
  { bg: "#FFCEDB", text: "#8B0000" },
  { bg: "#F5E1FF", text: "#6A006A" },
  { bg: "#FFEBE6", text: "#800000" },
  { bg: "#FFF8E6", text: "#9A6600" },
  { bg: "#D9FFFF", text: "#005C5C" },
  { bg: "#FFE6F2", text: "#8C0046" },
  { bg: "#E6FFEC", text: "#00662B" },
  { bg: "#F5F5F5", text: "#333333" },
  { bg: "#FFF2CC", text: "#7F6000" },
  { bg: "#E0F2F1", text: "#004D40" },
  { bg: "#FFE0B2", text: "#E65100" },
];

function CoatingPopup({
  show,
  handleClose,
  lensData,
  sum,
  setSum,
  setStep,
  back,
  handleLensData,
}: any) {
  const [showReading, setShowReading] = useState(false);
  const handleCloseReading = () => setShowReading(false);

  const {
    data: coatings,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["coating", lensData?.brand],
    queryFn: () => {
      return api
        .get(`${endpoints.lensCoating}/${lensData?.brand}`)
        .then((res) => {
          if (res.status === 200) {
            return res.data?.result?.coatings;
          } else[];
        });
    },
  });

  const [localSum, setLocalSum] = useState(sum);

  const handleShowReading = () => {
    setStep("coating");
    // setSum("coating", localSum);
  };

  const handleClick = (coating: any) => {
    handleLensData({ coating: coating?._id });
    // setLocalSum(coating?.price);
    setSum("coating", coating?.price);
  };

  useEffect(() => {
    if (coatings && coatings?.length > 0) {
      setLocalSum(coatings[0]?.price);
    }
  }, [coatings]);
  return (
    <>
      <Modal
        className="coasting-popup"
        show={show}
        onHide={handleClose}
        backdrop="static"
        keyboard={false}
        centered
      >
        <Modal.Header closeButton></Modal.Header>
        <Modal.Body>
          <h2>
            <Image quality={100} priority
              src="/images/common/bakarrow.png"
              width={40}
              height={40}
              alt="back arrow"
              onClick={() => back("coating")}
            />
            Coating <LensPrice sum={sum} />
          </h2>
          {coatings?.map((coating: any, index: number) => (
            <div
              key={coating?._id}
              className="coasting-popup_flex"
              onClick={() => handleClick(coating)}
              style={{ backgroundColor: colors[index]?.bg }}
            >
              {lensData.coating === coating?._id && (
                <span className="check">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="15"
                    height="10"
                    viewBox="0 0 15 10"
                    fill="none"
                  >
                    <path
                      d="M14 1L5.0625 9L1 5.36364"
                      stroke="white"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                </span>
              )}
              <div className="coasting-popup_image">
                <Image quality={100} priority
                  src={coating?.image}
                  width={260}
                  height={104}
                  alt="coasting"
                />
              </div>
              <div className="coasting-popup_content">
                <h5>{coating?.name}</h5>
                <span style={{ color: colors[index]?.text }}>
                  {coating?.currency} {coating?.price}
                </span>
              </div>
            </div>
          ))}

          <button onClick={handleShowReading}>Continue</button>
        </Modal.Body>
      </Modal>
      {/* <Reading show={showReading} handleClose={handleCloseReading} /> */}
    </>
  );
}

export default CoatingPopup;
