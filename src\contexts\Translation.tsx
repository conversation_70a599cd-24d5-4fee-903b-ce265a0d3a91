"use client";

import { createContext, useState } from "react";

export const TranslationContext = createContext({} as any);

export const TranslationProvider = ({ children, data }: any) => {
  const [translation, setTranslation] = useState<any>(data);

  return (
    <TranslationContext.Provider
      value={{
        translation,
        setTranslation
      }}>
      {children}
    </TranslationContext.Provider>
  );
};
