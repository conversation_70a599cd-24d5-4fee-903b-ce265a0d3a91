import NotFound from "@/app/not-found";
import EmptyState from "@/components/empty-states/EmptyState";
import MyOrders from "@/components/my-accounts/my-orders/MyOrders";
import { endpoints } from "@/config/apiEndpoints";
import api from "@/config/axios.interceptor";
import { HydrationBoundary, QueryClient, dehydrate } from "@tanstack/react-query";

import React from "react";

async function page() {
  const queryClient = new QueryClient();
  try {

    const getOrders = async({pageParam = 1}:any) =>{
      const res = await api.get(`${endpoints.orders}?page=${pageParam}&limit=15`);
      return res.data.result;
    }

    await queryClient.prefetchInfiniteQuery({
      queryKey: ["orders"],
      queryFn: getOrders,
      initialPageParam: 1,
      getNextPageParam: (lastPage, pages) => lastPage.nextPage,
      pages: 2,
    })

    // const order = await api.get(`${endpoints.orders}`);
    // await queryClient.prefetchQuery({
    //   queryKey: ["orders"],
    //   queryFn: () => {
    //     return api.get(`${endpoints.orders}`).then((res: any) => {
    //       return res.data?.result;
    //     });
    //   },
    // })


    // const orders = order?.data?.result;
    // if (orders.length === 0) {
    //   return <EmptyState icon="x-doc" title="No Orders Found!" />;
    // }
    return (
      <>
        <HydrationBoundary state={dehydrate(queryClient)}>
          <MyOrders />
        </HydrationBoundary>
      </>
    );
  } catch (error) {
  }
}

export default page;
