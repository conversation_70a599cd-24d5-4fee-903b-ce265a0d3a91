"use client";

import { createContext, useEffect, useState } from "react";

export const FilterContext = createContext({});

export const FilterProvider = ({ children, data }: any) => {
  const [allFilters, setAllFilters] = useState<any>({});

  return (
    <FilterContext.Provider
      value={{
        allFilters,
        setAllFilters
      }}>
      {children}
    </FilterContext.Provider>
  );
};
