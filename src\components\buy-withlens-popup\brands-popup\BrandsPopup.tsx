import Modal from "react-bootstrap/Modal";
import "./brandsPopup.scss";
import { Image } from "react-bootstrap";
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { endpoints } from "@/config/apiEndpoints";
import api from "@/config/axios.interceptor";
import { toast } from "sonner";

const colors = [
  { bg: "#DBF2FF", text: "#045B8D" },
  { bg: "#CEFFED", text: "#00492F" },
  { bg: "#FFF2DB", text: "#8B0000" },
  { bg: "#FFCEDB", text: "#8B0000" },
  { bg: "#F5E1FF", text: "#6A006A" },
  { bg: "#FFEBE6", text: "#800000" },
  { bg: "#FFF8E6", text: "#9A6600" },
  { bg: "#D9FFFF", text: "#005C5C" },
  { bg: "#FFE6F2", text: "#8C0046" },
  { bg: "#E6FFEC", text: "#00662B" },
  { bg: "#F5F5F5", text: "#333333" },
  { bg: "#FFF2CC", text: "#7F6000" },
  { bg: "#E0F2F1", text: "#004D40" },
  { bg: "#FFE0B2", text: "#E65100" },
];

function BrandsPopup({ show, handleClose, setStep, lensData, handleLensData, back }: any) {
  const [showReading, setShowReading] = useState(false);
  const handleCloseReading = () => setShowReading(false);

  const {
    data: coatings,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["lens-brands"],
    queryFn: () => {
      return api.get(endpoints.lensBrands).then((res) => {
        if (res.status === 200) {
          return res.data?.result;
        }
      });
    },
  });
  const handleShowReading = () => {
    if (lensData.brand === "") {
      toast.error("Please select brand");
      return;
    }
    setStep("brands");
  };

  const handleClick = (brand: any) => {
    handleLensData({ brand: brand });
  };
  return (
    <>
      <Modal
        className="brands-popup"
        show={show}
        onHide={handleClose}
        backdrop="static"
        keyboard={false}>
        <Modal.Header closeButton>
          <h2>
            <Image 
              onClick={() => back("brands")}
              src="/images/common/backarrow.svg"
              width={40}
              height={40}
              alt="back arrow"
            />
            Brands
          </h2>
        </Modal.Header>
        <Modal.Body>

          {coatings?.map((coating: any, index: number) => (
            <div
              key={coating?._id}
              onClick={() => handleClick(coating?._id)}
              className="brands-popup_flex"
              style={{ backgroundColor: colors[index % colors.length]?.bg }}>
              {lensData.brand === coating?._id && (
                <span className="check">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="15"
                    height="10"
                    viewBox="0 0 15 10"
                    fill="none">
                    <path
                      d="M14 1L5.0625 9L1 5.36364"
                      stroke="white"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                </span>
              )}
              <div className="brands-popup_image" title={coating?.name}>
                <Image src={coating?.cover ?? ""} width={260} height={104} alt={coating?.name} />
              </div>
              <div className="brands-popup_content">
                {/* <h5>{coating?.name}</h5>
                <span style={{ color: colors[index % colors.length]?.text }}>
                  {coating?.currency || "AED"} {coating?.price}
                </span> */}
                <Image  src={coating?.logo ?? ""} width={100} height={50} alt={coating?.name} />
              </div>
            </div>
          ))}

          <button onClick={handleShowReading}>Continue</button>
        </Modal.Body>
      </Modal>
    </>
  );
}

export default BrandsPopup;
