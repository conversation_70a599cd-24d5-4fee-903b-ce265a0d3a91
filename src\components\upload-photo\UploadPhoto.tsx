import React, { useContext } from "react";
import "./UploadPhoto.scss";
import Image from "next/image";
import { TranslationContext } from "@/contexts/Translation";

function UploadPhoto({
  show,
  onClick,
  handleCloseUploadModal,
  handleFileUpload,
  fileName,
}: any) {
  const {translation: {productPage}} = useContext(TranslationContext)
  return (
    <div className="upload-photo">
      <div className="upload-photo_header">
        <h2>
          <Image quality={100} priority
            src="/images/common/backarrow.svg"
            width={40}
            height={40}
            className=" cursor-pointer"
            alt="back arrow"
            onClick={handleCloseUploadModal}
          />
          {productPage?.uploadPhoto ?? "Upload Photo"}
        </h2>
        <button>
          <Image quality={100} priority
            src="/images/common/close.svg"
            width={40}
            height={40}
            alt="close"
            onClick={onClick}
          />
        </button>
      </div>
      <div id="check">
      <svg width="20px" height="20px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="M12 17.75C12.4142 17.75 12.75 17.4142 12.75 17V11C12.75 10.5858 12.4142 10.25 12 10.25C11.5858 10.25 11.25 10.5858 11.25 11V17C11.25 17.4142 11.5858 17.75 12 17.75Z" fill="#000000"></path> <path d="M12 7C12.5523 7 13 7.44772 13 8C13 8.55228 12.5523 9 12 9C11.4477 9 11 8.55228 11 8C11 7.44772 11.4477 7 12 7Z" fill="#000000"></path> <path fill-rule="evenodd" clip-rule="evenodd" d="M1.25 12C1.25 6.06294 6.06294 1.25 12 1.25C17.9371 1.25 22.75 6.06294 22.75 12C22.75 17.9371 17.9371 22.75 12 22.75C6.06294 22.75 1.25 17.9371 1.25 12ZM12 2.75C6.89137 2.75 2.75 6.89137 2.75 12C2.75 17.1086 6.89137 21.25 12 21.25C17.1086 21.25 21.25 17.1086 21.25 12C21.25 6.89137 17.1086 2.75 12 2.75Z" fill="#000000"></path> </g></svg>
        <p>{productPage?.checkPrescription ?? "Check if your prescription is not more 6 months old"}</p>
      </div>

      <div className="upload-photo_box">
        <input type="file" onChange={handleFileUpload} />
        {!fileName ? (
          <Image quality={100} priority
            src="/images/modal/upload.svg"
            width={500}
            height={500}
            alt="images"
          />
        ) : (
          <p>{fileName}</p>
        )}
      </div>
      <ul>
        <li>*{productPage?.fileSize ?? "Files must be less than 2 MB"}.</li>
        <li>*{productPage?.fileType ?? "Allowed file types : PNG,JPG,JPEG,PDF"}</li>
      </ul>
    </div>
  );
}

export default UploadPhoto;
