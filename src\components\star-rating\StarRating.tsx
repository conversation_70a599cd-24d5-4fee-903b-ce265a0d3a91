"use client";

import React, { useState } from "react";
import { set } from "react-hook-form";
import { Rating } from "react-simple-star-rating";

export function StarRating({ setStarRating, disabled, color, defaultRating = 0 }: any) {
  const [rating, setRating] = useState(defaultRating);

  // Catch Rating value
  const handleRating = (rate: number) => {
    setRating(rate);
    if (setStarRating) {
      setStarRating(rate);
    }
    // other logic
  };
  // Optinal callback functions
  const onPointerEnter = () => {};
  const onPointerLeave = () => {};
  const onPointerMove = (value: number, index: number) => {};

  return (
    <div className="start-rating">
      <Rating
        initialValue={rating}
        onClick={handleRating}
        onPointerEnter={onPointerEnter}
        onPointerLeave={onPointerLeave}
        onPointerMove={onPointerMove}
        fillColor={color || "#000"}
        readonly={disabled}
        allowFraction
      />
    </div>
  );
}

export default StarRating;
