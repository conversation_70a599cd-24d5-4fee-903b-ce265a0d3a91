.reading-popup {
  &.modal {
    .modal-dialog-centered {
      @media (max-width: 575.98px) {
        align-items: flex-end;
      }
    }
    .modal-header {
      padding: 0;
      border: none;

      .btn-close {
        background-image: url(../../../public/images/common/close.png);
        width: 4.8rem;
        height: 4.8rem;
        background-position: center;
        background-repeat: no-repeat;
        background-size: contain;
        opacity: 1;
        padding: 0;

        @media (max-width: 575.98px) {
          width: 2.8rem;
          height: 2.8rem;
        }

        &:focus {
          box-shadow: none;
          outline: none;
          border: none;
        }
      }
    }

    .modal-content {
      border: none;
      border-radius: 3rem;
      padding: 1.8rem 2rem 4.2rem 5.6rem;
      border: none;

      @media (max-width: 575.98px) {
        padding: 2rem 1.8rem 5.7rem 2.1rem;
        border-radius: 3rem 3rem 0 0;
      }
    }

    .modal-body {
      padding: 0;
      display: flex;
      flex-direction: column;

      h2 {
        text-align: left;
        line-height: 4rem;
        display: flex;
        align-items: center;
        column-gap: 2rem;

        @media (max-width: 575.98px) {
          line-height: 3rem;
        }

        img {
          width: 4rem;
          height: 4rem;
          object-fit: contain;

          @media (max-width: 575.98px) {
            display: none;
          }
        }
      }

      button {
        margin-top: 4rem;
        color: #fff;
        text-align: center;
        font-size: 1.5rem;
        font-weight: 500;
        border: none;
        background-color: #000;
        height: 5.6rem;
        width: 40rem;
        border-radius: 6rem;

        @media (max-width: 575.98px) {
          width: 100%;
          height: 4.5rem;
          margin-top: 6.7rem;
        }
      }
    }
  }

  &_radio {
    margin-bottom: 1.9rem;

    @media (max-width: 575.98px) {
      margin-top: 2.8rem;
    }

    label {
      display: flex;
      align-items: center;
      color: #000;
      font-size: 1.6rem;
      font-weight: 500;
      column-gap: 1.4rem;
      margin-top: 3rem;

      input {
        appearance: none;
        width: 2rem;
        height: 2rem;
        border-radius: 50%;
        border: 1px solid #000;
        position: relative;

        &:checked {
          &::after {
            content: "";
            width: 1.2rem;
            height: 1.2rem;
            background-color: #000;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            position: absolute;
            border-radius: 50%;
          }
        }
      }
    }
  }

  &_move {
    margin-left: 3rem;
  }

  &_select-box {
    .icon {
      position: relative;
      display: inline-block;

      &::after {
        content: "";
        position: absolute;
        background-image: url(../../../public/images/common/Icon.png);
        background-repeat: no-repeat;
        width: 2rem;
        height: 2rem;
        top: 21px;
        right: 6px;
      }
    }
    label {
      color: #000;
      font-size: 1.3rem;
      font-weight: 400;
      display: flex;
    }

    select {
      margin-top: 1.1rem;
      border-radius: 2.95rem;
      background: #f3f3f3;
      border: none;
      height: 4.5rem;
      padding: 0 1.6rem;
      padding-right: 3rem;
      appearance: none;
      position: relative;
      cursor: pointer;

      &:focus-visible {
        border: none;
        outline: none;
      }
    }
  }

  &_flex {
    display: flex;
    column-gap: 1.5rem;
    margin-top: 2.4rem;
  }
}
