"use client";

import { useForm, SubmitHandler } from "react-hook-form";
import "./insurance-form.scss";
import { useQueryClient } from "@tanstack/react-query";
import api from "@/config/axios.interceptor";
import { endpoints } from "@/config/apiEndpoints";
import { toast } from "sonner";
import GenericBackButton from "../GenericBackButton/GenericBackButton";
import { countryCodeWithFlags } from "@/lib/countryCodeWithFlag";
import Select from "react-select";
import { useContext, useEffect, useRef, useState } from "react";
import { Modal } from "react-bootstrap";
import TermsAndConditions from "../Terms-pop-up/TermsPopUp";
import { TranslationContext } from "@/contexts/Translation";
import { useLocaleContext } from "@/contexts/LocaleProvider";
import { useSettingsContext } from "@/contexts/SettingsProvider";
import Link from "next/link";
import Image from "next/image";
type InsuranceInputs = {
  name: string;
  email: string;
  mobile: string;
  insurance: string;
  nationality: string;
  emirates: string;
  emiratesId: string;
  file: File;
  memberId: string;
  countryCode: string;
};

type InsuranceFormProps = {
  insuranceProviders: any;
  user: any;
  content: any;
};

function InsuranceForm({
  insuranceProviders,
  user,
  content,
}: InsuranceFormProps) {
  const [termsChecked, setTermsChecked] = useState(false);
  const [showTerms, setShowTerms] = useState(false);
  const [termsError, setTermsError] = useState(false);
  const [showPopup, setShowPopup] = useState(false);

  const { currentLocale: locale, countryCodes } = useLocaleContext()
  const {
    translation: { formFields },
  }: any = useContext(TranslationContext);
  const emiratesOptions = [
    {
      label: locale.includes("en") ? "Abu Dhabi" : "أبو ظبي",
      value: "Abu Dhabi",
    },
    {
      label: locale.includes("en") ? "Ajman" : "عجمان",
      value: "Ajman",
    },
    {
      label: locale.includes("en") ? "Dubai" : "دبي",
      value: "Dubai",
    },
    {
      label: locale.includes("en") ? "Fujairah" : "الفجيرة",
      value: "Fujairah",
    },
    {
      label: locale.includes("en") ? "Ras Al Khaimah" : "رَأْس ٱلْخَيْمَة",
      value: "Ras Al Khaimah",
    },
    {
      label: locale.includes("en") ? "Sharjah" : "الشارقة",
      value: "Sharjah",
    },
    {
      label: locale.includes("en") ? "Umm Al Quwain" : "أم القيوين",
      value: "Umm Al Quwain",
    },
  ];

  const handleClose = () => setShowTerms(false);
  const handleClosePopup = () => setShowPopup(false);

  const insuranceRef: any = useRef();
  const emiratesRef: any = useRef();

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    reset,
    watch,
    resetField,
  } = useForm<InsuranceInputs>();

  useEffect(() => {
    setValue("name", user?.name);
    setValue("mobile", user?.mobile);
    setValue("countryCode", user?.countryCode || countryCodes[locale.split("-")[0]]);
    setValue("email", user?.email);

    if (insuranceProviders?.length === 1) {
      insuranceRef?.current?.setValue({
        label: insuranceProviders?.[0]?.name,
        value: insuranceProviders?.[0]?._id,
      });
      setValue("insurance", insuranceProviders?.[0]?._id);
    }
  }, []);

  const onSubmit: SubmitHandler<InsuranceInputs> = (data: any) => {
    if (!termsChecked) return setTermsError(true);

    let formData = new FormData();
    Object.keys(data).forEach((key) => {
      if (key === "file") {
        formData.append(key, data?.[key]?.[0]);
      } else {
        formData.append(key, data[key]);
      }
    });
    api
      .post(endpoints.insuranceForm, formData)
      .then((res) => {
        insuranceRef?.current?.setValue("");
        emiratesRef?.current?.setValue("");
        // toast.success(
        //   res?.data?.message ||
        //   (locale.includes("en")
        //     ? "Form submitted successfully"
        //     : "تم إرسال النموذج بنجاح")
        // );
        setShowPopup(true);
        reset();
        setValue("countryCode", user?.countryCode || countryCodes[locale.split("-")[0]]);
        setTermsChecked(false);
      })
      .catch((err) => {
        toast.error(
          err?.response?.data?.message ||
          (locale.includes("en") ? "Something went wrong" : "لقد حدث خطأ ما")
        );
      });
  };

  const file: any = watch("file");

  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)} className="insurance-form">
        <div className="container position-relative">
          <span className="back">
            <GenericBackButton style={{ top: "5px" }} />{" "}
          </span>
          <h2 className="mb-4">{content?.pageTitle}</h2>
          <div
            className="insurance-form_description"
            dangerouslySetInnerHTML={{ __html: content?.pageDescription }}
          ></div>
          <div className="insurance-form_wrapper">
            <div className="insurance-form_inputs">
              <label htmlFor="name">
                {formFields?.fullName ?? "Customer name"}*
              </label>
              <input
                {...register("name", {
                  required:
                    formFields?.fullNameRequiredError ??
                    "Customer Name is Required",
                })}
                type="text"
                name="name"
                placeholder="John Smith"
              />
              {errors.name && (
                <small className="form-error text-danger">
                  {errors.name?.message}
                </small>
              )}
            </div>

            <div className="insurance-form_inputs">
              <label htmlFor="email">
                {formFields?.emailAddress ?? "Email"}*
              </label>
              <input
                {...register("email", {
                  required:
                    formFields?.emailAddressRequiredError ??
                    "Email is Required",
                })}
                type="text"
                name="email"
              // placeholder="John Smith"
              />
              {errors.email && (
                <small className="form-error text-danger">
                  {errors.email?.message}
                </small>
              )}
            </div>

            <div className="insurance-form_inputs">
              <div className="select">
                <label htmlFor="mobile">
                  {formFields?.phoneNumber ?? "Contact Number"}*
                </label>
                <Select
                  className="select-container"
                  onChange={(e: any) => setValue("countryCode", e.value)}
                  styles={
                    {
                      // option:(state)=>{}
                    }
                  }
                  theme={(theme) => ({
                    ...theme,
                    borderRadius: 0,
                    colors: {
                      ...theme.colors,
                      primary25: "#ccc",
                      primary: "black",
                    },
                  })}
                  classNames={{
                    control: (state) => "react-select",

                    dropdownIndicator: () => "d-none",
                    option: (state) =>
                      state.isSelected ? "option selected" : "option",
                    menu: () => "menu",
                  }}
                  formatOptionLabel={(country) => (
                    <div className="drop-item">
                      <img src={country.image} alt="" />
                      {country.label}
                    </div>
                  )}
                  options={countryCodeWithFlags?.map((country) => ({
                    label: country.name,
                    value: country.dial_code,
                    image: country.image,
                  }))}
                />

                <div className="countrycode">
                  <div className="countrycode-icon">
                    <input
                      {...register("countryCode")}
                      id="countryCode"
                      name="countryCode"
                    />
                  </div>

                  <input
                    {...register("mobile", {
                      required:
                        formFields?.phoneNumberRequiredError ??
                        "Phone Number is Required",
                      pattern: {
                        value: /^[0-9]{9}/,
                        message:
                          formFields?.phoneNumberInvalidError ??
                          "Invalid Phone Number",
                      },
                    })}
                    tabIndex={1}
                    type="tel"
                    id="mobile"
                    placeholder="************"
                  />
                </div>
              </div>
              {errors.mobile && (
                <small className="form-error text-danger">
                  {errors.mobile?.message}
                </small>
              )}
            </div>

            <div className="insurance-form_inputs">
              <label htmlFor="insurance">
                {formFields?.nameOfInsurance ?? "Name of the Insurance"}*
              </label>

              <Select
                ref={insuranceRef as any}
                // defaultValue={}
                options={insuranceProviders?.map((item: any) => ({
                  label: item.name,
                  value: item._id,
                }))}
                onChange={(e: any) => setValue("insurance", e.value)}
                placeholder={formFields?.selectInsurance ?? "Select Insurance"}
              />
              <input
                className="inp"
                {...register("insurance", {
                  required:
                    formFields.nameOfInsuranceRequiredError ??
                    "Please select an insurance!",
                })}
                id="insurance"
                name="insurance"
              />
              {errors.insurance && (
                <small className="form-error text-danger">
                  {errors.insurance.message}
                </small>
              )}
            </div>

            <div className="insurance-form_inputs">
              <label htmlFor="">
                {formFields?.nationality ?? "Nationality"}*
              </label>
              <input
                {...register("nationality", {
                  required:
                    formFields?.nationalityRequiredError ??
                    "Nationality is Required",
                })}
                type="text"
                name="nationality"
                placeholder="UAE"
              />
              {errors.nationality && (
                <small className="form-error text-danger">
                  {errors.nationality?.message}
                </small>
              )}
            </div>

            <div className="insurance-form_inputs">
              <label htmlFor="">{formFields?.emirates ?? "Emirates"}*</label>

              <Select
                ref={emiratesRef}
                options={emiratesOptions}
                onChange={(e: any) => setValue("emirates", e.value)}
                placeholder={formFields?.selectEmirates ?? "Select Emirates"}
              />
              <input
                className="inp"
                {...register("emirates", {
                  required:
                    formFields?.emiratesRequiredError ?? "Emirates is required",
                })}
                id="emirates"
                name="emirates"
              />
              {errors.emirates && (
                <small className="form-error text-danger">
                  {errors.emirates?.message}
                </small>
              )}
            </div>

            <div className="insurance-form_inputs">
              <label htmlFor="">{formFields?.emiratesId ?? "Emirates ID"}*</label>
              <input
                type="text"
                {...register("emiratesId", {
                  required:
                    formFields?.emiratesIdRequiredError ??
                    "Emirates ID is Required",
                  pattern: {
                    value: 
                    locale?.includes("ae") ? /^784\d{12}$/ 
                    : locale?.includes("sa") ? /^[12]\d{9}$/
                    : locale?.includes("qa") ? /^\d{11}$/
                    : locale?.includes("om") ? /^[12]\d{7}$/
                    : locale?.includes("bh") ? /^\d{9}$/
                    : locale?.includes("kw") ? /^[23]\d{11}$/
                    : /^\d{15}$/,
                    message:
                      formFields?.emiratesIdInvalidError ?? "Invalid Emirates Id",
                  },
                })}
                name="emiratesId"
                placeholder="EMRI 3264XXXXXXXXX44"
              />
              {errors.emiratesId && (
                <small className="form-error text-danger">
                  {errors.emiratesId?.message}
                </small>
              )}
            </div>

            <div className="insurance-form_inputs">
              <label htmlFor="">
                {formFields?.uploadEmirates ?? "Upload Emirates or Insurance Id"}
              </label>
              <input
                accept="image/*, application/pdf"
                type="file"
                {...register("file")}
                name="file"
                style={{ cursor: "pointer" }}
              />
              <small className="form-error text-danger">
                {errors.file?.message}
              </small>
            </div>

            <div className="insurance-form_inputs">
              <label htmlFor="">{formFields?.memberId ?? "Member ID"}</label>
              <input
                {...register("memberId")}
                type="text"
                name="memberId"
                placeholder="3264XXXXXXXXX44"
              />
              {errors.memberId && (
                <small className="form-error text-danger">
                  {errors.memberId?.message}
                </small>
              )}
            </div>

            <div
              style={{ display: "flex", gap: "1rem", flexDirection: "column", width: "100%" }}
              className="w-full terms-text"
            >
              <div style={{ display: "flex", gap: "1rem" }}>
                <input
                  checked={termsChecked}
                  onChange={() => {
                    setTermsChecked((prev) => !prev);
                    setTermsError(false);
                  }}
                  type="checkbox"
                  name="terms"
                  id="terms"
                />
                <label htmlFor="terms">
                  {locale.includes("en") ? "I accept the" : "أوافق على"}
                  <button
                    type="button"
                    onClick={() => setShowTerms(true)}
                    style={{
                      background: "transparent",
                      border: 0,
                      fontWeight: "600",
                    }}
                  >
                    {locale.includes("en") ? "terms and conditions" : "الشروط والأحكام"}
                  </button>
                </label>
              </div>
              {termsError && (
                <small className="text-danger">
                  {locale.includes("en")
                    ? "Accept terms & conditions"
                    : "قبول الشروط والأحكام"}
                </small>
              )}
            </div>

            <div className="insurance-form_btn">
              <input
                type="submit"
                value={formFields?.submit ?? "Submit Now"}
                className="primary-btn"
              />
            </div>
          </div>
        </div>
        {showTerms && (
          <TermsAndConditions
            show={showTerms}
            content={content}
            handleClose={handleClose}
          />
        )}
      </form>
      {showPopup && (
        <Modal
          className={`success-popup ${locale == "ar" ? "rtl" : ""}`}
          show={showPopup}
          onHide={handleClosePopup}
          backdrop="static"
          keyboard={false}
          centered={true}
        >
          <Modal.Header closeButton></Modal.Header>
          <Modal.Body>
            <Image
              quality={100}
              priority
              src={
                "/images/common/thanks.gif"
              }
              width={364}
              height={273}
              alt="tick"
              style={{ objectFit: "cover" }}
            />

            <h4>{content?.popup?.title ?? "Your Enquiry Submitted"}</h4>
            <p>{content?.popup?.description ?? "Congratulations! 🎉 Your message has been successfully sent. Thank you for reaching out! We'll be in touch shortly to assist you further."}</p>

            <div className="success-btn">
              <Link href={content?.popup?.primaryBtnLink ?? "/"}>
                <button className="view-order">{content?.popup?.primaryBtn ?? "Home"}</button>
              </Link>
              {content?.popup?.secondaryBtn && <Link href={content?.popup?.secondaryBtnLink ?? "/store-locator"}>
                <button className="continue-shopping">{content?.popup?.secondaryBtn ?? "Our Stores"}</button>
              </Link>}

            </div>
          </Modal.Body>
        </Modal>
      )}
    </>
  );
}

export default InsuranceForm;
