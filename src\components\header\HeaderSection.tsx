import { AuthProvider } from "@/contexts/AuthProvider";
import Preheader from "../preheader/Preheader";
import Header from "./Header";
import { endpoints } from "@/config/apiEndpoints";
import { cookies } from "next/headers";

export default async function HeaderSection({data, stores}:any) {
  try {
    return (
      <>
        <Preheader data={data?.result} stores={stores} />
        <AuthProvider>
          <Header stores={stores}  navData={data?.result} />
        </AuthProvider>
      </>
    );
  } catch (e) {

  }
}
