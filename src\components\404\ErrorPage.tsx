"use client";

import "./error-page.scss";
import Image from "next/image";
import Link from "next/link";
import { redirect, useRouter } from "next/navigation";
import { AuthContext } from "@/contexts/AuthProvider";
import { useContext, useEffect, useState } from "react";
import { QueryCache, QueryObserver, useQueryClient } from "@tanstack/react-query";
import { useLocaleContext } from "@/contexts/LocaleProvider";


type ErrorCode = "404" | "500" | "401" | "emptyCart";

const errorData = {
  404: {
    image: "/images/common/notFound.png",
    title: { en: "Page Not Found", ar: "صفحة غير موجودة" },
    description: {
      en: "The page you are trying to access does not exist or has been moved. Try going back to our homepage.",
      ar: "الصفحة التي تحاول الوصول إليها غير موجودة أو تم نقلها. حاول العودة إلى صفحتنا الرئيسية.",
    },
  },
  500: {
    image: "/images/common/500.png",
    title: { en: "Internal Server Error", ar: "خطأ في الخادم الداخلي" },
    description: {
      en: "An error occurred while processing your request.",
      ar: "حدث خطأ أثناء معالجة طلبك.",
    },
  },
  401: {
    image: "/images/common/401.svg",
    title: { en: "Unauthorized for Guest", ar: "غير مصرح" },
    description: {
      en: "Please Signup as User to Yateem to access this page.",
      ar: "يرجى الاشتراك في يتيم للوصول إلى هذه الصفحة..",
    },
  },
  emptyCart: {
    image: "/images/common/empty-cart.png",
    title: { en: "Empty Cart", ar: "عربة التسوق فارغة" },
    description: { en: "Your cart is empty.", ar: "عربة التسوق فارغة" },
  },
};

function ErrorPage({ errorcode }: { errorcode: ErrorCode }) {
  const { currentLocale:locale, t } = useLocaleContext()
  const router = useRouter();
  const { userProfile } = useContext(AuthContext);
  const queryCache = new QueryCache({});

  const queryClient = useQueryClient();
  const observer = new QueryObserver(queryClient, { queryKey: ["user"] });

  const unsubscribe = observer.subscribe((result: any) => {
    if (result.data?.isGuest === false) {
      router.refresh();
    }
  });
  useEffect(() => {
    if (errorcode !== "401") return;

    return () => {
      unsubscribe();
    };
  }, []);


  return (
    <section className="error">
      <div className="container">
        <div className="error_wrapper">
          <Image quality={100} priority
            src={errorData[errorcode].image ?? ""}
            width={335}
            height={294}
            alt="error"
            style={{ maxWidth: "355px" }}
          />
          <h6>{errorData[errorcode].title[locale]}</h6>
          <p>{errorData[errorcode].description[locale]}</p>
          {errorcode !== "401" && <Link href="/">{t("home.page")}</Link>}
          {errorcode === "401" && <Link href="/login">{t("authorization.signup")}</Link>}
        </div>
      </div>
    </section>
  );
}

export default ErrorPage;
