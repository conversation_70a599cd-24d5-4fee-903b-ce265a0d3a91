import { endpoints } from "@/config/apiEndpoints";
import api from "@/config/axios.interceptor";
import { notFound } from "next/navigation";

export const getProducts = async (slug?: string, page?: number) => {
    const res = await api.post(endpoints.products, {
        page: 1,
        limit: process.env.PAGESIZE || 15,
        keyword: slug,
    });
    return res.data.result;
};

export const getProductsSync = () => {
    const res:any = api.get(endpoints.about);
    return res.data.result;
};

export const getProduct = async (slug: string) => {
    try {
        const res = await api.get(endpoints.productDetail + '/' + slug);
        if (res.data.errorCode === 0) {
    
            return res.data.result;
        }
        
    } catch (error:any) {
        if(error.status == 404){
            notFound()
        }
    }

    // throw error('notfound')
}

export const getBanner = async (prevPage:any) => {
    const res = await api.post(endpoints.productBanner, {
        prevPage
    });
    if (res.data.errorCode === 0) {

        return res.data.result;
    }

    // throw error('notfound')
}