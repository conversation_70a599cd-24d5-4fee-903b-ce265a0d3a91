.faq {
  margin-top: 6.8rem;
  padding-bottom: 5rem;

  @media (max-width: 575.98px) {
    order: 3;
    margin-top: 5rem;
  }

  &_head {
    h2 {
      line-height: 5.5rem;

      @media (max-width: 575.98px) {
        text-align: left;
        line-height: 3.2rem;
        font-size: 2rem;
      }
    }

    p {
      color: #52525b;
      font-size: 1.8rem;
      font-weight: 400;
      line-height: 3rem;
      max-width: 86rem;
      width: 100%;
      margin: 0 auto;
      text-align: center;

      @media (max-width: 575.98px) {
        text-align: left;
        line-height: 2.42rem;
        font-size: 1.5rem;
      }
    }
  }

  .accordion {
    margin-top: 4.2rem;

    @media (max-width: 575.98px) {
      width: calc(100% - 3.5rem);
      margin: 0 auto;
      margin-top: 3.5rem;
    }

    &-header {
      button {
        background-color: transparent;
        box-shadow: none;
        padding: 0;

        h3 {
          color: #000;
          font-size: 1.8rem;
          font-weight: 600;
          line-height: 2.16rem;
          padding: 0;

          @media (max-width: 575.98px) {
            font-size: 1.5rem;
            line-height: 1.8rem;
            max-width: 26rem;
            width: 100%;
          }
        }

        &:focus {
          box-shadow: none;
          border: none;
          outline: none;
          background-color: transparent;
        }

        &::after {
          background-image: url(../../../public/images/common/plus.png);
          width: 2.4rem;
          height: 2.4rem;
          background-size: cover;
        }

        &:not(.collapsed)::after {
          background-image: url(../../../public/images/common/xmark.png);
          width: 2.4rem;
          height: 2.4rem;
          background-size: cover;
        }
      }
    }

    &-item {
      padding-bottom: 1.6rem;
      margin-bottom: 1.6rem;
      border-bottom: 2px solid #cdd6da40;

      &:last-child {
        border-bottom: 2px solid #cdd6da40;
      }
    }

    &-body {
      padding: 0;

      p {
        padding: 1.6rem 2.4rem 3.2rem 2.4rem;
        line-height: 2.8rem;

        @media (max-width: 575.98px) {
          padding: 1.6rem 2.4rem 1.8rem 2.4rem;
        }
      }
    }
  }
}


.app.rtl {
  .faq .accordion-header button:not(.collapsed)::after {
    margin-left: 0;
    margin-right: auto;
  }

  .faq .accordion-header button::after {
    margin-left: 0;
    margin-right: auto;
  }
  .faq .accordion-header button h3 {
    text-align: right;
  }
}