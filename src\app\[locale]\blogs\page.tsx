import BreadCrumbs from "@/components/breadcrumbs/BreadCrumbs";
import "./blogs-listing.scss";
import BlogsListingComponent from "@/components/blogs/BlogsListing";
import {
  QueryClient,
  QueryFunction,
  QueryKey,
  dehydrate,
  HydrationBoundary,
} from "@tanstack/react-query";
import { endpoints } from "@/config/apiEndpoints";
import api from "@/config/axios.interceptor";

const getblogs: QueryFunction<unknown, QueryKey, unknown> = async ({ pageParam = 1 }) => {
  const res = await api.post(endpoints.blogs, {
    page: pageParam,
    limit: process.env.PAGESIZE || 15,
  });
  return res.data.result;
};

async function BlogListing() {
  const queryClient = new QueryClient();
  const pageLimit = process.env.PAGESIZE || 15;
  await queryClient.prefetchInfiniteQuery({
    queryKey: ["blogs"],
    queryFn: getblogs,
    initialPageParam: 1,
    getNextPageParam: (lastPage: any, pages) => lastPage.nextPage,
    pages: 2,
  });

  return (
    <main>
      <BreadCrumbs backHome="Home" currentPage="Blogs" image="/images/common/blogs.png" />
      <HydrationBoundary state={dehydrate(queryClient)}>
        <BlogsListingComponent pageLimit={pageLimit} />
      </HydrationBoundary>
    </main>
  );
}

export default BlogListing;
