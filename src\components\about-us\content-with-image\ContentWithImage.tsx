import Image from "next/image";
import "./content-with-image.scss";

function ContentWithImage({ sectionData }: any) {
  return (
    <section className="content-with-image">
      <div className="container">
        <h2>{sectionData?.title}</h2>
        {sectionData?.data?.map((items: any, index: number) => (
          <div className="content-with-image_row" key={index}>
            <div className="content-with-image_content">
              <h4>{items?.title}</h4>
              <p>{items?.description}</p>
            </div>
            <div className="content-with-image_image">
              <Image
                quality={100}
                priority
                src={items?.image ?? ""}
                width={541}
                height={379}
                alt="images"
              />
            </div>
          </div>
        ))}
      </div>
    </section>
  );
}

export default ContentWithImage;
