import OrderId from "@/components/my-accounts/order-id/OrderId";
import { endpoints } from "@/config/apiEndpoints";
import api from "@/config/axios.interceptor";
import { useLocaleContext } from "@/contexts/LocaleProvider";
import {
  HydrationBoundary,
  QueryClient,
  dehydrate,
} from "@tanstack/react-query";
import { cookies } from "next/headers";

async function page({ params }: any) {
  const queryClient = new QueryClient();
  const currentLocale = cookies().get("Next-Locale")?.value || "sa-en";
  try {
    //onst res = await api.get(`${endpoints.orderDetail}/${params.order}`);
    await queryClient.prefetchQuery({
      queryKey: ["orders", params.order, currentLocale?.split("-")[0]],
      queryFn: () => {
        return api
          .get(`${endpoints.orderDetail}/${params.order}`)
          .then((res: any) => {
            return res.data?.result;
          })
      },
    });
    // const data = res?.data?.result;
    return (
      <>
        <HydrationBoundary state={dehydrate(queryClient)}>
          <OrderId order={params.order} />
        </HydrationBoundary>
      </>
    );
  } catch (error) {
  }
}

export default page;
