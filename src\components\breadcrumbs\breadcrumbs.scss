.app.rtl {
  .breadcrumb {
    &_image {
      left: 0;
      right: unset;
      clip-path: polygon(0 0, 86% 0, 100% 100%, 0% 100%);
    }
    &_link {
      clip-path: polygon(0 0, 100% 0, 100% 100%, 4% 100%);
    }
  }
}


.breadcrumb {
  background: #1f2738;
  min-height: 11.8rem;
  background-repeat: no-repeat;
  background-position: right;
  position: relative;
  overflow: hidden;

  @media (max-width: 575.98px) {
    min-height: 6.4rem;
  }

  &_wrapper {
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: 100%;
  }

  &_link {
    width: 51%;
    clip-path: polygon(0 0, 86% 0, 100% 100%, 0% 100%);
    display: flex;
    align-items: center;

    ul {
      display: flex;
      align-items: center;

      li:not(:last-child) {
        flex: 1 0 auto;
      }
    }
  }

  &_image {
    width: 49%;
    clip-path: polygon(0 0, 100% 0, 100% 100%, 4% 100%);
    position: absolute;
    right: 0;

    img {
      min-height: 11.9rem;
      max-height: 11.9rem;

      @media (max-width: 575.98px) {
        min-height: 6.4rem;
        max-height: 6.4rem;
      }
    }
  }

  &_item {
    font-size: 1.5rem;
    font-style: normal;
    font-weight: 400;
    color: #fff;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    -webkit-box-orient: vertical;

    @media (max-width: 575.98px) {
      line-height: 1.6rem;
    }

    &.array_item {
      margin-left: 0.1rem;
      margin-right: 0.6rem;
    }

    &.active {
      a {
        color: #fff;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        line-clamp: 1;
        -webkit-box-orient: vertical;
        max-width: 44.8rem;
        width: 100%;
      }
    }

    @media (max-width: 575.98px) {
      font-size: 1.3rem;
    }

    a {
      color: #6b7280;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}