"use client"

import React from 'react'
import { useMediaQuery } from 'usehooks-ts';

function VideoBanner({ item }: any) {
    const mobile = useMediaQuery("(max-width: 768px)");
    return (
        <div style={{ width: "100%" }} >
            {mobile && <video
                style={{ width: "100%", height: "auto" }}
                className="d-block d-md-none"
                muted
                src={item?.mobileSrc ? item?.mobileSrc : item?.src}
                autoPlay
                loop
                playsInline
            ></video>}
            {!mobile && <video
                style={{ width: "100%", height: "auto" }}
                className="d-none d-md-block"
                muted
                src={item?.src}
                autoPlay
                loop
                playsInline
            ></video>}
        </div>
    )
}

export default VideoBanner