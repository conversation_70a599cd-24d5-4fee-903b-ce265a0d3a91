import Toaster from "@/components/toast/Toaster";
import { endpoints } from "@/config/apiEndpoints";
import api from "@/config/axios.interceptor";
import { toast } from "sonner";

export const addToCart = (
  product: any,
  quantity: number,
  size?: string | null,
  lensId?: string
): Promise<any> => {
  return new Promise(async (resolve, reject) => {
    try {
      const res = await api.post(endpoints.addToCart, {
        product,
        quantity,
        size,
        lensId,
      });
      if (res?.data?.errorCode === 0) {
        resolve(res.data);
        console.log(res.data)
        toast(
          <Toaster
            title={res?.data.message || "Product added to cart."}
            subTotal={`${res.data?.result?.currency || "AED"} ${res.data?.result?.baseTotal} `}
            link="/cart"
          />,
          {
            unstyled: true,
            id: "cart",
            duration: 2000,
          }
        );
      } else {
        toast.error(res?.data?.message);
      }
    } catch (err: any) {
      toast.error(err?.response?.data?.message);
      reject(err);
    }
  });
};

export const removeFromCart = async (id: string, size?: string, lensId?: string | undefined) => {
  try {
    const res = await api.post(endpoints.removeFromCart, {
      product: id,
      size,
      lensId,
    });
    return res.data;
  } catch (err) {
    return err;
  }
};

export const getCart = async () => {
  try {
    const res = await api.get(endpoints.cart);
    return res.data.result;
  } catch (err) {
    return [];
  }
};


export const getLoyalityPoints = async () => {
  try {
    const res = await api.get(endpoints.getLoyality);
    return res.data.result;
  } catch (err) {
    return [];
  }
};

export const getTryCart = async () => {
  try {
    const res = await api.get(endpoints.tryCart);
    return res.data.result;
  } catch (err) {
    return [];
  }
};

export const getSubscriptionSummary = async (id: string) => {
  try {
    const res = await api.post(endpoints.subscribeSummary, { id: id });
    return res.data.result;
  } catch (err) {
    return [];
  }
};

export const updateCart = (
  product: any,
  quantity: number,
  size?: string | null,
  lensId?: string,
  isToast?: boolean
): Promise<any> => {
  return new Promise(async (resolve, reject) => {
    try {
      const res = await api.post(endpoints.cartUpdate, {
        product,
        quantity,
        size,
        lensId,
        isToast
      });
      if (res?.data?.errorCode === 0) {
        resolve(res.data);
        console.log(isToast)
        isToast && toast(
          <Toaster
            title={res?.data.message || "Product added to cart."}
            subTotal={`${res.data?.result?.currency || "AED"} ${res.data?.result?.baseTotal} `}
            link="/cart"
          />,
          {
            unstyled: true,
            id: "cart",
            duration: 2000,
          }
        );
      } else {
        toast.error(res?.data?.message);
      }
    } catch (err: any) {
      toast.error(err?.response?.data?.message);
      reject(err);
    }
  });
};
