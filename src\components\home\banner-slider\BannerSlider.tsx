"use client";
import Image from "next/image";
import { Autoplay, Navigation } from "swiper/modules";
import "./banner-slider.scss";
import { Swiper, SwiperSlide } from "swiper/react";

function BannerSlider({ id, item }: any) {
  return (
    <section className="banner-slider" id={id}>
      <Swiper
        className="mySwiper"
        modules={[Autoplay, Navigation]}
        navigation={true}
        loop={true}
        speed={500}
        autoplay={{
          delay: 2000,
          disableOnInteraction: false,
          pauseOnMouseEnter: true,
        }}>
        {
          item?.items?.map((slide: any, i:number) => (
            <SwiperSlide key={i}>
              <Image quality={100} priority={i === 0} loading={i === 0 ? undefined : "lazy"} src={slide?.image} width={1366} height={780} alt="banner image" />
            </SwiperSlide>
          ))
        }
      </Swiper>
    </section>
  );
}

export default BannerSlider;
