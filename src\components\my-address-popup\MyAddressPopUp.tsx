import Modal from "react-bootstrap/Modal";
import "./my-address-popup.scss";
import { Submit<PERSON><PERSON><PERSON>, useForm } from "react-hook-form";
import api from "@/config/axios.interceptor";
import { endpoints } from "@/config/apiEndpoints";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { AuthContext } from "@/contexts/AuthProvider";
import { useContext } from "react";
import { countryCodeWithFlags } from "@/lib/countryCodeWithFlag";
import Select from "react-select";
import { TranslationContext } from "@/contexts/Translation";
import { useLocaleContext } from "@/contexts/LocaleProvider";
import { useSettingsContext } from "@/contexts/SettingsProvider";

interface Inputs {
  name: string;
  country: string;
  email?: string;
  suiteUnit: string;
  street: string;
  emirates: string;
  city: string;
  postalCode: string;
  countryCode: string;
  mobile: string;
  isDefault: boolean;
  type: "Home" | "Work" | "Other";
}

type addressPopupProps = {
  show: boolean;
  handleClose: () => void;
  address?: any;
  setAddress?: (address: any) => void;
};

function MyAddressPopUp({
  show,
  handleClose,
  address,
  setAddress,
}: addressPopupProps) {
  const { userProfile } = useContext(AuthContext);
  const { currentStore } = useSettingsContext()
  const { translation }: any = useContext(TranslationContext)
  const {
    register,
    handleSubmit,
    setFocus,
    watch,
    setValue,
    formState: { errors },
    setError
  } = useForm<Inputs>({
    values: {
      ...address,
      country: currentStore
    }, defaultValues: {
      countryCode: userProfile?.countryCode,
      mobile: userProfile?.mobile,
      country: currentStore,
      type: "Home"
    }
  });

  const { currentLocale: locale } = useLocaleContext()
  const emiratesOptions = [
    {
      label: locale.includes("en") ? "Abu Dhabi" : "أبو ظبي",
      value: "Abu Dhabi",
    },
    {
      label: locale.includes("en") ? "Ajman" : "عجمان",
      value: "Ajman",
    },
    {
      label: locale.includes("en") ? "Dubai" : "دبي",
      value: "Dubai",
    },
    {
      label: locale.includes("en") ? "Fujairah" : "الفجيرة",
      value: "Fujairah",
    },
    {
      label: locale.includes("en") ? "Ras Al Khaimah" : "رَأْس ٱلْخَيْمَة",
      value: "Ras Al Khaimah",
    },
    {
      label: locale.includes("en") ? "Sharjah" : "الشارقة",
      value: "Sharjah",
    },
    {
      label: locale.includes("en") ? "Umm Al Quwain" : "أم القيوين",
      value: "Umm Al Quwain",
    },
  ];

  const saudiEmiratesOptions = [
    {
      label: locale.includes("en") ? "Riyadh" : "الرياض",
      value: "Riyadh",
    },
    {
      label: locale.includes("en") ? "Makkah" : "مكة المكرمة",
      value: "Makkah",
    },
    {
      label: locale.includes("en") ? "Al Madinah" : "المدينة المنورة",
      value: "Al Madinah",
    },
    {
      label: locale.includes("en") ? "Tabuk" : "تبوك",
      value: "Tabuk",
    },
    {
      label: locale.includes("en") ? "Asir" : "أَسير",
      value: "Asir",
    },
    {
      label: locale.includes("en") ? "Northen Borders" : "الحدود الشمالية",
      value: "Northen Borders",
    },
    {
      label: locale.includes("en") ? "Hail" : "حائل",
      value: "Hail",
    },
    {
      label: locale.includes("en") ? "Eastern Province" : "المنطقة الشرقية",
      value: "Eastern Province",
    },
    {
      label: locale.includes("en") ? "Al Jawf" : "الجوف",
      value: "Al Jawf",
    },
    {
      label: locale.includes("en") ? "Jizan" : "جازان",
      value: "Jizan",
    },
    {
      label: locale.includes("en") ? "Al Bahah" : "الباحة",
      value: "Al Bahah",
    },
    {
      label: locale.includes("en") ? "Najran" : "نجران",
      value: "Najran",
    },
    {
      label: locale.includes("en") ? "Al Qassim" : "القصيم",
      value: "Al Qassim",
    }

  ]


  const queryClient = useQueryClient();
  const countryQuery = watch("country");
  const { data: countries } = useQuery({
    queryKey: ["countries", countryQuery],
    queryFn: () => {
      return fetch("/api/countries?limit=10&query=" + countryQuery).then(
        (res) => res.json()
      );
    },
  });

  const onSubmit: SubmitHandler<Inputs> = (data) => {
    if(locale.includes("ae")){
      if(!emiratesOptions?.map((item: any) => item.value).includes(data.emirates)){
        setError("emirates", { message: "Invalid Emirates" });
        return;
      }
    }else{
      if(!saudiEmiratesOptions?.map((item: any) => item.value).includes(data.emirates)){
        setError("emirates", { message: "Invalid Emirates" });
        return;
      }
    }
    if (address) {
      api
        .put(`${endpoints.updateAddress}/${address?.refid}`, data)
        .then((res) => {
          if (res.data.errorCode === 0) {
            handleClose();
            queryClient.invalidateQueries({ queryKey: ["address"] });
          }
        });
    } else {
      api.post(endpoints.addAddress, data).then((res) => {
        if (res.data.errorCode === 0) {
          queryClient
            .invalidateQueries({ queryKey: ["address"] })
            .finally(() => {
              if (setAddress) setAddress(res.data.result._id);
              handleClose();
            });
        }
      });
    }
  };

  return (
    <>
      <Modal
        className="address-book-popup"
        show={show}
        onHide={handleClose}
        backdrop="static"
        keyboard={false}
        centered
      >
        <Modal.Header closeButton>
          <h2>{address ? (translation?.myAccount?.addressEdit ?? "Edit") : (translation?.myAccount?.addressAdd ?? "Add New Address")}</h2>
        </Modal.Header>
        <Modal.Body>
          <div className="contact-form">
            <form
              className="contact-form_wrapper"
              onSubmit={handleSubmit(onSubmit)}
            >
              <div className="contact-form_inputs">
                <label htmlFor="">{translation?.formFields?.fullName ?? "Full name"}*</label>
                <input
                  {...register("name", {
                    required: (translation?.formFields?.fullNameRequiredError ?? "Name is Required"),
                    pattern: {
                      value: /^[a-zA-Z ]+$/,
                      message: (translation?.formFields?.numericError ?? "Only alphabets are allowed"),
                    },
                  })}
                  name="name"
                  type="text"
                  placeholder="Alex Smith"
                />
                <small className="form-error text-danger">
                  {errors.name?.message}
                </small>
              </div>
              {userProfile?.isGuest && <div className="contact-form_inputs">
                <label htmlFor="">{translation?.formFields?.emailAddress ?? "Email"}*</label>
                <input
                  {...register("email", {
                    required: (translation?.formFields?.emailAddressRequiredError ?? "Email is Required"),
                  })}
                  name="email"
                  type="email"
                  placeholder="<EMAIL>"
                />
                <small className="form-error text-danger">
                  {errors.email?.message}
                </small>
              </div>}

              <div className="contact-form_inputs">
                <label htmlFor="">{translation?.formFields?.country ?? "Country / Region"}*</label>
                <input
                  {...register("country", { required: (translation?.formFields?.countryRequiredError ?? "Country is Required") })}
                  type="text"
                  placeholder="Dubai"
                  list="countryDataList"
                  disabled
                />
                <small className="form-error text-danger">
                  {errors.country?.message}
                </small>

                <datalist id="countryDataList">
                  {countries?.map((country: any) => (
                    <option key={country.id} value={country.name}>
                      {country.name}
                    </option>
                  ))}
                </datalist>
              </div>

              <div className="contact-form_inputs">
                <label htmlFor="emirates">{translation?.formFields?.emirates ?? "Emirates"}*</label>
                {/* <input
                  {...register("emirates", {
                    required: (translation?.formFields?.emiratesRequiredError ?? "Emirates is required"),
                  })}
                  type="text"
                  placeholder="Dubai"
                /> */}
                <Select
                  options={locale.includes("ae") ? emiratesOptions: saudiEmiratesOptions}
                  onChange={(e: any) => setValue("emirates", e.value)}
                  placeholder={translation?.formFields?.selectEmirates ?? "Select Emirates"}
                  value={emiratesOptions.find((item: any) => item.value === watch("emirates"))}
                />
                <input
                  className="inp"
                  {...register("emirates", { required: (translation?.formFields?.emiratesRequiredError ?? "Emirates is required") })}
                  id="emirates"
                  name="emirates"
                />
                <small className="form-error text-danger">
                  {errors.emirates?.message}
                </small>
              </div>

              <div className="contact-form_inputs">
                <label htmlFor="">{translation?.formFields?.streat ?? "Street Address"}*</label>
                <input
                  {...register("street", {
                    required: (translation?.formFields?.streatRequiredError ?? "Street Address is Required"),
                  })}
                  name="street"
                  type="text"
                  placeholder="House number and street name"
                />
                <small className="form-error text-danger">
                  {errors.street?.message}
                </small>
              </div>

              <div className="contact-form_inputs">
                <label htmlFor="">{translation?.formFields?.apartment ?? "Apt, suite unit"}</label>
                <input
                  {...register("suiteUnit")}
                  name="suiteUnit"
                  type="text"
                  placeholder="Apartment, suite unit etc (optional)"
                />
              </div>

              <div className="contact-form_inputs">
                <label htmlFor="">{translation?.formFields?.city ?? "City"}*</label>
                <input
                  {...register("city", { required: (translation?.formFields?.cityRequiredError ?? "City is Required") })}
                  name="city"
                  type="text"
                  placeholder="Dubai"
                />
                <small className="form-error text-danger">
                  {errors.city?.message}
                </small>
              </div>

              <div className="contact-form_inputs">
                <label htmlFor="">{translation?.formFields?.postalCode ?? "Postal Code"}</label>
                <input
                  style={{ appearance: "textfield" }}
                  {...register("postalCode")}
                  name="postalCode"
                  type="number"
                  placeholder="Postal Code"
                />
              </div>

              <div className="contact-form_inputs ">
                <div className="select">
                  <label htmlFor="">{translation?.formFields?.phoneNumber ?? "Enter your phone number"}</label>
                  <Select
                    className="select-container"
                    onChange={(e: any) => setValue("countryCode", e.value)}
                    styles={
                      {
                        // option:(state)=>{}
                      }
                    }
                    theme={(theme) => ({
                      ...theme,
                      borderRadius: 0,
                      colors: {
                        ...theme.colors,
                        primary25: "#ccc",
                        primary: "black",
                      },
                    })}
                    classNames={{
                      control: (state) => "react-select",

                      dropdownIndicator: () => "d-none",
                      option: (state) =>
                        state.isSelected ? "option selected" : "option",
                      menu: () => "menu",
                    }}
                    formatOptionLabel={(country) => (
                      <div className="drop-item">
                        <img src={country.image} alt="" />
                        {country.label}
                      </div>
                    )}
                    options={countryCodeWithFlags?.map((country) => ({
                      label: country.name,
                      value: country.dial_code,
                      image: country.image,
                    }))}
                  />

                  <div className="countrycode">
                    <div className="countrycode-icon">
                      <input
                        {...register("countryCode")}
                        id="countryCode"
                        name="countryCode"
                      />
                    </div>

                    <input
                      {...register("mobile",
                        {
                          required: (translation?.formFields?.phoneNumberRequiredError ?? "Phone Number is Required"),
                        }
                      )}
                      tabIndex={1}
                      type="tel"
                      id="mobile"
                      placeholder="************"
                    />
                  </div>
                </div>
              </div>

              <div className="contact-form_inputs">
                <label htmlFor="">{translation?.formFields?.addressType ?? "Address Type"}</label>

                <div className="contact-form_inputs-radio">
                  <label htmlFor="one">
                    <input
                      {...register("type", {
                        required: (translation?.formFields?.addressTypeRequiredError ?? "Address Type is required"),
                      })}
                      name="type"
                      type="radio"
                      value="Home"
                      id="one"
                    />
                    <span>{translation?.myAccount?.addressHome ?? "Home"}</span>
                  </label>

                  <label htmlFor="two">
                    <input
                      {...register("type", {
                        required: "Address Type is required",
                      })}
                      name="type"
                      type="radio"
                      value="Work"
                      id="two"
                    />
                    <span>{translation?.myAccount?.addressWork ?? "Work"}</span>
                  </label>
                  <label htmlFor="three">
                    <input
                      {...register("type", {
                        required: "Address Type is required",
                      })}
                      name="type"
                      type="radio"
                      value="Other"
                      id="three"
                    />
                    <span>{translation?.myAccount?.addressOther ?? "Other"}</span>
                  </label>
                </div>

                <small className="form-error text-danger">
                  {errors.type?.message}
                </small>
              </div>

              <div className="contact-form_btns">
                <input value={translation?.formFields?.submit ?? "Save"} type="submit" className="save  button" />
                <button type="button" className="cancel button" onClick={() => handleClose()}>{translation?.formFields?.cancel ?? "Cancel"}</button>
              </div>

              <div className="contact-form_checkbox">
                <label htmlFor="checkbox">
                  <input
                    type="checkbox"
                    id="checkbox"
                    {...register("isDefault")}
                  />
                  <span className="checkmark">
                    {translation?.myAccount?.addressSaveCheck ?? "Save my information for a faster checkout"}
                  </span>
                </label>
              </div>
            </form>
          </div>
        </Modal.Body>
      </Modal>
    </>
  );
}

export default MyAddressPopUp;
