/***************************************************
 * Generated by SVG Artista on 11/24/2023, 5:11:55 PM
 * MIT license (https://opensource.org/licenses/MIT)
 * W. https://svgartista.net
 **************************************************/

@-webkit-keyframes animate-svg-stroke-1 {
  0% {
    stroke-dashoffset: 509.80548095703125px;
    stroke-dasharray: 509.80548095703125px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 509.80548095703125px;
  }
}

@keyframes animate-svg-stroke-1 {
  0% {
    stroke-dashoffset: 509.80548095703125px;
    stroke-dasharray: 509.80548095703125px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 509.80548095703125px;
  }
}

@-webkit-keyframes animate-svg-fill-1 {
  0% {
    fill: transparent;
  }

  100% {
    fill: rgb(255, 255, 255);
  }
}

@keyframes animate-svg-fill-1 {
  0% {
    fill: transparent;
  }

  100% {
    fill: rgb(255, 255, 255);
  }
}

.svg-elem-reverse-1 {
  -webkit-animation: animate-svg-stroke-1 0.6s cubic-bezier(0.47, 0, 0.745, 0.715) 0s both,
    animate-svg-fill-1 0.6s cubic-bezier(0.47, 0, 0.745, 0.715) 0s both;
  animation: animate-svg-stroke-1 0.6s cubic-bezier(0.47, 0, 0.745, 0.715) 0s both,
    animate-svg-fill-1 0.6s cubic-bezier(0.47, 0, 0.745, 0.715) 0s both;
}

@-webkit-keyframes animate-svg-stroke-2 {
  0% {
    stroke-dashoffset: 224.4982147216797px;
    stroke-dasharray: 224.4982147216797px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 224.4982147216797px;
  }
}

@keyframes animate-svg-stroke-2 {
  0% {
    stroke-dashoffset: 224.4982147216797px;
    stroke-dasharray: 224.4982147216797px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 224.4982147216797px;
  }
}

@-webkit-keyframes animate-svg-fill-2 {
  0% {
    fill: transparent;
  }

  100% {
    fill: rgb(225, 225, 225);
  }
}

@keyframes animate-svg-fill-2 {
  0% {
    fill: transparent;
  }

  100% {
    fill: rgb(225, 225, 225);
  }
}

.svg-elem-reverse-2 {
  -webkit-animation: animate-svg-stroke-2 0.6s cubic-bezier(0.47, 0, 0.745, 0.715) 0.12s both,
    animate-svg-fill-2 0.6s cubic-bezier(0.47, 0, 0.745, 0.715) 0.2s both;
  animation: animate-svg-stroke-2 0.6s cubic-bezier(0.47, 0, 0.745, 0.715) 0.12s both,
    animate-svg-fill-2 0.6s cubic-bezier(0.47, 0, 0.745, 0.715) 0.2s both;
}

@-webkit-keyframes animate-svg-stroke-3 {
  0% {
    stroke-dashoffset: 179.45474243164062px;
    stroke-dasharray: 179.45474243164062px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 179.45474243164062px;
  }
}

@keyframes animate-svg-stroke-3 {
  0% {
    stroke-dashoffset: 179.45474243164062px;
    stroke-dasharray: 179.45474243164062px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 179.45474243164062px;
  }
}

@-webkit-keyframes animate-svg-fill-3 {
  0% {
    fill: transparent;
  }

  100% {
    fill: rgb(225, 225, 225);
  }
}

@keyframes animate-svg-fill-3 {
  0% {
    fill: transparent;
  }

  100% {
    fill: rgb(225, 225, 225);
  }
}

.svg-elem-reverse-3 {
  -webkit-animation: animate-svg-stroke-3 0.6s cubic-bezier(0.47, 0, 0.745, 0.715) 0.24s both,
    animate-svg-fill-3 0.6s cubic-bezier(0.47, 0, 0.745, 0.715) 0.4s both;
  animation: animate-svg-stroke-3 0.6s cubic-bezier(0.47, 0, 0.745, 0.715) 0.24s both,
    animate-svg-fill-3 0.6s cubic-bezier(0.47, 0, 0.745, 0.715) 0.4s both;
}

@-webkit-keyframes animate-svg-stroke-4 {
  0% {
    stroke-dashoffset: 255.05491638183594px;
    stroke-dasharray: 255.05491638183594px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 255.05491638183594px;
  }
}

@keyframes animate-svg-stroke-4 {
  0% {
    stroke-dashoffset: 255.05491638183594px;
    stroke-dasharray: 255.05491638183594px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 255.05491638183594px;
  }
}

@-webkit-keyframes animate-svg-fill-4 {
  0% {
    fill: transparent;
  }

  100% {
    fill: rgb(225, 225, 225);
  }
}

@keyframes animate-svg-fill-4 {
  0% {
    fill: transparent;
  }

  100% {
    fill: rgb(225, 225, 225);
  }
}

.svg-elem-reverse-4 {
  -webkit-animation: animate-svg-stroke-4 0.6s cubic-bezier(0.47, 0, 0.745, 0.715) 0.36s both,
    animate-svg-fill-4 0.6s cubic-bezier(0.47, 0, 0.745, 0.715) 0.6000000000000001s both;
  animation: animate-svg-stroke-4 0.6s cubic-bezier(0.47, 0, 0.745, 0.715) 0.36s both,
    animate-svg-fill-4 0.6s cubic-bezier(0.47, 0, 0.745, 0.715) 0.6000000000000001s both;
}

@-webkit-keyframes animate-svg-stroke-5 {
  0% {
    stroke-dashoffset: 128.2685546875px;
    stroke-dasharray: 128.2685546875px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 128.2685546875px;
  }
}

@keyframes animate-svg-stroke-5 {
  0% {
    stroke-dashoffset: 128.2685546875px;
    stroke-dasharray: 128.2685546875px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 128.2685546875px;
  }
}

@-webkit-keyframes animate-svg-fill-5 {
  0% {
    fill: transparent;
  }

  100% {
    fill: rgb(225, 225, 225);
  }
}

@keyframes animate-svg-fill-5 {
  0% {
    fill: transparent;
  }

  100% {
    fill: rgb(225, 225, 225);
  }
}

.svg-elem-reverse-5 {
  -webkit-animation: animate-svg-stroke-5 0.6s cubic-bezier(0.47, 0, 0.745, 0.715) 0.48s both,
    animate-svg-fill-5 0.6s cubic-bezier(0.47, 0, 0.745, 0.715) 0.8s both;
  animation: animate-svg-stroke-5 0.6s cubic-bezier(0.47, 0, 0.745, 0.715) 0.48s both,
    animate-svg-fill-5 0.6s cubic-bezier(0.47, 0, 0.745, 0.715) 0.8s both;
}

@-webkit-keyframes animate-svg-stroke-6 {
  0% {
    stroke-dashoffset: 211.56094360351562px;
    stroke-dasharray: 211.56094360351562px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 211.56094360351562px;
  }
}

@keyframes animate-svg-stroke-6 {
  0% {
    stroke-dashoffset: 211.56094360351562px;
    stroke-dasharray: 211.56094360351562px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 211.56094360351562px;
  }
}

@-webkit-keyframes animate-svg-fill-6 {
  0% {
    fill: transparent;
  }

  100% {
    fill: rgb(225, 225, 225);
  }
}

@keyframes animate-svg-fill-6 {
  0% {
    fill: transparent;
  }

  100% {
    fill: rgb(225, 225, 225);
  }
}

.svg-elem-reverse-6 {
  -webkit-animation: animate-svg-stroke-6 0.6s cubic-bezier(0.47, 0, 0.745, 0.715) 0.6s both,
    animate-svg-fill-6 0.6s cubic-bezier(0.47, 0, 0.745, 0.715) 1s both;
  animation: animate-svg-stroke-6 0.6s cubic-bezier(0.47, 0, 0.745, 0.715) 0.6s both,
    animate-svg-fill-6 0.6s cubic-bezier(0.47, 0, 0.745, 0.715) 1s both;
}

@-webkit-keyframes animate-svg-stroke-7 {
  0% {
    stroke-dashoffset: 232.0189666748047px;
    stroke-dasharray: 232.0189666748047px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 232.0189666748047px;
  }
}

@keyframes animate-svg-stroke-7 {
  0% {
    stroke-dashoffset: 232.0189666748047px;
    stroke-dasharray: 232.0189666748047px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 232.0189666748047px;
  }
}

@-webkit-keyframes animate-svg-fill-7 {
  0% {
    fill: transparent;
  }

  100% {
    fill: rgb(225, 225, 225);
  }
}

@keyframes animate-svg-fill-7 {
  0% {
    fill: transparent;
  }

  100% {
    fill: rgb(225, 225, 225);
  }
}

.svg-elem-reverse-7 {
  -webkit-animation: animate-svg-stroke-7 0.6s cubic-bezier(0.47, 0, 0.745, 0.715) 0.72s both,
    animate-svg-fill-7 0.6s cubic-bezier(0.47, 0, 0.745, 0.715) 1.2000000000000002s both;
  animation: animate-svg-stroke-7 0.6s cubic-bezier(0.47, 0, 0.745, 0.715) 0.72s both,
    animate-svg-fill-7 0.6s cubic-bezier(0.47, 0, 0.745, 0.715) 1.2000000000000002s both;
}

@-webkit-keyframes animate-svg-stroke-8 {
  0% {
    stroke-dashoffset: 512px;
    stroke-dasharray: 512px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 512px;
  }
}

@keyframes animate-svg-stroke-8 {
  0% {
    stroke-dashoffset: 512px;
    stroke-dasharray: 512px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 512px;
  }
}

@-webkit-keyframes animate-svg-fill-8 {
  0% {
    fill: transparent;
  }

  100% {
    fill: rgb(255, 255, 255);
  }
}

@keyframes animate-svg-fill-8 {
  0% {
    fill: transparent;
  }

  100% {
    fill: rgb(255, 255, 255);
  }
}

.svg-elem-reverse-8 {
  -webkit-animation: animate-svg-stroke-8 0.6s cubic-bezier(0.47, 0, 0.745, 0.715) 0.84s both,
    animate-svg-fill-8 0.6s cubic-bezier(0.47, 0, 0.745, 0.715) 1.4000000000000001s both;
  animation: animate-svg-stroke-8 0.6s cubic-bezier(0.47, 0, 0.745, 0.715) 0.84s both,
    animate-svg-fill-8 0.6s cubic-bezier(0.47, 0, 0.745, 0.715) 1.4000000000000001s both;
}
