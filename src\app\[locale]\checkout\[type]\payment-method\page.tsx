"use client";

import CheckoutOrderSummary from "@/components/checkout-order-summary/CheckoutOrderSummary";
import "./payment-method.scss";
import Image from "next/image";
import { useContext, useEffect, useState } from "react";
import SuccessPopup from "@/components/success-popup/SuccessPopup";
import api from "@/config/axios.interceptor";
import { endpoints } from "@/config/apiEndpoints";
import { CheckoutContext } from "@/contexts/CheckoutContext";
import { toast } from "sonner";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { AuthContext } from "@/contexts/AuthProvider";
import { sendGTMEvent } from "@next/third-parties/google";
import { getCart, getLoyalityPoints, getSubscriptionSummary, getTryCart } from "@/lib/methods/cart";
import { TranslationContext } from "@/contexts/Translation";
import Loyality from "@/components/cart/Loyality";
import CouponModal from "@/components/coupon-modal/Coupon";
import Coupon from "@/components/cart/Coupon";
import { createHmac } from "crypto";
import { SettingsContext } from "@/contexts/SettingsProvider";
import { useLocaleContext } from "@/contexts/LocaleProvider";

type paymentMethodTypes = "COD" | "creditCard" | "tamara" | "tabby";

function isObject(value: any) {
  return value !== null && typeof value === 'object' && !Array.isArray(value) && Object.keys(value).length > 0;
}

type checkOutConfirmationDataType = {
  title: string;
  description: string;
  primaryBtnTxt: string;
  secondaryBtnTxt?: string | null;
  secondaryBtnLink?: any | null;
  primaryBtnLink?: string;
};

export default function Page({
  params,
}: {
  params: { id: string; type: string };
}) {
  const router = useRouter();
  const [show, setShow] = useState(false);
  const [couponShow, setCouponShow] = useState(false);
  const [paymentCharges, setPaymentCharges] = useState<any>(null)
  const handleClose = () => {
    router.push("/");
    setShow(false);
  };
  const handleCouponClose = () => {
    setCouponShow(false);
  };
  const handleShow = () => setShow(true);
  const handleCouponShow = () => setCouponShow(true);
  const { selectedAddress, paymentMethod, shippingMethod, boundingRect, id, storeId } =
    useContext(CheckoutContext);
  const [method, setMethod] = useState<paymentMethodTypes>("creditCard");
  const [tabbyError, setTabbyError] = useState("");
  const [order, setOrder] = useState<any>(null);
  const [modalCoupon, setModalCoupon] = useState<null | string>("");
  const [couponLoading, setCouponLoading] = useState(false);
  const [loading, setLoading] = useState(false);
  const queryClient = useQueryClient();
  const { userProfile } = useContext(AuthContext);
  const { translation } = useContext(TranslationContext);
  const { settings } = useContext(SettingsContext)
  const [isGiftWrapping, setIsGiftWrapping] = useState(false);
  const [giftWrapMessage, setGiftWrapMessage] = useState("");
  const {
    data: shippingCharge,
    isLoading: shippingChargeLoading,
    error,
  } = useQuery({
    queryKey: ["shipping-charge", selectedAddress],
    queryFn: () => {
      return api.post(`${endpoints.shippingCharge}`).then((res) => {
        if (res.status === 200) {
          return res.data?.result;
        } else {
          return [];
        }
      });
    },
  });

  useEffect(() => {
    if (!selectedAddress) {
      router.push(`/checkout/${params.type}`);
    }
    if (!shippingMethod) {
      router.push(`/checkout/${params.type}/shipping-method`);
    }
  }, [selectedAddress]);

  const { data: loyalityPoints, isLoading: isLoyalityPointsLoading, isError } = useQuery({
    queryKey: ["loyality"],
    queryFn: getLoyalityPoints,
  });

  useEffect(() => {
    api.get(`${endpoints.paymentCharge}`).then((res) => {
      if (res.status === 200) {
        let values: any = {}
        res.data?.result?.forEach((item: any) => {
          values[item.type] = item.fee
        })
        setPaymentCharges(values)
      } else {
        return [];
      }
    })
  }, [])

  const [checkOutConfirmationData, setCheckOutConfirmationData] =
    useState<checkOutConfirmationDataType>({
      title: translation?.popup?.checkoutTitle ?? "Thank you for ordering!",
      description: translation?.popup?.checkoutDescription ??
        "Your order was completed successfully. We will send you an update with your order confirmation shortly.",
      primaryBtnTxt: translation?.popup?.continueShopping ?? "Continue Shopping",
      secondaryBtnTxt: `${order ? (translation?.popup?.viewOrder ?? "View Order") : (translation?.popup?.viewOrders ?? "View Orders")}`,
      primaryBtnLink: "/",
      secondaryBtnLink: `${params?.type === "try-cart"
        ? "/my-accounts/try-cart-order"
        : order
          ? `/my-accounts/my-orders/${order}`
          : "/my-accounts/my-orders"
        }`,
    });
  const { currentLocale, currencyCode } = useLocaleContext()
  const { data: cart, isFetching } = useQuery({
    queryKey: [
      params?.type === "try-cart"
        ? "tryCart"
        : params?.type === "subscription"
          ? "subscription-summary"
          : "cart", currentLocale?.split("-")[0],
    ],
    queryFn:
      params?.type === "try-cart"
        ? getTryCart
        : params?.type === "subscription"
          ? () => getSubscriptionSummary(id)
          : getCart,
  });

  const sendPaymentDataLayer = () => {
    console.log("payment")
    sendGTMEvent({ ecommerce: null })
    let eventData: any = {
      event: "add_payment_info",
      ecommerce: {
        currency: currencyCode,
        value: cart?.total,
        payment_type: method,
        coupon: cart?.couponCode,
        items: cart?.products?.map((item: any, i: number) => {
          return {
            item_id: item?.sku,
            item_name: item?.name,
            index: i,
            item_brand: item?.brand,
            item_category: item?.category?.[0]?.name,
            item_category2: item?.category?.[1]?.name,
            item_variant: item?.color,
            price: item?.price,
            quantity: item.quantity,
          }
        })
      }
    }
    if (userProfile) {
      const mobile = createHmac('sha256', '123').update(userProfile?.mobile).digest('hex')
      let email = null;
      if (userProfile?.email) email = createHmac('sha256', '123').update(userProfile?.email).digest('hex')
      eventData.user = {
        mobile,
        email,
        user_id: userProfile?._id
      }
    }
    sendGTMEvent(eventData)
  }

  const sendPurchaseDataLayer = (orderId: string) => {
    sendGTMEvent({ ecommerce: null })
    console.log("purchase")
    console.log(orderId)
    let eventData: any = {
      event: "purchase",
      ecommerce: {
        currency: currencyCode,
        transaction_id: orderId,
        value: cart?.total,
        tax: cart?.tax,
        shipping: cart?.shipping || "",
        coupon: cart?.couponCode || "",
        items: cart?.products?.map((item: any, i: number) => {
          return {
            item_id: item?.sku,
            item_name: item?.name,
            index: i,
            item_brand: item?.brand,
            item_category: item?.category?.[0]?.name,
            item_category2: item?.category?.[1]?.name,
            item_variant: item?.color,
            price: item?.price,
            quantity: item.quantity,
          }
        })
      }
    }
    if (userProfile) {
      const mobile = createHmac('sha256', '123').update(userProfile?.mobile).digest('hex')
      let email = null;
      if (userProfile?.email) email = createHmac('sha256', '123').update(userProfile?.email).digest('hex')
      eventData.user = {
        mobile,
        email,
        user_id: userProfile?._id
      }
    }
    sendGTMEvent(eventData)
    console.log("purchase sent")
  }

  const placeOrder = () => {
    setLoading(true);
    sendPaymentDataLayer()
    api
      .post(endpoints.placeOrder, {
        cart: params?.type || "cart",
        // cart: "try-cart",
        address: selectedAddress,
        paymentMethod: method,
        shippingMethod: shippingMethod,
        store: storeId,
        isGiftWrapping,
        giftWrapMessage,
      })
      .then((res) => {
        if (method === "creditCard") {
          setLoading(false)
          router.push(res?.data?.result?.postUrl);
          return;
        }
        if (method === "tamara") {
          setLoading(false)
          router.push(res?.data?.result?.checkout_url);
          return;
        }
        if (method === "tabby") {
          setLoading(false)
          router.push(res?.data?.result?.configuration?.available_products?.installments?.[0]?.web_url);
          return;
        }
        if (userProfile?.isGuest === true) {
          setCheckOutConfirmationData({
            title: translation?.popup?.checkoutTitle ?? "Thank you for ordering!",
            description: translation?.popup?.checkoutDescription ??
              "Your order was completed successfully. We will send you an email with your order confirmation shortly.",
            primaryBtnTxt: translation?.popup?.continueShopping ?? "Continue Shopping",
            secondaryBtnTxt: null,
            primaryBtnLink: "/",
            secondaryBtnLink: `${params?.type === "try-cart"
              ? "/my-accounts/try-cart-order"
              : order
                ? `/my-accounts/my-orders/${order}`
                : "/my-accounts/my-orders"
              }`,
          });
        }
        setLoading(false)
        setOrder(res.data?.result?.orderNo);
        sendPurchaseDataLayer(res.data?.result?.orderNo)
        handleShow();
        queryClient.invalidateQueries({ queryKey: ["cart"] });
        queryClient.invalidateQueries({ queryKey: ["tryCart"] });
        queryClient.invalidateQueries({ queryKey: ["user"] });

        // queryClient.invalidateQueries({ queryKey: ["cart", "counts"] });
      })
      .catch((err) => {
        setLoading(false)
        console.log(err)
        queryClient.invalidateQueries({ queryKey: ["cart"] });
        queryClient.invalidateQueries({ queryKey: ["user"] });
        toast.error(err.response.data.message);
        if (err.response.data.errorCode === 1) {
          router.push(`/checkout/${params.type}`);
        }
        if (err.response.data.errorCode === 2) {
          router.push(`/cart`);
        }
      });
  };

  useEffect(() => {
    if (cart) {
      console.log(cart)
      api.post(endpoints.tabbyCheck, {
        amount: cart?.total,
        address: selectedAddress,
      }).then((res) => {
        if (res.data.errorCode === 0) {
          setTabbyError("");
        } else {
          setTabbyError("Tabby not available for this order");
        }
      }).catch((err) => {
        console.log(err)
        setTabbyError(err.response?.data?.result?.error);
      })
    }
  }, [cart])

  return (
    <div
      className="pay-method"
      style={{ minHeight: `${boundingRect?.height + 120}px` }}
    >
      <div className="container">
        <div className="pay-method_wrapper">
          <div className="pay-method_left">
            <h4>{translation?.cartPage?.paymentMethods ?? "Payment Methods"}</h4>
            <div className="pay-method_box">

              <div className="pay-method_radio">
                <label htmlFor="creditCard">
                  <input
                    type="radio"
                    name="payment"
                    id="creditCard"
                    value="creditCard"
                    onChange={() => setMethod("creditCard")}
                    checked={method === "creditCard"}
                  />
                  {translation?.cartPage?.card ?? "Credit / Debit Cards"}
                </label>
              </div>

              {params?.type !== "subscription" && (
                <div className="pay-method_radio">
                  <label htmlFor="COD">
                    <input
                      type="radio"
                      name="payment"
                      value="COD"
                      id="COD"
                      onChange={() => setMethod("COD")}
                      checked={method === "COD"}
                    />
                    {translation?.cartPage?.cod ?? "Cash on Delivery"}
                  </label>
                  <span>{translation?.cartPage?.codText ?? "Pay with cash upon delivery"}</span>
                </div>
              )}

              {settings?.tamara && <div className="pay-method_radio" style={{ display: "flex", justifyContent: "space-between" }}>
                <div className="" >
                  <label htmlFor="tamara">
                    <input
                      type="radio"
                      name="tamara"
                      id="tamara"
                      value="tamara"
                      onChange={() => setMethod("tamara")}
                      checked={method === "tamara"}
                    />
                    {/* {translation?.cartPage?.card ?? "Credit / Debit Cards"} */}
                    {translation?.cartPage?.tamara ?? "Tamara"}
                  </label>
                  <span>{translation?.cartPage?.tamaraDesc ?? "Split your payment into 4 interest-free installments"}</span>
                </div>
                <Image
                  quality={100}
                  priority
                  src="/icons/tamara-en.png"
                  unoptimized
                  width={100}
                  height={30}
                  alt="tamara"
                  style={{ width: "100px", objectFit: "contain", margin: "auto 0" }}
                />
              </div>}

              {settings?.tabby &&
                <>
                  <div className="pay-method_radio" style={{ display: "flex", justifyContent: "space-between" }}>
                    <div className="" >
                      <label htmlFor="tabby">
                        <input
                          type="radio"
                          name="tabby"
                          id="tabby"
                          value="tabby"
                          onChange={() => {
                            if (!tabbyError) {
                              setMethod("tabby")
                            }
                          }}
                          checked={method === "tabby"}
                        />
                        {/* {translation?.cartPage?.card ?? "Credit / Debit Cards"} */}
                        {translation?.cartPage?.tabby ?? "Tabby - Pay in 4. No interest, no fees."}
                      </label>
                      <span>{translation?.cartPage?.tabbyDesc ?? "Use any card."}</span>
                    </div>
                    <Image
                      quality={100}
                      priority
                      src="/icons/tabby-badge.png"
                      unoptimized
                      width={100}
                      height={30}
                      alt="tabby"
                    />
                  </div>
                  {tabbyError && <p style={{ color: "red", fontSize: "12px", marginLeft: "2rem" }}>{tabbyError}</p>}
                </>
              }


            </div>
            {settings?.isGiftWrapping ? (
              <div style={{ display: "flex", flexDirection: "column", gap: "1rem", padding: "2rem 2.5rem", marginTop: "2rem", background: "#fdfdfe", borderRadius: "2rem", border: "1px solid #ebebeb" }}>
                <div style={{ display: "flex", gap: "1rem", fontSize: "1.7rem", fontWeight: "500", alignItems: "center" }}>
                  <input
                    style={{ cursor: "pointer" }}
                    type="checkbox"
                    name="isGiftWrapping"
                    id="isGiftWrapping"
                    onChange={() => setIsGiftWrapping(prev => !prev)}
                    checked={isGiftWrapping}
                  />
                  <label style={{ cursor: "pointer" }} htmlFor="isGiftWrapping">
                    {translation?.cartPage?.giftWrapping ?? "Gift Wrapping"}
                  </label>
                </div>
              </div>
            ) : ""}

            {isGiftWrapping && (
              <div style={{ marginTop: "1rem", width: "100%"}}>
                <textarea
                  style={{width: "100%", padding: "2rem", borderRadius: "2rem", border: "1px solid rgb(235, 235, 235)"}}
                  onChange={(e) => setGiftWrapMessage(e.target.value)}
                  name=""
                  id=""
                  value={giftWrapMessage}
                  rows={5}
                  placeholder="Gift Message..."
                />
              </div>
            )}

            <div style={{ maxWidth: "500px" }} className="cart_order-coupon">
              {couponShow && (
                <CouponModal
                  couponLoading={couponLoading}
                  setCouponLoading={setCouponLoading}
                  appliedCoupon={cart?.couponCode}
                  setCoupon={setModalCoupon}
                  show={couponShow}
                  handleClose={handleCouponClose}
                />
              )}
            </div>
            <div style={{ marginTop: "2rem" }}>
              <h5 style={{ fontSize: "2rem", fontWeight: "600" }}>{translation?.cartPage?.discountCode ?? "Discount Codes"}</h5>
              <p>{translation?.cartPage?.discountCodeText ?? "Enter your coupon code if you have one"}</p>
              <div style={{ padding: '2rem', borderRadius: "2rem", marginTop: "2rem", border: "1px solid #e4e4e4", background: '#fdfdfe' }} className="">
                <div className="cart_order-coupon">
                  <button style={{ marginTop: "0rem" }} type="button" onClick={handleCouponShow}>
                    <Image
                      quality={100}
                      priority
                      src="/images/cart/fi_879757.png"
                      width={24}
                      height={24}
                      alt="image"
                    />
                    <span>{translation?.cartPage?.viewOffers ?? "View available offers"}</span>
                    <Image
                      quality={100}
                      priority
                      className="arrow"
                      src="/images/cart/Arrow-Down.png"
                      width={14}
                      height={14}
                      alt="image"
                    />
                  </button>
                </div>

                <div style={{ maxWidth: "500px", padding: '1rem' }} className="cart_order-code">
                  <Coupon
                    couponLoading={couponLoading}
                    setCouponLoading={setCouponLoading}
                    modalCoupon={modalCoupon}
                    handleCouponShow={handleCouponShow}
                    setModalCoupon={setModalCoupon}
                    couponCode={cart?.couponCode}
                    isApplied={cart?.couponApplied}
                    couponsModalClose={handleCouponClose}
                  />
                </div>

              </div>
            </div>

            {isObject(loyalityPoints) && (
              <Loyality
                loyalityPoints={loyalityPoints}
                loyalityApplied={cart?.loyaltyApplied}
                isCardLoading={isFetching}
              />
            )}

            <button disabled={loading} style={{ opacity: loading ? 0.5 : 1 }} className="paynow-btn" onClick={placeOrder}>{translation?.cartPage?.placeOrder ?? "Place Order"}</button>
            <SuccessPopup
              show={show}
              handleClose={handleClose}
              data={checkOutConfirmationData}
            />
          </div>

          <div className="pay-method_right">
            <CheckoutOrderSummary
              params={params}
              shippingCharge={
                // params?.type === "cart" ? shippingCharge?.data?.price || 0 : 0
                params?.type === "cart" ? 0 || 0 : 0
              }
              paymentMethodCharge={Number(paymentCharges ? paymentCharges[method] : 0)}
              method={method}
              giftWrappingFee={isGiftWrapping ? Number(settings?.giftWrappingFee) : 0}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
