.bottom-navigation {
  background: #fff;
  box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
  position: fixed;
  bottom: 0;
  width: 100%;
  background-color: #fff;
  z-index: 99;
  padding: 1.1rem 0 0.8em 0;

  ul {
    display: flex;
    justify-content: space-between;

    li {
      display: flex;
      flex-direction: column;
      align-items: center;
      color: #050f3180;
      font-size: 1.3rem;
      line-height: 1.6rem;
      font-weight: 400;
      cursor: pointer;
      position: relative;

      a {
        color: #050f3180;
      }

      .cart-count {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        color: #ffffff;
        background-color: #111827;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 8px;
        font-weight: 400;
        position: absolute;
        left: 19px;
        top: -7px;
      }

      img {
        width: 2.4rem;
        height: 2.4rem;
        object-fit: contain;
        margin-bottom: 0.4rem;
        filter: brightness(0) saturate(100%) invert(99%) sepia(12%) saturate(2124%) hue-rotate(178deg) brightness(90%) contrast(73%);
      }

      &.isActive {
        color: #050f31;

        a {
          color: #050f31;
        }

        img {
          filter: brightness(0) saturate(100%) invert(5%) sepia(27%) saturate(6892%) hue-rotate(217deg) brightness(98%) contrast(100%);
        }
      }
    }
  }
}

.react-modal-sheet-container {
  border-radius: 2rem 2rem 0rem 0rem !important;
  height: 64rem !important;

  &.accountSheet {
    height: 49rem !important;

    &+.react-modal-sheet-backdrop {
      background-color: rgba(0, 0, 0, 0) !important;
    }
  }
}

.react-modal-sheet-content {
  padding: 2rem 4.7rem;

  ul {
    li {
      position: relative;
      padding: 0 1.4rem;

      a {
        color: #000;
        font-size: 1.7rem;
        font-weight: 400;
        padding: 0 1.6rem;
        cursor: pointer;
        padding: 0;
      }

      &::after {
        content: "";
        position: absolute;
        background-image: url(../../../public/images/common/arrow-down.png);
        background-position: right;
        width: 1.2rem;
        height: 1.2rem;
        background-repeat: no-repeat;
        background-size: contain;
        top: 39%;
        transform: translateY(-50%);
        right: 10px;
      }

      &:not(:last-child) {
        margin-bottom: 1.4rem;
        padding-bottom: 1.4rem;
        border-bottom: 1px solid rgba(163, 163, 163, 0.2);
      }
    }
  }
}

.react-modal-sheet-drag-indicator {
  width: 3rem !important;
  height: 0.5rem !important;
}