.share-popup {
  &.modal {
    .modal-dialog {
      max-width: 52.9rem;
      width: 100%;
      margin: 0 auto;
    }

    .modal-header {
      padding: 0;
      border: none;

      .btn-close {
        background-image: url(../../../public/images/common/close.png);
        width: 4.8rem;
        height: 4.8rem;
        background-position: center;
        background-repeat: no-repeat;
        background-size: contain;
        opacity: 1;
        padding: 0;
        transform: translateX(38px);

        @media (max-width: 575.98px) {
          width: 2.8rem;
          height: 2.8rem;
          transform: none;
        }

        &:focus {
          box-shadow: none;
          outline: none;
          border: none;
        }
      }
    }

    .modal-content {
      border: none;
      border-radius: 3rem;
      padding: 1.8rem 5.3rem 6.2em 5.3rem;

      @media (max-width: 575.98px) {
        padding: 2.1rem 1.7rem 4.2rem 2.5rem;
        border-radius: 2rem;
      }
    }

    .modal-body {
      padding: 0;
      display: flex;
      flex-direction: column;

      h2 {
        text-align: left;
        line-height: 4rem;
        display: flex;
        align-items: center;
        column-gap: 2rem;

        @media (max-width: 575.98px) {
          line-height: 3rem;
          font-size: 2.4rem;
        }

        img {
          width: 4rem;
          height: 4rem;
          object-fit: contain;

          @media (max-width: 575.98px) {
            display: none;
          }
        }
      }

      .copy-btn {
        position: relative;

        button {
          border: none;
          background-color: transparent;
          padding: 0;
          position: absolute;
          right: 0;

          img {
            object-fit: contain;
          }
        }
      }

      label {
        color: #000;
        font-size: 1.6rem;
        font-weight: 600;
        padding-bottom: 0.8rem;
      }

      input {
        color: #242426;
        font-size: 1.8rem;
        font-weight: 400;
        line-height: 2.8rem;
        border: none;
        border-bottom: 1px solid #e2e4e5;
        padding-bottom: 0.8rem;
        padding-right: 4rem;
        width: 100%;

        &:focus-visible {
          outline: none;
          border-bottom: 1px solid #e2e4e5;
        }

        &::placeholder {
          color: #242426;
          font-size: 1.8rem;
          font-weight: 400;
          line-height: 2.8rem;
        }
      }

      h6 {
        margin-top: 3rem;
        color: #000;
        font-size: 1.6rem;
        font-weight: 600;

        @media (max-width: 575.98px) {
          margin-top: 2rem;
        }
      }

      ul {
        display: flex;
        column-gap: 3.9rem;
        row-gap: 3rem;
        max-width: 42.2rem;
        width: 100%;
        flex-wrap: wrap;
        margin-top: 2rem;

        img {
          width: 2.4rem;
          height: 2.4rem;
          object-fit: contain;
          transition: scale 500ms cubic-bezier(0.19, 1, 0.22, 1);

          &:hover {
            scale: 1.1;
          }
        }
      }
    }
  }
}