import MyCashbacks from '@/components/my-accounts/my-cashbacks/MyCashbacks'
import { getCashbacks } from '@/lib/methods/user';
import { dehydrate, HydrationBoundary, QueryClient } from '@tanstack/react-query';
import React from 'react'

export default async function page() {

  const queryClient = new QueryClient();
  queryClient.prefetchQuery({
    queryKey: ["my-cashbacks"],
    queryFn: getCashbacks,
  })

  return (
    <>
      <HydrationBoundary state={dehydrate(queryClient)}>
        <MyCashbacks />
      </HydrationBoundary>
    </>
  )
}
