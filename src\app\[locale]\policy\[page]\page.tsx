import ErrorPage from "@/components/404/ErrorPage";
import GenericBackButton from "@/components/GenericBackButton/GenericBackButton";
import { cookies } from 'next/headers';
import { Metadata } from "next";

export async function generateMetadata({ params }: { params: any }): Promise<Metadata> {
  const { page } = params;
  const { locale } = params;

    const res = await fetch(process.env.NEXT_PUBLIC_API_URL + page, {
      method: "GET",
      // cache: "no-store",
      next: {
        tags: ["policy", "privacy"],
      },

      headers: {
        "Content-Type": "application/json",
        language: locale || "en",
      },
    });
    const data = await res.json();
    return {
      title: locale === "ar" ? data?.result[0]?.seoDetails?.title?.ar : data?.result[0]?.seoDetails?.title?.en,
      description: locale === "ar" ? data?.result[0]?.seoDetails?.description?.ar : data?.result[0]?.seoDetails?.description?.en,
      keywords: locale === "ar" ? data?.result[0]?.seoDetails?.keywords?.ar : data?.result[0]?.seoDetails?.keywords?.en,
      openGraph: {
        title: locale === "ar" ? data?.result[0]?.seoDetails?.title?.ar : data?.result[0]?.seoDetails?.title?.en,
        description: locale === "ar" ? data?.result[0]?.seoDetails?.description?.ar : data?.result[0]?.seoDetails?.description?.en,
        images: data?.result[0]?.seoDetails?.ogImage,
      },
      alternates: {
        canonical: locale === "ar" ? data?.result[0]?.seoDetails?.canonical?.ar : data?.result[0]?.seoDetails?.canonical?.en,
      },
    };
}

export default async function page({
  params,
}: {
  params: { page: string; locale: string };
}) {
  const baseURL = process.env.NEXT_PUBLIC_API_URL;
  const { locale } = params;

  try {
    const { page } = params;
    const locale = cookies().get("Next-Locale")?.value || "sa-en";
    const [storeId, language] = locale.split("-");

    const res = await fetch(baseURL + page, {
      method: "GET",
      // cache: "no-store",
      next: {
        tags: ["policy", "privacy"],
      },

      headers: {
        "Content-Type": "application/json",
        language: language || "en",
        storeid: storeId || "sa"
      },
    });
    const data = await res.json();
    if (data.errorCode === 0 && data?.result[0]?.content) {
      return (
        <>
          <div className="position-relative">
            <div className="policy-title">
              <GenericBackButton
                style={{ top: "5px", left: "0px", padding: "0" }}
              />
              <h2 className="text-capitalize mx-auto mt-4">
                {data?.result[0]?.title || params?.page?.split("-").join(" ")}
              </h2>
            </div>
          </div>
          <div
            className="policy-page mt-4"
            dangerouslySetInnerHTML={{ __html: data?.result[0]?.content }}
          ></div>
        </>
      );
    } else {
      return (
        <div>
          <ErrorPage errorcode="500" />
        </div>
      );
    }
  } catch (e) {
    <ErrorPage errorcode="500" />;
  }
}
