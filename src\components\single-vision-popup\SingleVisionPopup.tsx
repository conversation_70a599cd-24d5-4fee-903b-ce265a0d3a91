import Modal from "react-bootstrap/Modal";
import "./single-vision-popup.scss";
import Image from "next/image";

function SingleVisionPopup({ show, handleClose }: any) {
  return (
    <>
      <Modal
        show={show}
        onHide={handleClose}
        backdrop="static"
        keyboard={false}
        centered
        className="single-vision-popup">
        <Modal.Header closeButton></Modal.Header>
        <Modal.Body>
          <h2>
            <Image quality={100} priority src="/images/common/bakarrow.png" width={40} height={40} alt="back arrow" />
            Single Vision
          </h2>
          <label htmlFor="">
            <input type="radio" checked />
            Upload Prescription
          </label>

          <div className="file-upload">
            <label htmlFor="">Upload</label>
            <input type="file" />
          </div>

          <label htmlFor="">
            <input type="radio" />
            Manually Enter Specification
          </label>
          {/* <button onClick={handleShowSingleVisionDrop}>Next</button> */}
        </Modal.Body>
      </Modal>
    </>
  );
}

export default SingleVisionPopup;
