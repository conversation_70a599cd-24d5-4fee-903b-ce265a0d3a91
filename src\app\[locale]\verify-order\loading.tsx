import LogoAnimation from "@/components/LogoAnimation/LogoAnimation";
import React from "react";

export default function Loading() {
  return (
    <div
      className="overlay-loader position-fixed"
      style={{
        zIndex: 9999,
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "rgba(0, 0, 0, 0.5)",
        backdropFilter: "blur(5px)",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        flexDirection: "column",
      }}>
      <LogoAnimation />
      <div className="spinner-border text-light mt-4" role="status"></div>
      <small className="text-light">Verifing your purchase..</small>
    </div>
  );
}
