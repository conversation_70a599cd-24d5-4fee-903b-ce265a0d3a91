import { Image } from "react-bootstrap";
import "./virtualStore.scss";
type VirtualStoreBannerType = {
  title: string;
  description: string;
  buttonText: string;
  link: string;
  image: string;
  video: string | null;
  _id: string;
};

export default function VirtualStoreBanner({ id, data }: any) {
  return (
    <section className="virtualtry" id={id}>
      <div className="container-fluid">
        <div className="virtualtry_wrapper">
          <div className="virtualtry_left">
            <Image  src={data?.image??""} alt="Main Image" width={790} height={520} />
          </div>
          <div className="virtualtry_right" style={{ backgroundImage: `url("${data?.subImage}")` }}>
            <div className="virtualtry_content">
              <h4>{data?.title}</h4>
              <p>{data?.description}</p>
              <a href={data?.link} className="primary-btn">
                {data?.buttonText}
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
