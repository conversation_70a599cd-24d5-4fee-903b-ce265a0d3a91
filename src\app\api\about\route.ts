import connectDB from "@/lib/db/connectdb";
import { NextResponse } from "next/server"
import AboutModel from "./model";

export const GET = async (req: Request, res: NextResponse) => {
    try {
        await connectDB();
        let result = await AboutModel.find({ isDelete: false });
        return NextResponse.json(result, { status: 200 });
    } catch (error) {
        return NextResponse.json({ error: "Error in fetching data from db", message: error });
    }

}
