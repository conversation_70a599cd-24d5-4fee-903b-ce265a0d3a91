// import { createI18nServer } from 'next-international/server';
// // import en from './en';

// export const { getI18n, getScopedI18n, getCurrentLocale, getStaticParams } = createI18nServer(
//     {
//         ["sa-en"]: () => import('./en'),
//         ["sa-ar"]: () => import('./ar'),
//         ["ae-en"]: () => import('./en'),
//         ["ae-ar"]: () => import('./ar'),
//         ["qa-en"]: () => import('./en'),
//         ["qa-ar"]: () => import('./ar'),
//         ["om-en"]: () => import('./en'),
//         ["om-ar"]: () => import('./ar'),
//         ["bh-en"]: () => import('./en'),
//         ["bh-ar"]: () => import('./ar'),
//     },
//     {
//         // Uncomment to use custom segment name
//         // segmentName: 'locale',
//         // Uncomment to set fallback locale
//         // fallbackLocale: en,
//     },
// );