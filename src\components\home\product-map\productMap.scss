.team {
  position: relative;

  @media screen and (max-width: 991px) {
    &:has(.team_image.show) {
      .team_bg {
        img {
          -webkit-filter: opacity(0.5);
          filter: brightness(0.5);
        }
      }

   
    }
  }

  

  &_bg {
    aspect-ratio: 1349/669;

    img {
      transition: filter 0.3s ease;
      object-fit: cover;
      object-position: top;
      height: 100%;
    }
  }

  &_wrapper {
    display: flex;
  }

  &_box {
    transform: translate(-33%, -50%);

    &:nth-of-type(1) {
      position: absolute;
      top: 26.5%;
      left: 15.5%;
    }

    &:nth-of-type(2) {
      position: absolute;
      left: 48.6%;
      top: 23%;
    }

    &:nth-of-type(3) {
      position: absolute;
      left: 85%;
      top: 22%;
    }

    .hover_btn {
      cursor: pointer;
      border: none;
      background: none;
      transition: opacity 0.3s ease;

      svg {
        width: 15px;
        height: 15px;
        object-fit: contain;
        border-radius: 50%;
      }
    }

    .hover_btn {

      @keyframes pulse {
        100% {
          opacity: 0;
          transform: scale(1.4);
        }
      }

      .button-wrapper {
        align-items: center;
        display: flex;
        justify-content: center;
        width: 100%;
      }

      .floating-btn {
        animation: bounce 2s linear infinite;
        align-items: center;
        background-color: #ffffff;
        border-radius: 50%;
        box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.25);
        color: #fff;
        cursor: pointer;
        display: flex;
        height: 35px;
        justify-content: center;
        position: relative;
        transition: 0.3s;
        width: 35px;

        .pulse-item {
          background-color: #ffffff;
          border-radius: 100%;
          height: 100%;
          opacity: 0.7;
          position: absolute;
          width: 100%;
        }

        .icons {
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;
          transition: 0.8s;
        }

        &:hover {
          animation-play-state: paused;
          box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.25);

          .pulse-item {
            animation: pulse 1s ease-out infinite;

            &:nth-of-type(2) {
              animation-delay: -0.5s
            }
          }

        }
      }
    }
  }

  &_image {
    width: auto;
    margin-top: 2.2rem;
    border-radius: 2rem;
    position: relative;
    text-align: center;

    @media screen and (max-width: 991px) {
      translate: 18% -37%;
      z-index: 10;
    }

    @media screen and (max-width: 575.98px) {
      width: 190px !important;
      height: 219px;

      img {
        width: 190px;
        height: 219px;
      }
    }

    @media screen and (max-width: 480px) {
      width: 120px !important;
      height: 140px;

      img {
        width: 120px;
        height: 140px;
      }
    }

    img {
      -webkit-filter: grayscale(100%);
      filter: grayscale(100%);
      border-radius: 2rem;
    }

    &::after {
      content: "";
      position: absolute;
      border-radius: 2rem;
      background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.5) 100%);
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
    }
  }

  &_title {
    position: absolute;
    bottom: 36px;
    z-index: 2;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;

    h5 {
      font-size: 2rem;
      color: #fff;
      font-weight: 500;

      @media (max-width: 575.98px) {
        font-size: 1.3rem;
        font-weight: 500;
      }
    }

    a {
      font-size: 1.4rem;
      font-weight: 500;
      letter-spacing: 0.07rem;
      text-decoration-line: underline;
      margin-top: 0.82rem;
      color: #fff;

      @media (max-width: 575.98px) {
        font-size: 1.2rem;
      }
    }
  }
}

.team_image {
  display: none;
  position: absolute;
  width: 250px;
  left: 50%;
  transform: translateX(-50%) !important;

  @media (max-width: 991.98px) {
    left: unset;
    transform: none !important;
  }


  &.right-0 {
    right: 0;

  }
}

.team_image.show {
  display: block;
}