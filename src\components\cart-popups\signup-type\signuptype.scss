.singup-popup {
  &.modal {
    .modal-dialog {
      max-width: 68.3rem;
      width: 100%;
      margin: 0;
      margin-left: auto;
      height: 100%;
    }

    .modal-header {
      padding: 0;
      border: none;

      .btn-close {
        background-image: url(../../../../public/images/common/close.png);
        width: 4.8rem;
        height: 4.8rem;
        background-position: center;
        background-repeat: no-repeat;
        background-size: contain;
        opacity: 1;
        padding: 0;

        @media (max-width: 575.98px) {
          width: 2.8rem;
          height: 2.8rem;
        }

        &:focus {
          box-shadow: none;
          outline: none;
          border: none;
        }
      }
    }

    .modal-content {
      padding: 4rem 4.5rem 5rem 16.7rem;
      border: none;
      background: #fff;
      border-radius: 0;
      height: 100vh;
      overflow-y: auto;

      @media (max-width: 575.98px) {
        padding: 5.4rem 2rem 4.2rem 2rem;
      }
    }

    .modal-body {
      padding: 0;
      display: flex;
      justify-content: center;
      flex-direction: column;

      h2 {
        line-height: 3.6rem;
        color: #242731;
        font-weight: 700;
        max-width: 40.4rem;
        width: 100%;
        text-align: left;
      }

      p {
        margin-top: 1.6rem;
        color: #575f6e;
        font-size: 1.6rem;
        font-weight: 300;
        line-height: 2.2rem;
        max-width: 35rem;
        width: 100%;
      }

      .ingup-popup-fill {
        margin-top: 2.6rem;

        .ingup-popup-inputs {
          max-width: 35rem;
          position: relative;

          &:not(:last-child) {
            margin-bottom: 2.8rem;

            @media (max-width: 575.98px) {
              margin-bottom: 3.5rem;
            }
          }

          label {
            color: #242426;
            font-size: 1.4rem;
            font-weight: 400;
            line-height: 2rem;
            padding-bottom: 0.8rem;
          }

          &.radio {
            margin-bottom: 1.9rem;

            @media (max-width: 575.98px) {
              margin-top: 2.8rem;
            }

            label {
              display: flex;
              align-items: center;
              color: #000;
              font-size: 1.6rem;
              font-weight: 500;
              column-gap: 1.4rem;
              margin-top: 3rem;

              input {
                appearance: none;
                width: 2rem;
                height: 2rem;
                border-radius: 50%;
                border: 1px solid #000;
                position: relative;

                &:checked {
                  &::after {
                    content: "";
                    width: 1.2rem;
                    height: 1.2rem;
                    background-color: #000;
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    position: absolute;
                    border-radius: 50%;
                  }
                }
              }
            }
          }
        }

        .button {
          width: 15.9rem;
          height: 4.5rem;
          font-size: 1.5rem;
          font-weight: 500;
          border-radius: 6rem;
          border: 0;
          margin-top: 2.4rem;
        }
        .save {
          background-color: #000;
          color: #fff;
        }
      }
    }
  }
}
