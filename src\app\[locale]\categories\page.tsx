import BreadCrumbs from "@/components/breadcrumbs/BreadCrumbs";
import OurCategory from "@/components/home/<USER>/OurCategory";
import { endpoints } from "@/config/apiEndpoints";
import { getCategories } from "@/lib/methods/categories";
import { Metadata } from "next";

export async function generateMetadata({ params }: { params: any }): Promise<Metadata> {
  const categories = await getCategories("all");
  const categoryNames = categories?.categories?.map((category: any) => category.name);
  return {
    title: "Our Categories  | Yateem Optician",
    description: categoryNames.join(", ") || "Yateem Optician",
    keywords: categoryNames,
    openGraph: {
      title: categoryNames.join(", ") || "Yateem Optician",
      type: "website",
    },
  };
}

function Page() {
  return (
    <main className="mobile-category">
      <BreadCrumbs backHome="Home" currentPage="Category" image="/images/common/banner.png" />
      <OurCategory type="home" />
    </main>
  );
}

export default Page;
