"use client";
import { useState } from "react";
import "./faq.scss";
import Accordion from "react-bootstrap/Accordion";
import { Button } from "react-bootstrap";

function Faq({ data, title, description }: any) {
  const [show, setShow] = useState(false);
  const handleClose = () => setShow(false);
  const handleShow = () => setShow(true);


  return (
    <section className="faq">
      <div className="container">
        <div className="faq_head">
          <h2>{title}</h2>
          <p>{description}</p>
        </div>
        <Accordion defaultActiveKey="0" flush={true}>
          {data?.map((items: any, index: number) => (
            <Accordion.Item eventKey={index.toString()} key={index}>
              <Accordion.Header>
                <h3>{items?.question}</h3>
              </Accordion.Header>
              <Accordion.Body>
                <p>{items?.answer}</p>
              </Accordion.Body>
            </Accordion.Item>
          ))}
        </Accordion>
      </div>
    </section>
  );
}

export default Faq;
