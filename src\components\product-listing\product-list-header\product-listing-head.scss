body:has(.listing-head) {
  background: #f2f4f9;
}

.listing-head {

  @media (min-width: 991.98px) {
    .cat-chips {
      display: none !important;
    }
  }

  &_flex {
    display: flex;
    justify-content: space-between;
    border-radius: 1.5rem;
    background: #fff;
    align-items: center;
    padding: 2.2rem 2.9rem 2.4rem 1.6rem;

    @media (max-width: 767.98px) {
      background: none;
      padding: 0;
    }


    h6 {
      color: #000;
      font-size: 1.4rem;
      font-weight: 400;
      letter-spacing: 0.028rem;

      span {
        font-weight: 600;
      }
    }

    ul {
      display: flex;
      align-items: center;
      gap: 1.5rem;
      // @media (max-width: 767.98px) {
      //   display: none;
      // }

      li {

        &:last-child,
        &.listing-head_three-grids {
          @media (max-width: 767.98px) {
            display: none;
          }
        }

        &.listing-head_one-grid {
          @media (min-width: 767.98px) {
            display: none;
          }

          // margin: 0 .8rem;
        }


        display: flex;
        align-items: center;
        cursor: pointer;
        gap: .4rem;
        img {
          object-fit: contain;
          width: 2rem;
          height: 2rem;

          // cursor: pointer;
          &:hover {
            filter: brightness(0.5) contrast(200);
          }
        }

        .bars{
          display: flex;
          gap: .4rem;
          padding: 0 1rem;
          .bar {
            width: .2rem;
            height: 2rem;
            background: black;
            border-radius: 1rem;
          }
        }

        &.active {
          background-color: #000;
          padding: 4px;
          border-radius: 4px;

          .bars{
            .bar {
              background: white;
            }
          }
         

          img {
            filter: invert(1);
          }
        }
      }
    }
  }

  &_toggler {
    margin-right: 2.9rem;

    form {
      display: flex;
      align-items: center;
      color: #000;
      font-size: 1.4rem;
      font-weight: 600;
      letter-spacing: 0.028rem;
      column-gap: 0.53rem;

      .form-switch {
        padding: 0;

        .form-check-input {
          cursor: pointer;
          width: 3.9322rem;
          height: 2.3435rem;
          border-radius: 4rem;
          margin: 0;
          background-color: #e3e3e3;
          border: none;

          &:checked {
            background-color: #e3e3e3;
            background-image: url(../../../../public/images/common/round.png);
            background-size: auto;
            background-repeat: no-repeat;
          }

          &:focus {
            box-shadow: none;
            border: none;
          }
        }
      }
    }
  }

  &_three-grids {
    // margin: 0 1.7rem;
  }

  &_sort {
    display: flex;
    align-items: center;
    column-gap: 0.8rem;
    color: #6b7280;
    font-size: 1.4rem;
    font-weight: 400;
    letter-spacing: 0.028rem;
    margin-left: 2.2rem;

    .dropdown-menu {
      border-radius: 0.7rem;
      background: #fff;
      box-shadow: 1px 1px 7px 0px rgba(0, 0, 0, 0.07);
      padding: 1rem 0.8rem 1.3rem 0.8rem;
    }

    .dropdown-item {
      color: #7f7f7f;
      font-size: 1.5rem;
      font-weight: 400;
      padding: 0 0.6rem;
      line-height: 1.8rem;

      &:not(:last-child) {
        border-bottom: 1px solid #e4e4e4;
        padding-bottom: 1rem;
        margin-bottom: 1rem;
      }

      &:active {
        background: none !important;
      }

      &:hover {
        background: none !important;
        color: black
      }
    }

    .dropdown-toggle {
      background-color: transparent !important;
      color: #1f2937 !important;
      border: none;
      font-size: 1.4rem;
      font-weight: 600;
      letter-spacing: 0.028rem;
      padding: 0;

      &::after {
        background-image: url(../../../../public/images/common/droparrow.png);
        border: none;
        width: 1.1rem;
        height: 0.6rem;
        background-size: cover;
        vertical-align: 1px;
      }
    }
  }
}

.app.rtl {
  .listing-head {
    .listing-head_sort {
      margin-left: 0;
      margin-right: 2.2rem;
    }
  }

  .listing-head_sort .dropdown-toggle::after {
    margin-left: 0;
    margin-right: 0.255em;
  }

  .listing-head_flex {
    padding: 2.2rem 1.6rem 2.4rem 2.6rem;
  }
}