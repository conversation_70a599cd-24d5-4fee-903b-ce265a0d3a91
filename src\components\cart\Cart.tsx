"use client";

import { getCart, getLoyalityPoints } from "@/lib/methods/cart";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import Image from "next/image";
import CartCountBtn from "../cart-count/cartCountBtn";
import EmptyCart from "../empty-cart/EmptyCart";
import Link from "next/link";
import Coupon from "./Coupon";
import { HistoryContext } from "@/contexts/HistoryProvider";
import { useContext, useEffect, useState } from "react";
import { AuthContext } from "@/contexts/AuthProvider";
import { useRouter } from "next/navigation";
import { endpoints } from "@/config/apiEndpoints";
import api from "@/config/axios.interceptor";
import { toast } from "sonner";
import dynamic from "next/dynamic";
import OverLayLoader from "../LogoAnimation/OverLayLoader";
import GenericBackButton from "../GenericBackButton/GenericBackButton";
import Loyality from "./Loyality";
import { ImageViewer } from "./ImageViewer";
import { getUser } from "@/lib/methods/auth";
import { sendGTMEvent } from "@next/third-parties/google";
import { calculateSavings } from "@/app/[locale]/product/[slug]/ProductVariantSection";
import Toaster from "../toast/Toaster";
import { TranslationContext } from "@/contexts/Translation";
import { createHmac } from "crypto";
import { SettingsContext } from "@/contexts/SettingsProvider";
import { useLocaleContext } from "@/contexts/LocaleProvider";
import Script from "next/script";

const TAMARA_PUBLIC_KEY = process.env.NEXT_PUBLIC_TAMARA_PUBLIC_KEY ?? "";
const TABBY_PUBLIC_KEY = process.env.NEXT_PUBLIC_TABBY_PUBLIC_KEY ?? "";

const CouponModal = dynamic(() => import("../coupon-modal/Coupon"), {
  loading: () => <OverLayLoader />,
});

function isObject(value: any) {
  return value !== null && typeof value === 'object' && !Array.isArray(value) && Object.keys(value).length > 0;
}

export default function Cart() {
  const { isLoggedIn } = useContext(AuthContext);
  const { userProfile } = useContext(AuthContext);
  const { settings } = useContext(SettingsContext);
  const { translation: { cartPage, productPage } } = useContext(TranslationContext)
  const [imageSrc, setImageSrc] = useState<any>({});
  const router = useRouter();
  const [modalCoupon, setModalCoupon] = useState<null | string>("");
  const [modalLoyality, setModalLoyality] = useState<null | string>("");
  const { currentLocale, currencyCode } = useLocaleContext()
  const { data: cart, isLoading } = useQuery({ queryKey: ["cart", currentLocale?.split("-")[0]], queryFn: getCart });
  const [tamaraShow, setTamaraShow] = useState(false)

  const { currentLocale: locale } = useLocaleContext()
  const queryClient = useQueryClient();
  const { setPrevPage } = useContext(HistoryContext);
  useEffect(() => {
    setPrevPage({ title: "My Cart", url: "/cart" });
  }, []);

  useEffect(() => {
    if (!isLoading) {

      sendGTMEvent({ ecommerce: null })
      let eventData: any = {
        event: "view_cart",
        ecommerce: {
          currency: currencyCode,
          value: cart?.total,
          coupon: cart?.couponCode,
          items: cart?.products?.map((item: any, i: number) => {
            return {
              item_id: item?.sku,
              item_name: item?.name,
              index: i,
              item_brand: item?.brand,
              item_category: item?.category?.[0]?.name,
              item_category2: item?.category?.[1]?.name,
              item_variant: item?.color,
              price: item?.price,
              quantity: item.quantity,
            }
          })
        }
      }
      if (userProfile) {

        const mobile = createHmac('sha256', '123').update(userProfile?.mobile).digest('hex')
        let email = null;
        if (userProfile?.email) email = createHmac('sha256', '123').update(userProfile?.email).digest('hex')
        eventData.user = {
          mobile,
          email,
          user_id: userProfile?._id
        }
      }
      sendGTMEvent(eventData)
    }
  }, [isLoading])


  const handleCheckout = async () => {
    sendGTMEvent({ ecommerce: null })
    let eventData: any = {
      event: "begin_checkout",
      ecommerce: {
        currency: currencyCode,
        value: cart?.total,
        coupon: cart?.couponCode ?? undefined,
        items: cart?.products?.map((item: any, i: number) => {
          return {
            item_id: item?.sku,
            item_name: item?.name,
            index: i,
            item_brand: item?.brand,
            item_category: item?.category?.[0]?.name,
            item_category2: item?.category?.[1]?.name,
            item_variant: item?.color,
            price: item?.price,
            quantity: item.quantity * (item?.contactLensDetails?.multiple ? 2 : 1),
          }
        })
      }
    }
    if (userProfile) {
      const mobile = createHmac('sha256', '123').update(userProfile?.mobile).digest('hex')
      let email = null;
      if (userProfile?.email) email = createHmac('sha256', '123').update(userProfile?.email).digest('hex')
      eventData.user = {
        mobile,
        email,
        user_id: userProfile?._id
      }
    }
    sendGTMEvent(eventData)
    const res = await getUser()
    if (res && !res.isGuest) {
      router.push(`/checkout/cart`);
    } else {
      router.push("/login?type=checkout");
    }
  };

  const removeFromCart = (
    product: any,
    size?: string | undefined,
    lensId?: string | undefined
  ) => {
    api.post(endpoints.removeFromCart, {
      product: product?.productid,
      size: size || undefined,
      contactLensDetails: product?.contactLensDetails,
      lensId,
    }).then((res) => {
      if (res.data?.errorCode === 0) {
        sendGTMEvent({ ecommerce: null })
        let eventData: any = {
          event: "remove_from_cart",
          ecommerce: {
            currency: currencyCode,
            value: product?.price * product.quantity,
            items: [{
              item_id: product?.sku,
              item_name: product?.name,
              index: 0,
              item_brand: product?.brand,
              item_category: product?.category?.[0]?.name,
              item_category2: product?.category?.[1]?.name,
              item_variant: product?.color,
              price: product?.price,
              quantity: product?.quantity,
            }]
          }
        }
        if (userProfile) {

          const mobile = createHmac('sha256', '123').update(userProfile?.mobile).digest('hex')
          let email = null;
          if (userProfile?.email) email = createHmac('sha256', '123').update(userProfile?.email).digest('hex')
          eventData.user = {
            mobile,
            email,
            user_id: userProfile?._id
          }
        }
        sendGTMEvent(eventData)
        toast.success(res.data?.message);
        queryClient.invalidateQueries({ queryKey: ["cart", "counts"] });
        queryClient.invalidateQueries({ queryKey: ["cart"] });
      } else {
        toast.error(res.data?.message);
      }
    }).catch((err) => {
      toast.error(err.response.data.message);
    });
  };

  const [show, setShow] = useState(false);
  const handleClose = () => setShow(false);
  const handleShow = () => setShow(true);
  const TABBY_PUBLIC_KEY = process.env.NEXT_PUBLIC_TABBY_PUBLIC_KEY ?? "";

  const setTabby = () => {
    {/* @ts-ignore */ }
    window.TabbyPromo && new TabbyPromo({
      selector: '#tabby', // required, content of tabby Promo Snippet will be placed in element with that selector.
      currency: "AED", // required, AED|SAR|KWD only supported, with no spaces or lowercase.
      price: cart?.total, // required, price of the product. 2 decimals max for AED|SAR and 3 decimals max for KWD.
      lang: locale?.split("-")[1], // Optional, en|ar only supported
      source: 'cart', // Optional, snippet placement; `product` for product page and `cart` for cart page.
      publicKey: TABBY_PUBLIC_KEY, // required, Public Key
      merchantCode: 'YOUAE',  // required,
    });
  }


  useEffect(() => {
    setTabby()
  }, [cart, window.TabbyPromo])

  useEffect(() => {
    setTamaraShow(false)
    window.tamaraWidgetConfig = {
      lang: locale?.split("-")[1],
      country: "AE",
      publicKey: TAMARA_PUBLIC_KEY
    }
    setTamaraShow(true)
  }, [locale])


  return (
    <>
      <div className="cart">
        <div className="container">
          {(!cart?.products || cart?.products?.length === 0) && (
            <div className="empty_cart">
              <EmptyCart />
            </div>
          )}
          {cart?.products?.length > 0 && (
            <div
              className={`cart_wrapper
              }`}
            >
              <div className="table position-relatives p-0">
                <GenericBackButton style={{ top: "20px", transform: locale.includes("en") ? "" : "rotate(180deg)" }} />{" "}
                <h2>{cartPage?.cartItems ?? "Cart Items"} ({cart?.count?.toString().padStart(2, "0")})</h2>
                {cart?.products?.slice().reverse().map((product: any) => {
                  const { cashback } = product
                  let isCashback = false;
                  let cashbackPercentage = 0
                  if (cashback?.brand?.isEnabled) {
                    isCashback = true;
                    cashbackPercentage = cashback?.brand?.percentage;
                  } else if (cashback?.category?.isEnabled) {
                    isCashback = true;
                    cashbackPercentage = cashback?.category?.percentage;
                  } else if (cashback?.product?.isEnabled) {
                    isCashback = true;
                    cashbackPercentage = cashback?.product?.percentage;
                  }
                  return (
                    <div
                      className="table_item"
                      key={
                        product?.productid +
                        product?.slug +
                        product?.quantity +
                        product?.size +
                        product?.lensId
                      }
                    >
                      <div className="table_info">
                        <div className="p-0">
                          <Link href={`product/${product?.slug}`}>
                            {/* <Image
                          quality={100}
                          priority
                          src={
                            product?.thumbnail
                          }
                          width={120}
                          height={120}
                          alt={product?.name}
                          onError={() =>
                            setImageSrc({
                              [product?.productid]:
                                "/images/product/noImage.jpg",
                            })
                          }
                        /> */}
                            <ImageViewer product={product} />
                          </Link>

                          <div className="table_count d-sm-flex d-none">
                            <CartCountBtn
                              product={product}
                              initial={product?.quantity}
                              lensId={product?.lensId}
                            />
                          </div>
                        </div>
                        <div className="table_description">
                          <ul>
                            <Link href={`product/${product?.slug}`}>
                              <li className="title">{product?.name}</li>
                            </Link>
                            <li></li>
                            {product?.color && <li>{productPage?.color ?? "Color"} : {product.color}</li>}
                            {product?.size && <li>{productPage?.size ?? "Size"} : {product.size?.name}</li>}
                          </ul>
                          {isCashback && <ul style={{ margin: "1rem 0" }}>
                            <li>
                              <div style={{ padding: "1rem", borderRadius: ".5rem", background: "black", color: "white", textAlign: "center" }}>
                                {cashbackPercentage}% Cashback
                              </div>
                            </li>
                          </ul>}
                          {product?.lensDetails && <ul>
                            {product?.lensDetails?.prescription?.leftAxis && (
                              <li>
                                {cartPage?.axisLeft ?? "Axis Left"} :{" "}
                                {product?.lensDetails?.prescription?.leftAxis}
                              </li>
                            )}
                            {product?.lensDetails?.prescription?.rightAxis && (
                              <li>
                                {cartPage?.axisRight ?? "Axis Right"} :{" "}
                                {product?.lensDetails?.prescription?.rightAxis}
                              </li>
                            )}
                            {product?.lensDetails?.prescription?.leftCyl && (
                              <li>
                                {cartPage?.cylinderLeft ?? "Cylinder Left"} :{" "}
                                {product?.lensDetails?.prescription?.leftCyl}
                              </li>
                            )}
                            {product?.lensDetails?.prescription?.rightCyl && (
                              <li>
                                {cartPage?.cylinderRight ?? "Cylinder Right"} :{" "}
                                {product?.lensDetails?.prescription?.rightCyl}
                              </li>
                            )}
                            {product?.lensDetails?.prescription?.leftSph && (
                              <li>
                                {cartPage?.sphereLeft ?? "Sphere Left"} :{" "}
                                {product?.lensDetails?.prescription?.leftSph}
                              </li>
                            )}
                            {product?.lensDetails?.prescription?.rightSph && (
                              <li>
                                {cartPage?.sphereRight ?? "Sphere Right"} :{" "}
                                {product?.lensDetails?.prescription?.rightSph}
                              </li>
                            )}
                            {product?.lensDetails?.photocromic && (
                              <li>
                                Photochromic Coating :{" "}
                                {product?.lensDetails?.photocromic}
                              </li>
                            )}
                            {product?.lensDetails?.vision && (
                              <li>
                                Prescription Type : {product?.lensDetails?.vision}
                              </li>
                            )}
                            {product?.lensDetails?.lensType && (
                              <li>Lens Type : {product?.lensDetails?.lensType}</li>
                            )}
                            {product?.lensDetails?.index && (
                              <li>Lens Index : {product?.lensDetails?.index}</li>
                            )}
                            {product?.lensDetails?.index && (
                              <li>Lens Coating : {product?.lensDetails?.coating}</li>
                            )}
                            {product?.lensDetails?.brand && (
                              <li>Lens Brand : {product?.lensDetails?.brand}</li>
                            )}
                          </ul>}

                          {product?.contactLensDetails && <ul>
                            {product?.contactLensDetails.axisLeft && (
                              <li>
                                {cartPage?.axisLeft ?? "Axis Left"} :{" "}
                                {product?.contactLensDetails.axisLeft.name}
                              </li>
                            )}
                            {product?.contactLensDetails.axisRight && (
                              <li>
                                {cartPage?.axisRight ?? "Axis Right"} :{" "}
                                {product?.contactLensDetails.axisRight.name}
                              </li>
                            )}
                            {product?.contactLensDetails.cylLeft && (
                              <li>
                                {cartPage?.cylinderLeft ?? "Cylinder Left"} :{" "}
                                {product?.contactLensDetails.cylLeft.name}
                              </li>
                            )}
                            {product?.contactLensDetails.cylRight && (
                              <li>
                                {cartPage?.cylinderRight ?? "Cylinder Right"} :{" "}
                                {product?.contactLensDetails.cylRight.name}
                              </li>
                            )}
                            {product?.contactLensDetails.sphLeft && (
                              <li>
                                {cartPage?.sphereLeft ?? "Sphere Left"} :{" "}
                                {product?.contactLensDetails.sphLeft.name}
                              </li>
                            )}
                            {product?.contactLensDetails.sphRight && (
                              <li>
                                {cartPage?.sphereRight ?? "Sphere Right"} :{" "}
                                {product?.contactLensDetails.sphRight.name}
                              </li>
                            )}
                            {product?.contactLensDetails.multiFocal && (
                              <li>
                                {productPage?.addition ?? "Multi Focal"} :{" "}
                                {product?.contactLensDetails.multiFocal}
                              </li>
                            )}
                          </ul>}

                          <ul>
                            <li className="table_info-mobile">
                              <CartCountBtn
                                key={product?.productid}
                                product={product}
                                initial={product?.quantity}
                                lensId={product?.lensId}
                              />{" "}
                              <span style={{ fontSize: "1.4rem" }}>
                                {currencyCode + " " + (product?.priceTotal / product?.quantity)}
                              </span>
                              {(product?.couponAmount && product?.couponAmount > 0) ? <span style={{ fontSize: "1.4rem" }}>
                                -{currencyCode + " " + product?.couponAmount}
                              </span> : ""}
                            </li>
                          </ul>
                        </div>

                        <button
                          className="btn"
                          onClick={() =>
                            removeFromCart(
                              product,
                              product?.size?._id,
                              product?.lensId
                            )
                          }
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="16"
                            height="20"
                            viewBox="0 0 16 20"
                            fill="none"
                          >
                            <path
                              d="M6.9 8C6.9 7.50294 6.49706 7.1 6 7.1C5.50294 7.1 5.1 7.50294 5.1 8H6.9ZM5.1 16C5.1 16.4971 5.50294 16.9 6 16.9C6.49706 16.9 6.9 16.4971 6.9 16H5.1ZM10.9 8C10.9 7.50294 10.4971 7.1 10 7.1C9.50294 7.1 9.1 7.50294 9.1 8H10.9ZM9.1 16C9.1 16.4971 9.50294 16.9 10 16.9C10.4971 16.9 10.9 16.4971 10.9 16H9.1ZM3.09202 18.782L3.50061 17.9801H3.50061L3.09202 18.782ZM2.21799 17.908L3.01989 17.4994L3.01989 17.4994L2.21799 17.908ZM13.782 17.908L12.9801 17.4994V17.4994L13.782 17.908ZM12.908 18.782L12.4994 17.9801L12.4994 17.9801L12.908 18.782ZM1 4.1C0.502944 4.1 0.1 4.50294 0.1 5C0.1 5.49706 0.502944 5.9 1 5.9V4.1ZM15 5.9C15.4971 5.9 15.9 5.49706 15.9 5C15.9 4.50294 15.4971 4.1 15 4.1V5.9ZM3.6 5C3.6 5.49706 4.00294 5.9 4.5 5.9C4.99706 5.9 5.4 5.49706 5.4 5H3.6ZM10.6 5C10.6 5.49706 11.0029 5.9 11.5 5.9C11.9971 5.9 12.4 5.49706 12.4 5H10.6ZM5.1 8V16H6.9V8H5.1ZM9.1 8V16H10.9V8H9.1ZM13.1 5V15.8H14.9V5H13.1ZM10.8 18.1H5.2V19.9H10.8V18.1ZM1.1 5V15.8H2.9V5H1.1ZM5.2 18.1C4.6251 18.1 4.24805 18.0993 3.95969 18.0757C3.68185 18.053 3.5665 18.0137 3.50061 17.9801L2.68343 19.5839C3.04536 19.7683 3.42395 19.838 3.81312 19.8698C4.19177 19.9007 4.6548 19.9 5.2 19.9V18.1ZM1.1 15.8C1.1 16.3452 1.0993 16.8082 1.13024 17.1869C1.16203 17.576 1.23167 17.9546 1.41608 18.3166L3.01989 17.4994C2.98632 17.4335 2.94696 17.3182 2.92426 17.0403C2.9007 16.752 2.9 16.3749 2.9 15.8H1.1ZM3.50061 17.9801C3.29363 17.8746 3.12535 17.7064 3.01989 17.4994L1.41608 18.3166C1.69411 18.8622 2.13776 19.3059 2.68343 19.5839L3.50061 17.9801ZM13.1 15.8C13.1 16.3749 13.0993 16.752 13.0757 17.0403C13.053 17.3182 13.0137 17.4335 12.9801 17.4994L14.5839 18.3166C14.7683 17.9546 14.838 17.576 14.8698 17.1869C14.9007 16.8082 14.9 16.3452 14.9 15.8H13.1ZM10.8 19.9C11.3452 19.9 11.8082 19.9007 12.1869 19.8698C12.576 19.838 12.9546 19.7683 13.3166 19.5839L12.4994 17.9801C12.4335 18.0137 12.3182 18.053 12.0403 18.0757C11.752 18.0993 11.3749 18.1 10.8 18.1V19.9ZM12.9801 17.4994C12.8746 17.7064 12.7064 17.8746 12.4994 17.9801L13.3166 19.5839C13.8622 19.3059 14.3059 18.8622 14.5839 18.3166L12.9801 17.4994ZM1 5.9H2V4.1H1V5.9ZM2 5.9H14V4.1H2V5.9ZM14 5.9H15V4.1H14V5.9ZM5.4 4.2C5.4 3.00396 6.48645 1.9 8 1.9V0.1C5.64756 0.1 3.6 1.86142 3.6 4.2H5.4ZM8 1.9C9.51355 1.9 10.6 3.00396 10.6 4.2H12.4C12.4 1.86142 10.3524 0.1 8 0.1V1.9ZM3.6 4.2V5H5.4V4.2H3.6ZM10.6 4.2V5H12.4V4.2H10.6Z"
                              fill="#FF9C9C"
                            />
                          </svg>
                        </button>
                      </div>

                      <div style={{ flexDirection: "column" }} className="table_price-delete d-sm-flex d-none">
                        {/* <span>{product?.currency + " " + ((Number(product?.priceTotal) / Number(product?.contactLensDetails?.multiple? 2: 1)) / product?.quantity)}</span> */}
                        <div style={{ display: "flex", gap: "2rem", justifyContent: "center" }}>
                          <span>{currencyCode + " " + product?.price}</span>
                          <button
                            onClick={() =>
                              removeFromCart(
                                product,
                                product?.size?._id,
                                product?.lensId
                              )
                            }
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="16"
                              height="20"
                              viewBox="0 0 16 20"
                              fill="none"
                            >
                              <path
                                d="M6.9 8C6.9 7.50294 6.49706 7.1 6 7.1C5.50294 7.1 5.1 7.50294 5.1 8H6.9ZM5.1 16C5.1 16.4971 5.50294 16.9 6 16.9C6.49706 16.9 6.9 16.4971 6.9 16H5.1ZM10.9 8C10.9 7.50294 10.4971 7.1 10 7.1C9.50294 7.1 9.1 7.50294 9.1 8H10.9ZM9.1 16C9.1 16.4971 9.50294 16.9 10 16.9C10.4971 16.9 10.9 16.4971 10.9 16H9.1ZM3.09202 18.782L3.50061 17.9801H3.50061L3.09202 18.782ZM2.21799 17.908L3.01989 17.4994L3.01989 17.4994L2.21799 17.908ZM13.782 17.908L12.9801 17.4994V17.4994L13.782 17.908ZM12.908 18.782L12.4994 17.9801L12.4994 17.9801L12.908 18.782ZM1 4.1C0.502944 4.1 0.1 4.50294 0.1 5C0.1 5.49706 0.502944 5.9 1 5.9V4.1ZM15 5.9C15.4971 5.9 15.9 5.49706 15.9 5C15.9 4.50294 15.4971 4.1 15 4.1V5.9ZM3.6 5C3.6 5.49706 4.00294 5.9 4.5 5.9C4.99706 5.9 5.4 5.49706 5.4 5H3.6ZM10.6 5C10.6 5.49706 11.0029 5.9 11.5 5.9C11.9971 5.9 12.4 5.49706 12.4 5H10.6ZM5.1 8V16H6.9V8H5.1ZM9.1 8V16H10.9V8H9.1ZM13.1 5V15.8H14.9V5H13.1ZM10.8 18.1H5.2V19.9H10.8V18.1ZM1.1 5V15.8H2.9V5H1.1ZM5.2 18.1C4.6251 18.1 4.24805 18.0993 3.95969 18.0757C3.68185 18.053 3.5665 18.0137 3.50061 17.9801L2.68343 19.5839C3.04536 19.7683 3.42395 19.838 3.81312 19.8698C4.19177 19.9007 4.6548 19.9 5.2 19.9V18.1ZM1.1 15.8C1.1 16.3452 1.0993 16.8082 1.13024 17.1869C1.16203 17.576 1.23167 17.9546 1.41608 18.3166L3.01989 17.4994C2.98632 17.4335 2.94696 17.3182 2.92426 17.0403C2.9007 16.752 2.9 16.3749 2.9 15.8H1.1ZM3.50061 17.9801C3.29363 17.8746 3.12535 17.7064 3.01989 17.4994L1.41608 18.3166C1.69411 18.8622 2.13776 19.3059 2.68343 19.5839L3.50061 17.9801ZM13.1 15.8C13.1 16.3749 13.0993 16.752 13.0757 17.0403C13.053 17.3182 13.0137 17.4335 12.9801 17.4994L14.5839 18.3166C14.7683 17.9546 14.838 17.576 14.8698 17.1869C14.9007 16.8082 14.9 16.3452 14.9 15.8H13.1ZM10.8 19.9C11.3452 19.9 11.8082 19.9007 12.1869 19.8698C12.576 19.838 12.9546 19.7683 13.3166 19.5839L12.4994 17.9801C12.4335 18.0137 12.3182 18.053 12.0403 18.0757C11.752 18.0993 11.3749 18.1 10.8 18.1V19.9ZM12.9801 17.4994C12.8746 17.7064 12.7064 17.8746 12.4994 17.9801L13.3166 19.5839C13.8622 19.3059 14.3059 18.8622 14.5839 18.3166L12.9801 17.4994ZM1 5.9H2V4.1H1V5.9ZM2 5.9H14V4.1H2V5.9ZM14 5.9H15V4.1H14V5.9ZM5.4 4.2C5.4 3.00396 6.48645 1.9 8 1.9V0.1C5.64756 0.1 3.6 1.86142 3.6 4.2H5.4ZM8 1.9C9.51355 1.9 10.6 3.00396 10.6 4.2H12.4C12.4 1.86142 10.3524 0.1 8 0.1V1.9ZM3.6 4.2V5H5.4V4.2H3.6ZM10.6 4.2V5H12.4V4.2H10.6Z"
                                fill="#FF9C9C"
                              />
                            </svg>
                          </button>
                        </div>

                        <div style={{ marginTop: "auto", display: "flex", flexDirection: "column", gap: "0.5rem" }} className="">
                          <span style={{ fontSize: "1.6rem" }}>{cartPage?.subtotal ?? "Subtotal"}:{" "} {currencyCode + " " + product?.price * (product?.quantity * (product?.contactLensDetails?.multiple ? 2 : 1))}</span>
                          {(product?.couponAmount && product?.couponAmount > 0) ? <span style={{ fontSize: "1.6rem" }}>{cartPage?.couponDiscount ?? "Coupon Discount"}:{" "} {currencyCode + " " + product?.couponAmount}</span> : ""}
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>

              <div className="cart_order-summary">
                <div className="cart_order-width">
                  <h5>{cartPage?.orderSummary ?? "Order Summary"}</h5>

                  <ul>
                    {cart?.summary?.map((item: any, index: number) => (
                      <li key={item?.text + index}>
                        <div className="d-flex align-items-center ">
                          {item?.text}
                          {index === 0 && (
                            <h6>
                              &nbsp;({cart?.count?.toString().padStart(2, "0")}{" "}
                              {cartPage?.items ?? "Items"})
                            </h6>
                          )}
                        </div>
                        <div className="price-text">
                          {item.sign}&nbsp;{currencyCode}
                          <span>
                            &nbsp;
                            {item.value}
                          </span>
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>


                <div className="cart_order-code">

                  <button className="checkout-btn" onClick={handleCheckout}>
                    {cartPage?.checkout ?? "Proceed to Checkout"}
                  </button>
                  {/* <Link className="checkout-btn" href={isLoggedIn ? `/checkout/cart` : `/login`}>
                  Proceed to Checkout
                </Link> */}
                </div>
                {(settings?.tamara || settings?.tabby) ? <div style={{ margin: "1rem 0" }}>
                  {settings?.tabby ? <div id="tabby" style={{ marginBottom: "1rem" }}></div> : ""}
                  {settings?.tamara ? <div style={{ border: "1px solid #d6d6d3", borderRadius: "7px", padding: "16px" }}>
                    {/* @ts-ignore */}
                    {tamaraShow && <tamara-widget inline-variant="text" type="tamara-summary" amount={cart?.total} inline-type='2'></tamara-widget>}
                  </div> : ""}
                </div> : ""}
              </div>
            </div>
          )}
        </div>
      </div>
      <Script id='tabby-script' src="https://checkout.tabby.ai/tabby-promo.js" onReady={setTabby}></Script>
    </>
  );
}
