import { endpoints } from "@/config/apiEndpoints";
import api from "@/config/axios.interceptor";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import "./coupon.scss";
import Modal from "react-bootstrap/Modal";
import ErrorPage from "../404/ErrorPage";
import EmptyState from "../empty-states/EmptyState";
import { toast } from "sonner";
import { getUser } from "@/lib/methods/auth";
import { redirect, useRouter } from "next/navigation";
import { useContext, useState } from "react";
import { TranslationContext } from "@/contexts/Translation";

const jsonData = [
  {
    coupon_code: "First50",
    cashback: "Get 100 AED Cashback",
    description: "Get upto AED 100 cashback using online payment above AED500",
  },

  {
    coupon_code: "SAVE50",
    cashback: "Get 100 AED Cashback",
    description: "Get upto AED 100 cashback using online payment above AED500",
  },

  {
    coupon_code: "BUY100",
    cashback: "Get 100 AED Cashback",
    description: "Get upto AED 100 cashback using online payment above AED500",
  },

  {
    coupon_code: "First50",
    cashback: "Get 100 AED Cashback",
    description: "Get upto AED 100 cashback using online payment above AED500",
  },
];

function CouponModal({ show, handleClose, setCoupon, appliedCoupon, couponLoading}: any) {
  const { data: coupons, isLoading, error } = useQuery({
    queryKey: ["coupons"],
    queryFn: () => {
      return api.get(endpoints.coupons).then((res) => {
        if (res.status === 200) {
          return res.data?.result;
        }
        throw error;
      });
    },
  });

  const router = useRouter()

  const queryClient = useQueryClient();
  const { translation: { cartPage } } = useContext(TranslationContext);

  const handleRemove = (coupon: string) => {
    api
      .post(endpoints.removeCoupon, { coupon: coupon })
      .then((res) => {
        if (res.data?.errorCode === 0) {
          setCoupon(null);
          toast.success("Coupon removed successfully");
          queryClient.invalidateQueries({ queryKey: ["cart", "counts"] });
          queryClient.invalidateQueries({ queryKey: ["cart"] });
        } else {
          toast.error(res.data?.message);
        }
      })
      .catch((err) => {
        toast.error(err.response.data.message);
      });
  };

  const handleApplyCoupon = async (coupon: string) => {
    const res = await getUser()
    if (res) {
      setCoupon(coupon);
    } else {
      router.push("/login")
      handleClose()
    }
  }

  return (
    <div>
      <Modal className="coupon-modal" show={show} onHide={handleClose} centered>
        <Modal.Header closeButton></Modal.Header>
        <Modal.Body>
          <h2>{cartPage?.offersTitle ?? "Available Offers"}</h2>

          <div className="coupon-modal_scroll">
            {coupons?.length > 0 && <h5>{cartPage?.couponOffers ?? "Coupon Offers"}</h5>}
            {coupons?.map((items: any, index: number) => (
              <div className="coupon-modal_box" key={index}>
                <div className="coupon-modal_head">
                  <span>‘{items.code}’</span>
                  {appliedCoupon === items?.code ? (
                    <button disabled={couponLoading} style={{ opacity: couponLoading ? 0.5 : 1 }} onClick={() => handleRemove(items.code)}>{cartPage?.remove ?? "Remove"}</button>
                  ) : (
                    <button disabled={couponLoading} style={{ opacity: couponLoading ? 0.5 : 1 }} onClick={() => handleApplyCoupon(items.code)}>{cartPage?.applyCoupon ?? "Apply"}</button>
                  )}
                </div>
                <div className="coupon-modal_body">
                  <h4>{items?.title}</h4>
                  <p>{items?.description}</p>
                </div>
              </div>
            ))}

            {isLoading &&
              jsonData?.map((items: any, index: number) => (
                <div className="coupon-modal_box skeletonLoader" key={index}>
                  <div className="coupon-modal_head skeletonLoader">
                    <span></span>
                    <button></button>
                  </div>
                  <div className="coupon-modal_body skeletonLoader">
                    <h4></h4>
                    <p></p>
                  </div>
                </div>
              ))}

            {error && <ErrorPage errorcode={"500"} />}
            {!isLoading && coupons?.length === 0 && (
              <div className="h-100 d-flex justify-content-center align-items-center">
                <EmptyState icon="discount" title={cartPage?.noOffers ?? "No Offers Available"} />
              </div>
            )}
          </div>
        </Modal.Body>
      </Modal>
    </div>
  );
}

export default CouponModal;
