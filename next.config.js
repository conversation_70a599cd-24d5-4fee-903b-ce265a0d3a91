const {
    mainModule
} = require('process')

/** @type {import('next').NextConfig} */
const nextConfig = {
    compiler: {
        removeConsole: process.env.NODE_ENV === "production",
    },
    logging: {
        fetches: {
            fullUrl: true,
        },
    },
    typescript: {
        ignoreBuildErrors: true, // TODO: set to false
    },
    images: {
        remotePatterns: [
            {
                protocol: 'https',
                hostname: process.env.NEXT_PUBLIC_IMAGE_DOMAIN,

            },
            {
                protocol: 'http',
                hostname: 'localhost',

            }
        ],
    },

}

module.exports = nextConfig