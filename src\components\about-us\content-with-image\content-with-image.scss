.content-with-image {
  margin-top: 5rem;

  h2 {
    margin-bottom: 4.5rem;
    font-size: 3.2rem;
    line-height: 4.2rem;

    @media (max-width: 575.98px) {
      margin-bottom: 4rem;
    }
  }

  &_row {
    display: flex;
    column-gap: 7.5rem;
    align-items: center;

    @media (max-width: 767.98px) {
      &:last-child {
        margin-bottom: 4rem;
      }
    }

    &:not(:last-child) {
      margin-bottom: 6rem;

      @media (max-width: 767.98px) {
        margin-bottom: 5rem;
      }
    }

    &:nth-child(odd) {
      flex-direction: row-reverse;

      @media (max-width: 767.98px) {
        flex-direction: column;
      }
    }

    @media (max-width: 767.98px) {
      flex-direction: column;
    }

    @media (max-width: 991.98px) {
      column-gap: 3rem;
    }
  }

  &_content {
    width: 50%;

    @media (max-width: 767.98px) {
      width: 100%;
      order: 2;
    }

    h4 {
      font-size: 2.4rem;
      font-weight: 700;
      line-height: 3.12rem;
      color: #000;
    }

    p {
      margin-top: 2.3rem;
      line-height: 2.72rem;

      @media (max-width: 575.98px) {
        margin-top: 1.5rem;
      }
    }
  }

  &_image {
    width: 50%;

    @media (max-width: 767.98px) {
      width: 100%;
      margin-bottom: 3rem;
    }

    img {
      border-radius: 2rem;
      height: 37.9rem;

      @media (max-width: 575.98px) {
        height: 21.2rem;
      }
    }
  }
}