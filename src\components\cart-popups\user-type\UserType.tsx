import React from "react";
import Modal from "react-bootstrap/Modal";
import "./user-type.scss";

export default function UserType({ show, handleClose, stepHandler, phoneNumber }: any) {
  return (
    <>
      {" "}
      <Modal
        className="checkout-popup"
        show={show}
        onHide={handleClose}
        backdrop="static"
        keyboard={false}>
        <Modal.Header closeButton></Modal.Header>
        <Modal.Body>
          <h2>Hello! We are Glad to see you :)</h2>
          <div className="user-type-btn">
            <button
              onClick={() => {
                stepHandler(1);
                handleClose();
              }}>
              New User
            </button>
            <button
              onClick={() => {
                stepHandler(2);
                handleClose();
              }}>
              Returning User
            </button>
          </div>
        </Modal.Body>
      </Modal>
    </>
  );
}
