.prefooter {
  margin-top: 5.4rem;

  @media (max-width: 575.98px) {
    margin-top: 3rem;
  }

  &_wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    row-gap: 4rem;

    @media (max-width: 575.98px) {
      align-items: baseline;
      row-gap: 2.4rem;
    }
  }

  &_items {
    display: flex;
    column-gap: 1rem;

    @media (max-width: 575.98px) {
      text-align: center;
      width: 48%;
      flex-direction: column;
      align-items: center;
    }

    img {
      width: 4rem;
      height: 4rem;
      object-fit: contain;
    }

    h5 {
      color: #000;
      font-size: 1.7rem;
      font-weight: 500;
      line-height: 2.1rem;

      @media (max-width: 575.98px) {
        font-size: 1.6rem;
        line-height: 2rem;
        margin-top: 1rem;
      }
    }

    h6 {
      color: #949494;
      font-size: 1.6rem;
      font-weight: 500;
      margin-top: 0.6rem;
      line-height: 2rem;

      @media (max-width: 575.98px) {
        line-height: 1.8rem;
        font-size: 1.4rem;
        font-weight: 400;
        max-width: 16.5rem;
        width: 100%;
      }
    }
  }
}

// MAIN FOOTER STYLE

.footer {
  margin-top: 5rem;
  background-color: #000;
  color: #fff;
  padding-top: 5.8rem;

  @media (max-width: 991.98px) {
    padding-bottom: 7rem;
  }

  @media (max-width: 575.98px) {
    margin-top: 2rem;
  }

  &_top {
    display: flex;
    justify-content: space-between;

    @media (max-width: 991.98px) {
      flex-direction: column;
      text-align: center;
      align-items: center;
    }
  }

  &_icons {
    display: flex;
    column-gap: 3.4rem;
    align-items: center;
    margin-top: 3.3rem;

    @media (max-width: 575.98px) {
      justify-content: center;
    }

    span {
      font-size: 1.6rem;
      font-weight: 600;
      color: #fff;
    }

    img {
      width: auto !important;
      height: auto !important;
      max-width: 4rem;
      max-height: 3rem;
      transform: scale(1);
      transition: 0.3s all ease;

      &:hover {
        transform: scale(1.2);
        transition: 0.3s all ease;
      }
    }
  }

  &_logo {
    @media (max-width: 575.98px) {
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      padding-bottom: 3rem;
      width: 100%;
    }

    img {
      width: 21rem;
      height: 4.3rem;
      object-fit: contain;
    }
  }

  &_input {
    @media (max-width: 575.98px) {
      width: 100%;
    }

    p {
      color: rgba(255, 255, 255, 0.7);
      font-size: 1.6rem;
      font-weight: 500;

      @media (max-width: 575.98px) {
        max-width: 29.9rem;
        width: 100%;
        line-height: 2rem;
        margin: 0 auto;
        margin-top: 2rem;
      }
    }

    input {
      border: none;
      background: none;
      border-bottom: 1px solid #fff;
      width: 100%;
      margin-top: 3rem;
      padding-bottom: 2rem;
      color: #fff;
      padding-right: 2.6rem;
      border-radius: 0;

      &:focus-visible {
        border-bottom: 1px solid #fff;
        outline: none;
      }

      &::placeholder {
        color: #fff;
        font-size: 1.6rem;
        font-weight: 500;

        @media (max-width: 575.98px) {
          font-size: 1.4rem;
        }
      }
    }

    span {
      display: flex;
      align-items: baseline;
      position: relative;

      button {
        border: none;
        background-color: transparent;
        padding: 0;
        transform: translateY(-50%);
        position: absolute;
        top: 54%;
        right: 0;

        img {
          object-fit: contain;
          width: 2.4rem;
          height: 2.4rem;
          background-color: black;
          border-radius: 50%;
          border: 1px solid black;
        }
      }
    }
  }

  &_middle {
    display: flex;
    align-items: baseline;
    justify-content: space-between;
    margin-top: 4.6rem;
    padding-top: 4.6rem;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    flex-wrap: wrap;

    @media (max-width: 575.98px) {
      border: none;
      text-align: center;
      padding: 0;
      row-gap: 4rem;
    }

    &::after {
      content: "";
      height: 83%;
      width: 1px;
      background-color: rgba(255, 255, 255, 0.2);
      position: absolute;
      left: 22%;

      @media (max-width: 991.98px) {
        content: none;
      }
    }
  }

  &_info {
    @media (max-width: 767.98px) {
      width: 100%;
      margin-bottom: 5rem;
    }

    @media (max-width: 575.98px) {
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      padding-bottom: 5rem;
      margin-bottom: 3rem;
    }

    h5 {
      font-size: 1.6rem;
      font-weight: 500;
      color: #fff;
    }

    p {
      font-size: 1.5rem;
      font-weight: 400;
      color: #fff;
      margin-top: 2.3rem;
      max-width: 18rem;
      width: 100%;
      line-height: 1.8rem;

      @media (max-width: 575.98px) {
        margin: 0 auto;
        margin-top: 2.3rem;
      }
    }

    &-mail {
      color: #fff;
      font-size: 1.5rem;
      font-weight: 500;
      margin-top: 2.3rem;
      margin-bottom: 1.9rem;
      display: inline-block;
      position: relative;

      &:hover {
        color: #fff;
        text-decoration: underline;

        &::after {
          position: absolute;
          right: -14px;
          top: 2px;
          content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='12' height='12' fill='rgba(255,255,255,1)'%3E%3Cpath d='M10 6V8H5V19H16V14H18V20C18 20.5523 17.5523 21 17 21H4C3.44772 21 3 20.5523 3 20V7C3 6.44772 3.44772 6 4 6H10ZM21 3V11H19L18.9999 6.413L11.2071 14.2071L9.79289 12.7929L17.5849 5H13V3H21Z'%3E%3C/path%3E%3C/svg%3E");
        }
      }
    }

    &-number {
      display: block;
      color: #fff;
      font-size: 1.5rem;
      font-weight: 500;
      position: relative;

      &:hover {
        color: #fff;
        text-decoration: underline;

        // &::after {
        //   margin-left: 4px;
        //   content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='12' height='12' fill='rgba(254,254,254,1)'%3E%3Cpath d='M21 16.42V19.9561C21 20.4811 20.5941 20.9167 20.0705 20.9537C19.6331 20.9846 19.2763 21 19 21C10.1634 21 3 13.8366 3 5C3 4.72371 3.01545 4.36687 3.04635 3.9295C3.08337 3.40588 3.51894 3 4.04386 3H7.5801C7.83678 3 8.05176 3.19442 8.07753 3.4498C8.10067 3.67907 8.12218 3.86314 8.14207 4.00202C8.34435 5.41472 8.75753 6.75936 9.3487 8.00303C9.44359 8.20265 9.38171 8.44159 9.20185 8.57006L7.04355 10.1118C8.35752 13.1811 10.8189 15.6425 13.8882 16.9565L15.4271 14.8019C15.5572 14.6199 15.799 14.5573 16.001 14.6532C17.2446 15.2439 18.5891 15.6566 20.0016 15.8584C20.1396 15.8782 20.3225 15.8995 20.5502 15.9225C20.8056 15.9483 21 16.1633 21 16.42Z'%3E%3C/path%3E%3C/svg%3E");
        // }
      }
    }
  }

  &_links {
    @media (max-width: 575.98px) {
      width: 50%;
      text-align: left;
    }

    h5 {
      font-size: 1.6rem;
      font-style: normal;
      font-weight: 500;
      color: #fff;
    }

    ul {
      margin-top: 2.3rem;

      li {
        &:not(:last-child) {
          margin-bottom: 1.3rem;
        }

        a {
          color: rgba(255, 255, 255, 0.6);
          font-size: 1.4rem;
          font-style: normal;
          font-weight: 400;
          position: relative;
          padding-bottom: 2px;

          &::after {
            content: "";
            border-bottom: solid 2px #ffffff;
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            transform: scaleX(0);
            transform-origin: right;
            transition: transform 0.25s;
          }

          &:hover {
            &::after {
              transform-origin: left;
              transform: scaleX(1);
            }
          }

          @keyframes a {
            from {
              transform: scaleX(0);
            }

            to {
              transform: scaleX(1);
            }
          }
        }
      }
    }
  }

  &_bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    margin-top: 4.6rem;
    padding: 4rem 0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    @media (max-width: 575.98px) {
      margin-top: 3rem;
      flex-direction: column;
      row-gap: 2.4rem;
    }

    a {
      filter: grayscale(1) brightness(2.5);
      transition: filter 600ms cubic-bezier(0.075, 0.82, 0.165, 1);

      &:hover {
        filter: grayscale(0) brightness(1);
      }
    }

    p {
      font-size: 1.5rem;
      font-weight: 400;
      color: #fff;

      @media (max-width: 575.98px) {
        order: 2;
        text-align: center;
      }
    }

    select {
      background: none;
      color: #fff;
      border: none;
      border-bottom: 1px solid rgba(255, 255, 255, 0.6);
      padding-bottom: 6px;
      font-size: 1.4rem;
      font-weight: 400;
      min-width: 9rem;

      option {
        color: #000000;
      }

      &:focus-visible {
        outline: none;
        border-bottom: 1px solid rgba(255, 255, 255, 0.6);
      }
    }
  }
}

.app.rtl {

  .footer_input span button {
    left: 0;
    right: auto;
  }

  .footer_input input {
    padding-left: 2.6rem;
    padding-right: 0;
  }

  .footer_middle::after{
    right: 22%;
  }

}

.app.rtl {
  .footer_links {
    text-align: right !important;
  }
}