"use client";

import Link from "next/link";
import { Image, Spinner } from "react-bootstrap";
import "./my-orders.scss";
import ViewDetailBtn from "@/components/view-detail-btn/ViewDetailBtn";
import CancelOrder from "@/components/cancel-order/CancelOrder";
import FormattedDate from "@/components/common/FormattedDate";
import { useInfiniteQuery, useQuery } from "@tanstack/react-query";
import { endpoints } from "@/config/apiEndpoints";
import api from "@/config/axios.interceptor";
import EmptyState from "@/components/empty-states/EmptyState";
import GenericBackButton from "@/components/GenericBackButton/GenericBackButton";
import React, { useContext, useState } from "react";
import FallbackImage from "@/components/fallback-image/FallbackImage";
import { TranslationContext } from "@/contexts/Translation";
import { InView } from "react-intersection-observer";
import { useLocaleContext } from "@/contexts/LocaleProvider";

function MyOrders() {
  const [imageSrc, setImageSrc] = useState<any>({});

  const getOrders = async ({ pageParam = 1 }: any) => {
    const res = await api.get(`${endpoints.orders}?page=${pageParam}&limit=15`);
    return res.data.result;
  }

  const {
    data,
    error,
    fetchNextPage,
    hasNextPage,
    isFetching,
    isLoading,
    isRefetching,
    isFetchingNextPage,
    status,
  }: any = useInfiniteQuery({
    queryKey: ["orders"],
    queryFn: getOrders,
    getNextPageParam: (lastPage: any, allPages) => {
      const nextPage = lastPage.nextPage;
      return nextPage;
    },
    initialPageParam: 1, // Add this line with an appropriate initial value
  });

  const { translation: { orderPage, myAccount, productPage, other } }: any = useContext(TranslationContext)
  const { currentLocale:locale, currencyCode } = useLocaleContext()

  const convertToTitleCase = (str: string) => {
    return str
      .toLowerCase()
      .split(" ")
      .map(function (word) {
        return word.charAt(0).toUpperCase() + word.slice(1);
      })
      .join(" ");
  };

  if (data?.pages?.[0]?.length === 0)
    return <EmptyState icon="x-doc" title="No Orders Found!" />;
  return (
    <section className="orders position-relative">
      <GenericBackButton style={{ top: "5px" }} />
      <h2>{myAccount?.myOrders ?? "My Orders"}</h2>
      {data?.pages?.map((page: any, index: number) => (
        <React.Fragment key={index}>
          {page?.orders?.map((order: any) => (
            <div className="orders_body" key={order?._id}>
              <div className="orders_top">
                <div className="orders_id">
                  <h5>{orderPage?.orderNo ?? "Order no"}:{order?.orderNo}</h5>
                  <h6>
                    {orderPage?.orderDate ?? "Order Date"} :{" "}
                    <span>
                      <FormattedDate date={order?.orderDate} />
                    </span>
                  </h6>
                  {order?.orderStatus != "DELIVERED" && <h6>
                    {orderPage?.estimatedDelivery ?? "Estimated Delivery Date"} :
                    <span>
                      {new Date(order?.expectedDate)?.toDateString() || ""}
                    </span>
                  </h6>}
                </div>
                <div className="orders_status">
                  <h6>
                    {orderPage?.orderStatus ?? "Order Status"} :{" "}
                    <span> {
                      locale.includes("en") ? convertToTitleCase(order?.orderStatus)
                        : order?.orderStatus == "PLACED" ? orderPage?.orderPlaced
                          : order?.orderStatus == "CONFIRMED" || order?.orderStatus == "READY TO SHIP" ? orderPage?.confirm
                            : order?.orderStatus == "SHIPPED VIA ECO" || "SHIPPED VIA INHOUSE" ? orderPage?.shipped
                              : order?.orderStatus == "DELIVERED" ? orderPage?.delivered
                                : order?.orderStatus == "CANCELLED" ? orderPage?.cancelled
                                  : order?.orderStatus
                    }</span>
                  </h6>
                  <h6>
                    {orderPage?.paymentMethod ?? "Payment Method"} : <span> {
                      order?.paymentMethod === "COD" ? orderPage?.cod
                        : order?.paymentMethod === "ONLINE" ? orderPage?.online
                          : order?.paymentMethod
                    }</span>
                  </h6>
                  {order?.gateway ? <h6>
                    {orderPage?.paymentGateway ?? "Gateway"} : <span> {
                      order?.paymentMethod === "ONLINE" ? order?.gateway
                        : ""
                    }</span>
                  </h6>: ""}
                </div>
                <div className="orders_btn">
                  <ViewDetailBtn
                    link={`/my-accounts/my-orders/${order?.orderNo}` || ""}
                  />
                  {/* {!excludeStatuses?.includes(order?.orderStatus) && (
                    <CancelOrder order={order?._id} />
                  )} */}
                </div>
              </div>

              <div className="orders_bottom">
                {order?.products?.slice(0, 2)?.map((product: any) => (
                  <div className="orders_detail" key={product?._id}>
                    {/* <Image
                      src={product?.thumbnail}
                      width={120}
                      height={120}
                      alt="product image"
                    /> */}
                    <FallbackImage
                      width={120}
                      height={120}
                      src={product?.thumbnail}
                      alt="Product"
                      fallbackSrc="/images/product/noImage.jpg" />
                    <ul>
                      <li className="orders_detail-modal">{product?.name}</li>
                      <li className="orders_detail-color">

                        {
                          product?.color && <> {productPage?.color ?? "Color"} : {product?.color?.name}</>
                        }
                      </li>
                      <li className="orders_detail-size">
                        {
                          product?.size && <> {productPage?.size ?? "Size"} : {product?.size?.name}</>
                        }
                      </li>
                      <li className="orders_detail-price">
                        {currencyCode} {product?.priceTotal?.toFixed(2)}
                      </li>
                    </ul>
                  </div>
                ))}
                {order?.products?.length - 2 > 0 && (
                  <div className="orders_products">
                    <Link href={`/my-accounts/my-orders/${order?.orderNo}`}>
                      +{order?.products?.length - 2} Products
                    </Link>
                  </div>
                )}
              </div>
            </div>
          ))}
        </React.Fragment>
      ))}
      {isFetching && (
        <div className="d-grid" style={{ placeItems: "center", padding: "60px 0" }}>
          <Spinner />
          Loading
        </div>
      )}
      {!isFetching && data?.pages?.[0]?.orders?.length === 0 && (
        <div className="d-grid" style={{ placeItems: "center", padding: "60px 0" }}>
          <EmptyState icon="glasses" title="No orders Found!" />
        </div>
      )}

      <div className="text-center">
        {data?.pages?.[0]?.orders?.length !== 0 && (
          <button
            onClick={fetchNextPage}
            className="load-more btn"
            style={{ borderColor: "transparent" }}
            disabled={!hasNextPage || isFetchingNextPage}>
            {isFetchingNextPage
              ? "Loading more..."
              : hasNextPage
                ? "Load More"
                : (other?.nothingMoreToLoad ?? "Nothing more to load")}
          </button>
        )}

        <InView as="div" onChange={(inView, entry) => { if (inView && !isFetching) fetchNextPage(); }}></InView>
      </div>
    </section>
  );
}

export default MyOrders;
