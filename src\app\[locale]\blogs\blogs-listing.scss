.blog-listing {
  margin-top: 3.5rem;
  padding-bottom: 2.5rem;

  @media (max-width: 575.98px) {
    margin-top: 1.5rem;
    padding-bottom: 4rem;
  }

  &_wrapper {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 4rem;

    @media (max-width: 767.98px) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media (max-width: 575.98px) {
      margin-top: 2.4rem;
      grid-template-columns: repeat(1, 1fr);
      gap: 1.5rem;
    }
  }

  &_items {
    padding: 1.6rem;
    border-radius: 1.3rem;
    background: #f2f4f9;
    position: relative;

    &_date {
      position: absolute;
      top: 2.2rem;
      right: 2.2rem;
      color: #ffffff;
      font-size: 1.3rem;
      font-weight: 600;
      background-color: #000;
      padding: 7px 14px;
      border-radius: 2rem;
      animation: fade-in 0.5s ease-in-out;
    }

    &:has(img.opacity-0) {
      background-color: #fdfdfd;
      background-image: url(/images/common/logo.png);
      animation: skeleton 1s linear infinite alternate;
      background-repeat: no-repeat;
      background-position: center;
      background-size: 40%;
    }

    img {
      border-radius: 1.3rem;
    }

    h5 {
      color: #000;
      font-size: 1.8rem;
      font-weight: 500;
      margin-top: 1.5rem;
      line-height: 2.2rem;
      margin-top: 1.5rem;
    }

    p {
      margin-top: 1.2rem;
      line-height: 1.7rem;
      color: #878787;
      font-size: 1.4rem;
      font-weight: 300;
    }

    a {
      color: #000;
      font-size: 1.4rem;
      font-weight: 500;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}

@keyframes fade-in {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}


.app.rtl {
  .blog-listing_items_date {
    left: 2.2rem;
    right: auto;
  }
}