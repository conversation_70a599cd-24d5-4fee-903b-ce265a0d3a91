.address-book {
  padding-bottom: 2.1rem;
  position: relative;

  h2 {
    margin-bottom: 3rem;
    @media (max-width: 1199.98px) {
      margin-top: 4.4rem;
    }

    @media (max-width: 575.98px) {
      margin-bottom: 6rem;
      margin-top: 1.5rem;
    }
  }

  @media (max-width: 575.98px) {
    margin-bottom: 3rem;
  }

  &_wrapper {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2.5rem 2rem;

    @media (max-width: 767.98px) {
      grid-template-columns: repeat(1, 1fr);
    }
  }

  &_cards {
    border-radius: 1.2rem;
    border: 1px solid #edeef2;
    background: #fff;
    padding: 2.5rem 1.5rem 2.5rem 4.3rem;
    color: #000;

    @media (max-width: 575.98px) {
      padding: 2.5rem 1.4rem 2.3rem 2.4rem;
    }

    &.isActive {
      background: #f2f4f9;

      ul li.btns a {
        background-color: #000000;
        color: #fff;

        &:hover {
          background-color: transparent;
          color: #000;
        }
      }
    }

    h5 {
      font-size: 2rem;
      font-style: normal;
      font-weight: 400;
    }

    ul {
      margin-top: 1.9rem;

      li {
        font-size: 1.6rem;
        font-weight: 400;
        line-height: 2.2596rem;
        max-width: 36.4rem;
        width: 100%;

        &:not(:last-child) {
          margin-bottom: 2rem;

          @media (max-width: 575.98px) {
            margin-bottom: 1.9rem;
          }
        }

        a {
          color: #000000;
        }

        &.cta {
          margin: 2.5rem 0;
          display: flex;
          column-gap: 1.9rem;

          @media (max-width: 575.98px) {
            column-gap: 1.2rem;
          }

          a,
          button {
            padding: 0.7rem 1.9rem;
            border-radius: 0.8rem;
            border: 1px solid #000;
            font-size: 1.6rem;
            font-weight: 400;
            transition: 0.3s all ease;
            color: #000000;

            &:hover,
            &.activeBtn {
              color: #ffffff;
              transition: 0.3s all ease;
              background-color: #000;
            }

            &[disabled] {
              pointer-events: none;
            }
          }
        }

        &.btns {
          display: flex;
          gap: 1rem;

          .bar{
            width: 1px;
            background-color: #d9d9d9;
          }

          button {
            border: none;
            background-color: transparent;
            padding: 0;
            font-size: 1.6rem;
            font-weight: 400;
            line-height: 2rem;
          }
        }
      }
    }
  }

  &_btn {
    text-align: center;

    button {
      color: #000;
      margin-top: 5rem;
      display: inline-block;
      font-size: 1.5rem;
      font-weight: 500;
      border-radius: 6rem;
      border: 1px solid #000;
      padding: 1.6rem 8rem;
      background-color: transparent;

      @media (max-width: 575.98px) {
        padding: 0;
        border: none;
        margin-top: 0;
        position: absolute;
        top: 70px;
        right: 11px;
        text-decoration: underline;
        z-index: 2;
      }
    }
  }
}
