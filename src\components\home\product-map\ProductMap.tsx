import Image from "next/image";
import "./productMap.scss";
import Link from "next/link";
import { endpoints } from "@/config/apiEndpoints";
import Marker from "./Marker";
import { cookies } from "next/headers";

const teamData = [
  {
    button_image: "/images/home/<USER>",
    image: "/images/home/<USER>",
    title: "Women Sunglasses",
    link: "DISCOVER MORE",
  },

  {
    button_image: "/images/home/<USER>",
    image: "/images/home/<USER>",
    title: "Mens Sunglasses",
    link: "DISCOVER MORE",
  },

  {
    button_image: "/images/home/<USER>",
    image: "/images/home/<USER>",
    title: "Contact Lens",
    link: "DISCOVER MORE",
  },
];

export default async function ProductMap({ id, data }: any) {
  
  const imageMap = data.result;

  return (
    <section className="team" id={id}>
      <div className="team_bg">
        <Image
          quality={100}
          loading="lazy"
          src={imageMap?.image ?? ""}
          width={1000}
          height={1000}
          alt="images"
        />
      </div>

      <div className="team_wrapper">
        {imageMap?.items?.map((items: any, index: number) => (
          <Marker key={index} items={items} />
        ))}
      </div>
    </section>
  );
}
