.verify-popup {
  &.modal {
    .modal-dialog {
      max-width: 68.3rem;
      width: 100%;
      height: 100vh;
      margin: 0;
      margin-left: auto;
    }

    .modal-header {
      padding: 0;
      border: none;

      .btn-close {
        background-image: url(../../../../public/images/common/close.png);
        width: 4.8rem;
        height: 4.8rem;
        background-position: center;
        background-repeat: no-repeat;
        background-size: contain;
        opacity: 1;
        padding: 0;

        @media (max-width: 575.98px) {
          width: 2.8rem;
          height: 2.8rem;
        }

        &:focus {
          box-shadow: none;
          outline: none;
          border: none;
        }
      }
    }

    .modal-content {
      padding: 4rem 4.5rem 0rem 16.7rem;
      border: none;
      background: #fff;
      border-radius: 0;
      height: 100%;

      @media (max-width: 575.98px) {
        padding: 5.4rem 2rem 0 2rem;
      }
    }

    .modal-body {
      padding: 0;
      display: flex;
      justify-content: center;
      flex-direction: column;

      h2 {
        line-height: 3.6rem;
        color: #242731;
        font-weight: 700;
        max-width: 40.4rem;
        width: 100%;
        text-align: left;
      }

      button {
        margin-top: 6rem;
        border-radius: 6rem;
        background: #000;
        color: #fff;
        font-size: 1.5rem;
        font-weight: 500;
        line-height: 2.4rem;
        border: none;
        width: 11.5rem;
        height: 4.5rem;
        align-self: flex-start;

        @media (max-width: 575.98px) {
          width: 100%;
        }
      }

      .verify-popup-input {
        margin-top: 1.6rem;

        h6 {
          color: #575f6e;
          font-size: 1.6rem;
          font-weight: 300;
          line-height: 2.2rem;
        }

        span {
          display: block;
          color: #000;
          font-size: 1.6rem;
          font-weight: 500;
          line-height: 2.2rem;
        }

        label {
          color: #242426;
          font-size: 1.3rem;
          font-weight: 400;
          line-height: 1.8rem;
          margin-top: 3.2rem;
        }

        input {
          width: 100%;
          color: #242426;
          font-size: 2.3rem;
          font-weight: 400;
          line-height: 2.8rem;
          border: none;
          border-bottom: 1px solid #e2e4e5;
          padding: 0 !important;
          -moz-appearance:textfield;

          &::-webkit-inner-spin-button {
            display: none;
          }

          &::placeholder {
            letter-spacing: -3px;
            color: #cbcbcb !important;
          }

          &:focus-within {
            outline: none;
            border-bottom: 1px solid #e2e4e5;
          }

          &::placeholder {
            color: #242426;
            font-size: 1.8rem;
            font-weight: 400;
          }
        }
      }

      .verify-popup-otp {
        border-bottom: 1px solid #000;
        max-width: 35rem;
        width: 100%;
        position: relative;
      }

      .count-down {
        transform: translateY(-50%);
        position: absolute;
        right: 5px;
        top: 50%;
        color: #292929;
      }

      p {
        color: #575f6e;
        font-size: 1.2rem;
        font-weight: 400;
        line-height: 1.6rem;
      }

      .resend-otp {
        color: #000;
        font-size: 1.4rem;
        font-weight: 400;
        line-height: 2.4rem;
        border: none;
        background-color: transparent;
        margin: 0;
        height: auto;
        width: auto;
        padding-left: 3.6rem;

        &[disabled] {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }
    }
  }
}

.app.rtl {
  .verify-popup.modal .modal-body h2 {
    text-align: right;
  }

  .verify-popup.modal .modal-body .count-down {
    right: auto;
    left: 5px;
  }
}