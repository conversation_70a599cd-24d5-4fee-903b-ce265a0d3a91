.category {
  margin-top: 5rem;

  @media (max-width: 767.98px) {
    margin-top: 2.4rem;
  }

  &_title {
    text-align: center;
  }

  &_wrapper {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    column-gap: 4.5rem;
    row-gap: 4rem;
    margin-top: 3rem;

    @media (max-width: 991.98px) {
      column-gap: 2.9rem;
    }

    @media (max-width: 767.98px) {
      grid-template-columns: repeat(3, 1fr);
      margin-top: 2.4rem;
      row-gap: 2rem;
    }

    @media (max-width: 575.98px) {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  &_items {
    position: relative;

    img {
      border: 1px solid #eaeaea;
      height: auto;
      border-radius: 4rem;
      height: 22rem;

      @media (max-width: 767.98px) {
        border-radius: 2.6rem;
        height: 15.1rem;
      }
    }

    .hover_image {
      opacity: 0;
      position: absolute;
      left: 0;
      top: 0;
      transition: 0.3s all ease;
    }

    h4 {
      color: #000;
      margin-top: 2rem;
      text-align: center;
      font-style: n;

      @media (max-width: 575.98px) {
        margin-top: 1rem;
      }
    }

    @media(hover: hover) {
      &:hover {
        transition: 0.3s all ease;
  
        .hover_image {
          opacity: 1;
          transition: 0.3s all ease;
        }
  
        .normal_image {
          opacity: 0;
          transition: 0.3s all ease;
        }
      }
    }
  }
}

.mobile-category {
  .category {
    padding-bottom: 4rem;
  }
}