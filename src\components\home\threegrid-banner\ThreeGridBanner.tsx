"use client";

import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay } from "swiper/modules";
import "./threegridbanner.scss";
import Image from "next/image";
import Link from "next/link";

type ThreeGridSlide = {
  image: string;
  link: string;
  _id: string;
};

const jsonData = [
  {
    image: "/images/home/<USER>",
  },

  {
    image: "/images/home/<USER>",
  },

  {
    image: "/images/home/<USER>",
  },

  {
    image: "/images/home/<USER>",
  },

  {
    image: "/images/home/<USER>",
  },

  {
    image: "/images/home/<USER>",
  },
];

export default function ThreeGridBanner({ id, data }: any) {
  return (
    <section className="gridslider" id={id}>
      <div className="gridslider_wrapper">
        <div className="container-fluid px-md-5">
          <div style={{ width: "100%" }}>
            <Swiper
              modules={[Autoplay]}
              className="mySwiper"
              speed={500}
              loop={true}
              autoplay={{
                delay: 2000,
                disableOnInteraction: false,
                pauseOnMouseEnter: true,
              }}
              breakpoints={{
                320: {
                  slidesPerView: 1.2,
                  spaceBetween: 15,
                },

                600: {
                  slidesPerView: 2,
                  spaceBetween: 15,
                },

                900: {
                  slidesPerView: 2.5,
                  spaceBetween: 15,
                },

                1024: {
                  slidesPerView: 3,
                  spaceBetween: 30,
                },
              }}
            >
              {data.map((items: ThreeGridSlide, index: number) => (
                <SwiperSlide key={items?._id}>
                  <Link href={items.link || ""}>
                    <Image
                      quality={100}
                      priority
                      key={index}
                      src={items.image ?? ""}
                      width={10000}
                      height={10000}
                      alt="image"
                    />
                  </Link>
                </SwiperSlide>
              ))}
            </Swiper>
          </div>
        </div>
      </div>
    </section>
  );
}
