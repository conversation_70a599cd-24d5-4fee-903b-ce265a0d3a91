.brands {
  margin-top: 3.5rem;

  @media (max-width: 575.98px) {
    margin-top: 1.5rem;
  }

  &_filter {
    ul {
      display: flex;
      width: calc(100% - 68px);
      column-gap: 0.7rem;
      justify-content: center;
      margin: 0 auto;
      margin-top: 3.4rem;
      align-items: center;
      flex-wrap: wrap;

      @media (max-width: 575.98px) {
        width: 100%;
        margin-top: 2.4rem;
      }

      li {
        text-transform: uppercase;
        width: 3.2rem;
        height: 3.2rem;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 1.8rem;
        font-weight: 500;

        &.active {
          border-radius: 0.6rem;
          background: #000;
          color: #fff;
        }

        @media (max-width: 575.98px) {
          font-size: 1.4rem;
        }
      }
    }
  }

  .brandlogo {
    padding: 4rem 0;
    margin-top: 3.5rem;
    background: #f2f4f9;

    @media (max-width: 575.98px) {
      margin-top: 2.5rem;
      padding: 2.5rem 0;
    }

    &_logos {
      margin-top: 3.5rem;
    }

    &_grids {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 2rem;
      justify-content: space-between;

      @media (max-width: 991.98px) {
        grid-template-columns: repeat(3, 1fr);
      }

      @media (max-width: 767.98px) {
        grid-template-columns: repeat(2, 1fr);
      }

      @media (max-width: 575.98px) {
        gap: 1.5rem 1.9rem;
      }
    }

    &_image {
      border-radius: 1.5rem;
      background: #fff;
      text-align: center;
      padding: 2.2rem 2rem;
      position: relative;
      &:hover {
        filter: drop-shadow(0px 0px 5px rgba(0, 0, 0, 0.111));
      }

      img {
        object-fit: contain;
      }

      @media (max-width: 575.98px) {
        padding: 1.3rem 1.7rem;
      }

      img {
        width: 21.1546rem;
        height: 10.5773rem;

        @media (max-width: 575.98px) {
          width: 12.7711rem;
          height: 6.3856rem;
        }
      }
    }
  }
}
