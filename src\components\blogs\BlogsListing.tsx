"use client";

import Image from "next/image";
import Link from "next/link";
import LogoAnimation from "../LogoAnimation/LogoAnimation";
import { endpoints } from "@/config/apiEndpoints";
import api from "@/config/axios.interceptor";
import { QueryFunction, QueryKey, useInfiniteQuery } from "@tanstack/react-query";
import { InView } from "react-intersection-observer";
import { useLocaleContext } from "@/contexts/LocaleProvider";
const options: any = {
  year: "numeric",
  month: "long",
  day: "numeric",
};

const getblogs: QueryFunction<unknown, QueryKey, unknown> = async ({ pageParam = 1 }) => {
  const res = await api.post(endpoints.blogs, {
    page: pageParam,
    limit: 15,
  });
  return res.data.result;
};
export default function BlogsListingComponent({ pageLimit }: any) {

  const { currentLocale:locale } = useLocaleContext()

  const {
    data: blogs,
    error,
    fetchNextPage,
    hasNextPage,
    isFetching,
    isFetchingNextPage,
    status,
  } = useInfiniteQuery({
    queryKey: ["blogs"],
    queryFn: getblogs,
    getNextPageParam: (lastPage: any, allPages) => {
      const nextPage = lastPage.nextPage;
      return nextPage;
    },
    initialPageParam: 1, // Add this line with an appropriate initial value
  });

  return (
    <section className="blog-listing">
      <h2>{locale.includes("en")? "Our Blogs": "مدوناتنا"}</h2>
      <div className="container">
        <div className="blog-listing_wrapper">
          {blogs &&
            Array.isArray(blogs.pages) &&
            blogs.pages.map((page: any) =>
              page.blogs.map((blog: any) => (
                <div className="blog-listing_items" key={blog?._id}>
                  <span className="blog-listing_items_date">
                    {new Date(blog?.date)?.toLocaleDateString("en-US", options)}
                  </span>
                  <Link href={"/blogs/" + blog?.slug}>
                    <Image quality={100} priority
                      className="opacity-0"
                      onLoadingComplete={(image) => image.classList.remove("opacity-0")}
                      src={blog?.image ?? ""}
                      width={335}
                      height={196}
                      alt="blog images"
                    />
                  </Link>
                  <Link href={"/blogs/" + blog?.slug}>
                    <h5>{blog.title}</h5>
                  </Link>
                  <p>
                    {blog?.summary?.slice(0, 75)}...{" "}
                    <Link href={"/blogs/" + blog?.slug}>{locale.includes("en")? "Read More":"اقرأ المزيد"}</Link>
                  </p>
                </div>
              ))
            )}
        </div>
      </div>
      <InView as="div" onChange={(inView, entry) => fetchNextPage()}>
        {isFetching && (
          <div
            className="d-grid"
            style={{ placeItems: "center", padding: blogs?.pages?.length ? 0 : "150px 0" }}>
            <LogoAnimation />
          </div>
        )}
      </InView>
    </section>
  );
}
