"use client";
import { useContext, useEffect, useState } from "react";
import "./sizepicker.scss";
import useUrlState from "@/hooks/useUrlState";
import { CountContext } from "@/contexts/AddBtnContext";
import { useRouter } from "next/navigation";
import Link from "next/link";


export default function SizePicker({ sizes, translation, product, variant, setVariant, combinations }: any) {
  const [activeSize, setActiveSize] = useState(sizes?.[0]?.size || null);
  const { active, setActive } = useUrlState("size");
  const { setVarient } = useContext(CountContext);
  const router = useRouter()

  console.log(sizes)
  const handleSizeClick = (size: any) => {
    // setActive(size?.size?._id);
    // setActiveSize(size);
    // setVarient(size);
    console.log(combinations[variant?.color][size?._id])
    router.replace(`/product/${combinations[variant?.color][size?._id]}`, {
      scroll: false
    })
  };

  // useEffect(() => {
  //   if (sizes?.length)
  //     handleSizeClick(sizes?.[0]);
  // }, [sizes]);

  const sortedSizes = [...sizes].sort((a, b) => {
    // const order = ["S", "M", "L", "XL", "XXL"];
    // return order.indexOf(a?.size?.name) - order.indexOf(b?.size?.name);
    return a?.position - b?.position;
  }).filter((size: any) => size?._id != "all");

  // useEffect(() => {
  //   if (sizes) {
  //     setVarient(sizes?.[0] || null);
  //   }
  // }, []);

  return (
    sortedSizes.length > 0?
    <div className="block-three">
      <span>{translation?.size || "Size"}</span>
      <div className="block-three_sizes">
        <ul>
          {sortedSizes.map((option: any, index: number) => {
            let isAvailable = combinations[variant?.color][option?._id] ? true: false;
            return (
              <li
                style={{
                  cursor: isAvailable ? "pointer" : "not-allowed",
                  opacity: isAvailable ? 1 : 0.4,
                  backgroundColor:  "#ededed",
                  position: "relative"
                }}
                title={isAvailable ? option?.name : "Size not available for current variant"}
                key={option?._id || index}
                className={`${product.stock === 0 && "out-of-stock"} ${product?.size?._id === option?._id
                  ? "isActive"
                  : ""

                  }`}
              >
                <Link replace={true} style={{ color: "inherit", pointerEvents: isAvailable ? "auto" : "none", }} scroll={false} href={`/product/${combinations[variant?.color][option?._id]}`}>{option?.name}</Link>
              </li>
            )
          })}
        </ul>
      </div>
    </div>
    : ""
  );
}
