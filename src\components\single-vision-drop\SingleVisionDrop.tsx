import { useContext, useEffect, useRef, useState } from "react";
import "./single-vision-drop.scss";
import Modal from "react-bootstrap/Modal";
import Image from "next/image";
import { SubmitHandler, useForm } from "react-hook-form";
import { toast } from "sonner";
import api from "@/config/axios.interceptor";
import { QueryClient, useQuery } from "@tanstack/react-query";
import { endpoints } from "@/config/apiEndpoints";
import Link from "next/link";
import EnterManuallyModal from "../enter-manually-modal/EnterManuallyModal";
import Button from "react-bootstrap/Button";
import UploadPhoto from "../upload-photo/UploadPhoto";
import SelectPreferenceModal from "../select-preference/SelectPreferenceModal";
import { sortPower } from "@/lib/utils/utils";
import { TranslationContext } from "@/contexts/Translation";
import { useLocaleContext } from "@/contexts/LocaleProvider";

type PowerType = {
  leftSph: string;
  leftCyl: string;
  leftAxis: string;
  leftAdd: string;
  rightSph: string;
  rightCyl: string;
  rightAxis: string;
  rightAdd: string;
  pd: string;
};

function SingleVisionDrop({
  show,
  handleClose,
  lensData,
  handleLensData,
  setStep,
  setSum,
  sum,
  back,
  handleClick,
  handleSkipClick,
  successData,
  handleSuccessData,
  mobile,
  product
}: any) {
  const { currentLocale:locale } = useLocaleContext()
  const [prescriptionType, setPrescriptionType] = useState(
    lensData?.prescriptionType || ""
  );
  const [file, setFile] = useState<any>(lensData?.prescriptionType === "file"? lensData?.prescription: null);
  const [fileName, setFileName] = useState<any>(lensData?.prescriptionType === "file"? lensData?.prescription: null);
  const [isUploading, setIsUploading] = useState(false);
  const defaultFormRef: any = useRef();

  const [showUploadModal, setShowUploadModal] = useState(lensData?.prescriptionType === "file"? true: false);
  const [showModal, setShowModal] = useState(false);
  const handleShowUploadModal = () => {
    setShowUploadModal(true);
    setPrescriptionType("file");
  };
  const handleCloseUploadModal = () => {
    setShowUploadModal(false);
    setPrescriptionType("");
  };

  const {translation: {productPage, popup}} = useContext(TranslationContext)
  const {currencyCode} = useLocaleContext()

  // const {
  //   data: powersData,
  //   isLoading,
  //   error,
  // } = useQuery({
  //   queryKey: ["lens-powers", lensData?.brand],
  //   queryFn: () => {
  //     return api
  //       .get(`${endpoints.lensPowers}/${lensData?.brand}`)
  //       .then((res) => {
  //         if (res.status === 200) {
  //           return {
  //             sph: res.data?.result?.sph?.sort(sortPower),
  //             cyl: res.data?.result?.cyl?.sort(sortPower),
  //             add: res.data?.result?.add?.sort(sortPower),
  //             axis: res.data?.result?.axis?.sort((a:any, b:any)=> a.name - b.name),
  //           };
  //         } else {
  //           return [];
  //         }
  //       });
  //   },
  // });

  const powersData:any = {
    sph: product?.sphValues?.sort(sortPower),
    cyl: product?.cylValues?.sort(sortPower),
    axis: product?.axisValues?.sort((a: any, b: any) => a.name - b),
    add: product?.addValues?.sort(sortPower),
  }

  console.log(product.sphValues)

  const getPrice = (id: string | number, type: string) => {
    return powersData?.[type]?.find((power: any) => power._id == id)?.price;
  };

  const {
    register,
    handleSubmit,
    setFocus,
    getValues,
    setValue,
    trigger,
    watch,
    formState: { errors },
  } = useForm<PowerType>({ defaultValues: lensData.prescription });

  useEffect(() => {
    const subscription = watch((value:any, { name, type }:any) =>{
      if(true){
        let val = 0;
        let price = 0
        Object.keys(value).forEach((key) => {
          if(value[key]?.length > 0){
            switch (key) {
              case "leftSph":
              case "rightSph":
                price = getPrice(value[key], "sph")
                val += price
                break;
              
              case "leftCyl":
              case "rightCyl":
                price = getPrice(value[key], "cyl")
                val += price
                break;
              
              case "leftAxis":
              case "rightAxis":
                price = getPrice(value[key], "axis")
                val += price
                break;
              
              case "leftAdd":
              case "rightAdd":
                price = getPrice(value[key], "add")
                val += price
                break;

              default: break;
            }
          }
        })
        setSum(lensData?.vision, val)
      }
    })
    return () => subscription.unsubscribe()
  }, [watch, ])

  const onSubmit: SubmitHandler<PowerType> = (data) => {
    handleLensData({
      ...lensData,
      prescription: data,
      prescriptionType: "manual",
    });
    if (lensData?.vision === "progressive") {
      setSum(
        "progressive",
        getPrice(data.leftSph, "sph") +
        getPrice(data.leftCyl, "cyl") +
        getPrice(data.leftAxis, "axis") +
        getPrice(data.rightSph, "sph") +
        getPrice(data.rightCyl, "cyl") +
        getPrice(data.rightAxis, "axis") +
        getPrice(data.rightAdd, "add") 
      );
    } else {
      setSum(
        "single",
        getPrice(data.leftSph, "sph") +
        getPrice(data.leftCyl, "cyl") +
        getPrice(data.leftAxis, "axis") +
        getPrice(data.rightSph, "sph") +
        getPrice(data.rightCyl, "cyl") +
        getPrice(data.rightAxis, "axis") +
        getPrice(data.rightAdd, "add") 
      );
    }

    // if (lensData?.vision === "progressive") {
    //   api
    //     .post(endpoints.buyWithLens, {
    //       ...lensData,
    //       prescription: data,
    //       sum:
    //         getPrice(data.leftSph, "sph") +
    //         getPrice(data.leftCyl, "cyl") +
    //         getPrice(data.leftAxis, "axis") +
    //         getPrice(data.rightSph, "sph") +
    //         getPrice(data.rightCyl, "cyl") +
    //         getPrice(data.rightAxis, "axis"),
    //     })
    //     .then((res) => {
    //       handleSuccessData({
    //         ...successData,
    //         title:
    //           res?.data?.result?.message ||
    //           "Our experts will contact you soon.",
    //       });
    //       queryClient.invalidateQueries({ queryKey: ["cart"] });
    //       queryClient.invalidateQueries({ queryKey: ["cart", "counts"] });
    //       setStep("prescription");
    //     })
    //     .catch((err) => {
    //       toast.error(err.response.data.message);
    //     });
    // }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files ? event.target.files[0] : null;
    if (!selectedFile) {
      return;
    }

    const allowedFileTypes = [
      "image/png",
      "image/jpg",
      "image/jpeg",
      "application/pdf",
    ];
    const maxFileSize = 2 * 1024 * 1024;

    if (!allowedFileTypes.includes(selectedFile.type)) {
      toast.error(productPage?.fileType ?? "Allowed file types are PNG, JPG, JPEG, PDF");
      return;
    }

    if (selectedFile.size > maxFileSize) {
      toast.error(productPage?.fileSize ?? "File size must be less than 2 MB");
      return;
    }

    setIsUploading(true);
    setFileName(selectedFile.name);

    const formData = new FormData();
    formData.append("file", selectedFile);

    api
      .post("/media-upload", formData)
      .then((res) => {
        setFile(res.data?.fileUrl);
        toast.success(popup?.fileUploadSuccess ?? "File uploaded successfully");
        setIsUploading(false);
      })
      .catch((err) => {
        toast.error(err.response.data.message);
        setIsUploading(false);
      });
  };


  const onNavigatePrecription = async () => {
    const result = await trigger(); // trigger validation
    if (result && Object.keys(errors).length === 0) {
      setStep("prescription");
    }
  };

  const handleShowCoating = async () => {
    if (prescriptionType === "manual") {
      defaultFormRef?.current?.requestSubmit();
      await onNavigatePrecription();
    } else {
      if (file) {
        handleLensData({
          ...lensData,
          prescription: file,
          prescriptionType: "file",
        });
        setTimeout(() => {
          // if (lensData?.vision === "progressive") {
          //   api
          //     .post(endpoints.buyWithLens, {
          //       ...lensData,
          //       prescription: file,
          //       prescriptionType: "file",
          //     })
          //     .then((res) => {
          //       handleSuccessData({
          //         ...successData,
          //         title:
          //           res?.data?.result?.message ||
          //           "Our experts will contact you soon.",
          //       });
          //       queryClient.invalidateQueries({ queryKey: ["cart"] });
          //       queryClient.invalidateQueries({ queryKey: ["cart", "counts"] });
          //       setStep("prescription");
          //     })
          //     .catch((err) => {
          //       toast.error(err.response.data.message);
          //     });
          // }
          setStep("prescription");
        }, 600);
      } else {
        toast.error("Please upload prescription");
      }
    }
  };

  const handleCloseModal = () => {
    setShowModal(false);
  };

  const [showEnterManually, setShowEnterManually] = useState(lensData?.prescriptionType === "manual"? true: false);
  const handleCloseEnterManually = () => {
    setShowEnterManually(false);
    setPrescriptionType("");
  };
  const handleShowEnterManually = () => {
    setShowEnterManually(true);
    setPrescriptionType("manual");
  };

  return (
    <>
      <Modal
        show={show}
        onHide={handleClose}
        backdrop="static"
        keyboard={false}
        className="single-vision-drop"
        scrollable={true}
      >
        {showEnterManually ? (
          <EnterManuallyModal
            watch={watch}
            show={show}
            onClick={handleClose}
            handleCloseEnterManually={handleCloseEnterManually}
            powerData={powersData}
            lensData={lensData}
            errors={errors}
            register={register}
            onSubmit={onSubmit}
            handleSubmit={handleSubmit}
            defaultFormRef={defaultFormRef}
            setValue={setValue}
            pdValue={getValues("pd")}
          />
        ) : showUploadModal ? (
          <UploadPhoto
            show={showUploadModal}
            onClick={handleClose}
            handleCloseUploadModal={handleCloseUploadModal}
            handleFileUpload={handleFileUpload}
            fileName={fileName}
          />
        ) : (
          <div className="modal-wrap">
            <Modal.Header closeButton>
              <h2>
                <Image
                  quality={100}
                  priority
                  onClick={() => {
                    back("prescription");
                    setShowUploadModal(false);
                  }}
                  src="/images/common/backarrow.svg"
                  width={40}
                  height={40}
                  className=" cursor-pointer"
                  alt="back arrow"
                />
                {/* {lensData.vision === "single" */}
                  {/* ? (productPage?.uploadPrescription ?? "Upload Prescription") */}
                  {/* : "Reading"} */}
                  {(productPage?.uploadPrescription ?? "Upload Prescription")}
              </h2>
            </Modal.Header>
            <Modal.Body>
              <div
                className="single-vision-drop_box"
                onClick={handleShowUploadModal}
              >
                <div className="single-vision-drop_boxinner">
                  <Image
                    quality={100}
                    priority
                    src="/images/modal/document.svg"
                    width={82}
                    height={82}
                    alt="images"
                  />
                  <div>
                    <h5>{productPage?.uploadPhoto ?? "Upload Photo"}</h5>
                    <p>{productPage?.uploadPhotoTxt ?? "Upload a file or photo of your prescription"}</p>
                  </div>
                </div>
              </div>
              <div
                className="single-vision-drop_box"
                onClick={handleShowEnterManually}
              >
                <div className="single-vision-drop_boxinner">
                  <Image
                    quality={100}
                    priority
                    src="/images/modal/wirte.svg"
                    width={82}
                    height={82}
                    alt="images"
                  />
                  <div>
                    <h5>{productPage?.enterManually ?? "Enter Manually"}</h5>
                    <p>{productPage?.enterManuallyTxt ?? "Type your prescription details manually"}</p>
                  </div>
                </div>
              </div>

              {/* <div className="single-vision-drop_or">
                <Image
                  quality={100}
                  priority
                  src="/images/modal/or.svg"
                  width={500}
                  height={500}
                  alt=""
                />
              </div>

              <div className="single-vision-drop_box" onClick={handleSkipClick}>
                <div className="single-vision-drop_boxinner">
                  <Image
                    quality={100}
                    priority
                    src="/images/modal/skip.svg"
                    width={82}
                    height={82}
                    alt="images"
                  />
                  <div>
                    <h5>Skip for Now</h5>
                    <p>
                      You can skip this step and provide your prescription after
                      checkout
                    </p>
                  </div>
                </div>
              </div> */}

              <p className="cta" style={{direction: locale.includes("en")? "ltr": "rtl"}}>
                {productPage?.buyWithLensText ?? "Not sure what to select? Call"}{" "}
              <a href={`tel:${mobile}`}>
              &#x200E;{mobile}
              </a>
              </p>
            </Modal.Body>
          </div>
        )}

        <Modal.Footer>
          <h5>
            Subtotal:{currencyCode + " "} <span>{sum}</span>
          </h5>
          <button
            id="upload-btn"
            className={prescriptionType === "" ? "disabled" : ""}
            disabled={(prescriptionType === "" || isUploading) && true}
            type="submit"
            onClick={handleShowCoating}
          >
            {productPage?.next ?? "Next"}
          </button>
        </Modal.Footer>
      </Modal>
      {/* <SelectPreferenceModal
        showModal={showModal}
        handleClose={handleCloseModal}
      /> */}
    </>
  );
}

export default SingleVisionDrop;
