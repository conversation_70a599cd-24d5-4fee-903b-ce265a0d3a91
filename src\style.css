.bestseller-dropdown {
    min-width: 189px !important;
    margin-left: -36px !important;
    margin-top: 6px !important;

    @media (max-width: 575.98px) {
        margin-left: 0px !important;

        .dropdown-item {
            &:hover {
                background-color: #000000;
            }
        }
    }
}

.app.rtl {
    direction: rtl;
}

@media (max-width: 575.98px) {
    input {
        font-size: 16px !important;
    }
}

.hide-scroll::-webkit-scrollbar {
    display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.hide-scroll {
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
}

/* @font-face {
    font-family: 'Dirham';
    src: url('fonts/Currency.woff2') format('woff2'),
        url('fonts/Currency.woff') format('woff');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
} */