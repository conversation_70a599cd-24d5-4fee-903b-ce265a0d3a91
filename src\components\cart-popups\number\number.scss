.checkout-popup {
  &.modal {
    padding: 0 !important;

    .modal-dialog {
      max-width: 68.3rem;
      width: 100%;
      height: 100vh;
      margin: 0;
      margin-left: auto;
    }

    .modal-header {
      padding: 0;
      border: none;

      .btn-close {
        background-image: url(../../../../public/images/common/close.png);
        width: 4.8rem;
        height: 4.8rem;
        background-position: center;
        background-repeat: no-repeat;
        background-size: contain;
        opacity: 1;
        padding: 0;

        @media (max-width: 575.98px) {
          width: 2.8rem;
          height: 2.8rem;
        }

        &:focus {
          box-shadow: none;
          outline: none;
          border: none;
        }
      }
    }

    .modal-content {
      padding: 4rem 4.5rem 0rem 16.7rem;
      border: none;
      background: #fff;
      border-radius: 0;
      height: 100vh;

      @media (max-width: 575.98px) {
        padding: 5.4rem 2rem 0 2rem;
      }
    }

    .modal-body {
      padding: 0;
      display: flex;
      justify-content: center;
      flex-direction: column;

      h2 {
        line-height: 3.6rem;
        color: #242731;
        font-weight: 700;
        max-width: 40.4rem;
        width: 100%;
        text-align: left;
      }

      .button {
        margin-top: 6rem;
        border-radius: 6rem;
        background: #000;
        color: #fff;
        font-size: 1.5rem;
        font-weight: 500;
        line-height: 2.4rem;
        border: none;
        width: 15rem;
        height: 4.5rem;
        align-self: flex-start;

        &:disabled{
          opacity: 0.4;
        }

        @media (max-width: 575.98px) {
          width: 100%;
        }
      }

      .checkout-popup-input {
        margin-top: 3.2rem;

        .countrycode {
          display: flex;
          column-gap: 1.6rem;
          align-items: baseline;

          .countrycode-icon {
            position: relative;

            &::after {
              content: "";
              background-image: url("../../../../public/images/common/Icon.png");
              width: 2.4rem;
              height: 2.4rem;
              position: absolute;
              right: -13px;
            }
          }
        }

        .css-lkh0o5-menu {
          margin-top: 29px !important;
        }

        label {
          color: #242426;
          font-size: 1.4rem;
          font-weight: 400;
          line-height: 2rem;
          padding-bottom: 0.8rem;
        }

        #countryCode {
          border: none;
          border-bottom: 1px solid #e2e4e5;
          padding: 0 0.6rem .8rem;
          padding-bottom: 0.8rem;
          appearance: none;
          line-height: 2.8rem;
          max-width: 90px;

          &:focus-visible {
            outline: none;
            border-bottom: 1px solid #e2e4e5;
          }
        }

        .select-container {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;

          .drop-item {
            img {
              width: 30px;
              margin-right: 10px;
            }
          }
        }

        .react-select {
          width: 100%;
          max-width: 90px;
          top: 25px;
          left: 0;
          z-index: 10;
          opacity: 0;
        }

        input {
          width: 100%;
          color: #242426;
          font-size: 1.8rem;
          font-weight: 400;
          line-height: 2.8rem;
          border: none;
          border-bottom: 1px solid #e2e4e5;
          padding: 0 1.5rem;
          padding-bottom: 0.8rem;

          &:focus-within {
            outline: none;
            border-bottom: 1px solid #e2e4e5;
          }

          &::placeholder {
            color: #cacaca;
            font-size: 1.8rem;
            font-weight: 400;
          }
        }

        input[type="number"]::-webkit-inner-spin-button,
        input[type="number"]::-webkit-outer-spin-button {
          -webkit-appearance: none;
          -moz-appearance: none;
          appearance: none;
        }
      }
    }
  }
}

.app.rtl {
  .checkout-popup.modal .modal-content {
    padding: 4rem 16.7rem 0rem 4.5rem;
  }

  .checkout-popup.modal .modal-body h2 {
    text-align: right;
  }

  .checkout-popup.modal .modal-body .checkout-popup-input input {
    text-align: right;
  }

  .checkout-popup.modal .modal-body .checkout-popup-input .countrycode .countrycode-icon::after {
    left: -13px;
    right: auto;
  }
}