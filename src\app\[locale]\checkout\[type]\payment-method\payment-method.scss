.pay-method {
  position: relative;

  &_wrapper {
    display: flex;
    column-gap: 3.4rem;

    @media (max-width: 991.98px) {
      flex-direction: column;
    }
  }

  &_left {
    width: 65%;

    @media (max-width: 991.98px) {
      width: 100%;
    }

    h4 {
      margin-top: 6.4rem;
      color: #000;
      font-size: 2.4rem;
      font-weight: 500;

      @media (max-width: 575.98px) {
        margin-top: 3.3rem;
        font-size: 2rem;
      }
    }

    button {
      width: 16.3rem;
      height: 5.6rem;
      border-radius: 6rem;
      background: #000;
      border: 0;
      color: #fff;
      font-size: 1.5rem;
      font-weight: 500;
      margin-top: 6rem;

      @media (max-width: 575.98px) {
        width: 100%;
        margin-top: 5rem;
      }
    }
  }

  &_right {
    width: 35%;
    background: #f2f4f9;
    padding: 3.5rem 11.6rem 11rem 4.4rem;
    position: absolute;
    right: 0;
    top: -64px;

    @media (max-width: 991.98px) {
      position: unset;
      width: 100%;
      margin-top: 10.5rem;
    }

    @media (max-width: 767.98px) {
      margin-top: 4.7rem;
    }

    @media (max-width: 575.98px) {
      padding: 2.5rem 2rem 5.6rem 2.2rem;
      margin-bottom: 1.9rem;
      margin-top: 4rem;
    }

    h5 {
      color: #000;
      font-size: 2.4rem;
      font-weight: 500;
      margin-bottom: 2rem;

      @media (max-width: 575.98px) {
        font-size: 2rem;
        line-height: 2.5rem;
        margin-bottom: 2.5rem;
      }
    }
  }

  &_box {
    border-radius: 2rem;
    border: 1px solid #ebebeb;
    background: #fdfdfe;
    padding: 2.5rem;
    margin-top: 2rem;

    @media (max-width: 575.98px) {
      margin-top: 3.3rem;
      padding: 2.2rem 2.5rem 3.3rem 2.4rem;
    }
  }

  &_radio {

    // &:last-child {
    &:not(:first-child) {
      border-top: 1px solid #e4e4e4;
      margin-top: 2.3rem;
      padding-top: 2rem;
    }

    // }

    label {
      cursor: pointer;
      color: #242426;
      font-size: 1.6rem;
      font-style: normal;
      font-weight: 600;
      line-height: 2rem;
      display: flex;
      align-items: center;
      gap: 1rem;
      display: grid;
      grid-template-columns: auto 1fr;

      @media (max-width: 575.98px) {
        font-size: 1.5rem;
      }
    }

    input {
      appearance: none;
      width: 1.7rem;
      height: 1.7rem;
      border-radius: 50%;
      border: 1px solid #000;
      position: relative;

      &:checked {
        position: relative;

        &::after {
          content: "";
          width: 1rem;
          height: 1rem;
          background-color: #000;

          position: absolute;
          border-radius: 50%;
          left: 0.247rem;
          top: 0.247rem;
        }
      }
    }

    img {
      width: 100px;
      object-fit: contain;
      margin: auto 0;

      @media (max-width: 575.98px) {
        width: 80px;
      }
    }

    span {
      display: block;
      color: #808080;
      font-size: 1.5rem;
      font-weight: 400;
      line-height: 1.8rem;
      margin-left: 3rem;

      @media (max-width: 575.98px) {
        font-size: 1.4rem;
      }
    }

    ul {
      display: flex;
      column-gap: 0.4rem;
      margin-top: 1.8rem;
      margin-left: 3rem;
    }
  }

  &_form {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-top: 3rem;
    row-gap: 4rem;
    padding: 0 3rem;

    @media (max-width: 575.98px) {
      flex-direction: column;
      row-gap: 1.5rem;
      padding: 2rem 0 2rem 2rem;
    }
  }

  &_inputs {
    width: calc(50% - 3.7rem);

    @media (max-width: 575.98px) {
      width: 100%;
    }

    label {
      display: block;
      color: #808080;
      font-size: 1.3rem;
      font-weight: 400;
      line-height: 2rem;
      padding-bottom: 0.8rem;
    }

    input {
      border: none;
      border-bottom: 1px solid #e2e4e5;
      color: #242426;
      font-size: 1.8rem;
      font-weight: 400;
      padding-bottom: 0.8rem;
      padding-left: 1.6rem;
      width: 100%;

      @media (max-width: 575.98px) {
        font-size: 1.6rem;
      }

      &::placeholder {
        color: #242426;
        font-size: 1.8rem;
        font-weight: 400;

        @media (max-width: 575.98px) {
          font-size: 1.6rem;
        }
      }

      &:focus-within {
        outline: none;
        border-bottom: 1px solid #e2e4e5;
      }
    }
  }
}

.paynow-btn {
  border-radius: 6rem;
  background: #000;
  padding: 1.7rem 0 1.7rem 0;
  width: 100%;
  color: #fff;
  font-size: 1.5rem;
  font-weight: 500;
  display: inline-block;
  text-align: center;
  margin-top: 7.3rem;
  text-decoration: none;
  transition: all 500ms cubic-bezier(0.19, 1, 0.22, 1);
  margin-left: 0;

  &:hover {
    background-color: transparent;
    color: #000;
    border: 1px solid;
  }

  @media (max-width: 575.98px) {
    margin-top: 4.6rem;
  }
}

.app.rtl {
  .pay-method_right {
    right: auto;
    left: 0;
    padding: 3.5rem 4.4rem 11rem 11.6rem;

    @media (max-width: 575.98px) {
      padding: 2.5rem 2.2rem 5.6rem 2rem;
    }
  }
}

.cart_order-coupon {
  background-color: transparent !important;
  padding: 0 !important;

  button {
    width: 100%;
    border: none;
    padding: 0;
    background-color: transparent;
    display: flex;
    border-radius: 0.8rem;
    border: 1px solid #e4e4e4;
    background: #fff;
    padding: 1.3rem 1.3rem 1.3rem 1.5rem;
    align-items: center;
    column-gap: 1.5rem;
  }

  span {
    color: #000;
    text-align: center;
    font-size: 1.6rem;
    font-weight: 500;
  }

  img {
    width: 2.4rem;
    height: 2.4rem;
  }

  .arrow {
    width: 1.4rem;
    height: 1.4rem;
    margin-left: auto;
  }
}

.cart_order-code {
  // padding: 4.2rem 12rem 6.5rem 4.6rem;
  padding: 4.2rem 2rem 6.5rem 4.6rem;
  background-color: transparent !important;

  @media (max-width: 1199.98px) {
    padding: 3rem;
  }

  @media (max-width: 575.98px) {
    padding: 1rem 2rem 4.5rem 2.2rem;
  }

  h5 {
    color: #000;
    font-size: 2.2rem;
    font-weight: 500;
    line-height: 2.7rem;

    @media (max-width: 575.98px) {
      font-size: 2rem;
    }
  }

  p {
    color: #949494;
    font-size: 1.6rem;
    font-weight: 500;
    margin-top: 0.3rem;
    line-height: 2rem;

    @media (max-width: 575.98px) {
      font-size: 1.3rem;
      line-height: 1.6rem;
    }
  }

  label {
    color: #808080;
    font-size: 1.3rem;
    font-weight: 400;
    line-height: 2rem;
    padding-bottom: 0.8rem;

    @media (max-width: 575.98px) {
      margin-top: 2.5rem;
    }
  }

  button {
    color: #000;
    font-size: 1.7rem;
    font-weight: 600;
    padding: 0;
    border: 0;
    background-color: transparent;
    text-decoration-line: underline;
    min-width: fit-content;
    margin-left: 2.9rem;

    &:disabled {
      cursor: not-allowed;
    }

    @media (max-width: 575.98px) {
      font-size: 1.5rem;
      text-align: end;
    }
  }

  input {
    color: #242426;
    text-transform: uppercase;
    font-size: 1.8rem;
    font-weight: 400;
    line-height: 2.8rem;
    border: none;
    background-color: transparent;
    border-bottom: 1px solid #e2e4e5;
    padding-left: 1.5rem;
    padding-bottom: 0.8rem;
    width: 100%;

    @media (max-width: 575.98px) {
      font-size: 1.4rem;
    }

    &:focus-within {
      outline: none;
      border-bottom: 1px solid #e2e4e5;
    }

    &::placeholder {
      color: #e0e0e0;
      font-size: 1.8rem;
      font-weight: 400;

      @media (max-width: 575.98px) {
        font-size: 1.4rem;
      }
    }
  }

  .checkout-btn {
    border-radius: 6rem;
    background: #000;
    padding: 1.7rem 0 1.7rem 0;
    width: 100%;
    color: #fff;
    font-size: 1.5rem;
    font-weight: 500;
    display: inline-block;
    text-align: center;
    margin-top: 7.3rem;
    text-decoration: none;
    transition: all 500ms cubic-bezier(0.19, 1, 0.22, 1);
    margin-left: 0;

    &:hover {
      background-color: transparent;
      color: #000;
      border: 1px solid;
    }

    @media (max-width: 575.98px) {
      margin-top: 4.6rem;
    }
  }
}

.no-arrow::-webkit-inner-spin-button,
.no-arrow::-webkit-outer-spin-button {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  margin: 0;
}

.app.rtl {
  .cart_order-coupon {
    .arrow {
      transform: rotate(180deg);
    }
  }
}