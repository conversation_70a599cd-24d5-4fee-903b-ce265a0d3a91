.block-three_sizes {
  ul {
    display: flex;
    align-items: center;
    gap: 12px;
    padding-top: 5px;
    flex-wrap: wrap;
    li {
      a{
        padding: 7px 12px;
        display: block;
      }
      cursor: pointer;
      // padding: 7px 12px;
      border-radius: 25px;
      background: #ededed;
      font-size: 16px;
      font-weight: 600;
      min-width: 56px;
      text-align: center;
      &.out-of-stock {
        color: #d5d6d9;
        border: 1px solid #d5d6d9;
        cursor: default;
        font-weight: 700;
        outline: none;
        overflow: hidden;
        position: relative;
        &::after {
          position: absolute;
          content: "";
          top: 50%;
          left: 0;
          width: 100%;
          height: 1px;
          background-color: #d5d6d9;
          -webkit-transform: rotate(-45deg);
          transform: rotate(-45deg);
        }
      }
      &:not(.isActive):hover {
        background-color: #f8f8f8;
        outline: 1px solid #e3e3e3;
      }
      &.isActive {
        border: 1px solid #000;
      }
      &:not(.isActiveImg):hover {
        background-color: #f8f8f8;
        outline: 1px solid #e3e3e3;
      }
      &.isActiveImg {
        border: 1px solid #000;
      }
    }
  }
}
