"use client"

import LogoAnimation from "@/components/LogoAnimation/LogoAnimation";
import "./listing.scss";
import ProductCard from "@/components/product/product-card/ProductCard";
import { Spinner } from "react-bootstrap";
import EmptyState from "@/components/empty-states/EmptyState";
import { InView } from "react-intersection-observer";
import FilterChips from "../filter-chips/FilterChips";
import React, { useContext, useEffect } from "react";
import { sendGTMEvent } from "@next/third-parties/google";
import { AuthContext } from "@/contexts/AuthProvider";
import { createHmac } from "crypto";
import { TranslationContext } from "@/contexts/Translation";

function Listing({
  className,
  data,
  isFetching,
  hasNextPage,
  isFetchingNextPage,
  fetchNextPage,
  activeFilters,
  filters,
  isVirtualTry,
  slug
}: any) {

  const { userProfile } = useContext(AuthContext);

  const { translation: { other, productListing } }: any = useContext(TranslationContext)

  useEffect(() => {

    if (data?.pages?.length > 0) {
      sendGTMEvent({ ecommerce: null })
      let eventData:any = {
        event: "view_item_list",
        ecommerce: {
          item_list_id: slug,
          item_list_name: slug,
          items: data.pages.flatMap((item:any, page: number)=>(
            item.products.map((product:any, index: number)=>{
              let savings: undefined | number;
              let price;
              if(product?.offerPrice){
                savings = product.offerPrice - product.price;
                price = product.offerPrice;
              }else{
                price = product.price;
              }
              return {
                item_id: product?.sku,
                item_name: product?.name,
                brand: product?.brand,
                discount: savings,
                index: (page * 15) + index,
                item_brand: product?.brand,
                item_category: product?.category?.[0]?.name,
                item_category2: product?.category?.[1]?.name,
                item_variant: product?.color?.name,
                price,
                quantity: 1,
              }
            })
          ))
        }
      }
      if(userProfile){
        const mobile = createHmac('sha256', '123').update(userProfile?.mobile).digest('hex')
        let email = null;
        if(userProfile?.email) email = createHmac('sha256', '123').update(userProfile?.email).digest('hex')
        eventData.user = {
          mobile,
          email,
          user_id: userProfile?._id
        }
      }
      sendGTMEvent(eventData)
    }

  }, [data])
  return (
    <>
      <div className={`list-items ${className}`}>
        <FilterChips activeFilters={activeFilters} allFilters={filters} />
        <div className="list-items_wrapper">
          <div className="list-items_product">
            {data?.pages?.map((page: any, index: number) =>
              <React.Fragment key={index}>
                {page?.products?.map((product: any, i: number) => {
                  let key = Object.values(product).map((v: any) => v._id).join("-") + i;
                  return (
                  <ProductCard product={product} key={key} />
                )})}
              </React.Fragment>
            )}
          </div>
          {isFetching && (
            <div className="d-grid" style={{ placeItems: "center", padding: "60px 0" }}>
              <Spinner />
              {other?.loading ?? "Loading"}
            </div>
          )}
          {!isFetching && data?.pages?.[0]?.products?.length === 0 && (
            <div className="d-grid" style={{ placeItems: "center", padding: "60px 0" }}>
              <EmptyState icon="glasses" title={productListing?.noProductsFound ?? "No Products Found!"} />
            </div>
          )}
          {isFetchingNextPage && hasNextPage && (
            <div
              className="d-grid"
              style={{ placeItems: "center", padding: data?.pages?.length ? 0 : "150px 0" }}>
              <LogoAnimation />
            </div>
          )}

          {!isFetching && <div className="text-center">
            {data?.pages?.[0]?.products?.length !== 0 && (
              <button
                onClick={fetchNextPage}
                className="load-more btn"
                style={{ borderColor: "transparent" }}
                disabled={!hasNextPage || isFetchingNextPage}>
                {isFetchingNextPage
                  ? ""
                  : hasNextPage
                    ? ""
                    : (other?.nothingMoreToLoad ?? "Nothing more to load")}
              </button>
            )}

            <InView as="div" onChange={(inView, entry) => { if (inView && !isFetching) fetchNextPage(); }}></InView>
          </div>}
        </div>
      </div>
    </>
  );
}

export default Listing;
