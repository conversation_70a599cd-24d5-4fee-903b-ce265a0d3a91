"use client";
import React, { useRef, useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import Image from "next/image";
import { Autoplay, Pagination } from "swiper/modules";
import Link from "next/link";

export default function Slider(data: any) {
  return (
    <section className="gallery_slider">
      <Swiper
        className="mySwiper"
        pagination={true}
        modules={[Pagination, Autoplay]}
        autoplay={{ delay: 3000 }}
        loop={true}
        breakpoints={{
          // Define responsive breakpoints here
          320: {
            slidesPerView: 1,
            spaceBetween: 20,
          },
          600: {
            slidesPerView: 2,
          },
        }}>
        {data.data.map((items: any, index: any) => (
          <SwiperSlide key={index + items.id}>
            <Link href={`/brands/${items?.slug}`}>
              <Image quality={100} priority src={items.image?items.image: ""} width={1000} height={1000} alt="image" />
            </Link>
          </SwiperSlide>
        ))}
      </Swiper>
    </section>
  );
}
