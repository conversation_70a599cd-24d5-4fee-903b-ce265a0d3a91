"use client";

import { settings } from "firebase/analytics";
import { createContext, useContext, useState } from "react";

export const SettingsContext = createContext({} as any);

export const SettingsProvider = ({ children, data, store }: any) => {
  const [settings, setSettings] = useState<any>(data);
  const [currentStore, setCurrentStore] = useState<any>(store);

  return (
    <SettingsContext.Provider
      value={{
        settings,
        setSettings,
        currentStore,
        setCurrentStore,
      }}>
      {children}
    </SettingsContext.Provider>
  );
};

export function useSettingsContext() {
  const context: any = useContext(SettingsContext);
  if (!context) {
    throw new Error("useSettings must be used within a SettingsProvider");
  }
  const { settings, setSettings, currentStore } = context;
  return { settings, setSettings, currentStore }
}
