import Image from "next/image";

const FrameShapeData = [
  {
    image: "/images/product-listing/sp1.png",
    type: "Rectangle",
  },

  {
    image: "/images/product-listing/sp2.png",
    type: "Round",
  },

  {
    image: "/images/product-listing/sp3.png",
    type: "Square",
  },

  {
    image: "/images/product-listing/sp1.png",
    type: "Cat Eye",
  },

  {
    image: "/images/product-listing/sp2.png",
    type: "Hexagonal",
  },

  {
    image: "/images/product-listing/sp3.png",
    type: "Aviator",
  },

  {
    image: "/images/product-listing/sp1.png",
    type: "Wayfarer",
  },

  {
    image: "/images/product-listing/sp2.png",
    type: "Geometric",
  },

  {
    image: "/images/product-listing/sp3.png",
    type: "Oval",
  },
];

function FrameShape() {
  return (
    <>
      {FrameShapeData.map((items, index) => (
        <label
          className="brands-border"
          key={index}
          htmlFor={`frame-type-${index}`}
        >
          <input type="checkbox" id={`frame-type-${index}`} />
          <Image quality={100} priority src={items.image} width={64} height={31} alt="frame type" />
          <span>{items.type}</span>
        </label>
      ))}
    </>
  );
}

export default FrameShape;
