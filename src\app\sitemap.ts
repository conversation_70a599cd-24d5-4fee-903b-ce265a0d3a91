import type { MetadataRoute } from 'next'

const domain = process.env.NEXT_PUBLIC_DOMAIN as string

const urls = [
  "",
  "about",
  // "products/sunglasses",
  // "products/eyeglasses",
  // "products/contact-lens",
  // "products/sale",
  "brands",
  "insurance",
  "contact-us",
  "store-locator",
  "policy/privacy-policy",
  "policy/terms-and-conditions",
  "policy/shipping-policy",
  "policy/return-policy",
]

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {

  const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}slugs-for-sitemap`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    next: { revalidate: 1200 }
  });
  const data = await res.json();
  const result = data.result;
  result?.categories?.forEach((slug:any) => {
    urls.push(`products/${slug}`)
  })
  result?.products?.forEach((slug:any) => {
    urls.push(`product/${slug}`)
  })
  result?.brands?.forEach((slug:any) => {
    urls.push(`brands/${slug}`)
  })
  
  return [
    ...urls.map((url) => ({
      url: `${domain}/${url}`,
      lastModified: new Date(),
      alternates: {
        languages: {
          en: `${domain}/ae-en/${url}`,
          ar: `${domain}/ae-ar/${url}`,
        },
      },
    })),
  ]
}