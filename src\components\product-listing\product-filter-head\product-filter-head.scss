.filter-head {
  padding: 2rem 3.3rem 0rem 2rem;
  border-radius: 1.5rem;
  position: relative;

  &.active {
    border-radius: 1.5rem !important;

    .filter-head_flex {
      border-bottom: none;
    }

    .toggle-btn {
      transform: rotate(180deg);
      transition: 0.3s all ease;
    }
  }

  &_flex {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding-bottom: 2.3rem;

    button {
      border: none;
      background-color: transparent;
      padding: 0;
      color: #6b7280;
      font-size: 1.5rem;
      font-weight: 400;

      img {
        width: auto;
        height: auto;
        margin-right: 0.8rem;
      }
    }

    span {
      color: #000;
      font-size: 1.5rem;
      font-weight: 400;
      text-decoration-line: underline;

      &:hover {
        text-decoration: none;
      }
    }

    .toggle-btn {
      position: absolute;
      right: -11px;
      transform: rotate(0deg);
      transition: 0.3s all ease;

      img {
        margin: 0;
      }
    }
  }
}


.app.rtl {
  .filter-head_flex button img {
    width: auto;
    height: auto;
    margin-right: 0;
    margin-left: 0.8rem;
  }

  .filter-head {
    padding: 2rem 2rem 0rem 3.3rem;
  }

  .filter-head_flex .toggle-btn {
    left: -11px;
    right: auto;

    img {
      margin-left: 0 !important;
    }
  }
}