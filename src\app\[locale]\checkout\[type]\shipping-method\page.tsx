"use client";
import api from "@/config/axios.interceptor";

import CheckoutOrderSummary from "@/components/checkout-order-summary/CheckoutOrderSummary";
import React, { useContext, useEffect, useState } from "react";
import "./ship-method.scss";
import Link from "next/link";
import { CheckoutContext } from "@/contexts/CheckoutContext";
import { useQuery } from "@tanstack/react-query";
import { endpoints } from "@/config/apiEndpoints";
import { sendGTMEvent } from "@next/third-parties/google";
import { getCart, getSubscriptionSummary, getTryCart } from "@/lib/methods/cart";
import { TranslationContext } from "@/contexts/Translation";
import Select from "react-select";
import { useRouter } from "next/navigation";
import { calculateDistance } from "@/app/[locale]/store-locator/Map";
import { SettingsContext } from "@/contexts/SettingsProvider";
import { PiTruckThin, Pi<PERSON><PERSON>frontThin } from "react-icons/pi";
import { FaCircleCheck } from "react-icons/fa6";
import { useLocaleContext } from "@/contexts/LocaleProvider";

export default function Page({
  params,
}: {
  params: { id: string; type: string };
}) {
  const { boundingRect } = useContext(CheckoutContext);
  const { settings } = useContext(SettingsContext)
  const {
    selectedAddress,
    setShippingMethod,
    shippingMethod,
    setStoreId,
  } = useContext(CheckoutContext);
  const { translation } = useContext(TranslationContext);
  const router = useRouter();

  const [selectedStore, setSelectedStore] = useState<any>(null);
    const {  currencyCode } = useLocaleContext()


  const {
    data: shippingCharge,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["shipping-charge", selectedAddress],
    queryFn: () => {
      return api.post(`${endpoints.shippingCharge}`).then((res) => {
        if (res.status === 200) {
          return res.data?.result;
        } else {
          return [];
        }
      });
    },
  });

  const {
    data: storeData,
    isStoresLoading,
    storesError,
  }: any = useQuery({
    queryKey: ["stores"],
    queryFn: () => {
      return api.get(`${endpoints.stores}?clickAndCollect=true`).then((res) => {
        if (res.status === 200) {
          return res.data?.result;
        } else {
          return [];
        }
      });
    },
  });

  useEffect(() => {
    if (!selectedAddress) {
      router.push(`/checkout/${params.type}`)
    }
  }, [])

  useEffect(() => {
    if (navigator.geolocation && settings?.clickAndCollect) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const userLat = position.coords.latitude;
          const userLng = position.coords.longitude;

          let nearestStore: any = null;
          let minDistance = Infinity;

          storeData?.stores?.forEach((store: any) => {
            const distance = calculateDistance(
              userLat,
              userLng,
              store?.coordinates?.lat,
              store?.coordinates?.long
            );
            console.log(distance)
            if (distance < minDistance) {
              minDistance = distance;
              nearestStore = store;
            }
          });
          console.log(nearestStore)
          if (nearestStore) {
            setStoreId(nearestStore?._id);
            setSelectedStore({
              label: nearestStore?.name,
              value: nearestStore?._id,
              ...nearestStore
            }); // Store the nearest one in an array
          }
        },
        () => {
          alert('Location permission denied');
        }
      );
    }
  }, [storeData]);

  console.log(settings?.clickAndCollect)

  return (
    <div
      className="ship-method"
      style={{ minHeight: `${boundingRect?.height + 120}px` }}
    >
      <div className="container">
        <div className="ship-method_wrapper">
          <div className="ship-method_left">
            <h4>{translation?.cartPage?.shippingMethods ?? "Shipping Methods"}</h4>
            <div className="ship-method-container">
              <div className="ship-method_border" style={{ background: shippingMethod === "door" ? "#F2F4F9" : "none" }}>
                {/* <h5 className="bold">Arrives by Monday, Sep 20</h5>
              <div className="ship-method_flex"> */}
                <label htmlFor="door" className="ship-method_flex" style={{ cursor: "pointer", height: "100%" }}>
                  <div style={{ display: "flex", gap: "1rem", alignItems: "center", width: "100%", paddingBottom: "2rem", borderBottom: "1px solid #e4e4e4" }}>
                    <div style={{ display: "flex", gap: "1rem", width: "100%", alignItems: "center" }}>
                      <PiTruckThin size={60} />
                      <h5>{translation?.cartPage?.doorstepDelivery ?? "Doorstep Delivery"}</h5>
                    </div>
                    {shippingMethod == "door" && <FaCircleCheck size={30} color="green" />}
                  </div>
                  <div style={{ height: "100%", display: "flex", alignItems: "center", gap: "1rem", marginTop: "1.5rem" }}>

                    <p style={{ width: "100%" }}>
                      {translation?.cartPage?.doorstepDeliveryText
                        ?? "Additional fees may apply to COD payment methods"}
                    </p>
                    {/* <h6>
                      {shippingCharge?.data?.price && currencyCode}
                      <span className="ms-2">
                        {params?.type === "cart" ? shippingCharge?.data?.price : 0}
                        {params?.type === "cart" ? 0 : 0}
                      </span>
                    </h6> */}
                  </div>
                  <input type="radio" style={{ padding: 0, margin: 0, visibility: "hidden", width: "0", height: "0" }} checked={shippingMethod === "door"} onChange={() => setShippingMethod("door")} name="door" id="door" />
                </label>
              </div>
              {settings?.clickAndCollect && <div className="ship-method_border" style={{ background: shippingMethod === "click" ? "#F2F4F9" : "none" }}>
                <label htmlFor="click" className="ship-method_flex" style={{ cursor: "pointer", height: "100%" }}>
                  <div style={{ display: "flex", gap: "1rem", alignItems: "center", width: "100%", paddingBottom: "2rem", borderBottom: "1px solid #e4e4e4" }}>
                    <div style={{ display: "flex", gap: "1rem", width: "100%", alignItems: "center" }}>
                      <PiStorefrontThin size={60} />
                      <h5>{translation?.cartPage?.clickAndCollect ?? "Click & Collect"}</h5>
                    </div>
                    {shippingMethod == "click" && <FaCircleCheck size={30} color="green" />}
                  </div>
                  <div style={{ height: "100%", display: "flex", alignItems: "center", gap: "1rem", marginTop: "1.5rem" }}>

                    <p style={{ width: "100%" }}>
                      {translation?.cartPage?.clickAndCollectText
                        ?? "Collect from the nearest store"}
                    </p>
                    <h6>
                      {shippingCharge?.data?.price && currencyCode}
                      <span className="ms-2">
                        {/* {params?.type === "cart" ? shippingCharge?.data?.price : 0} */}
                        {/* {params?.type === "cart" ? 0 : 0} */}
                      </span>
                    </h6>
                  </div>
                  <input type="radio" style={{ padding: 0, margin: 0, visibility: "hidden", width: "0", height: "0" }} checked={shippingMethod === "click"} onChange={() => setShippingMethod("click")} name="door" id="click" />
                </label>
              </div>}
            </div>
            {shippingMethod === "click" && <div style={{ marginTop: "2rem", fontWeight: "500" }} className="">
              <p>Select pickup location</p>
              <Select
                className="select-container"
                // ref={emiratesRef}
                styles={{control: (base) => ({ ...base, borderRadius: "1rem", marginTop: "1rem", padding: ".5rem 1rem" })}}
                theme={(theme) => ({
                  ...theme,
                  borderRadius: 0,
                  colors: {
                    ...theme.colors,
                    // primary25: "#ccc",
                    // primary: "black",
                  },
                })}
                classNames={{
                  control: (state) => "react-select",

                  // dropdownIndicator: () => "d-none",
                  option: (state) =>
                    state.isSelected ? "option selected" : "option",
                  menu: () => "menu",
                }}
                formatOptionLabel={(option: any) => (
                  <div style={{ padding: ".6rem 0" }}>
                    <p style={{ margin: 0, fontSize: "1.5rem", fontWeight: "500", lineHeight: "1.7rem" }}>{option.label}</p>
                    <p style={{ margin: 0, fontSize: "1.4rem", lineHeight: "1.7rem" }}>{option.address}</p>
                  </div>
                )}
                value={selectedStore}
                options={storeData?.stores.map((store: any) => ({ label: store?.name, value: store?._id, ...store }))}
                onChange={(e: any) => { setStoreId(e.value); setSelectedStore(e) }}
              // placeholder={formFields?.selectEmirates ?? "Select Emirates"}
              />
            </div>}
            <Link href={`/checkout/${params.type}/payment-method`}>
              <button disabled={shippingMethod === "click" && !selectedStore} className="paynow-btn">{translation?.cartPage?.continueToPayment ?? "Continue to Payment"}</button>
            </Link>
          </div>

          <div className="ship-method_right">
            <CheckoutOrderSummary
              params={params}
              shippingCharge={
                // params?.type === "cart" ? shippingCharge?.data?.price || 0 : 0
                params?.type === "cart" ? 0 || 0 : 0
              }
            />
          </div>
        </div>
      </div>
    </div>
  );
}
