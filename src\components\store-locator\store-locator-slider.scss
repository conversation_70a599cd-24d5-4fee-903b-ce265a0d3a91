.locator-slider {
  padding-bottom: 6rem;

  @media (max-width: 575.98px) {
    padding-bottom: 2rem;
  }

  &_search {
    margin-top: 5rem;
    position: relative;

    @media (max-width: 575.98px) {
      margin-top: 2.5rem;
    }

    h4 {
      color: #000;
      font-size: 2.4rem;
      font-weight: 700;
      margin-bottom: 2.4rem;

      @media (max-width: 575.98px) {
        margin-bottom: 3.5rem;
      }
    }

    label {
      color: #808080;
      font-size: 1.3rem;
      font-weight: 400;
    }

    input {
      border: none;
      border-bottom: 1px solid #e2e4e5;
      width: 100%;
      padding: 8px 0;
      padding-left: 1.6rem;
      line-height: 2.8rem;

      &:focus-visible {
        border-bottom: 1px solid #e2e4e5;
        outline: none;
      }

      &::placeholder {
        color: #a7a7a7;
        font-size: 1.8rem;
        font-style: normal;
        font-weight: 400;
      }
    }

    button {
      position: absolute;
      right: 0;
      border: none;
      background: none;

      img {
        object-fit: contain;
      }
    }
  }

  .swiper {
    margin-top: 3rem;

    @media (max-width: 575.98px) {
      margin-top: 2.5rem;
    }

    .swiper-slide {
      border-radius: 2rem;
      border: 1px solid #ebebeb;
      background: #f2f4f9;
      padding: 3.3rem 3rem 3.1rem 3.9rem;
      height: auto;

      @media (max-width: 575.98px) {
        padding: 3.3rem 3rem 2.9em 2.9rem;
      }

      h5 {
        color: #1f2738;
        font-size: 1.9rem;
        font-weight: 700;
        text-transform: uppercase;

        @media (max-width: 575.98px) {
          font-size: 1.6rem;
        }
      }

      ul {
        margin-top: 1.7rem;
        max-height: 140px;
        overflow-y: auto;

        li {
          font-size: 1.7rem;
          color: #000;
          line-height: 2.1rem;

          @media (max-width: 575.98px) {
            font-size: 1.5rem;
            line-height: 1.9rem;
          }

          &:not(:last-child) {
            margin-bottom: 1.7rem;

            @media (max-width: 575.98px) {
              margin-bottom: 1rem;
            }
          }

          &:first-child {
            max-width: 18.7rem;
            width: 100%;
          }

          a {
            color: #000;
            font-size: 1.7rem;
            font-weight: 400;

            @media (max-width: 575.98px) {
              font-size: 1.5rem;
            }

            &:hover {
              text-decoration: underline;
            }
          }
        }
      }

      .primary-btn {
        background-color: #000;
        color: #fff;
        padding: 1.1rem 3.4rem;
        border: 1px solid #000;

        @media (max-width: 575.98px) {
          padding: 1.1rem 2.1rem;
          font-size: 1.4rem;
        }

        &:hover {
          background-color: transparent;
          color: #000;
        }
      }
    }

    .swiper-pagination {
      position: unset;
      background: #f2f4f9 !important;
      height: 0.3rem;
      margin-top: 3rem;

      @media (max-width: 575.98px) {
        margin-top: 2.6rem;
      }

      .swiper-pagination-progressbar-fill {
        border-radius: 0.4rem 0.4rem 0rem 0rem;
        background: #000;
        height: 0.3rem;
      }
    }
  }
}