import { useLocaleContext } from "@/contexts/LocaleProvider";

export default function LensPrice({ sum }: { sum: number | string }) {
  const {currencyCode} = useLocaleContext()
  return sum ? (
    <div
      style={{
        borderLeft: "1px solid black",
        paddingLeft: "20px",
        paddingBottom: "10px",
      }}>
      <p style={{ fontSize: "13px" }}>Lens Price:</p>
      <h4 style={{ lineHeight: 0.5, fontWeight: 600 }}>{currencyCode + " "} {sum}</h4>
    </div>
  ) : (
    <></>
  );
}
