.productslider {
  @media (max-width: 575.98px) {
    padding: 0 1.5rem;
  }

  .swiper-button-prev {
    &::after {
      background-image: url(../../../../public/images/home/<USER>
      background-repeat: no-repeat;
      font-size: 0;
      width: 3rem;
      height: 3rem;
      filter: invert(1);
    }
  }

  .swiper-button-next {
    &::after {
      background-image: url(../../../../public/images/home/<USER>
      background-repeat: no-repeat;
      font-size: 0;
      width: 3rem;
      height: 3rem;
      filter: invert(1);
    }
  }

  .productslider_wrapper{
    padding-left: 3rem;
  }

  &.product-slider-home {
    .productslider_wrapper .swiper-pagination {
      background: #CCD0DF;

      .swiper-pagination-progressbar-fill {
        background: #4E4E4E !important;
      }
    }
  }

  &_wrapper {
    .swiper {
      margin-top: 3.2rem;

      @media (max-width: 767.98px) {
        padding-top: 2.4rem;
        margin-top: 0;
      }
    }

    .swiper-slide {
      border-radius: 1.5rem;
      background: #fff;
      text-align: center;
      height: auto;
    }

    .swiper-pagination {
      position: unset;
      background: #616981;
      height: 0.1rem;
      margin-top: 3rem;

      @media (min-width: 1280px) {
        width: calc(100% - 3rem);
      }

      @media (max-width: 575.98px) {
        margin-top: 2.4rem;
      }

      .swiper-pagination-progressbar-fill {
        border-radius: 0.3rem 0.4rem 0rem 0rem;
        background: #FFFFFF !important;
        height: 0.2rem;

        @media (min-width: 1280px) {
          padding-right: 3rem;
        }
      }
    }
  }

  .product-card {

    img {
      max-width: 100%;
      height: 23rem;
      margin: 0 auto;
      margin-top: 0;
    }
  }
}

.loaderGrid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 10px;

  .productCardLoading {
    background-color: #4e4e4e;
    border-radius: 1.5rem;
    margin: 1rem;
    width: 100%;
    height: 360px;
  }
}

.app.rtl{
  .productslider{
    .productslider_wrapper{
      padding-left: 0 !important;
      padding-right: 3rem;
    }
  }
}