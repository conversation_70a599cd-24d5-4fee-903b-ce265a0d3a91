"use client"

import Image from 'next/image'
import React, { useContext } from 'react'
import "./my-cashbacks.scss"
import { TranslationContext } from '@/contexts/Translation'
import { useQuery } from '@tanstack/react-query'
import { getCashbacks } from '@/lib/methods/user'
import MyCashbacksLoading from './my-cashbacks-loading'
import { useLocaleContext } from '@/contexts/LocaleProvider'

export default function MyCashbacks() {

    const { data: cashbacks, isLoading } = useQuery({
        queryKey: ["my-cashbacks"],
        queryFn: getCashbacks,
      });

      console.log(cashbacks)

    const { translation: { myAccount, orderPage } }: any = useContext(TranslationContext)
    const { currencyCode } = useLocaleContext()

    const bannerItems = [
        {
            title: myAccount?.totalCashbackEarned ?? "Total Cashback earned",
            amount: cashbacks?.cashback?.totalEarned,
            icon: "/images/cashbacks/save-money.svg"
        },
        {
            title: myAccount?.totalCashbackSpent ?? "Total Cashback Spent",
            amount: cashbacks?.cashback?.totalSpent,
            icon: "/images/cashbacks/cashback.svg"
        },
        {
            title: myAccount?.cashbackBalance ?? "Cashback Balance",
            amount: cashbacks?.cashback?.balance,
            icon: "/images/cashbacks/wallet.svg"
        },
    ]

    if(isLoading) return <MyCashbacksLoading />
    
    if(cashbacks?.history?.length < 1) return (
        <div style={{width: "100%", height: "100%", minHeight: "50vh", display: "flex", flexDirection: "column", justifyContent: "center", alignItems: "center"}}>
            <Image width={100} height={100} style={{width: "300px", height: "300px", objectFit: "contain"}} unoptimized alt="no-data" src="/images/empty-states/no-data.svg" />
            <h3>{myAccount?.noCashbackHistory ?? "No Cashback History found"}</h3>
        </div>
    )
    return (
        <div className='my-cashbacks'>
            <h2>{myAccount?.myCashbacks ?? "My Cashbacks"}</h2>
            <div className="banner">
                {bannerItems?.map((item: any, key:number) => (
                    <div className="banner-item" key={key}>
                        <Image width={60} height={60} alt={item?.title} src={item?.icon} />
                        <p>{item?.title}</p>
                        <h4>{currencyCode + " "} {item?.amount}</h4>
                    </div>
                ))}
            </div>
            <div className="activity">
                {cashbacks?.history?.map((item:any, key:number) => (
                    <div className="activity-item" key={key}>
                        <div className="activity-item-info">
                            <h6>{item?.title}</h6>
                            <p>{item?.orderNo ? ((orderPage?.orderNo ?? "Order no") +": ") + item?.orderNo + " | " : ""} {item?.date}</p>
                        </div>
                        <div className="activity-item-amount">
                            <h6>{item?.sign}{item?.amount} {" "}{currencyCode}</h6>
                            <p>{item?.expires}</p>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    )
}
