import StoreLocatorSlider from "@/components/store-locator/StoreLocatorSlider";
import "./store-locator.scss";
import BreadCrumbs from "@/components/breadcrumbs/BreadCrumbs";
import { getStores } from "@/lib/methods/stores";
import { Metadata } from "next";
import GenericBackButton from "@/components/GenericBackButton/GenericBackButton";
import Text from "./Text";
import Map from "./Map";
import { getHeaderData } from "../layout";

export async function generateMetadata({ params }: { params: any }): Promise<Metadata> {
  const storesData = await getStores(params.locale);
  const stores = storesData?.result.stores;
  const categoryNames = stores.map((category: any) => category.name);
  return {
    title: "Store Locator | Yateem Optician",
    description: storesData?.result?.title || "Yateem Optician",
    keywords: categoryNames,
    openGraph: {
      title: categoryNames.join(", ") || "Yateem Optician",
      type: "website",
    },
  };
}

const stores = [
  {
    id: 1,
    name: 'Yateem Opticians',
    latitude: 25.216599743128455,
    longitude: 55.40870672883617,
  },
  {
    id: 2,
    name: 'Yateem Opticians',
    latitude: 25.217877204952895,
    longitude: 55.28280224232763,
  },
  {
    id: 3,
    name: 'Store 3',
    latitude: 25.23762892354328,
    longitude: 55.27480703068944,
  },
  {
    id: 4,
    name: 'Store 3',
    latitude: 25.22548639007757,
    longitude: 55.25848066931055,
  },
  {
    id: 5,
    name: 'Store 3',
    latitude: 25.210615743144587,
    longitude: 55.255819057672355,
  },
];

async function StoreLocator({ params }: { params: any }) {
  const [storesData, headerData] = await Promise.all([
    getStores(params.locale),
    getHeaderData(params.locale),
  ])
  const stores = storesData?.result.stores;
  const modStore = stores.map((store: any) => ({
    ...store,
    brands: store?.brands?.length > 0 ? store?.brands?.map((brand: any) => brand?.name?.en) : [],
    insurance: store?.insurance?.length > 0 ? store?.insurance?.map((insurance: any) => insurance?.name?.en) : [],
  }))
  // const categoryNames = stores.map((category: any) => category.name);
  const isIframe = headerData?.result?.isStoreLocatorIframe;

  return (
    <main>
      <BreadCrumbs
        backHome="Home"
        currentPage="/  Store Locator"
        image="/images/common/banner3.png"
      />
      {!isIframe ?
        <section className="store-locator">
          <div className="position-relative">
            <GenericBackButton style={{ top: "5px" }} />
            {/* <p>{storesData?.result?.title}</p> */}
            <Text />
            <Map stores={modStore} />
          </div>
        </section>
        :
        <section className="store-locator">
          <div className="container position-relative">
            <GenericBackButton style={{ top: "5px" }} />
            {/* <p>{storesData?.result?.title}</p> */}
            <Text />
          </div>

          <div className="store-locator_map">
            <iframe allow="geolocation *" src="https://www.yateemgroup.com/store-locator-1/" width="100%" height="100%" frameBorder="0" style={{ border: 0 }}></iframe>
          </div>
        </section>
      }
      {/* <StoreLocatorSlider stores={stores} /> */}
    </main>
  );
}

export default StoreLocator;
