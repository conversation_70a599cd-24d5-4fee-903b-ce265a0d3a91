// .main {
//   display: flex;
//   flex-direction: column;
//   justify-content: center;
//   align-items: center;
//   padding: 20px;
// }
.newArrivalSlider {
  background-color: #f2f4f9;
  color: black;
  width: 100%;
  padding-top: 4.9rem;
  padding-bottom: 9.1rem;

  @media (max-width: 767.98px) {
    padding-top: 2.4rem;
    padding-bottom: 2.8rem;
  }

  .swiper-pagination {
    background-color: #CCD0DF;
  }
}

.bestSellerSlider {
  width: 100%;
  position: relative;
  padding-top: 3.2rem;
  padding-bottom: 4rem;

  @media (max-width: 767.98px) {
    padding-top: 2.4rem;
    padding-bottom: 5.5rem;
  }

  &::after {
    content: "";
    background-image: url(../../../public/images/home/<USER>
    background-position: center;
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    z-index: -1;
  }

  .dropdown-menu {

    .show {
      min-width: 175px !important;
    }
  }

  .dropdown {
    display: flex;
    justify-content: center;
    align-items: center;
    column-gap: 1.1rem;
    padding: 0;

    .dropdown-menu {
      
      .show {
        min-width: 175px !important;
      }
    }

    button {
      font-size: 3.2rem;
      font-weight: 500;
      background-color: transparent;
      border: none;
      padding: 0;
      position: relative;

      &::before {
        content: "";
        background-color: #fff;
        height: 1px;
        width: calc(100% + 24px);
        position: absolute;
        bottom: 0;
      }

      @media (max-width: 575.98px) {
        font-size: 2.4rem;
        line-height: 3rem;
      }

      &::after {
        content: "";
        position: absolute;
        background-image: url(../../../public/images/product/icons/Arrow-Down.png);
        background-repeat: no-repeat;
        background-size: contain;
        width: 25px;
        height: 20px;
        top: 50%;
        transform: translate(25%, -50%);
        border: none;
        margin: 0;

        @media (max-width: 575.98px) {
          top: 55%;
          transform: translate(25%, -50%);
          width: 24px;
        }
      }

      &:hover {
        background-color: transparent;
      }
    }
  }

  h3 {
    color: #fff;
    font-size: 3.2rem;
    text-align: center;
    position: relative;
    font-weight: 400;
    line-height: 4rem;

    @media (max-width: 575.98px) {
      font-size: 2.4rem;
      line-height: 3rem;
    }
  }
}

.bestseller-dropdown {


  @media (max-width: 575.98px) {
    .dropdown-item {
      &:hover {
        background-color: #000000;
      }
    }
  }
}