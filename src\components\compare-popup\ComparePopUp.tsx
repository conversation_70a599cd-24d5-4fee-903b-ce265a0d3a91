"use client";

import Image from "next/image";
import Modal from "react-bootstrap/Modal";
import "./compare-popup.scss";
import AddToCart from "../add-to-cart/AddToCart";
import { useRouter } from "next/navigation";
import { TranslationContext } from "@/contexts/Translation";
import { useContext } from "react";
import { useLocaleContext } from "@/contexts/LocaleProvider";

function ComparePopUp({ show, handleClose, data }: any) {
  const router = useRouter();
  const { currentLocale:locale, currencyCode } = useLocaleContext()
  const {translation: { compare, productPage } } = useContext(TranslationContext)
  return (
    <>
      <Modal
        show={show}
        onHide={handleClose}
        backdrop="static"
        keyboard={false}
        centered
        className={`compare-popup ${locale == "ar"? "app rtl": ""}`}>
        <Modal.Header style={{direction: locale == "ar"? "rtl": "ltr"}} closeButton>
          <Modal.Title>
            <h2>{compare.title ?? "Comparison"}</h2>
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="compare-popup_flex">
            <table className="table">
              <tr className="table_row">
                <th></th>
                {data.map((item: any, index: number) => {
                  return (
                    <th key={"head" + index}>
                      <h6> {item?.name}</h6>
                      <Image quality={100} priority width={208} height={129} src={item?.thumbnail} alt={item?.name} />
                    </th>
                  );
                })}
                {Array.from({ length: data?.length ? 4 - data.length : 4 }).map((_, index) => {
                  return (
                    <th
                      key={index + "header"}
                      className="last-child"
                      onClick={() => router.push("/products")}>
                      <Image quality={100} priority
                        width={208}
                        height={189}
                        src="/images/common/shape.png"
                        alt="Acne Studios Black Ingrid Sunglasses"
                      />
                      <h6>{compare?.addProduct ?? "Add product"}</h6>
                    </th>
                  );
                })}
              </tr>

              <tr>
                <td className="item" style={{ paddingTop: "3rem" }}>
                  {compare?.sku ?? "SKU"}
                </td>
                {data.map((item: any, index: number) => {
                  return (
                    <td key={"sku" + index} style={{ paddingTop: "3rem" }}>
                      {item?.sku}
                    </td>
                  );
                })}
                {Array.from({ length: data?.length ? 4 - data.length : 4 }).map((_, index) => {
                  return (
                    <td key={"skuIndex" + index} style={{ paddingTop: "3rem" }}>
                      N/A
                    </td>
                  );
                })}
              </tr>

              <tr>
                <td className="item">{compare?.price ?? "Price"}</td>
                {data.map((item: any, index: number) => {
                  return (
                    <td key={"price" + index}>
                      {currencyCode} {item?.offerPrice ?? item?.price}
                    </td>
                  );
                })}
                {Array.from({ length: data?.length ? 4 - data.length : 4 }).map((_, index) => {
                  return <td key={"priceIndex" + index}>N/A</td>;
                })}
              </tr>
              <tr>
                <td className="item">{compare?.rating ?? "Rating"}</td>
                {data.map((item: any, index: number) => {
                  return <td key={"rating" + index}>{item?.rating}</td>;
                })}
                {Array.from({ length: data?.length ? 4 - data.length : 4 }).map((_, index) => {
                  return <td key={"ratingIndex" + index}>N/A</td>;
                })}
              </tr>

              <tr>
                <td className="item">{compare?.color ?? "Color"}</td>
                {data.map((item: any, index: number) => {
                  return <td key={"color" + index}>{item?.color}</td>;
                })}
                {Array.from({ length: data?.length ? 4 - data.length : 4 }).map((_, index) => {
                  return <td key={"colorIndex" + index}>N/A</td>;
                })}
              </tr>
              <tr>
                <td className="item">{compare?.sizesAvailable ?? "Sizes Available"}</td>
                {data.map((item: any, index: number) => {
                  return <td key={"size" + index}>{item?.sizes?.join(", ")}</td>;
                })}
                {Array.from({ length: data?.length ? 4 - data.length : 4 }).map((_, index) => {
                  return <td key={"sizeIndex" + index}>N/A</td>;
                })}
              </tr>

              <tr>
                <td className="item">{compare?.description ?? "Description"}</td>
                {data.map((item: any, index: number) => {
                  return (
                    <td
                      key={"description" + index}
                      dangerouslySetInnerHTML={{ __html: item?.description }}></td>
                  );
                })}
                {Array.from({ length: data?.length ? 4 - data.length : 4 }).map((_, index) => {
                  return <td key={"descriptionIndex" + index}>N/A</td>;
                })}
              </tr>

              {/* <tr>
                <td className="item">Weight</td>
                {data.map((item: any, index: number) => {
                  return <td key={"weight" + index}>{item?.weight || "N/A"}</td>;
                })}
                {Array.from({ length: data?.length ? 4 - data.length : 4 }).map((_, index) => {
                  return <td key={"weightIndex" + index}>N/A</td>;
                })}
              </tr> */}

              {/* <tr>
                <td className="item">Dimension</td>
                <td>N/A</td>
                <td>N/A</td>
                <td>N/A</td>
                <td>N/A</td>
              </tr> */}

              <tr>
                <td className="item"></td>
                {data.map((item: any, index: number) => {
                  return (
                    <td key={"add" + index}>
                      <AddToCart translation={productPage} product={item} qty={1} showSvg={true} />
                    </td>
                  );
                })}
              </tr>
            </table>
          </div>
        </Modal.Body>
      </Modal>
    </>
  );
}

export default ComparePopUp;
