"use client";


import { useState } from "react";
import dynamic from 'next/dynamic'
 
const Number  = dynamic(() => import("@/components/cart-popups/number/Number"), {
  ssr: false
})
// import Number from "@/components/cart-popups/number/Number";
import VerifyOtp from "@/components/cart-popups/verify/Verify";
import Signup from "@/components/cart-popups/sign-up/SignUp";
import { useRouter, useSearchParams } from "next/navigation";
import { GoogleOAuthProvider } from "@react-oauth/google";
import SignUpType from "@/components/cart-popups/signup-type/SignUpType";
import api from "@/config/axios.interceptor";
import { endpoints } from "@/config/apiEndpoints";
import { toast } from "sonner";
import { useQueryClient } from "@tanstack/react-query";
import ReactDOM from 'react-dom'

export default function Login({ lang, translation, code, page }:any) {
    const [show, setShow] = useState(page);
    const [mobile, setMobile] = useState("");
    const [otp, setOtp] = useState("");

    const [countryCode, setCountryCode] = useState("+" + code);
    const [user, setUser] = useState(null);
    const router = useRouter();
  
    const searchParams = useSearchParams()
    const queryClient = useQueryClient();
  
    const handleClose = async() => {
      setShow("number");
      router.back();
    };
  
    const redirectToCheckout = () => {
      router.push("/checkout/cart");
    }
  
    const triggerStep = (step: string) => {
      setShow(step);
    };
  
    return (
      <>
        <GoogleOAuthProvider
          clientId={
            "267704808546-3kvchtujg5uinj7nc97nbl806lmnc620.apps.googleusercontent.com"
          }
        >
          <Number lang={lang}
            show={show === "number"}
            stepHandler={triggerStep}
            handleClose={handleClose}
            setShow={setShow}
            setCountryCode={setCountryCode}
            setMobile={setMobile}
            translation={translation}
            code={code}
          />
  
          <VerifyOtp lang={lang}
            show={show === "verify"}
            phone={{ countryCode, mobile }}
            stepHandler={triggerStep}
            handleClose={handleClose}
            setOtp={setOtp}
            otp={otp}
            setUser={setUser}
            translation={translation}
          />
  
          <SignUpType lang={lang}
            redirectToCheckout={redirectToCheckout}
            show={show === "userType"}
            stepHandler={triggerStep}
            handleClose={handleClose}
            translation={translation}
          />
  
          <Signup lang={lang} show={show === "signup"} handleClose={handleClose} translation={translation} />
        </GoogleOAuthProvider>
      </>
    );
}
