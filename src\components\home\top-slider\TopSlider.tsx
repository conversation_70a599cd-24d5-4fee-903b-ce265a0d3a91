"use client";
import Image from "next/image";
import { Autoplay, Navigation } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import Link from "next/link";
import "./topSlider.scss"

function TopSlider({ id, item }: any) {
    return (
        <section className="top-slider" id={id}>
            <Swiper
                className="mySwiper"
                modules={[Autoplay, Navigation]}
                navigation={true}
                speed={500}
                loop={true}
                autoplay={{
                    delay: 6000,
                    disableOnInteraction: false,
                    pauseOnMouseEnter: true,
                }}>
                {
                    item?.items?.map((slide: any, i: number) => (
                        <SwiperSlide key={i}>
                            <Link style={{ pointerEvents: !slide?.link ? "none" : "auto", width: "100%" }} href={slide?.link ?? ""}>
                                {
                                    slide?.type == "video" ?
                                        (
                                            <div className="homebanner" id={id}>
                                                <video
                                                    className="d-block d-md-none"
                                                    muted
                                                    src={slide?.srcType == "link" ? slide?.mobileSrc ? slide?.mobileSrc: slide?.src : slide?.mobileVideo ? slide?.mobileVideo : slide?.video}
                                                    autoPlay
                                                    loop
                                                    playsInline
                                                ></video>
                                                <video
                                                    className="d-none d-md-block"
                                                    muted
                                                    src={slide?.srcType == "link" ? slide?.src : (slide?.video ?? "")}
                                                    autoPlay
                                                    loop
                                                    playsInline
                                                ></video>
                                            </div>
                                        )
                                        :
                                        (<>
                                            <Image className="d-none d-md-block" quality={100} priority={i === 0} loading={i === 0 ? undefined : "lazy"} src={slide?.image ?? ""} width={1366} height={780} alt="banner image" />
                                            <Image className="d-block d-md-none" quality={100} priority={i === 0} loading={i === 0 ? undefined : "lazy"} src={slide?.mobileImage ?? slide?.image} width={1366} height={780} alt="mobile banner image" />
                                        </>
                                        )
                                }
                            </Link>
                        </SwiperSlide>
                    ))
                }
            </Swiper>
        </section>
    );
}

export default TopSlider;
