"use client";

import { AnimatePresence, motion } from "framer-motion";
import "./product-select.scss";
import { useContext, useEffect, useRef, useState } from "react";
import Select from "react-select";
import { CountContext } from "@/contexts/AddBtnContext";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import api from "@/config/axios.interceptor";
import { endpoints } from "@/config/apiEndpoints";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import { toast } from "sonner";
import Toaster from "../toast/Toaster";
import { useRouter, useSearchParams } from "next/navigation";
import { sortPower } from "@/lib/utils/utils";
import { useLocaleContext } from "@/contexts/LocaleProvider";

interface selectType {
  label: string;
  value: string;
}
interface ContactLensForm {
  purchaseType: "manual" | "call";
  multiple: boolean;
  sphLeft: selectType;
  sphRight: selectType;
  cylLeft: selectType;
  cylRight: selectType;
  axisLeft: selectType;
  axisRight: selectType;
  multiFocal: selectType;
}

const multi: any = [
  {
    label: "Low",
    value: "low"
  },
  {
    label: "Medium",
    value: "medium"
  },
  {
    label: "High",
    value: "high"
  },
]

function ProductSelelct({ product, translation }: any ) {
  const [active, setActive] = useState({
    label: product?.plans?.[0]?.name,
    value: product?.plans?.[0]?._id,
  });
  const { subscriptionType, setSubscriptionType, count, formRef, setMulti } = useContext(CountContext);
  const { currencyCode } = useLocaleContext()
  const searchParams = useSearchParams();
  const size = searchParams?.get("size") || product?.contactSizes?.[0]?.size?._id || null;
  const queryClient = useQueryClient();
  const [isSubscripton, setIsSubscription] = useState(false);
  const [showPowerPrice, setShowPowerPrice] = useState(false)
  const [powerPrice, setPowerPrice] = useState(0)
  const router = useRouter();
  // const [multiPower, setMultipower] = useState(false);
  // const {
  //   data: lensPowers,
  //   isLoading,
  //   error,
  // } = useQuery({
  //   queryKey: ["contact-lens-power"],
  //   queryFn: async () => {
  //     return await api.get(endpoints.contactLensPower).then((res) => {
  //       return {
  //         result:
  //         {
  //           sph: res.data?.result?.sph?.sort(sortPower),
  //           cyl: res.data?.result?.cyl?.sort(sortPower),
  //           axis: res.data?.result?.axis?.sort((a: any, b: any) => a.name - b.name),
  //         }
  //       };
  //     });
  //   },
  // });
  console.log(product?.contactSph)
  const lensPowers: any = {
    result: {
      sph: product?.contactSph?.sort(sortPower),
      cyl: product?.contactCyl?.sort(sortPower),
      axis: product?.contactAxis?.sort((a: any, b: any) => a.name - b),
    }
  }

  const {
    register,
    watch,
    setValue,
    handleSubmit,
    control,
    formState: { errors },
  } = useForm<ContactLensForm>();

  const formWatch = watch(["sphLeft", "sphRight", "axisLeft", "axisRight", "cylLeft", "cylRight"])

  useEffect(() => {
    let flag = false
    let leftFlag = false
    let rightFlag = false
    formWatch?.forEach((item: any, index: number) => {

      if (item) {
        if (item?.label !== "0.00" && item?.label !== "None" && item?.label !== "none" && item?.label !== "NONE") flag = true
        // if(!multiPower){
        // }else{
        //   if(index == 0 || index == 2 || index == 4){
        //     if(item?.label !== "0.00") leftFlag = true
        //   }else{
        //     if(item?.label !== "0.00") rightFlag = true
        //   }
        // }

      }
    })

    if (flag) setShowPowerPrice(true)
    else setShowPowerPrice(false)
  }, [formWatch])

  
  const multiPower = watch("multiple");
  
  useEffect(()=>{
    setMulti(multiPower)
  }, [multiPower])

  const handleSubscriptionSelect = (subscription: any) => {
    setActive(subscription);
  };

  const subscribe = () => {
    setIsSubscription(true);
  };

  const onSubmit: SubmitHandler<ContactLensForm> = (data: ContactLensForm) => {
    const lensData = {
      sphLeft: data.sphLeft ? data.sphLeft.value : undefined,
      sphRight: data.sphRight ? data.sphRight.value : undefined,
      cylLeft: data.cylLeft ? data.cylLeft.value : undefined,
      cylRight: data.cylRight ? data.cylRight.value : undefined,
      axisLeft: data.axisLeft ? data.axisLeft.value : undefined,
      axisRight: data.axisRight ? data.axisRight.value : undefined,
      multiple: data.multiple,
      purchaseType: data.purchaseType,
      multiFocal: data.multiFocal ? data.multiFocal.value : undefined,
    };

    if (isSubscripton) {
      api
        .post(endpoints.subscribe, {
          product: product?._id,
          contactLens: lensData,
          plan: active.value,
          size
        })
        .then((res) => {
          router.push(`/checkout/subscription?sub=${res.data?.result}`);
        })
        .catch((error) => {
          if (error.response.status === 401) {
            router.push("/login");
            toast(error.response.data.message);
          }
        });
      return;
    } else {
      api
        .post(endpoints.contactLensCart, {
          quantity: count,
          product: product?._id,
          contactLens: lensData,
          size
        })
        .then((res) => {
          queryClient.invalidateQueries({ queryKey: ["cart"] });
          toast(
            <Toaster
              title={res?.data.message || "Product added to cart."}
              subTotal={`${currencyCode} ${res.data?.result?.baseTotal} `}
              link="/cart"
            />,
            {
              unstyled: true,
              id: "cart",
              duration: 2000,
            }
          );
        });
    }
    setIsSubscription(false);
  };

  useEffect(() => {
    if (isSubscripton === true) formRef?.current?.requestSubmit();
  }, [isSubscripton === true]);
  return (
    <form className="product-select" ref={formRef} onSubmit={handleSubmit(onSubmit)}>
      {(product?.sph || product?.cyl || product?.axis) &&
        <>
          <AnimatePresence>
            <motion.div
              className="product-select_box"
              initial={{ opacity: 0, height: 0 }}
              animate={{
                opacity: 1,
                height: "auto",
              }}
              exit={{ opacity: 0, height: 0 }}>
              <span>{translation?.contactLensPowerTitle || "Select the Needed Contact Lens Power"}</span>

              <label className="checkbox">
                <input {...register("multiple")} type="checkbox" />{translation?.contactLensCheckBox || "I need 2 different powers"}
              </label>
              {/* {produc} */}
              <div className="pt-4">
                {product?.sph && (
                  <div className="product-select_select">
                    <label>{translation?.spere ?? "Sphere"}</label>
                    <div className="arrow-down text-center">
                      {multiPower && <span>{translation?.leftEye ?? "OS (Left Eye)"}</span>}
                      <Controller
                        name="sphLeft"
                        control={control}
                        rules={{ required: product?.sph }}
                        render={({ field }) => (
                          <Select
                          placeholder={translation?.select ?? "Select"}
                            theme={(theme) => ({
                              ...theme,
                              borderRadius: 0,
                              colors: {
                                ...theme.colors,
                                primary25: "#ccc",
                                primary: "black",
                              },
                            })}
                            classNames={{
                              control: (state) => (errors.sphLeft ? "select select-error" : "select"),

                              dropdownIndicator: () => "d-none",
                              option: (state) => (state.isSelected ? "option selected" : "option"),
                              menu: () => "menu",
                            }}
                            {...field}
                            options={lensPowers?.result?.sph?.map((power: any) => ({
                              label: power.name,
                              value: power._id,
                            }))}
                          />
                        )}
                      />
                    </div>
                    {multiPower && (
                      <div className="arrow-down">
                        {multiPower && <span>{translation?.rightEye ?? "OD (Right Eye)"}</span>}
                        <Controller
                          name="sphRight"
                          control={control}
                          rules={{ required: multiPower && product?.sph }}
                          render={({ field }) => (
                            <Select
                            placeholder={translation?.select ?? "Select"}
                              theme={(theme) => ({
                                ...theme,
                                borderRadius: 0,
                                colors: {
                                  ...theme.colors,
                                  primary25: "#ccc",
                                  primary: "black",
                                },
                              })}
                              classNames={{
                                control: (state) =>
                                  errors.sphLeft ? "select select-error" : "select",

                                dropdownIndicator: () => "d-none",
                                option: (state) => (state.isSelected ? "option selected" : "option"),
                                menu: () => "menu",
                              }}
                              {...field}
                              options={lensPowers?.result?.sph?.map((power: any) => ({
                                label: power.name,
                                value: power._id,
                              }))}
                            />
                          )}
                        />
                      </div>
                    )}
                  </div>
                )}

                {product?.axis && (
                  <div className="product-select_select">
                    <label>{translation?.axis ?? "Axis"}</label>
                    <div className="arrow-down">
                      <Controller
                        name="axisLeft"
                        control={control}
                        rules={{ required: product?.axis }}
                        render={({ field }) => (
                          <Select
                          placeholder={translation?.select ?? "Select"}
                            theme={(theme) => ({
                              ...theme,
                              borderRadius: 0,
                              colors: {
                                ...theme.colors,
                                primary25: "#ccc",
                                primary: "black",
                              },
                            })}
                            classNames={{
                              control: (state) =>
                                errors.axisLeft ? "select select-error" : "select",

                              dropdownIndicator: () => "d-none",
                              option: (state) => (state.isSelected ? "option selected" : "option"),
                              menu: () => "menu",
                            }}
                            {...field}
                            options={lensPowers?.result?.axis?.map((power: any) => ({
                              label: power.name,
                              value: power._id,
                            }))}
                          />
                        )}
                      />
                    </div>
                    {multiPower && (
                      <div className="arrow-down">
                        <Controller
                          name="axisRight"
                          control={control}
                          rules={{ required: multiPower && product?.sph }}
                          render={({ field }) => (
                            <Select
                            placeholder={translation?.select ?? "Select"}
                              theme={(theme) => ({
                                ...theme,
                                borderRadius: 0,
                                colors: {
                                  ...theme.colors,
                                  primary25: "#ccc",
                                  primary: "black",
                                },
                              })}
                              classNames={{
                                control: (state) =>
                                  errors.axisRight ? "select select-error" : "select",

                                dropdownIndicator: () => "d-none",
                                option: (state) => (state.isSelected ? "option selected" : "option"),
                                menu: () => "menu",
                              }}
                              {...field}
                              options={lensPowers?.result?.axis?.map((power: any) => ({
                                label: power.name,
                                value: power._id,
                              }))}
                            />
                          )}
                        />
                      </div>
                    )}
                  </div>
                )}

                {product?.cyl && (
                  <div className="product-select_select">
                    <label>{translation?.cyl ?? "Cylinder"}</label>
                    <div className="arrow-down">
                      <Controller
                        name="cylLeft"
                        control={control}
                        rules={{ required: product?.cyl }}
                        render={({ field }) => (
                          <Select
                          placeholder={translation?.select ?? "Select"}
                            theme={(theme) => ({
                              ...theme,
                              borderRadius: 0,
                              colors: {
                                ...theme.colors,
                                primary25: "#ccc",
                                primary: "black",
                              },
                            })}
                            classNames={{
                              control: (state) => (errors.cylLeft ? "select select-error" : "select"),

                              dropdownIndicator: () => "d-none",
                              option: (state) => (state.isSelected ? "option selected" : "option"),
                              menu: () => "menu",
                            }}
                            {...field}
                            options={lensPowers?.result?.cyl?.map((power: any) => ({
                              label: power.name,
                              value: power._id,
                            }))}
                          />
                        )}
                      />
                    </div>
                    {multiPower && (
                      <div className="arrow-down">
                        <Controller
                          name="cylRight"
                          control={control}
                          rules={{ required: multiPower && product?.cyl }}
                          render={({ field }) => (
                            <Select
                              placeholder={translation?.select ?? "Select"}
                              theme={(theme) => ({
                                ...theme,
                                borderRadius: 0,
                                colors: {
                                  ...theme.colors,
                                  primary25: "#ccc",
                                  primary: "black",
                                },
                              })}
                              classNames={{
                                control: (state) =>
                                  errors.cylRight ? "select select-error" : "select",

                                dropdownIndicator: () => "d-none",
                                option: (state) => (state.isSelected ? "option selected" : "option"),
                                menu: () => "menu",
                              }}
                              {...field}
                              options={lensPowers?.result?.cyl?.map((power: any) => ({
                                label: power.name,
                                value: power._id,
                              }))}
                            />
                          )}
                        />
                      </div>
                    )}
                  </div>
                )}

                {product?.multiFocal && (
                  <div className="product-select_select">
                    <label>{translation?.addition ?? "Addition"}</label>
                    <div className="arrow-down">
                      <Controller
                        name="multiFocal"
                        control={control}
                        rules={{ required: product?.multiFocal }}
                        render={({ field }) => (
                          <Select
                            placeholder={translation?.select ?? "Select"}
                            theme={(theme) => ({
                              ...theme,
                              borderRadius: 0,
                              colors: {
                                ...theme.colors,
                                primary25: "#ccc",
                                primary: "black",
                              },
                            })}
                            classNames={{
                              control: (state) =>
                                errors.multiFocal ? "select select-error" : "select",

                              dropdownIndicator: () => "d-none",
                              option: (state) => (state.isSelected ? "option selected" : "option"),
                              menu: () => "menu",
                            }}
                            {...field}
                            options={multi}
                          />
                        )}
                      />
                    </div>
                  </div>
                )}

              </div>
              {showPowerPrice && <div className="contactPrice">
                <div className="price">
                  <span>{translation?.totalPriceIncludingPower ?? "Total Price Including Power"}</span>
                  <h5>
                    {currencyCode}{" "}
                    {count * ((product?.offerPrice?.aed ?? product?.price?.aed) + (product?.powerPrice || 0))}{" "}
                    {(multiPower ?
                      <><span style={{lineHeight: "normal", fontSize: "2rem", fontWeight: "500", verticalAlign: "text-top"}}>x</span>{" 2 = "+ 2 * (count * ((product?.offerPrice?.aed ?? product?.price?.aed) + (product?.powerPrice || 0)))}</>
                      :" ")}
                  </h5>
                </div>
              </div>}
            </motion.div>

          </AnimatePresence>
        </>}
      {(product?.plans && product?.plans?.length > 0) && <div className="product-select_input">
        <h6>Subscribe & Save</h6>
        <label htmlFor="yateem">
          <input
            type="radio"
            id="yateem"
            name="subscription"
            onChange={() => setSubscriptionType("subscription")}
            checked={subscriptionType === "subscription"}
          />
          Yateem Subscription
        </label>
        <AnimatePresence>
          {subscriptionType === "subscription" && (
            <motion.div
              className="product-select_select shipEvery"
              initial={{ opacity: 0, height: 0 }}
              animate={{
                opacity: 1,
                height: "auto",
              }}
              exit={{ opacity: 0, height: 0 }}>
              <label>Ship Every</label>
              <div className="arrow-down">
                <Select
                  value={active || product?.plans?.[0]?._id}
                  onChange={(e) => handleSubscriptionSelect(e)}
                  theme={(theme) => ({
                    ...theme,
                    borderRadius: 0,
                    colors: {
                      ...theme.colors,
                      primary25: "#ccc",
                      primary: "black",
                    },
                  })}
                  classNames={{
                    control: (state) => (errors.cylRight ? "select select-error" : "select"),

                    dropdownIndicator: () => "d-none",
                    option: (state) => (state.isSelected ? "option selected" : "option"),
                    menu: () => "menu",
                  }}
                  options={product?.plans?.map((plan: any) => ({
                    label: plan.name,
                    value: plan._id,
                  }))}
                />
              </div>
              <button onClick={subscribe} className="subscribeBtn">
                Subscribe
              </button>
            </motion.div>
          )}
        </AnimatePresence>

        <label htmlFor="oneTime">
          <input
            type="radio"
            id="oneTime"
            name="subscription"
            onChange={() => setSubscriptionType("oneTime")}
            checked={subscriptionType !== "subscription"}
            defaultChecked
          />
          Deliver one time only
        </label>
      </div>}
    </form>
  );
}

export default ProductSelelct;
