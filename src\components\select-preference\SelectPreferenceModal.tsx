import Button from "react-bootstrap/Button";
import Modal from "react-bootstrap/Modal";
import "./SelectPreferenceModal.scss";
import Image from "next/image";
import LensMaterial from "./lens-material/LensMaterial";
import React, { useState } from "react";
import LensIndex from "./lens-index/LensIndex";
import Link from "next/link";
import LensCoating from "./lens-coating/LensCoating";
import { useLocaleContext } from "@/contexts/LocaleProvider";

function SelectPreferenceModal({
  show,
  handleClose,
  lensData,
  handleLensData,
  setStep,
  sum,
  setSum,
  back,
}: any) {
  const [type, setType] = useState("");
  const [selected, setSelected] = useState(false);
  const {currencyCode} = useLocaleContext()

  const stepOrder = ["material", "index", "coating"];

  function isActive(itemType: any) {
    const currentIndex = stepOrder.indexOf(type);
    const itemIndex = stepOrder.indexOf(itemType);
    return itemIndex <= currentIndex ? "isActive" : "";
  }

  const handleNextClick = () => {
    switch (type) {
      case "material":
        setType("index");
        setSelected(false);
        break;
      case "index":
        setType("coating");
        setSelected(false);
        break;
      case "coating":
        setStep("preference");
        break;
      default:
        break;
    }
  };
  

  return (
    <Modal
      show={show}
      onHide={handleClose}
      className="select-preference"
      backdrop="static"
      scrollable={true}
    >
      <div className="modal-wrap">
        <Modal.Header closeButton>
          <h2>
            <Image quality={100} priority
              src="/images/common/backarrow.svg"
              width={40}
              height={40}
              alt="back arrow"
              className="cursor-pointer"
              onClick={() => back("preference")}
            />
            Select Your Preference
          </h2>
        </Modal.Header>
        <Modal.Body>
          <ul className="status-bar">
            <li
              className={isActive("material")}
              onClick={() => {
                setType("material")
                handleLensData({ index: '' });
                handleLensData({ coating: '' });
                setSum("index",0,2);
              }}
            >
              <span className="number">01</span>
              <span className="title">Lens Material</span>
            </li>

            <li
              className={isActive("index")}
              onClick={() => {
                if (type !== "" && type !== "material") {
                  setType("index");
                  setSum("coating",0);
                  handleLensData({ coating: '' });
                }
              }}
            >
              <span className="number">02</span>
              <span className="title">Lens Index</span>
            </li>

            <li
              className={isActive("coating")}
              onClick={() => {
                if (type === "coating") {
                  setType("coating");
                }
              }}
            >
              <span className="number">03</span>
              <span className="title">Lens Coating</span>
            </li>
          </ul>

          {type === "" && (
            <>
              <div
                className="select-preference_box cursor-pointer"
                onClick={() => setType("material")}
              >
                <Image quality={100} priority
                  src="/images/modal/b1.svg"
                  width={48}
                  height={48}
                  alt="images"
                />

                <div>
                  <h5>Lens Material</h5>
                  <p>
                    Lorem Ipsum, giving information on its origins, as well as a
                    random Lipsum generator.
                  </p>
                </div>
              </div>

              <div className="select-preference_box cursor-default opacity-50">
                <Image quality={100} priority
                  src="/images/modal/b2.svg"
                  width={48}
                  height={48}
                  alt="images"
                />

                <div>
                  <h5>Lens Index</h5>
                  <p>
                    Lorem Ipsum, giving information on its origins, as well as a
                    random Lipsum generator.
                  </p>
                </div>
              </div>

              <div className="select-preference_box cursor-default opacity-50">
                <Image quality={100} priority
                  src="/images/modal/b3.svg"
                  width={48}
                  height={48}
                  alt="images"
                />

                <div>
                  <h5>Lens Coating</h5>
                  <p>
                    Lorem Ipsum, giving information on its origins, as well as a
                    random Lipsum generator.
                  </p>
                </div>
              </div>
            </>
          )}
          {type === "material" && (
            <LensMaterial
              lensData={lensData}
              handleLensData={handleLensData}
              sum={sum}
              setSum={setSum}
              setSelected={setSelected}
            />
          )}
          {type === "index" && (
            <LensIndex
              lensData={lensData}
              handleLensData={handleLensData}
              sum={sum}
              setSum={setSum}
              setSelected={setSelected}
            />
          )}
          {type === "coating" && (
            <LensCoating
              lensData={lensData}
              handleLensData={handleLensData}
              sum={sum}
              setSum={setSum}
              setSelected={setSelected}
            />
          )}

          {/* <h5 className='skip'>You can skip this step</h5>
          <p className='cta'>Not sure what to select? Call<Link href="">+971 509559296</Link></p> */}
        </Modal.Body>
      </div>
      <Modal.Footer>
        <h5>
        Subtotal:{currencyCode + " "} <span>{sum||0}</span>
        </h5>
        <Button
          className="px-5 w-auto"
          variant="primary"
          onClick={handleNextClick}
          disabled={(type === "" || selected === false) && true}
        >
          {type === "coating" ? "Submit & Add To Cart" : "Next"}
        </Button>
      </Modal.Footer>
    </Modal>
  );
}

export default SelectPreferenceModal;
