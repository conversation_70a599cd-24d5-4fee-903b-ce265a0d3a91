import ProductSlider from '@/components/product/product-slider/ProductSlider';
import { endpoints } from '@/config/apiEndpoints';
import { cookies } from 'next/headers';
import Image from 'next/image';
import React from 'react'
import "./widget.scss"
import VideoBanner from './VideoBanner';

async function BrandWidgets({ params }: any) {
    const locale = cookies().get("Next-Locale")?.value || "sa-en";

    const [storeId, language] = locale.split("-")
    const brandDetail = await fetch(
        process.env.NEXT_PUBLIC_API_URL + endpoints.products,
        {
            headers: {
                "Content-Type": "application/json",
                "Access-Control-Allow-Origin": "*",
                language: language || "en",
                storeid: storeId || "sa"
            },
            method: "POST",
            body: JSON.stringify({
                keyword: params,
                brandPage: true,
                page: 1,
                limit: 1,
            }),
        }
    );
    const brandDetailData = await brandDetail.json();
    const brandData = brandDetailData.result;

    function renderWidgets(item: any) {
        switch (item?.type) {
            case "banner":
                return <>
                    {item?.bannerType == "image" ? (
                        <>
                            {item?.banner && (
                                <Image
                                    quality={100}
                                    priority
                                    className="d-none d-lg-block h-100"
                                    style={{ maxHeight: "40vh", objectPosition: "center" }}
                                    src={item?.banner ?? ""}
                                    width={1366}
                                    height={375}
                                    alt="brands"
                                />
                            )}
                            {item?.banner && (
                                <Image
                                    quality={100}
                                    priority
                                    className="d-block d-lg-none h-100"
                                    src={item?.banner ?? ""}
                                    width={375}
                                    height={193}
                                    alt="brands"
                                />
                            )}
                        </>

                    ) : <VideoBanner item={item} />}
                </>
            case "content":
                return <>
                    <div
                        style={{
                            marginTop: "3rem"
                        }}
                        className='container'
                        dangerouslySetInnerHTML={{ __html: item?.content }}
                    ></div>
                </>
            case "carousel":
                return item?.products?.length > 0 && <div className='carousel'>
                    <h3 className='container widget-title'>{item?.name}</h3>
                    <ProductSlider products={item?.products} status={false} color={"white"} />
                </div>
            case "offerBanner":
                return <div style={{ margin: "5rem 0rem", display: "flex" }}>
                    <div style={{ width: "100%" }}>
                        {item?.banner && (
                            <Image
                                quality={100}
                                priority
                                className="d-none d-lg-block h-100"
                                style={{ maxHeight: "40vh", objectPosition: "center" }}
                                src={item?.banner ?? ""}
                                width={1366}
                                height={375}
                                alt="brands"
                            />
                        )}
                        {item?.banner && (
                            <Image
                                quality={100}
                                priority
                                className="d-block d-lg-none h-100"
                                src={item?.banner ?? ""}
                                width={375}
                                height={193}
                                alt="brands"
                            />
                        )}
                    </div>
                    <div style={{ width: "100%" }}>
                        {item?.bannerTwo && (
                            <Image
                                quality={100}
                                priority
                                className="d-none d-lg-block h-100"
                                style={{ maxHeight: "40vh", objectPosition: "center" }}
                                src={item?.bannerTwo ?? ""}
                                width={1366}
                                height={375}
                                alt="brands"
                            />
                        )}
                        {item?.bannerTwo && (
                            <Image
                                quality={100}
                                priority
                                className="d-block d-lg-none h-100"
                                src={item?.bannerTwo ?? ""}
                                width={375}
                                height={193}
                                alt="brands"
                            />
                        )}
                    </div>
                </div>
            default:
                return null;
        }
    }


    return (
        <div className="widgets">
            {brandData?.brandPage && brandData?.brandPage?.length > 0 ?
                brandData?.brandPage?.map((item: any, index: any) => (
                    renderWidgets(item)
                ))
                : <>
                    {brandData?.banner && (
                        <Image
                            quality={100}
                            priority
                            className="d-none d-lg-block h-100"
                            style={{ maxHeight: "40vh", objectPosition: "center" }}
                            src={brandData?.banner ?? ""}
                            width={1366}
                            height={375}
                            alt="brands"
                        />
                    )}

                    {brandData?.banner && (
                        <Image
                            quality={100}
                            priority
                            className="d-block d-lg-none h-100"
                            src={brandData?.banner ?? ""}
                            width={375}
                            height={193}
                            alt="brands"
                        />
                    )}
                </>}
        </div>
    )
}


export default BrandWidgets