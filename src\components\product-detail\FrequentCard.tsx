"use client"

import Link from "next/link";
import AddToCartButtonWrapper from "../addToCartButtonWrapper";
import Image from "next/image";
import { useState } from "react";
import { useLocaleContext } from "@/contexts/LocaleProvider";

export default function FrequentCard({ data }: any) {
  const [imageSrc, setImageSrc] = useState<any>(data?.thumbnail)
  const {currencyCode} = useLocaleContext()
  return (
    <div className="block-six_flex">
      <Link href={`/product/${data?.slug}`}>
        <Image
          quality={100}
          priority
          className="opacity-0"
          onError={() => setImageSrc("/images/product/noImage.jpg")}
          onLoad={(e:{target:any}) => e.target.classList.remove("opacity-0")}
          src={imageSrc ?? ""} width={147} height={96} alt="specs" />
      </Link>

      <div className="block-six_info">
        <Link href={`/product/${data?.slug}`}>
          <p>{data?.name}</p>
        </Link>
        <Link href={`/product/${data?.slug}`}>
          <ul>
            {data?.color && <li>Color: {data?.color}</li>}
            <li>
              {currencyCode + " "}
              {data?.price}
            </li>
          </ul>
        </Link>
        {(data?.productType === "contactLens"? !(data?.sph || data?.cyl || data?.axis): data?.isAddToCart) && <div className="block-six_btns" style={{ minWidth: "126px" }}>
          <AddToCartButtonWrapper product={data} />
        </div>}
      </div>
    </div>
  );
}
