import { FilterContext } from "@/contexts/FilterContaxt";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useContext, useEffect, useState } from "react";

function useFilter(name: string) {
    const searchParams = useSearchParams();
    const pathname = usePathname();
    const router = useRouter();
    const filters = searchParams.getAll(name);
    const [active, setActive] = useState(filters);

    useEffect(() => {
        const filters = searchParams.getAll(name);
        setActive(filters);
        // window.scrollTo({
        //     top: 0,
        //     left: 0,
        //     behavior: "smooth",
        //   });

        var element = document.getElementById("product-navigation");
        var headerOffset = 105;
        var elementPosition = element?.getBoundingClientRect().top || 0; // Add this line to handle null case
        var offsetPosition = elementPosition + window.pageYOffset - headerOffset;

        window.scrollTo({
            top: offsetPosition,
            behavior: "smooth"
        });
    }, [searchParams])

    useEffect(() => {
        if (!pathname.includes("products") && !pathname.includes("brand")) {
            return;
        }
        const newParams = new URLSearchParams(searchParams.toString());
        newParams.delete(name);
        active.forEach((selectedOption) => {
            newParams.append(name, selectedOption);
        });

        newParams.sort()
        if (newParams.toString() === searchParams.toString()) {
            return;
        }
        window.history.pushState(null, '', `${pathname}?${newParams.toString()}`)
        // console.log(`${pathname}?${newParams.toString()}`)
        // router.push(`${pathname}?${newParams.toString()}`);
    }, [active]);

    useEffect(() => {
        const filtersFromURL = filters || [];
        setActive(Array.isArray(filtersFromURL) ? filtersFromURL : [filtersFromURL]);
    }, [name]);


    return { active, setActive };
}

export default useFilter;