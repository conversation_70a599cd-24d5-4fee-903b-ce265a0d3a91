"use client";

import UploadPopUp from "@/components/upload-prescription/UploadPopUp";
import "./my-perscription.scss";
import Image from "next/image";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { getPrescription } from "@/lib/methods/user";
import GenericModal from "@/components/generic-modal/GenericModal";
import { useContext, useState } from "react";
import api from "@/config/axios.interceptor";
import { endpoints } from "@/config/apiEndpoints";
import { toast } from "sonner";
import EmptyState from "@/components/empty-states/EmptyState";
import LogoAnimation from "@/components/LogoAnimation/LogoAnimation";
import Link from "next/link";
import DocumentCard from "@/components/document-card/DocumentCard";
import { TranslationContext } from "@/contexts/Translation";


const MyPerscriptionData = [
  {
    image: "/images/common/pdf.png",
    title: "Left Eye Test",
    data: "Jan 30 2023",
    download_btn: "Download",
  },

  {
    image: "/images/common/pdf.png",
    title: "Left Eye Test",
    data: "Jan 30 2023",
    download_btn: "Download",
  },

  {
    image: "/images/common/pdf.png",
    title: "Left Eye Test",
    data: "Jan 30 2023",
    download_btn: "Download",
  },

  {
    image: "/images/common/pdf.png",
    title: "Left Eye Test",
    data: "Jan 30 2023",
    download_btn: "Download",
  },
];

const isPDFFile = (fileName: string) => {
  const extension = fileName?.split(".").pop(); // Extract the file extension
  return extension?.toLowerCase() === "pdf"; // Check if the extension is 'pdf'
};

function MyPerscription() {
  const { data: prescription, isLoading } = useQuery({
    queryKey: ["prescription"],
    queryFn: getPrescription,
  });

  const {translation}:any = useContext(TranslationContext)

  const queryClient = useQueryClient();

  const [deleteModal, setDeleteModal] = useState(false);
  const [deleteId, setDeleteId] = useState("");
  const confirmDelete = (id: string) => {
    setDeleteId(id);
    setDeleteModal(true);
  };

  const handleDelete = () => {
    api.put(`${endpoints.deletePrescription}/${deleteId}`).then((res) => {
      if (res.data.errorCode === 0) {
        toast.success(res?.data?.message);
        queryClient.invalidateQueries({ queryKey: ["prescription"] });
        setDeleteId("");

        setDeleteModal(false);
      }
    });
  };
  return (
    <>
      <section className="my-perscription" style={!isLoading && prescription?.length === 0 ? { paddingBottom: "5rem" } : {}}>
        <div className="my-perscription_head">
          <h2>{translation?.myAccount?.myPrescription ?? "My Prescription"}</h2>
          <UploadPopUp />
        </div>
        {!isLoading && prescription?.length > 0 && (
          <div className="my-perscription_wrapper">
            {prescription?.map((items: any, index: number) => (
              <DocumentCard items={items} key={items?._id} confirmDelete={confirmDelete} />
            ))}
          </div>
        )}
        {!isLoading && prescription?.length === 0 && (
          <EmptyState icon="x-doc" title={translation?.myAccount?.emptyPrescription ?? "Your prescription is empty"} />
        )}
        {isLoading && (
          <div style={{ padding: "50px 0" }}>
            <LogoAnimation className="d-flex justify-content-center py-5" />
          </div>
        )}
      </section>

      <GenericModal
        callBack={handleDelete}
        show={deleteModal}
        primary={translation?.myAccount?.prescriptionDeleteYes ?? "Yes"}
        secondary={translation?.myAccount?.prescriptionDeleteNo ?? "No"}
        closeFn={() => setDeleteModal(false)}
        title={translation?.myAccount?.prescriptionDeleteWarning ?? "Are you sure you want to delete this prescription?"}
      />
    </>
  );
}

export default MyPerscription;
