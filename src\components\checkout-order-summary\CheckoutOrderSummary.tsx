"use client";

import { CheckoutContext } from "@/contexts/CheckoutContext";
import { HistoryContext } from "@/contexts/HistoryProvider";
import {
  getCart,
  getTryCart,
  getSubscriptionSummary,
} from "@/lib/methods/cart";
import { QueryClient, useQuery, useQueryClient } from "@tanstack/react-query";
import Image from "next/image";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { useContext, useEffect, useRef, useState } from "react";
import CouponModal from "../coupon-modal/Coupon";
import { toast } from "sonner";
import api from "@/config/axios.interceptor";
import { endpoints } from "@/config/apiEndpoints";
import SuccessPopup from "../success-popup/SuccessPopup";
import { ImageViewer } from "./ImageViewer";
import { TranslationContext } from "@/contexts/Translation";
import { useLocaleContext } from "@/contexts/LocaleProvider";

function CheckoutOrderSummary({
  params,
  shippingCharge,
  paymentMethodCharge,
  method,
  giftWrappingFee
}: {
  params: { id: string; type: string };
  shippingCharge?: number;
  paymentMethodCharge?: number;
  method?: string;
  giftWrappingFee?: number;
}) {
  const searchParams = useSearchParams();
  const [successData, setSuccessData] = useState({
    title: "Coupon Applied Successfully",
    description: "Great you have Applied the coupon successfully!",
    primaryBtnTxt: "Continue",
  });
  const [imageSrc, setImageSrc] = useState<any>({});
  const [showSuccess, setShowSuccess] = useState(false);
  const [cartSummary, setCartSummary] = useState<any>();
  const queryClient = useQueryClient();
  const id = searchParams.get("sub") || "";

  const { currentLocale, currencyCode } = useLocaleContext()

  const { data: cart, isLoading } = useQuery({
    queryKey: [
      params?.type === "try-cart"
        ? "tryCart"
        : params?.type === "subscription"
          ? "subscription-summary"
          : "cart", currentLocale?.split("-")[0]
    ],
    queryFn:
      params?.type === "try-cart"
        ? getTryCart
        : params?.type === "subscription"
          ? () => getSubscriptionSummary(id)
          : getCart,
  });
  const [modalCoupon, setModalCoupon] = useState<null | string>(null);
  const [show, setShow] = useState(false);
  const handleClose = () => setShow(false);
  const handleShow = () => setShow(true);
  const [summary, setSummary] = useState<any>([])

  const { setPrevPage } = useContext(HistoryContext);
  const { translation } = useContext(TranslationContext);
  const router = useRouter();

  const { setBoundingRect, selectedAddress } = useContext(CheckoutContext);
  useEffect(() => {
    setPrevPage({
      title: "Checkout",
      url: `/checkout/${params.type}`,
    });
  }, []);

  useEffect(() => {
    if (shippingCharge !== undefined && shippingCharge !== null) {
      getCartwithShipping(shippingCharge);
    } else {
      setCartSummary(null);
    }
  }, [shippingCharge]);

  const getCartwithShipping = async (charge: number) => {
    try {
      const res = await api.get(`/cart?shipping=${charge}`);
      setCartSummary(res.data.result);
    } catch (err) {
      return [];
    }
  };

  const summaryRef = useRef<any>(null);

  const handleSubmit = (externalCoupon?: any) => {
    api
      .post(endpoints.applyCoupon, { coupon: externalCoupon })
      .then((res) => {
        if (res.data?.errorCode === 0) {
          setSuccessData({
            ...successData,
            title: res?.data?.message,
            description: `You have applied coupon ${externalCoupon} successfully`,
          });
          queryClient.invalidateQueries({ queryKey: ["cart", "counts"] });
          queryClient.invalidateQueries({ queryKey: ["cart"] });
          setShowSuccess(true);
          setShow(false);
        } else {
          toast.error(res.data?.message);
        }
      })
      .catch((err) => {
        toast.error(err.response?.data?.message || "Something went wrong!");
      });
  };

  useEffect(() => {
    const boundingRect = summaryRef.current.getBoundingClientRect();
    setBoundingRect(boundingRect);
    if (!cart || !cart.products || cart.products.length === 0) {
      // router.push("/");
    }
    if (!isLoading) {
    }
  }, [cart, isLoading]);

  useEffect(() => {
    if (modalCoupon) {
      handleSubmit(modalCoupon);
    }
  }, [modalCoupon]);

  useEffect(() => {
    if (!isLoading && (cart?.summary && cart?.summary?.length > 0)) {
      let values = [];
      for (let [key, item] of cart?.summary?.entries()) {
        if (key == cart?.summary?.length - 1) {
          if (paymentMethodCharge && (paymentMethodCharge > 0)) {
            values.push({
              text: method === "COD" ? "Cash On Delivery Fee" : "Additional charges",
              value: paymentMethodCharge?.toFixed(2),
              sign: "+"
            })
          }
          if (giftWrappingFee && (giftWrappingFee > 0)) {
            values.push({
              text: "Gift Wrapping Fee",
              value: giftWrappingFee?.toFixed(2),
              sign: "+"
            })
          }
          values.push(item);
        } else {
          values.push(item);
        }
      }
      setSummary(values);
    }
  }, [isLoading, paymentMethodCharge, giftWrappingFee, cart])



  return (
    <>
      <div ref={summaryRef}>
        {/* {params?.type !== "subscription" && (
          <div className="checkout_order-coupon">
            <button onClick={handleShow}>
              <Image
                quality={100}
                priority
                src="/images/cart/fi_879757.png"
                width={24}
                height={24}
                alt="image"
              />
              <span>View available offers</span>
              <Image
                quality={100}
                priority
                className="arrow"
                src="/images/cart/Arrow-Down.png"
                width={14}
                height={14}
                alt="image"
              />
            </button>

            {show && (
              <CouponModal
                appliedCoupon={cart?.couponCode}
                setCoupon={setModalCoupon}
                show={show}
                handleClose={handleClose}
              />
            )}
          </div>
        )} */}

        <h5>{translation?.cartPage?.orderSummary ?? "Order Summary"}</h5>

        {cart?.products?.map((product: any) => (
          <div className="checkout_order" key={product?.productid}>
            <Link href={`/product/${product?.slug}`} className="pd-img">
              {/* <Image
                quality={100}
                priority
                src={
                  imageSrc?.[product?.productid]
                    ? imageSrc?.[product?.productid]
                    : product?.thumbnail
                }
                width={120}
                height={120}
                style={{ filter: product?.stock === 0 ? "grayscale(1)" : "" }}
                alt={product?.name}
                onError={() =>
                  setImageSrc({
                    [product?.productid]: "/images/product/noImage.jpg",
                  })
                }
              /> */}
              <ImageViewer product={product} />
            </Link>

            <Link href={`/product/${product?.slug}`}>
              <ul>
                <li>
                  {product?.name}
                  {params?.type !== "try-cart" && (
                    <span>&nbsp; x{product?.quantity}</span>
                  )}
                </li>
                {product?.stock === 0 && (
                  <li>
                    <span className="badge bg-light text-dark">
                      {translation?.productPage?.outOfStock ?? "Out of Stock"}
                    </span>
                  </li>
                )}
                {product?.color && <li>{translation?.productPage?.color ?? "Color"} : {product?.color}</li>}
                {product?.size?.name && <li>{translation?.productPage?.size ?? "Size"} : {product?.size?.name}</li>}
                {params?.type === "try-cart" ? (
                  <li>{currencyCode + " " + product?.price}</li>
                ) : (
                  <li>{currencyCode + " " + product?.priceTotal}</li>
                )}
              </ul>
            </Link>
          </div>
        ))}

        {/* <div className="checkout_order">
        <Image  quality={100}  priority    src="/images/common/s1.png" width={120} height={120} alt="Picture of the author" />

        <ul>
          <li>
            Police Moc 456 <span>x 1</span>
          </li>
          <li>Color : Black</li>
          <li>Size : M</li>
          <li>AED 129.00</li>
        </ul>
      </div>

      <div className="checkout_order">
        <Image  quality={100}  priority    src="/images/common/s2.png" width={120} height={120} alt="Picture of the author" />

        <ul>
          <li>
            Police Moc 456 <span>x 1</span>
          </li>
          <li>Color : Black</li>
          <li>Size : M</li>
          <li>AED 129.00</li>
        </ul>
      </div> */}

        <div className="checkout_total">
          <ul>
            {/* {paymentMethodCharge ? paymentMethodCharge > 0 && <>
              <li>
                <div className="d-flex align-items-center ">
                  {method === "COD" ? "Cash On Delivery Fee" : "Additional charges"}
                </div>
                <div>
                  {currencyCode}{" "}
                  <span>{paymentMethodCharge.toFixed(2)}</span>
                </div>
              </li>
            </> : ""} */}
            {/* {giftWrappingFee ? giftWrappingFee > 0 && <>
              <li>
                <div className="d-flex align-items-center ">
                  {"Gift Wrapping Fee"}
                </div>
                <div>
                  {currencyCode}{" "}
                  <span>{giftWrappingFee.toFixed(2)}</span>
                </div>
              </li>
            </> : ""} */}
            {cart
              ? summary?.map((item: any, index: number) => (
                item?.text !== "Shipping" ? <li key={item?.text + index}>
                  <div className="d-flex align-items-center ">
                    {item?.text}
                    {index === 0 && <h6>&nbsp; ({cart?.count} {translation?.cartPage?.items ?? "Items"})</h6>}
                  </div>
                  <div>
                    {item.sign}{' '}{currencyCode}{" "}
                    <span>{index === summary?.length - 1 ? <Total paymentMethodCharge={paymentMethodCharge} giftWrappingFee={giftWrappingFee} cartTotal={item.value} /> : item.value}</span>
                  </div>
                </li> : ""
              ))
              : summary?.map((item: any, index: number) => (
                item?.text !== "Shipping" ? <li key={item?.text + index}>
                  <div className="d-flex align-items-center ">
                    {item?.text}
                    {index === 0 && <h6>&nbsp; ({cart?.count} Items)</h6>}
                  </div>
                  <div>
                    {currencyCode}{" "}
                    <span>{index === summary?.length - 1 ? <Total paymentMethodCharge={paymentMethodCharge} giftWrappingFee={giftWrappingFee} cartTotal={item.value} /> : item.value}</span>
                  </div>
                </li> : ""
              ))}
          </ul>
        </div>
      </div>

      <SuccessPopup
        show={showSuccess}
        handleClose={() => setShowSuccess(false)}
        data={successData}
      />
    </>
  );
}

function Total({
  paymentMethodCharge, giftWrappingFee, cartTotal
}: any) {
  let total = Number(cartTotal) || 0;
  if (paymentMethodCharge) {
    total += Number(paymentMethodCharge);
  }
  if (giftWrappingFee) {
    total += Number(giftWrappingFee);
  }
  return <>{total?.toFixed(2)}</>;
}

export default CheckoutOrderSummary;
