"use client";
import ProductFilterHead from "@/components/product-listing/product-filter-head/ProductFilterHead";
import ProductListingHead from "@/components/product-listing/product-list-header/ProductListingHead";
import "./page.scss";
import Filter from "@/components/product-listing/filter/Filter";
import Listing from "@/components/product-listing/listing/Listing";
import { useContext, useEffect, useState } from "react";

import { useMediaQuery } from "usehooks-ts";

import Image from "next/image";
import MobileFilter from "@/components/product-listing/mobile-filter/MobileFilter";
import MobileSort from "@/components/product-listing/mobile-filter/MobileSort";
import { useInfiniteQuery, useQuery, useQueryClient } from "@tanstack/react-query";
import { endpoints } from "@/config/apiEndpoints";
import api from "@/config/axios.interceptor";
import { notFound, usePathname, useSearchParams } from "next/navigation";
import { HistoryContext } from "@/contexts/HistoryProvider";
import { FilterProvider } from "@/contexts/FilterContaxt";
import { TranslationContext } from "@/contexts/Translation";
import BreadCrumbs from "../breadcrumbs/BreadCrumbs";

function ListingPage({ slug, pageLimit, brandPage, query, breadCrump }: any) {
  const params = useSearchParams();
  const pathname = usePathname();
  const [filters, setFilters] = useState<any>(query ?? {});
  const queryClient = useQueryClient();
  const { setPrevPage } = useContext(HistoryContext);
  const { translation: { productListing: translation } }: any = useContext(TranslationContext)

  useEffect(() => {
    const newFilters: any = {};

    for (const [key, value] of params.entries()) {
      if (!newFilters[key]) {
        newFilters[key] = [value];
      } else {
        newFilters[key].push(value);
      }
    }
    setFilters(newFilters);
  }, [params]);

  // useEffect(() => {
  //   setPrevPage({
  //     title: `${slug === "allProducts" ? "Products" : category?.replaceAll("-", " ")
  //       }`,
  //     url: `${slug !== "allProducts" ? `/products/${slug}` : "/products"}`,
  //   });
  // }, [slug]);

  const getProducts = async ({ pageParam = 1 }) => {
    const res = await api.post(endpoints.products, {
      page: pageParam,
      limit: pageLimit,
      keyword: slug,
      brandPage,
      client: true,
      ...filters,
    });
    return res.data.result;
  };
  const {
    data: products,
    error,
    fetchNextPage,
    hasNextPage,
    isFetching,
    isLoading,
    isRefetching,
    isFetchingNextPage,
    status,
  } = useInfiniteQuery({
    queryKey: ["products", slug, filters],
    queryFn: getProducts,
    getNextPageParam: (lastPage: any, allPages) => {
      const nextPage = lastPage.nextPage;
      return nextPage;
    },
    initialPageParam: 1, // Add this line with an appropriate initial value
  });
  const keys = ["filters", slug]
  if (slug.includes("contact-lens")) {
    keys.push(filters)
  }
  const { data: allFilters, isLoading: filterLoading } = useQuery({
    queryKey: keys,
    queryFn: () => {
      return api.post(endpoints.filters + `?category=${slug}`, {
        ...filters,
      }).then((res) => res.data?.result );
    },
    
  })

  const [isFilterVisible, setFilterVisibility] = useState<boolean | "unset">("unset");
  const [isVirtualTry, setIsVirtualTry] = useState(false)
  const [gridView, setGridView] = useState("threeCols");
  const matches = useMediaQuery("(max-width: 991px)");
  const mobile = useMediaQuery("(max-width: 768px)");

  const [show, setShow] = useState(false);
  const handleClose = () => setShow(false);
  const handleShow = () => setShow(true);

  const [showSort, setShowSort] = useState(false);
  const handleCloseSort = () => setShowSort(false);
  const handleShowSort = () => setShowSort(true);

  const toggleFilterVisibility = () => {
    setFilterVisibility(!isFilterVisible);
  };

  const toggleView = (e: any) => {
    setGridView(e);
  };

  useEffect(() => {
    if (!isLoading) {
      if (error && error?.status == 404) {
        notFound()
      }
    }
  }, [isLoading])

  useEffect(() => {
    if (!isFilterVisible && gridView === "twoCols") {
      if (!mobile) {
        setGridView("threeCols");
      }
    }
  }, [isFilterVisible, gridView]);

  useEffect(() => {
    if (matches) {
      setFilterVisibility(false);
      if (gridView === "fourCols") {
        setGridView("threeCols");
      }
    } else {
      if (isFilterVisible === "unset") {
        setFilterVisibility(true);
      }
    }
    if (mobile) {
      console.log(gridView)
      if (gridView != "twoCols" && gridView != "oneCol") {
        setGridView("twoCols");
      }
    } else {
      if (gridView == "oneCol") {
        setGridView("twoCols");
      }
    }
  }, [matches, mobile, gridView]);

  // if(filterLoading) return null;

  return (
    <>
      {breadCrump && <BreadCrumbs
        backHome="Home"
        currentPage={
          // params.slug?.[0] ? `/ ${params.slug?.[0]?.replaceAll("-", " ").replaceAll("%20", " ")}` : "/ All Products"
          slug != "allProducts" ? products ? `${products?.pages[0]?.breadCrump?.category}` : "" : "/ All Products"
        }
        image={
          products
            ? products?.pages[0]?.banner || "/images/common/banner4.png"
            : "/images/common/banner4.png"
        }
      />}
      <FilterProvider>
        <div className="container">
          <div id="product-navigation" className="product-navigation">
            <ProductFilterHead onButtonClick={toggleFilterVisibility} />
            <ProductListingHead
              data={products?.pages?.[0]?.count || 0}
              filterVisibility={isFilterVisible}
              onViewChange={toggleView}
              matches={matches}
              view={gridView}
              cats={allFilters?.subCategory}
              mainCats={allFilters?.category}
              brand={products?.pages[0]?.brand}
            />
          </div>

          <div className="product-flex">
            {(isFilterVisible && isFilterVisible !== "unset") && <Filter translation={translation} isVirtualTry={isVirtualTry} setIsVirtualTry={setIsVirtualTry} brandPage={brandPage} slug={slug} filters={allFilters} />}
            <Listing
              slug={slug}
              fetchNextPage={fetchNextPage}
              isFetchingNextPage={isFetchingNextPage}
              isFetching={isFetching}
              hasNextPage={hasNextPage}
              data={products}
              isVirtualTry={isVirtualTry}
              className={`${gridView} ${isFilterVisible ? "filter-show" : "filter-hide"
                } `}
              activeFilters={filters}
              filters={allFilters} // Add this line
            />
          </div>

          <div className="filter-sort">
            <div className="filter-sort_flex">
              <button onClick={handleShow} className="filter-btn">
                <Image
                  quality={100}
                  priority
                  src="/images/common/filter.svg"
                  unoptimized={true}
                  width={24}
                  height={24}
                  alt="filter"
                />
                {translation?.filterBy || "Filter by"}
              </button>
              <MobileFilter
                filters={allFilters}
                translation={translation}
                isVirtualTry={isVirtualTry}
                setIsVirtualTry={setIsVirtualTry}
                brandPage={brandPage}
                slug={slug}
                show={show}
                handleClose={handleClose}
              />

              <button onClick={handleShowSort} className="sort-btn">
                <Image
                  quality={100}
                  priority
                  src="/images/common/sort.svg"
                  unoptimized={true}
                  width={24}
                  height={24}
                  alt="filter"
                />
                {translation?.sortBy || "Sort by"}
              </button>
              <MobileSort show={showSort} translation={translation} handleClose={handleCloseSort} />
            </div>
          </div>
        </div>
      </FilterProvider>
    </>
  );
}

export default ListingPage;
