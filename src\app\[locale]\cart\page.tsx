import BreadCrumbs from "@/components/breadcrumbs/BreadCrumbs";
import "./cart.scss";
import { dehydrate, HydrationBoundary, QueryClient } from "@tanstack/react-query";
import Cart from "@/components/cart/Cart";
import { getCart } from "@/lib/methods/cart";

async function Page(props: {params: { locale: string }}) {
  const queryClient = new QueryClient();
  await queryClient.prefetchQuery({
    queryKey: ["cart", props.params.locale?.split("-")[0]],
    queryFn: getCart,
  });

  return (
    <main>
      <BreadCrumbs backHome="Home" currentPage="My Cart" image="/images/common/banner5.png" />
      <HydrationBoundary state={dehydrate(queryClient)}>
        <Cart />
      </HydrationBoundary>
    </main>
  );
}

export default Page;
