.buy-with {
  &.modal {
    &.show .modal-dialog {
      transform: none !important;
    }

    .modal-dialog {
      margin: 0;
      position: fixed;
      right: 0;
      width: 100%;
      max-width: 68.3rem;
      height: 100%;
    }

    .modal-header {
      padding: 0;
      border: none;

      .btn-close {
        background-image: url(../../../public/images/common/close.svg);
        width: 4.8rem;
        height: 4.8rem;
        background-position: center;
        background-repeat: no-repeat;
        background-size: contain;
        opacity: 1;
        padding: 0;

        @media (max-width: 575.98px) {
          width: 2.8rem;
          height: 2.8rem;
        }

        &:focus {
          box-shadow: none;
          outline: none;
          border: none;
        }
      }

      h2 {
        text-align: left;
        line-height: 3.1rem;
        display: flex;
        align-items: center;
        column-gap: 1.4rem;
        font-size: 2.5rem;
        font-weight: 500;


        img {
          width: 4rem;
          height: 4rem;
          object-fit: contain;

          @media (max-width: 575.98px) {
            display: none;
          }
        }
      }
    }

    .modal-content {
      border: none;
      border-radius: 0;
      overflow-y: auto;
      height: 100vh;

      @media (max-width: 575.98px) {
        height: 100%;
      }

      .cta {
        font-size: 1.2rem;
        font-weight: 400;
        color: #000000;
        opacity: 50%;
        line-height: 1.5rem;
        margin-top: 3rem;
        margin-bottom: 3.7rem;
        text-align: center;

        a {
          color: #000000;
          opacity: 100%;

          &:hover {
            color: #0018ff;
          }
        }

        @media (max-width: 575.98px) {
          margin: 2.5rem 0;
          line-height: 1.5rem;
        }
      }
    }

    .modal-wrap {
      padding: 4rem 5rem 0rem 6.2rem;

      @media (max-width: 575.98px) {
        padding: 1.5rem;
        padding-bottom: 0;
      }

    }

    .modal-body {
      padding: 0;
      display: flex;
      flex-direction: column;

      label {
        display: flex;
        align-items: center;
        color: #000;
        font-size: 1.6rem;
        font-weight: 500;
        column-gap: 1.4rem;
        margin-top: 3rem;
      }

      input {
        appearance: none;
        width: 2rem;
        height: 2rem;
        border-radius: 50%;
        border: 1px solid #000;
        position: relative;

        &:checked {
          &::after {
            content: "";
            width: 1.2rem;
            height: 1.2rem;
            background-color: #000;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            position: absolute;
            border-radius: 50%;
          }
        }
      }

      .dropDown {
        margin-top: 1.5rem;
        border: 1px solid #E4E4E4;
        border-radius: 1.5rem;
        padding: 2rem;

        @media (max-width: 575.98px) {
          margin-top: 1.5rem;
          padding: 1.9rem 1.5rem;
        }

        &.active {
          border: 2px solid #000000
        }

        &_head {
          display: flex;
          align-items: center;
          column-gap: 2.5rem;

          @media (max-width: 575.98px) {
            column-gap: 1rem;
          }

          img {
            width: 7.2rem;
            height: 7.2rem;
            object-fit: contain;

            @media (max-width: 575.98px) {
              width: 6.5rem;
              height: 6.5rem;
              object-fit: contain;
            }
          }

          h5 {
            font-size: 1.6rem;
            font-weight: 600;
            line-height: 2.4rem;
            letter-spacing: -0.011em;
            color: #000000;

            @media (max-width: 575.98px) {
              font-size: 1.4rem;
              font-weight: 500;
              line-height: 1.7rem;
            }
          }

          p {
            font-size: 1.3rem;
            font-size: 400;
            line-height: 1.6rem;
            color: #000000;

            @media (max-width: 575.98px) {
              margin-top: 0.6rem;
              max-width: 19.8rem;
              width: 100%;
            }
          }
        }

        &_select {

          p {
            font-size: 1.3rem;
            font-weight: 400;
            line-height: 1.9rem;
            letter-spacing: -0.011em;
            color: rgba(0, 0, 0, 0.8);
            margin-top: 1.5rem;
          }

          .css-1jqq78o-placeholder {
            font-size: 1.3rem;
            font-weight: 400;
            color: #999999;
            line-height: 1.6rem;
          }

          .css-13cymwt-control {
            border: none;
            border-bottom: 1px solid rgba(0, 0, 0, 0.15) !important;
            border-radius: 0;
            max-width: 33.8rem;
            width: 100%;

            .css-1fdsijx-ValueContainer {
              padding: 0 !important;
              margin: 0 !important;

              .css-qbdosj-Input {
                padding: 0;
                margin: 0;
              }
            }

            .css-1u9des2-indicatorSeparator {
              display: none;
            }

            .css-1xc3v61-indicatorContainer {
              padding-right: 0;
            }

            .css-13cymwt-control {
              background-color: red;

              &:hover {
                border: none;
                background-color: blue;
              }
            }
          }

          .css-1dimb5e-singleValue {
            color: #000000;
            font-size: 1.3rem;
            font-weight: 400;
          }

          .css-t3ipsp-control {
            border: none !important;
            border-bottom: 1px solid rgba(0, 0, 0, 0.15) !important;
            outline: none;
            box-shadow: none;
            padding: 0;
            max-width: 33.8rem;
            width: 100%;

            .css-1fdsijx-ValueContainer {
              padding: 0;
              margin: 0;
            }

            .css-1xc3v61-indicatorContainer {
              padding-right: 0;
            }
          }

          .css-1nmdiq5-menu {
            color: #000000;
            font-size: 1.3rem;
            font-weight: 400;
            z-index: 10;

            div {
              &>div {
                transition: .2s;

                &[aria-selected="true"] {
                  background-color: black;
                  color: white;
                }

                &[aria-selected="false"] {
                  background-color: white;
                  color: black;
                }

                &:hover {
                  background-color: black;
                  color: white;
                }
              }
            }

          }

          .css-1u9des2-indicatorSeparator {
            display: none;
          }
        }
      }

      .accordion {


        .accordion-item {
          border: none;
          border: 1px solid #E4E4E4;
          border-radius: 1.5rem;
          padding: 2rem;
          margin-top: 1.5rem;

          &:has(.show){
            border: 2px solid black
          }
        }

        .accordion-button {
          background-color: transparent;
          padding: 0;
          box-shadow: none;

          &:not(.collapsed)::after {
            background-image: url(../../../public/images/modal/Arrow-Down.svg);
          }

          &::after {
            background-image: url(../../../public/images/modal/Arrow-Down.svg);

            @media (max-width: 575.98px) {
              margin-top: -43px;
            }

          }
        }

        .accordion-body {
          padding: 0;
          padding-left: 14rem;

          @media (max-width: 575.98px) {
            padding-left: 7rem;
          }
        }

        .accordion-collapse {
          transition: .3s all ease;

          &.show {
            border-top: 1px solid rgba(0, 0, 0, 0.1) !important;
            margin-top: 1.5rem;
            transition: .3s all ease;
          }
        }
      }


    }

    .modal-footer {
      padding: 0;
      border: none;
      background-color: #F2F4F9;
      padding: 0.8rem 4.5rem .8rem 6.2rem;
      margin-top: auto;
      display: flex;
      justify-content: space-between;
      flex-wrap: nowrap;

      @media (max-width: 575.98px) {
        padding: 1rem 1.5rem;
      }

      button {
        color: #fff;
        text-align: center;
        font-size: 1.5rem;
        font-weight: 500;
        border: none;
        background-color: #000;
        height: 4.5rem;
        width: 15rem;
        border-radius: 6rem;

        &.disabled {
          background-color: #797A7D;
        }
      }

      h5 {
        font-size: 1.4rem;
        font-weight: 600;
        color: #000000;

        span {
          font-size: 1.8rem;
        }
      }
    }
  }
  // &.popup-rtl{
  //   &.modal{
  //     .modal-body{
  //       .accordion{
  //         .accordion-button::after{
  //           margin-left: 0;
  //           margin-right: auto;
  //         }
  //       }
  //       .dropDown_head{
  //         h5{
  //           width: fit-content;
  //         }
  //       }
  //     }
  //   }
  //   .modal-header{
  //     .btn-close{
  //       margin: 0;
  //       margin-right: auto;
  //     }
  //     h2 {
  //       img{
  //         transform: rotate(180deg);
  //       }
  //     }
  //   }
  // }
}

.app.rtl {
  .buy-with.modal .modal-wrap {
    padding: 4rem 6.2rem 0rem 5rem;

    @media (max-width: 575.98px) {
      padding: 1.5rem;
    }
  }

  .buy-with.modal .modal-body .dropDown_head {
    text-align: right;
  }

  .buy-with.modal .modal-body .accordion .accordion-button::after {
    margin-left: 0;
    margin-right: auto;
  }

  .buy-with.modal .modal-header .btn-close {
    margin-left: 0;
  }

  .buy-with.modal .modal-header h2 img {
    transform: rotate(180deg);
  }
}