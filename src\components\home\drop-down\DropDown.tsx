"use client";

import { useState } from "react";
import { Dropdown } from "react-bootstrap";

export default function DropDown({ className, setCollection, collection, keyword, item }: any) {
  const [show, setShow] = useState(false);
  console.log(item)
  return (
    <>
      <Dropdown
        className={className}
        onSelect={() => setShow(false)}
        onMouseOver={() => setShow(true)}
        onMouseLeave={() => setShow(false)}>
        <div className="pb-4 d-flex gap-3 align-items-center">
          <h3>{item?.title}</h3>
          <Dropdown.Toggle id="dropdown-collection">{collection?.title}</Dropdown.Toggle>
          </div>

          <Dropdown.Menu
            onMouseOver={() => setShow(true)}
            onMouseLeave={() => setShow(false)}
            show={show}
            bsPrefix="bestsellerx"
            style={{
              minWidth: "176px",
              fontSize: "16px",
              padding: 0,
              overflow: "hidden",
              borderRadius: "12px",
            }}
            className="dropdown-menu bestseller-dropdown">
            {keyword?.map((item: any) => (
              <Dropdown.Item
                className={item._id === collection?._id ? "active" : ""}
                //onMouseEnter={(e) => e.target.classList.add("active")}
                style={{ padding: "10px 10px" }}
                key={item._id}
                onClick={() => {
                  setCollection(item);
                  setShow(false);
                }}>
                {item.title}
              </Dropdown.Item>
            ))}
          </Dropdown.Menu>

      </Dropdown>
    </>
  );
}
