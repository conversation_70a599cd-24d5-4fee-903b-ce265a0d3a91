"use client";
import Image from "next/image";
import "./product-sticky-bar.scss";
import { useState, lazy, Suspense, useContext } from "react";
//import ComparePopUp from "@/components/compare-popup/ComparePopUp";
const ComparePopUp = lazy(() => import("@/components/compare-popup/ComparePopUp"));
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { AnimatePresence, motion } from "framer-motion";
import api from "@/config/axios.interceptor";
import { endpoints } from "@/config/apiEndpoints";
import { toast } from "sonner";
import OverLayLoader from "../LogoAnimation/OverLayLoader";
import { TranslationContext } from "@/contexts/Translation";

function ProductStickyBar({ compareData }: any) {
  const {translation: { compare, productPage } } = useContext(TranslationContext)
  const [show, setShow] = useState(false);
  const handleClose = () => setShow(false);
  const handleShow = () => setShow(true);
  const queryClient = useQueryClient();
  const removeCompare = async (data: any) => {
    api.post(endpoints.removeFromCompare, data).then((res) => {
      if (res.data?.errorCode === 0) {
        toast.success(res.data?.message);
        queryClient.invalidateQueries({ queryKey: ["compare"] });
      } else {
        toast.error(res.data?.message);
      }
    });
  };

  return (
    <>
      <AnimatePresence>
        {compareData?.length > 0 && (
          <motion.div
            initial={{ opacity: 0, translateY: 100 }}
            animate={{ opacity: 1, translateY: 0 }}
            transition={{
              duration: 0.8,
              delay: 0.5,
              ease: [0, 0.71, 0.2, 1.01],
            }}
            className="product-sticky-bar">
            <div className="container">
              <div className="product-sticky-bar_wrapper">
                <ul>
                  {compareData?.map((item: any, index: number) => (
                    <motion.li
                      key={item?._id}
                      initial={{ scale: 0, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      exit={{ scale: 0, opacity: 0 }}
                      transition={{ type: "spring", stiffness: 900, damping: 40 }}>
                      <Image quality={100} priority src={item?.thumbnail ?? ""} width={56} height={29} alt="specs" />
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        transition={{ type: "spring", stiffness: 400, damping: 17 }}
                        onClick={() => removeCompare({ product: item?._id, type: "single" })}>
                        <Image quality={100} priority
                          src="/images/product-detail/close-btn.png"
                          width={13}
                          height={13}
                          alt="close btn"
                        />
                      </motion.button>
                    </motion.li>
                  ))}
                </ul>

                <div className="product-sticky-bar_btns">
                  <button
                    onClick={() => removeCompare({ product: "", type: "all" })}
                    className="remove-btn">
                    {compare?.removeAll ?? "Remove All"}
                  </button>
                  <button onClick={handleShow} className="compare-btn">
                    {compare?.compareBtn ?? "Compare"} {compareData?.length}
                  </button>
                  <Suspense fallback={<OverLayLoader />}>
                    <ComparePopUp data={compareData} show={show} handleClose={handleClose} />
                  </Suspense>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}

export default ProductStickyBar;
