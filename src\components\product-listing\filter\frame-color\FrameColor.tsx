const FrameColorData = [
  {
    frame_color: "Black(70)",
  },

  {
    frame_color: "Blue(27)",
  },

  {
    frame_color: "Green(19)",
  },

  {
    frame_color: "<PERSON>(19)",
  },

  {
    frame_color: "<PERSON>(18)",
  },

  {
    frame_color: "Transparent(14)",
  },
];

function FrameColor() {
  return (
    <>
      {FrameColorData.map((items, index) => (
        <label key={index} htmlFor={`frame-${index}`}>
          <input type="checkbox" id={`frame-${index}`} />
          {items.frame_color}
        </label>
      ))}
    </>
  );
}

export default FrameColor;
