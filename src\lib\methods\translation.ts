import { endpoints } from "@/config/apiEndpoints";
import { cookies } from "next/headers";

export async function getTranslation(locale:string) {
    // const locale = cookies().get("Next-Locale")?.value || "ae-en";
    const [storeId, language] = locale.split("-")
    const res = await fetch(
        process.env.NEXT_PUBLIC_API_URL + endpoints.translation,
        {
            headers: {
                "Content-Type": "application/json",
                language: language || "en",
                storeid: storeId || "ae",
            },
            next: {
                tags: ["texts"],
            },
            cache: "force-cache"
        }
    );
    const data = await res.json();
    return data
}