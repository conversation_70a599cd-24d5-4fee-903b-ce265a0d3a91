import { NextRequest } from "next/server";
import jsonData from "../countries+states.json";

export async function GET(
  req: NextRequest,
  { params }: { params: { country: string } }
) {
  const country = jsonData?.find((country) => country.name === params.country);
  const states = country?.states;
  // if (states && states?.length > 0) {
  return new Response(JSON.stringify(states));
  // }
  // else {
  return new Response(
    JSON.stringify([{ name: "Not Applicable", code: "N/A" }])
  );
  // }
}
