import BreadCrumbs from "@/components/breadcrumbs/BreadCrumbs";
import Image from "next/image";
import "./blogs-detail.scss";
import api from "@/config/axios.interceptor";
import { endpoints } from "@/config/apiEndpoints";
import ErrorPage from "@/components/404/ErrorPage";
import BlogShare from "@/components/blogs/BlogShare";
import { Metadata } from "next";

export async function generateMetadata({
  params,
}: {
  params: { blog: string };
}): Promise<Metadata> {
  const res = await api.post(endpoints.blogDetail, {
    slug: params.blog,
  });
  const blog = await res.data.result;

  return {
    title: blog?.title + " | Yateem Optician",
    description: blog?.summary,

    openGraph: {
      publishedTime: blog?.createdAt,
      modifiedTime: blog?.updatedAt,
      type: "article",
      images: [
        {
          url: blog?.image,
          width: 742,
          height: 396,
          alt: "blog?.title",
        },
      ],
    },
  };
}
async function BlogsDetail({ params }: any) {
  try {
    const res = await api.post(endpoints.blogDetail, {
      slug: params.blog,
    });
    if (res.data.errorCode !== 0) {
      return (
        <div>
          <ErrorPage errorcode="404" />
        </div>
      );
    }
    const blog = await res.data.result;
    return (
      <main>
        <BreadCrumbs
          backHome="Home"
          currentPage={[
            { title: "Blogs", link: "/blogs" },
            { title: blog?.title, link: "" },
          ]}
          image={"/images/common/blogs.png"}
        />

        <section className="blogs_head">
          <div className="container">
            <h2>{blog?.title}</h2>
            <div className="blogs_flex">
              <div className="blogs_user">
                <Image
                  src={
                    blog?.createdBy?.image
                      ?? "/images/common/default_user.jpg"
                  }
                  width={51}
                  height={51}
                  alt="user"
                />
                <div className="blogs_user-info">
                  <h5>{blog?.author || blog?.createdBy?.name}</h5>
                  <span>{new Date(blog?.createdAt).toDateString()}</span>
                </div>
              </div>
              <div className="blogs_icons d-none d-sm-block">
                <BlogShare />
              </div>
            </div>
            <p style={{paddingTop: "1rem"}}>{blog?.summary}</p>

            <div className="blogs_image">
              <Image
                quality={100}
                priority
                src={blog?.image ?? ""}
                width={1140}
                height={383}
                alt="main image"
              />
            </div>
            <div className="blogs_icons d-sm-none d-block">
              <BlogShare />
            </div>
          </div>
        </section>

        <section className="image-content">
          <div className="container">
            <div className="image-content_desc">
              <div
                className="image-content_text"
                dangerouslySetInnerHTML={{ __html: blog?.content }}
              ></div>
            </div>
          </div>
        </section>
      </main>
    );
  } catch (e) {
    return <div></div>;
  }
}

export default BlogsDetail;
