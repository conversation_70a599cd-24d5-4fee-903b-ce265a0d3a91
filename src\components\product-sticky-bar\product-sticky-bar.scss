.product-sticky-bar {
  background-color: rgba(0, 0, 0, 1);
  padding: 1.2rem 0;
  position: fixed;
  width: 100%;
  bottom: 0;
  left: 0;
  z-index: 3;

  @media (max-width: 575.98px) {
    bottom: 69px;
  }

  &_wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;

    ul {
      display: flex;
      column-gap: 1.5rem;

      @media (max-width: 575.98px) {
        column-gap: 0.5rem;
      }

      li {
        width: 5.6rem;
        height: 5.9rem;
        border-radius: 0.8rem;
        background: #fff;
        display: flex;
        align-items: center;
        position: relative;

        @media (max-width: 575.98px) {
          width: 3.7rem;
          height: 3.9rem;
        }

        img {
          width: 5.6rem;
          height: 2.9rem;
          object-fit: contain;

          @media (max-width: 575.98px) {
            width: 3.7rem;
            height: 1.9169rem;
          }
        }

        button {
          border: none;
          background-color: transparent;
          position: absolute;
          padding: 0;
          top: -4px;
          right: 4px;
          z-index: 11;

          img {
            width: 1.2rem;
            height: 1.2rem;
            object-fit: contain;
          }
        }
      }
    }
  }

  &_btns {
    display: flex;
    column-gap: 1.5rem;

    @media (max-width: 575.98px) {
      column-gap: 0.8rem;
    }

    button {
      border-radius: 2.95rem;
      border: 1px solid #fff;
      width: 17.2rem;
      height: 4.5rem;
      background-color: transparent;
      font-size: 1.6rem;
      font-weight: 600;

      @media (max-width: 575.98px) {
        width: 9.1rem;
        height: 3.7rem;
        font-size: 1.2rem;
      }
    }

    .remove-btn {
      color: #fff;
    }

    .compare-btn {
      background-color: #ffffff;
      color: #000000;
    }
  }
}