import BreadCrumbs from "@/components/breadcrumbs/BreadCrumbs";
import Counter from "@/components/about-us/counter/Counter";
import TimeLine from "@/components/about-us/timeline/TimeLine";
import ContentWithImage from "@/components/about-us/content-with-image/ContentWithImage";
import Image from "next/image";
import "./aboutstyle.scss";
import { endpoints } from "@/config/apiEndpoints";
import ErrorPage from "@/components/404/ErrorPage";
import { Metadata } from "next";
import { cookies } from 'next/headers';

export async function generateMetadata({
  params,
}: {
  params: any;
}): Promise<Metadata> {
  const baseURL = process.env.NEXT_PUBLIC_API_URL || "";
  const locale = cookies().get("Next-Locale")?.value || "sa-en";
  const [storeId, language] = locale.split("-");
  
  const res = await fetch(`${baseURL}${endpoints.about}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      language: language || "en",
      storeid: storeId || "sa"
    },
  });
  const data = await res.json();
  const aboutData = data.result;
  return {
    title: locale === "ar" ? aboutData?.seoDetails?.title?.ar : aboutData?.seoDetails?.title?.en || "Yateem Optician",
    description: locale === "ar" ? aboutData?.seoDetails?.description?.ar : aboutData?.seoDetails?.description?.en || "Yateem Optician",
    keywords: locale === "ar" ? aboutData?.seoDetails?.keywords?.ar : aboutData?.seoDetails?.keywords?.en || "Yateem Optician",

    openGraph: {
      images: [
        {
          url: aboutData?.seoDetails?.ogImage,
          width: 742,
          height: 396,
          alt: locale === "ar" ? aboutData?.sectionOne?.title?.ar : aboutData?.sectionOne?.title?.en,
        },
      ],
      title: locale === "ar" ? aboutData?.seoDetails?.title?.ar : aboutData?.seoDetails?.title?.en || "Yateem Optician",
      description: locale === "ar" ? aboutData?.seoDetails?.description?.ar : aboutData?.seoDetails?.description?.en || "Yateem Optician",
      type: "website",
    },
    alternates: {
      canonical: locale === "ar" ? aboutData?.seoDetails?.canonical?.ar : aboutData?.seoDetails?.canonical?.en,
    },
  };
}

async function AboutUs({ params }: { params: { locale: string } }) {
  const baseURL = process.env.NEXT_PUBLIC_API_URL;
  try {
    const locale = cookies().get("Next-Locale")?.value || "sa-en";
    const [storeId, language] = locale.split("-");
    
    const res = await fetch(baseURL + endpoints.about, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        language: language || "en",
        storeid: storeId || "sa"
      },
      next: { tags: ["about-us"] },
    });
    const data = await res.json();
    if (data?.errorCode === 0) {
      const aboutData = data?.result;
      console.log(aboutData?.sectionTwo)
      return (
        <main>
          <BreadCrumbs
            backHome="Home"
            currentPage="/ About us"
            image="/images/common/blogs.png"
          />
          <div className="aboutus">
            <section className="aboutus_content">
              <div className="container">
                <h2>{aboutData?.sectionOne?.title}</h2>
                <p>{aboutData?.sectionOne?.description}</p>
              </div>
            </section>

            <section className="aboutus_image">
              <div className="container">
                <Image
                  quality={100}
                  priority
                  src={
                    aboutData?.sectionOne?.image
                      ?? ""
                  }
                  width={1140}
                  height={426}
                  alt="images"
                />
              </div>
            </section>

            <section className="counter">
              <div className="container">
                <div className="counter_wrapper">
                  <Counter
                    title={aboutData?.sectionTwo[0]?.title}
                    count={aboutData?.sectionTwo[0]?.count}
                    mode={aboutData?.sectionTwo[0]?.symbol}
                  />
                  <Counter
                    title={aboutData?.sectionTwo[1]?.title}
                    count={aboutData?.sectionTwo[1]?.count}
                    mode={aboutData?.sectionTwo[1]?.symbol}
                  />
                  <Counter
                    title={aboutData?.sectionTwo[2]?.title}
                    count={aboutData?.sectionTwo[2]?.count}
                    mode={aboutData?.sectionTwo[2]?.symbol}
                  />
                  <Counter
                    title={aboutData?.sectionTwo[3]?.title}
                    count={aboutData?.sectionTwo[3]?.count}
                    mode={aboutData?.sectionTwo[3]?.symbol}
                  />
                </div>
              </div>
            </section>

            <TimeLine timelineData={aboutData?.sectionThree} />
            <ContentWithImage sectionData={aboutData?.sectionFour} />
          </div>
        </main>
      );
    } else {
      return <ErrorPage errorcode="500" />;
    }
  } catch (e) {
    return <ErrorPage errorcode="500" />;
  }
}

export default AboutUs;
