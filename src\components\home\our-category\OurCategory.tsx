import { getCategories } from "@/lib/methods/categories";
import "./ourCategory.scss";
import Image from "next/image";
import Link from "next/link";

export default async function OurCategory({ id, type = "all" }: any) {
  const categories = await getCategories(type);
  return (
    <section className="category" id={id}>
      <div className="container">
        <div className="category_title">
          <h2>{categories?.title || "Our Category"}</h2>
        </div>
        <div className="category_wrapper">
          {categories?.categories?.map((items: any) => (
            <Link
              href={`/products/${items?.slug}`}
              key={items?._id}
              className="category_items"
            >
              <Image
                quality={100}
                priority
                className="normal_image"
                src={items.image ?? ""}
                width={1000}
                height={1000}
                alt="image"
              />
              {/* <img
                className="normal_image"
                src={items.image ?? ""}
                alt="image"
              />
              <img
                className="hover_image"
                src={items.hoverImage ?? ""}
                alt="image"
              /> */}
              <Image
                quality={100}
                loading="lazy"
                className="hover_image"
                src={items.hoverImage ?? ""}
                width={1000}
                height={1000}
                alt="image"
              />
              <h4>{items?.name}</h4>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}
