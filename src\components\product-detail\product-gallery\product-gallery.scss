.gallery {
  &_single-row {
    position: relative;

    &:has(img.opacity-0) {
      background-color: #fdfdfd;
      background-image: url(/images/common/logo.png);
      animation: skeleton 1s linear infinite alternate;
      background-repeat: no-repeat;
      background-position: center;
      background-size: 40%;
    }

    img {
      border-radius: 2rem;
      cursor: zoom-in;
      // box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
      height: 50rem;
      object-fit: contain;
      border: 1px solid #c1c1c169;
    }

    button {
      position: absolute;
      right: 0;
      border: none;
      border-radius: 2.7rem;
      background: #000;
      color: #fff;
      font-size: 1.4rem;
      font-weight: 600;
      width: 14.2rem;
      height: 3.8rem;
      top: 2.8rem;
      right: 2.1rem;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: auto;
        height: auto;
        margin-left: 0.7rem;
      }
    }
  }

  &_multi-row {
    display: flex;
    margin-top: 2.3rem;
    justify-content: space-between;
    flex-wrap: wrap;
    row-gap: 2.3rem;

    img {
      cursor: zoom-in;
      width: calc(50% - 1.8rem / 2);
      border-radius: 2rem;
      // box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
      height: 30rem;
       object-fit: contain;
       border: 1px solid #c1c1c169;
    }
  }
}