"use client";

import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay } from "swiper/modules";
import "./brandcollection.scss";
import Image from "next/image";
import Link from "next/link";
import { NavItem } from "react-bootstrap";

const brandcollection = [
  {
    image: "/images/home/<USER>",
    title: "Boutique",
    link: "DISCOVER MORE",
  },

  {
    image: "/images/home/<USER>",
    title: "Premium",
    link: "DISCOVER MORE",
  },

  {
    image: "/images/home/<USER>",
    title: "Life style",
    link: "DISCOVER MORE",
  },

  {
    image: "/images/home/<USER>",
    title: "Boutique",
    link: "DISCOVER MORE",
  },

  {
    image: "/images/home/<USER>",
    title: "Premium",
    link: "DISCOVER MORE",
  },

  {
    image: "/images/home/<USER>",
    title: "Life style",
    link: "DISCOVER MORE",
  },
];

type CollectionType = {
  title: string;
  buttonText: string;
  link: string;
  image: string;
  video: string | null;
  _id: string;
};

export default function BrandsCollection({ id, data, item }: any) {
  return (
    <section className="brandcoll" id={id}>
      <div className="brandcoll_wrapper">
        <div className="container-fluid">
          <div className="brandcoll_title">
            <h2>{item?.mainTitle}</h2>
          </div>
          <Swiper
            modules={[Autoplay]}
            className="mySwiper"
            speed={500}
            loop={true}
            autoplay={{
              delay: 2000,
              disableOnInteraction: false,
              pauseOnMouseEnter: true,
            }}
            breakpoints={{
              320: {
                slidesPerView: 1.2,
                spaceBetween: 15,
              },

              600: {
                slidesPerView: 2,
                spaceBetween: 20,
              },

              1199: {
                slidesPerView: 3,
                spaceBetween: 30,
              },
            }}
          >
            {data.map((items: CollectionType) => (
              <SwiperSlide key={items?._id}>
                <Link href={items?.link} className="d-block">
                  <Image quality={100} priority
                    src={items.image??""}
                    width={10000}
                    height={10000}
                    alt="image"
                  
                  />
                </Link>
                <div className="brandcoll_card-content">
                  <h5>{items.title}</h5>
                  <Link href={items?.link}>{items.buttonText}</Link>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
        </div>
      </div>
    </section>
  );
}
