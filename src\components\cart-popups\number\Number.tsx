"use client";
import Modal from "react-bootstrap/Modal";
import { toast } from "sonner";
import "./number.scss";
import { useEffect, useRef, useState } from "react";
import Select from "react-select";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import { RecaptchaVerifier, signInWithPhoneNumber } from "firebase/auth";
import { countryCodeWithFlags } from "@/lib/countryCodeWithFlag";
// import { auth } from "../../../../firebase-config";
import ReactDOM from 'react-dom'
import { endpoints } from "@/config/apiEndpoints";
import api from "@/config/axios.interceptor";

type Inputs = {
  mobile: string;
  countryCode: string;
};
function Number({
  show,
  handleClose,
  setShow,
  setCountryCode,
  setMobile,
  lang,
  translation,
  code
}: any) {
  const {
    register,
    handleSubmit,
    setFocus,
    setValue,
    control,
    watch,
    formState: { errors },
  } = useForm<Inputs>({ defaultValues: { countryCode: code } });
  const [resetRecapcha, setResetRecapcha] = useState(1);
  const [loading, setLoading] = useState(false);
  // async function onCaptchaVerify() {
  //   return new Promise((resolve, reject)=>{
  //     if (typeof window !== "undefined") {
  //       const recaptchaContainer = document.getElementById("recaptcha-container");
  //       if (!recaptchaContainer) {
  //         console.error(
  //           "Missing reCAPTCHA container element with ID 'recaptcha-container'"
  //         );
  //         return reject("Missing reCAPTCHA container element with ID 'recaptcha-container");
  //       }
  //       console.log('start')
  //       window.recaptchaVerifier = new RecaptchaVerifier(
  //         auth,
  //         "recaptcha-container",
  //         {
  //           size: "invisible",
  //           callback: (response: any) => {
  //             // onSubmit({});
  //             resolve(response);
  //             console.log('resolce')
  //           },
  //           "expired-callback": () => {
  //             console.warn(
  //               "RecaptchaVerifier callback expired. Ask user to solve again."
  //             );
  //           },
  //         }
  //       );
  //     }
  //   })
  // }

  const onSubmit = async (data?: any) => {
    setLoading(true)
    // const res = await onCaptchaVerify();
    // console.log(res)
    // const appVerifier =
    //   typeof window !== "undefined" && window.recaptchaVerifier
    //     ? window.recaptchaVerifier
    //     : null;

    // if (!appVerifier) {
    //   console.error("Recaptcha verifier not found.");
    //   setLoading(false)
    //   toast.error("Recaptcha verifier not found.");
    //   return;
    // }

    const formatMobile = data?.countryCode + data?.mobile;

    if (data?.mobile?.length < 1) {
      toast.error(translation?.formFields?.phoneNumberRequiredError ?? "Please enter mobile number")
      setLoading(false)
      return;
    }
    if(isNaN(data?.mobile) || data?.mobile?.length < 9){
      toast.error(translation?.formFields?.phoneNumberInvalidError ?? "Please enter valid mobile number")
      setLoading(false)
      return
    }

    try {
      const payload = {
        countryCode: data?.countryCode,
        mobile: data?.mobile,
      }
      const res = await api.post(endpoints.getOtp, payload);
      setShow("verify");
      setLoading(false)
      setCountryCode(data?.countryCode);
      setMobile(data?.mobile);

      toast.success(translation?.popup?.otpSentSuccess ?? `OTP sent successfully`);
    } catch (error: any) {
      console.error("Error during sign-in:", error);
      const errorMessage = error.message || error.toString()
      if (
        !errorMessage.includes("reCAPTCHA has already been rendered in this element")
      ) {
        if (error?.code.includes("invalid-phone-number")) {
          toast.error(translation?.formFields?.phoneNumberInvalidError ?? "Invalid phone number")
          setLoading(false)
          return
        }
        toast.error(`${translation?.popup?.somethingWentWrong ?? "Something went wrong"}, ${error}`);
      }
      setLoading(false)
      setResetRecapcha(resetRecapcha + 1);
    }

    // signInWithPhoneNumber(auth, formatMobile, appVerifier)
    //   .then((confirmationResult) => {
    //     if (typeof window !== "undefined") {
    //       window.confirmationResult = confirmationResult;
    //     }
    //     setShow("verify");
    //     setLoading(false)
    //     setCountryCode(data?.countryCode);
    //     setMobile(data?.mobile);

    //     toast.success(`OTP sent successfully`);
    //   })
    //   .catch((error) => {
    //     console.error("Error during sign-in:", error);
    //     const errorMessage = error.message || error.toString()
    //     if (
    //       !errorMessage.includes("reCAPTCHA has already been rendered in this element")
    //     ) {
    //       if (error?.code.includes("invalid-phone-number")) {
    //         toast.error("Invalid phone number")
    //         setLoading(false)
    //         return
    //       }
    //       toast.error(`Something went wrong, ${error}`);
    //     }
    //     setLoading(false)
    //     setResetRecapcha(resetRecapcha + 1);
    //   });
  };

  useEffect(() => {
    setFocus("mobile");
  }, [setFocus]);

  return <>
    <Modal
      key={resetRecapcha}
      className="checkout-popup"
      show={show}
      onHide={handleClose}
      backdrop="static"
      keyboard={false}
    >
      <Modal.Header closeButton></Modal.Header>
      <Modal.Body>
        <div id="recaptcha-container"></div>
        <form onSubmit={handleSubmit(onSubmit)}>
          <h2 style={{direction: lang.includes("ar") ? "rtl" : "ltr", textAlign: lang.includes("ar") ? "right" : "left"}} dangerouslySetInnerHTML={{ __html: translation?.login?.welcomeTitle}}>
            {/* Hello! <br /> We are Glad to see you :) */}
            
          </h2>

          <div className="checkout-popup-input position-relative">
            <label htmlFor="mobile">{translation?.login?.enterPhoneNumber ?? "Enter your phone number"}</label>

            <Select
              className="select-container"
              onChange={(e: any) => setValue("countryCode", e.value)}
              styles={
                {
                  // option:(state)=>{}
                }
              }
              theme={(theme) => ({
                ...theme,
                borderRadius: 0,
                colors: {
                  ...theme.colors,
                  primary25: "#ccc",
                  primary: "black",
                },
              })}
              classNames={{
                control: (state) => "react-select",

                dropdownIndicator: () => "d-none",
                option: (state) =>
                  state.isSelected ? "option selected" : "option",
                menu: () => "menu",
              }}
              formatOptionLabel={(country) => (
                <div className="drop-item">
                  <img src={country.image} alt="" />
                  {country.label}
                </div>
              )}
              options={countryCodeWithFlags?.map((country) => ({
                label: country.name,
                value: country.dial_code,
                image: country.image,
              }))}
            />

            <div className="countrycode">
              <div className="countrycode-icon">
                <input
                  {...register("countryCode")}
                  id="countryCode"
                  name="countryCode"
                />
              </div>

              <input
                {...register("mobile",
                  //   {
                  //   required: "Phone Number is Required",
                  //   pattern: {
                  //     value: /^[0-9]{10}/,
                  //     message: "Invalid Phone Number",
                  //   },
                  // }
                )}
                tabIndex={1}
                type="tel"
                id="mobile"
                placeholder="************"
              />
            </div>
            {errors.mobile && (
              <small className="form-error text-danger">
                {errors.mobile.message}
              </small>
            )}
          </div>
          <input type="submit" disabled={loading} style={{direction: lang.includes("ar") ? "rtl" : "ltr"}} className="button" value={loading ? ((translation?.login?.pleaseWait ?? "Please wait") + "..."): (translation?.productPage?.continue ?? "Continue")} />
        </form>
      </Modal.Body>
    </Modal>
  </>
}

export default Number;
