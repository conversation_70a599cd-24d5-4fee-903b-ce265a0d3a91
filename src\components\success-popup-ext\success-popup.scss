.success-popup {

  &.success-popup-side {
    .modal-dialog {
      position: fixed;
      right: 0;
      border-radius: 0 !important;
      max-width: 88.6rem;
      width: 100%;
      top: 0;
      height: 100%;
    }

    .modal-content {
      border-radius: 0 !important;
      height: 100%;
    }

    .success-btn {
      margin-top: 0.5rem !important;
    }
  }

  &.success-popup-side.show {
    .modal-dialog {
      transform: none !important;
      margin-top: 0 !important;
      max-width: 68.3rem;
      width: 100%;
    }
  }

  &.modal {
    padding: 0 2rem;

    .modal-dialog {
      max-width: 88.6rem;
      width: 100%;

      @media (max-width: 575.98px) {
        margin: auto;
      }
    }

    .modal-content {
      border: none;
      border-radius: 3rem;
      padding: 1.8rem;
      padding-bottom: 10rem;
      min-height: 58rem;

      @media (max-width: 575.98px) {
        min-height: 46.6rem;
        border-radius: 2rem;
        padding: 2.5rem 1.8rem 7.9rem 1.8rem;
      }
    }

    .modal-header {
      border: none;
      padding: 0;

      .btn-close {
        background-image: url(../../../public/images/common/close.png);
        width: 4.8rem;
        height: 4.8rem;
        background-position: center;
        background-repeat: no-repeat;
        background-size: contain;
        opacity: 1;
        padding: 0;

        @media (max-width: 575.98px) {
          width: 2.8rem;
          height: 2.8rem;
        }

        &:focus {
          box-shadow: none;
          outline: none;
          border: none;
        }
      }
    }

    .modal-body {
      text-align: center;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      img {
        width: 100%;
        max-width: 380px;
        height: 150px;
      }

      h4 {
        color: #242731;
        font-size: 2.6rem;
        font-weight: 700;
        line-height: 3.6rem;
        margin-top: 3.4rem;
      }

      p {
        margin-top: 2.7rem;
        max-width: 37.2426rem;
        width: 100%;
        color: #000;
        font-size: 1.4rem;
        font-weight: 400;
        line-height: 2rem;

        @media (max-width: 575.98px) {
          margin-top: 2rem;
        }
      }

      .success-btn {
        display: flex;
        margin-top: 7.5rem;
        column-gap: 2.3rem;
        justify-content: center;

        @media (max-width: 575.98px) {
          column-gap: 1.7rem;
          margin-top: 5.6rem;
        }

        button {
          border: none;
          height: 6.1389rem;
          font-size: 1.5rem;
          font-weight: 500;
          line-height: 2.4rem;
          border-radius: 6rem;
          border: 1px solid #000;
          padding: 0;

          @media (max-width: 575.98px) {
            height: 4.5rem;
            font-size: 1.4rem;
          }
        }

        .continue-shopping {
          background: #000;
          width: 25.6469rem;
          color: #fff;

          &:hover {
            background-color: transparent;
            color: #000;
            border: 1px solid;
          }

          @media (max-width: 575.98px) {
            width: 18.8rem;
          }
        }

        .view-order {
          color: #000;
          width: 16.234rem;
          background-color: transparent;

          @media (max-width: 575.98px) {
            width: 11.9rem;
          }
        }
      }
    }
  }

  &.rtl {
    .modal-header .btn-close {
      margin-left: 0;
    }
  }
}
