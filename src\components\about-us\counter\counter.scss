.counter {
  margin-top: 5rem;

  @media (max-width: 575.98px) {
    margin-top: 3.3rem;
  }

  &_wrapper {
    // display: flex;
    // justify-content: space-between;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    width: calc(100% - 8.4rem);
    // flex-wrap: wrap;
    margin: 0 auto;

    @media (max-width: 575.98px) {
      width: calc(100% - 3rem);
      row-gap: 3.8rem;
      grid-template-columns: repeat(2, 1fr);
    }
  }

  &_items {
    text-align: center;


    h2 {
      font-size: 6.2rem;
      font-weight: 600;
      color: #000;
      line-height: 8.6rem;

      @media (max-width: 767.98px) {
        font-size: 4.2rem;
        font-weight: 600;
        line-height: 5.46rem;
      }

      span {
        font-size: 6.2rem;
        font-weight: 600;
        color: #000;
        line-height: 8.6rem;

        @media (max-width: 767.98px) {
          font-size: 4.2rem;
          font-weight: 600;
          line-height: 5.46rem;
        }
      }
    }

    .counter-span {
      margin-top: 1rem;
      display: inline-block;
      color: #52525b;
      font-size: 1.6rem;
      font-weight: 400;
      line-height: 2.72rem;

      @media (max-width: 767.98px) {
        margin-top: 0;
      }
    }
  }
}
