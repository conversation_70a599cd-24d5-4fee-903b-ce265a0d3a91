"use client";

import { useContext, useState } from "react";
import Count from "./count/Count";
import AddToCart from "./add-to-cart/AddToCart";
import { useCounter } from "@/hooks/useCounter";
import { TranslationContext } from "@/contexts/Translation";

export default function AddToCartButtonWrapper({ product }: any) {
  const { count, increment, decrement } = useCounter();
  const { translation: { productPage: translation } }: any = useContext(TranslationContext)
  return (
    <>
      <Count countProp={count} incrementProp={increment} decrementProp={decrement} />
      <AddToCart translation={translation} product={product} qty={count} showSvg={true} />
    </>
  );
}
