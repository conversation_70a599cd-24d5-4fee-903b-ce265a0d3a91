.product-navigation {
  display: flex;
  column-gap: 1.9rem;
  margin-top: 4.1rem;

  @media (max-width: 767.98px) {
    margin-top: 1.8rem;
  }

  .filter-head {
    width: 30%;
    background: #fff;
    border-radius: 1.5rem 1.5rem 0 0;

    @media (max-width: 991.98px) {
      display: none;
    }
  }

  .listing-head {
    width: 70%;

    @media (max-width: 991.98px) {
      width: 100%;
    }
  }
}

.product-flex {
  display: flex;
  column-gap: 1.9rem;
  padding-bottom: 4rem;
}

.filter-sort {
  background-color: #000;
  border-radius: 7rem;
  width: 23.6rem;
  height: 5.1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  transform: translateX(-50%);
  left: 50%;
  bottom: 113px;
  z-index: 2;

  @media (min-width: 992px) {
    display: none;
  }

  &_flex {
    display: flex;
  }

  button {
    border: none;
    background-color: transparent;
    padding: 0;
    color: #fff;
    font-size: 1.5rem;
    font-weight: 400;
    display: flex;
    align-items: center;
    column-gap: 1.1rem;
    line-height: 1.8rem;

    img {
      width: 2.4rem;
      height: 2.4rem;
    }

    &.sort-btn {
      padding-left: 9px;
      margin-left: 9px;
      border-left: 1px solid #fff;
    }
  }
}

.main-body {
  .filter-offcanvas {
    height: 67.5rem;
    border-radius: 1.5rem 1.5rem 0 0;
    background-color: #fff;

    .offcanvas-body {
      padding: 0;

      h4 {
        color: #000;
        font-size: 2.4rem;
        font-weight: 500;
        margin-left: 2rem;
        line-height: 3rem;
      }
    }

    .offcanvas-header {
      justify-content: flex-end;
      padding-bottom: 0;
    }

    .filter-offcanvas-btn {
      display: flex;
      column-gap: 1.6rem;
      padding: 0 1rem 1.1rem 2.5rem;

      @media (max-width: 991.98px) {
        position: fixed;
        bottom: 0;
        z-index: 99;
        background-color: rgb(255, 255, 255);
        width: 100%;
        padding: 1rem 1rem 1.1rem 2.5rem;
      }

      button {
        padding: 1.1rem 6rem;
        background: #000;
        border-radius: 6rem;
        border: none;
        color: #fff;
        font-size: 1.5rem;
        font-weight: 500;
        border: 1px solid #000;

        &.reset {
          background-color: #ffffff;
          color: #000;
        }
      }
    }
  }

  .sort-offcanvas {
    height: 50vh;
    border-radius: 1.5rem 1.5rem 0 0;
    background-color: #fff;

    .offcanvas-body {
      padding: 0;
      padding-bottom: 4rem;
      padding-right: 4rem;

      label {
        color: #000;
        font-size: 1.4rem;
        font-weight: 500;
        display: flex;
        align-items: center;
        margin-left: 2.1rem;
        column-gap: 1.4rem;
        line-height: 1.7rem;

        &:not(:last-child) {
          margin-bottom: 2.8rem;
        }

        input {
          appearance: none;
          width: 2rem;
          height: 2rem;
          border-radius: 50%;
          border: 1px solid #000;
          position: relative;

          &:checked {
            &::after {
              content: "";
              width: 1.2rem;
              height: 1.2rem;
              background-color: #000;
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              position: absolute;
              border-radius: 50%;
            }
          }
        }
      }

      h4 {
        color: #000;
        font-size: 2.4rem;
        font-weight: 500;
        margin-left: 2.1rem;
        margin-bottom: 2rem;
        line-height: 3rem;
      }
    }

    .offcanvas-header {
      justify-content: flex-end;
      padding-bottom: 0;
    }

    .apply {
      padding: 1.1rem 6rem;
      background: #000;
      border-radius: 6rem;
      border: none;
      color: #fff;
      font-size: 1.5rem;
      font-weight: 500;
      border: 1px solid #000;
      margin-left: 2.1rem;

      @media (max-width: 575.98px) {
        width: 100%;
      }
    }
  }
}

.app.rtl {
  .sort-btn {
    padding-right: 9px;
    margin-right: 9px;
    border-right: 1px solid #fff;
    border-left: 0;
    padding-left: 0px;
    margin-left: 0px;
  }

  .sort-offcanvas .offcanvas-body {
    padding: 0;
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .filter-offcanvas .offcanvas-body h4 {
    margin-right: 2rem;
    margin-left: 0;
  }

  .filter-offcanvas .close-btn {
    margin-right: auto !important;
    margin-left: 2rem !important;
  }

  .filter_wrapper .accordion-button::after {
    margin-right: auto;
    margin-left: 0;
  }

  .sort-offcanvas .sort-close-btn {
    margin-right: auto !important;
    margin-left: 2rem !important;
  }
}