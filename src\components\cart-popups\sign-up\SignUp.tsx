import { useForm, SubmitHandler, set } from "react-hook-form";
import "./sign-up.scss";
import Modal from "react-bootstrap/Modal";
import { useEffect, useState } from "react";
import { endpoints } from "@/config/apiEndpoints";
import api from "@/config/axios.interceptor";
import { toast } from "sonner";
import { useQueryClient } from "@tanstack/react-query";
import { CredentialResponse, GoogleLogin } from "@react-oauth/google";
import { jwtDecode } from "jwt-decode";
import { useRouter, useSearchParams } from "next/navigation";

interface Inputs {
  name: string;
  email: string;
  insurance: string;
  insuranceId: string;
  emirates: string;
}

function SignUp({ show, handleClose, translation, lang }: any) {
  const {
    register,
    handleSubmit,
    setFocus,
    setValue,
    watch,
    formState: { errors },
  } = useForm<Inputs>();

  const watchShowInsurance = watch("insurance", "true");
  const queryClient = useQueryClient();
  const searchParams = useSearchParams()
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
  }, [watchShowInsurance]);

  const onSubmit: SubmitHandler<Inputs> = (data) => {
    setLoading(true)
    api
      .post(endpoints.register, data)
      .then(async(res) => {
        if (res.data.errorCode === 0) {
          switch (searchParams.get("type")) {
            case 'checkout':
              router.back();
              setTimeout(() => {
                router.push("/cart");
                window && window.location.reload()
              }, 10)
              break;
            case 'wishlist':
              try {
                const res = await api.post(endpoints.manageWishlist, { product: searchParams.get('id') })
                if (res.data.errorCode === 0) {
                  queryClient.invalidateQueries({ queryKey: ["wishList"] });
                  queryClient.invalidateQueries({ queryKey: ["cart", "counts"] });
                  queryClient.invalidateQueries({ queryKey: ["products"] });
                  toast.success(res.data.message);
                }
                setTimeout(() => {
                  window && window.location.reload()
                }, 10)
                router.back();
              } catch (error: any) {
                toast.error(error.response.data.message);
                router.back();
              }
              break;
            default:
              handleClose()
              break;
          }
          toast.success(`${lang.includes("en") ?"Welcome": "مرحباً"} ${data.name}`, {});
          queryClient.invalidateQueries({ queryKey: ["user"] });
          setLoading(false)
        } else {
          setLoading(false)
          console.log(res.data.message);
          toast.error(res.data.message);
        }
      })
      .catch((err) => {
        setLoading(false)
        console.log(err.response.data.message);
        toast.error(err.response.data.message);
      });
  };

  const signInwithGoogle = (credentialResponse: CredentialResponse) => {
    const response = jwtDecode(credentialResponse.credential as string);
    const value = response as any;
    setValue("name", `${value?.given_name} ${value?.family_name}`);
    setValue("email", value?.email);
    onSubmit({
      name: `${value?.given_name} ${value?.family_name}`,
      email: value?.email,
      insurance: "",
      insuranceId: "",
      emirates: "",
    });
  };

  return (
    <>
      <Modal
        className="singup-popup"
        show={show}
        onHide={handleClose}
        backdrop="static"
        keyboard={false}>
        <Modal.Header closeButton></Modal.Header>
        <Modal.Body>
          <h2>{translation?.login?.signup ?? "Sign Up"}</h2>
          <p>{translation?.login?.signupTxt ?? "Fill in the registration data. It will take a couple of minutes."} </p>
          <div className="pt-4">
            <GoogleLogin
              onSuccess={signInwithGoogle}
              onError={() => {
              }}
            />
          </div>

          <form className="ingup-popup-fill" onSubmit={handleSubmit(onSubmit)}>
            <div className="ingup-popup-inputs">
              <label htmlFor="">{translation?.formFields?.fullName ?? "Full name"}</label>
              <input
                {...register("name", { required: (translation?.formFields?.fullNameRequiredError ?? "Name is required") })}
                type="text"
                name="name"
                placeholder="Alex Smith"
              />
              <small className="form-error text-danger">{errors.name?.message}</small>
            </div>

            <div className="ingup-popup-inputs">
              <label htmlFor="">{translation?.formFields?.emailAddress ?? "Email Address"}</label>
              <input
                type="email"
                {...register("email", {
                  required: (translation?.formFields?.emailAddressRequiredError ?? "Email is required"),
                  pattern: {
                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                    message: (translation?.formFields?.emailAddressInvalidError ?? "Invalid email address"),
                  },
                })}
                placeholder="<EMAIL>"
              />
              <small className="form-error text-danger">{errors.email?.message}</small>
            </div>

            <div className="ingup-popup-inputs select">
              <label htmlFor="">{translation?.formFields?.emirates ?? "Emirates"}</label>
              <select
                {...register("emirates", { required: (translation?.formFields?.emiratesRequiredError ?? "Emirates is required") })}
                name="emirates">
                <option selected disabled value="">
                  {translation?.formFields?.selectEmirates ?? "Select Emirates"}
                </option>
                <option value="Abu Dhabi">Abu Dhabi</option>
                <option value="Ajman">Ajman</option>
                <option value="Dubai">Dubai</option>
                <option value="Fujairah">Fujairah</option>
                <option value="Ras Al Khaimah">Ras Al Khaimah</option>
                <option value="Sharjah">Sharjah</option>
                <option value="Umm Al Quwain">Umm Al Quwain</option>
              </select>
              <small className="form-error text-danger">{errors.emirates?.message}</small>
            </div>

            <div className="ingup-popup-inputs radio">
              <span>{translation?.login?.haveInsurance ?? "Do you have insurance?"}</span>
              <label htmlFor="" style={{ marginTop: "0" }}>
                <input type="radio" value="true" {...register("insurance")} defaultChecked />
                {translation?.login?.yes ?? "Yes"}
              </label>
              <label htmlFor="" style={{ marginLeft: "2.5rem", marginTop: "0" }}>
                <input {...register("insurance")} value="false" type="radio" />
                {translation?.login?.no ?? "No"}
              </label>
            </div>
            {watchShowInsurance === "true" && (
              <div className="ingup-popup-inputs">
                <label htmlFor="">{translation?.formFields?.insuranceId ?? "Enter insurance Id"}</label>
                <input {...register("insuranceId")} type="text" placeholder="456894966" />
                <small className="form-error text-danger">{errors.insurance?.message}</small>
              </div>
            )}

            <div className="ingup-popup-btn">
              <input type="submit" disabled={loading} style={{opacity: loading ? 0.5 : 1}} className="save button" value={translation?.productPage?.continue ?? "Save"} />
              {/* <button className="skip button">Continue as Guest</button> */}
            </div>
          </form>
        </Modal.Body>
      </Modal>
    </>
  );
}

export default SignUp;
