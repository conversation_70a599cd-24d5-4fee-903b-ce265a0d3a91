.error {
  text-align: center;
  padding-bottom: 3.8rem;
  padding-top: 12rem;

  &_wrapper {
    img {
      width: auto;
      height: auto;
      object-fit: contain;
    }

    p {
      max-width: 43.9rem;
      font-size: 1.5rem;
      font-weight: 400;
      line-height: 2.4rem;
      margin: 0 auto;
    }

    h6 {
      color: #000;
      font-size: 2.4rem;
      font-weight: 600;
      line-height: 2.4rem;
      margin-bottom: 1rem;
    }

    a {
      margin-top: 2.4rem;
      display: inline-block;
      border-radius: 5.3rem;
      background: #111827;
      font-size: 1.5rem;
      font-weight: 400;
      letter-spacing: 0.15rem;
      color: #fff;
      padding: 1.6rem 4.2rem;
      border: 1px solid #111827;
      transition: 0.3s all ease;

      &:hover {
        background-color: transparent;
        color: #111827;
        transition: 0.3s all ease;
      }
    }
  }
}