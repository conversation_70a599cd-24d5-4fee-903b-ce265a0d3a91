import "./filter.scss";
import Accordion from "react-bootstrap/Accordion";

import "react-range-slider-input/dist/style.css";
import FilterWithIcon from "./FilterWithIcon";
import CheckBoxFilter from "@/components/product-listing/filter/CheckBoxFilter";
import { usePathname } from "next/navigation";
import RangeFilter from "./RangeFilter";
import { endpoints } from "@/config/apiEndpoints";
import api from "@/config/axios.interceptor";

function Filter({ filters, slug, brandPage, isVirtualTry, setIsVirtualTry, translation }: any) {
  const pathname = usePathname();
  // let subCats = filters?.subCategory || [];
  // let subCategory: any = {}
  // let track:any = []
  // filters?.subCategory?.forEach((cat:any)=>{
  //   filters?.subCategory?.forEach((item:any)=>{
  //     if(item?.parent === cat?._id){
  //       if(cat._id in subCategory){
  //         subCategory[cat._id] = {
  //           ...cat,
  //           childs: [...subCategory[cat._id]?.childs, item]
  //         }
  //         track.push(item?._id)
  //       }else{
  //         subCategory[cat._id] = {
  //           ...cat,
  //           childs: [item]
  //         }
  //         track.push(item?._id)
  //         track.push(cat?._id)
  //       }
  //     }
  //   })
  // })

  // var result = Object.keys(subCategory).map((key) => subCategory[key]);

  // const r = subCats.filter((item:any)=> !track.includes(item?._id))

  function buildCategoryHierarchy(categories: any): Record<string, any> {
    const categoryMap: any = {};
    const hierarchy: any = {};

    // Build a map of categories by their id
    categories.forEach((category: any) => {
      categoryMap[category._id] = category;
    });

    const stack: any = [];

    // Find the root categories and initialize the hierarchy
    categories
      .filter((category: any) => category.isRoot)
      .forEach((rootCategory: any) => {
        const rootNode = {
          _id: rootCategory._id,
          title: rootCategory.name,
          childs: []
        };
        hierarchy[rootCategory._id] = rootNode;
        stack.push(rootCategory._id);
      });

    while (stack.length > 0) {
      const parentId = stack.pop();
      const children = categories.filter((category: any) => category.parent === parentId);

      children.forEach((child: any) => {
        const childNode = {
          _id: child._id,
          name: child.name,
          active: child?.active,
          childs: []
        };
        hierarchy[parentId]?.childs.push(childNode);
        stack.push(child._id); // Add child to the stack for further processing
      });
    }

    // Helper function to recursively build the hierarchy
    // function getChildren(parentId: string): any[] {
    //   const children = categories.filter((category:any) => category.parent === parentId);
    //   if (children.length === 0) return [];

    //   return children.map((child:any) => ({
    //     _id: child._id,
    //     name: child.name,
    //     childs: getChildren(child._id)
    //   }));
    // }

    // // Find the root categories and build their hierarchy
    // categories
    //   .filter((category:any) => category.isRoot)
    //   .forEach((rootCategory:any) => {
    //     hierarchy[rootCategory._id] = {
    //       _id: rootCategory._id,
    //       title: rootCategory.name,
    //       childs: getChildren(rootCategory._id)
    //     };
    //   });

    return hierarchy;
  }

  const hierarchy = buildCategoryHierarchy([...(filters?.subCategory || []), ...(filters?.category || [])]);
  var resultObj = Object.keys(hierarchy).map((key) => hierarchy[key]);

  const last = resultObj.filter((item: any) => item.childs.length)


  const pageLimit = process.env.PAGESIZE || 15;

  const getResultCount = async (pageParam = 1, toFilters: any) => {
    const res = await api.post(endpoints.nextFilterCount, {
      page: pageParam,
      limit: pageLimit,
      brandPage,
      keyword: slug,
      ...toFilters,
    });
    return res.data.result.count;
  };

  return (
    <div className="filter">
      {slug !== "contact-lens" && <div style={{ marginTop: "1rem" }} className="filter_inputs">
        <CheckBoxFilter name={"isVirtualTry"} filter={[{ _id: "1", name: translation?.virtualTry ?? "Virtual try" }]} />
      </div>}
      <div className="filter_wrapper">
        <Accordion
          defaultActiveKey={["0", "1", "2", "3", "4", "5", "6", "8", "7"]}
          alwaysOpen
        >
          {(filters?.subCategory && filters?.subCategory?.length > 0) && <Accordion.Item eventKey="0">
            <Accordion.Header>
              <h5>{translation?.category || "Category"}</h5>
            </Accordion.Header>
            <Accordion.Body>
              <div className="filter_inputs">
                <CheckBoxFilter getCount={getResultCount} name={"subCategory"} filter={last} />
              </div>
            </Accordion.Body>
          </Accordion.Item>}
          {filters?.frameType && filters?.frameType?.length > 0 && <Accordion.Item eventKey="1">
            <Accordion.Header>
              <h5>{translation?.frameType || "Frame Type"}</h5>
            </Accordion.Header>
            <Accordion.Body>
              <div className="filter_grids">
                <FilterWithIcon
                  name={"frameType"}
                  filter={filters?.frameType}
                  showName
                />
              </div>
            </Accordion.Body>
          </Accordion.Item>}
          {filters?.frameShape && filters?.frameShape?.length > 0 && <Accordion.Item eventKey="2">
            <Accordion.Header>
              <h5>{translation?.frameShape || "Frame Shape"}</h5>
            </Accordion.Header>
            <Accordion.Body>
              <div className="filter_grids">
                <FilterWithIcon
                  name={"frameShape"}
                  filter={filters?.frameShape}
                  showName
                />
              </div>
            </Accordion.Body>
          </Accordion.Item>
          }
          {(!pathname.includes("/brands/") && filters?.brand && filters?.brand?.length > 0) ? (
            <Accordion.Item eventKey="3">
              <Accordion.Header>
                <h5>{translation?.brands || "Brands"}</h5>
              </Accordion.Header>
              <Accordion.Body>
                <div className="filter_grids">
                  <FilterWithIcon name={"brand"} filter={filters?.brand} />
                </div>
              </Accordion.Body>
            </Accordion.Item>
          ) : ""}
          {filters?.subBrand && filters?.subBrand?.length > 0 && <Accordion.Item eventKey="4">
            <Accordion.Header>
              <h5>{translation?.subBrand || "Sub Category"}</h5>
            </Accordion.Header>
            <Accordion.Body>
              <div className="filter_inputs">
                <CheckBoxFilter name={"subBrand"} filter={filters?.subBrand} />
              </div>
            </Accordion.Body>
          </Accordion.Item>
          }

          {filters?.size && filters?.size?.length > 0 && <Accordion.Item eventKey="4">
            <Accordion.Header>
              <h5>{translation?.frameSize || "Frame Size"}</h5>
            </Accordion.Header>
            <Accordion.Body>
              <div className="filter_inputs">
                <CheckBoxFilter name={"size"} filter={filters?.size} />
              </div>
            </Accordion.Body>
          </Accordion.Item>
          }
          {filters?.contactSize && filters?.contactSize?.length > 0 && <Accordion.Item eventKey="4">
            <Accordion.Header>
              <h5>{translation?.contactSize || "Pack size"}</h5>
            </Accordion.Header>
            <Accordion.Body>
              <div className="filter_inputs">
                <CheckBoxFilter name={"contactSize"} filter={filters?.contactSize} />
              </div>
            </Accordion.Body>
          </Accordion.Item>
          }

          {filters?.frontMaterial && filters?.frontMaterial?.length > 0 && <Accordion.Item eventKey="5">
            <Accordion.Header>
              <h5>{translation?.frontMaterial || "Front Material"}</h5>
            </Accordion.Header>
            <Accordion.Body>
              <div className="filter_inputs">
                <CheckBoxFilter
                  name={"frontMaterial"}
                  filter={filters?.frontMaterial}
                />
              </div>
            </Accordion.Body>
          </Accordion.Item>
          }

          {filters?.color && filters?.color?.length > 0 && <Accordion.Item eventKey="6">
            <Accordion.Header>
              <h5>{translation?.color || "Color"}</h5>
            </Accordion.Header>
            <Accordion.Body>
              <div className="filter_inputs">
                <CheckBoxFilter getCount={getResultCount} name={"color"} filter={filters?.color} />
              </div>
            </Accordion.Body>
          </Accordion.Item>
          }

          <Accordion.Item eventKey="7">
            <Accordion.Header>
              <h5>{translation?.price || "Price"}</h5>
            </Accordion.Header>
            <Accordion.Body>
              <RangeFilter priceRange={filters?.priceRange} />
            </Accordion.Body>
          </Accordion.Item>

          {filters?.type && filters?.type?.length > 0 && <Accordion.Item eventKey="8">
            <Accordion.Header>
              <h5>{translation?.types || "Type"}</h5>
            </Accordion.Header>
            <Accordion.Body>
              <div className="filter_inputs">
                <CheckBoxFilter name={"type"} filter={filters?.type} />
              </div>
            </Accordion.Body>
          </Accordion.Item>
          }

          {filters?.lensType && filters?.lensType?.length > 0 && <Accordion.Item eventKey="9">
            <Accordion.Header>
              <h5>{translation?.lensType || "Lens Material"}</h5>
            </Accordion.Header>
            <Accordion.Body>
              <div className="filter_inputs">
                <CheckBoxFilter name={"lensType"} filter={filters?.lensType} />
              </div>
            </Accordion.Body>
          </Accordion.Item>
          }





          {/* <Accordion.Item eventKey="10">
            <Accordion.Header>
              <h5>Gender</h5>
            </Accordion.Header>
            <Accordion.Body>
              <div className="filter_inputs">
                <CheckBoxFilter name={"gender"} filter={filters?.gender} />
              </div>
            </Accordion.Body>
          </Accordion.Item> */}
          {/* <Accordion.Item eventKey="11">
            <Accordion.Header>
              <h5>Age Group</h5>
            </Accordion.Header>
            <Accordion.Body>
              <div className="filter_inputs">
                <CheckBoxFilter name={"ageGroup"} filter={filters?.ageGroup} />
              </div>
            </Accordion.Body>
          </Accordion.Item> */}
        </Accordion>
      </div>
    </div>
  );
}

export default Filter;
