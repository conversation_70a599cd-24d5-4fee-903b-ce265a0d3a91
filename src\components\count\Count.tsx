"use client";

import { CountContext } from "@/contexts/AddBtnContext";
import "./count.scss";
import React, { useContext, useState } from "react";

interface CountProps {
  countProp?: number;
  incrementProp?: () => void;
  decrementProp?: () => void;
  useContextValue?: boolean;
}

function Count({
  countProp,
  incrementProp,
  decrementProp,
  useContextValue,
}: CountProps) {
  const context = useContext(CountContext);
  const { subscriptionType } = useContext(CountContext);
  const count = useContextValue ? context?.count : countProp;
  const increment = useContextValue ? context?.increment : incrementProp;
  const decrement = useContextValue ? context?.decrement : decrementProp;

  return subscriptionType !== "subscription" ? (
    <div className="count" style={{ alignItems: "center" }}>
      <button onClick={decrement}>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="11"
          height="2"
          viewBox="0 0 11 2"
          fill="none"
        >
          <path
            d="M10.2412 1L1 1"
            stroke="#3C4242"
            strokeWidth="1.03964"
            strokeLinecap="round"
          />
        </svg>
      </button>
      <span>{count}</span>
      <button onClick={increment}>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="11"
          height="12"
          viewBox="0 0 11 12"
          fill="none"
        >
          <path
            d="M5.86279 1.37891V10.6201M10.4834 5.99951L1.24219 5.99951"
            stroke="#3C4242"
            strokeWidth="1.03964"
            strokeLinecap="round"
          />
        </svg>
      </button>
    </div>
  ) : (
    <div className="" style={{ alignItems: "center" }}></div>
  );
}

export default Count;
