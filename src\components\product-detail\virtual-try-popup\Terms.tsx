"use client"

import { TranslationContext } from "@/contexts/Translation";
import { useContext } from "react";
import { Modal } from "react-bootstrap";
import "./Terms.scss"

export default function Terms({show, handleClose, content, showVm, vmPolicy}: any) {

  const {translation: { formFields, productPage}} = useContext(TranslationContext)

    const onContinue = () => {
        showVm(true)
        handleClose()
    }

    return (
      <Modal
          className="terms-pop-up"
          show={show}
          onHide={handleClose}
          backdrop="static"
          keyboard={false}
          centered
        >
          <Modal.Header closeButton>
            <h2 style={{margin: "0 auto"}}>{vmPolicy?.title || "Welcome"}</h2>
          </Modal.Header>
          <Modal.Body>
            <div dangerouslySetInnerHTML={{ __html: vmPolicy?.content }}>
            </div>
            <div className="btns">
                <button onClick={handleClose} className="button cancel">{formFields?.cancel ?? "Cancel"}</button>
                <button onClick={onContinue} className="button">{productPage?.continue ?? "Continue"}</button>
            </div>
          </Modal.Body>
        </Modal>
    )
  }
  