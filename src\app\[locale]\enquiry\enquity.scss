.enquiry {
  padding-bottom: 2rem;

  @media (max-width: 575.98px) {
    background: #f2f4f9;
    padding-bottom: 4rem;
  }

  &_flex {
    display: flex;
    column-gap: 7rem;
    padding-top: 5.5rem;

    @media (max-width: 991.98px) {
      flex-direction: column;
      row-gap: 2.5rem;
    }

    @media (max-width: 575.98px) {
      padding-top: 3rem;
    }
  }

  &_left {
    width: 55%;

    @media (max-width: 991.98px) {
      width: 100%;
    }

    h5 {
      color: #242731;
      font-size: 2rem;
      font-weight: 700;
    }
  }

  &_box {
    border-radius: 1.5rem;
    border: 1px solid #ccd3e7;
    background: #f2f4f9;
    padding: 1.5rem;
    margin-top: 1.5rem;

    h6 {
      color: #000;
      font-size: 1.7rem;
      font-weight: 600;
    }

    ul {
      margin-top: 1.5rem;

      li,
      li a {
        color: #000;
        font-size: 1.3rem;
        font-weight: 400;

        &:not(:last-child) {
          margin-bottom: 0.8rem;
        }
      }
    }
  }

  &_right {
    width: 45%;
    border-radius: 1.5rem;
    border: 1px solid #dadada;
    padding: 5.7rem 4rem 4rem 4rem;

    @media (max-width: 991.98px) {
      width: 100%;
    }

    @media (max-width: 575.98px) {
      border: none;
      padding: 0;
    }

    h4 {
      color: #242731;
      font-size: 2.6rem;
      font-weight: 700;
      margin-bottom: 2rem;

      @media (max-width: 575.98px) {
        font-size: 2rem;
        margin-bottom: 1.8rem;
      }
    }

    label {
      color: #242426;
      font-size: 1.4rem;
      font-weight: 400;
      line-height: 2rem;
      display: block;
      padding-bottom: 0.8rem;
    }

    input {
      border: none;
      border-bottom: 1px solid #e2e4e5;
      width: 100%;
      padding-left: 1.6rem;
      padding-bottom: 0.8rem;

      @media (max-width: 575.98px) {
        background: #f2f4f9;
      }

      &::placeholder {
        color: #bababa;
        font-size: 1.5rem;
        font-weight: 400;
        line-height: 2.8rem;
      }

      &:focus-visible {
        outline: none;
        border-bottom: 1px solid #e2e4e5;
      }
    }
  }

  &_input {
    position: relative;

    &:not(:last-child) {
      margin-bottom: 3.2rem;
    }
  }

  &_select {
    display: flex;
    column-gap: 1.6rem;

    select {
      border: none;
      padding-bottom: 0.8rem;
      border-bottom: 1px solid #e2e4e5;

      @media (max-width: 575.98px) {
        background: #f2f4f9;
      }

      &:focus-visible {
        outline: none;
        border-bottom: 1px solid #e2e4e5;
      }
    }

    .select {
      position: relative;

      .select-container {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        z-index: 100;

        .drop-item {
          z-index: 10;

          img {
            width: 30px;
            margin-right: 10px;
          }
        }
      }

      .css-lkh0o5-menu {
        margin-top: 29px !important;
      }

      label {
        color: #242426;
        font-size: 1.4rem;
        font-weight: 400;
        line-height: 2rem;
        padding-bottom: 0.8rem;
      }

      .countrycode {
        display: flex;
        column-gap: 1.6rem;
        align-items: baseline;

        .countrycode-icon {
          position: relative;
          max-width: 100px;

          &::after {
            content: "";
            background-image: url("../../../../public/images/common/Icon.png");
            width: 2.4rem;
            height: 2.4rem;
            position: absolute;
            right: -7px;
            z-index: 50;
          }
        }
      }

      .react-select {
        width: 100%;
        max-width: 100px;
        top: 26px;
        left: 0;
        z-index: 10;
        opacity: 0;
      }

      input[type="number"]::-webkit-inner-spin-button,
      input[type="number"]::-webkit-outer-spin-button {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
      }

      input {
        width: 100%;
        color: #242426;
        font-size: 1.8rem;
        font-weight: 400;
        line-height: 2.8rem;
        border: none;
        border-bottom: 1px solid #e2e4e5;
        padding: 0 1.5rem;
        padding-bottom: 0.8rem;

        &:focus-within {
          outline: none;
          border-bottom: 1px solid #e2e4e5;
        }

        &::placeholder {
          color: #cacaca;
          font-size: 1.8rem;
          font-weight: 400;
        }
      }

    }

  }

  &_btn {
    button {
      border: none;
      background-color: #000;
      height: 5.6rem;
      width: 100%;
      border-radius: 6rem;
      color: #fff;
      font-size: 1.5rem;
      font-weight: 500;
      line-height: 2.4rem;
    }
  }
}