import { ImageResponse } from "next/og";
import LogoIcon from "./LogoIcon";

export type Props = {
  title?: string;
};
export const runtime = "edge";
// export default async function OpengraphImage(props?: Props): Promise<ImageResponse> {
export default async function OpengraphImage(props?: Props): Promise<any> {
  const { title } = {
    ...{
      title: process.env.SITE_NAME,
    },
    ...props,
  };
  return null

  // return new ImageResponse(
  //   (
  //     <div
  //       style={{
  //         display: "flex",
  //         flexDirection: "column",
  //         alignItems: "center",
  //         justifyContent: "center",
  //         backgroundColor: "#1f2738",
  //         height: "100%",
  //         width: "100%",
  //       }}>
  //       <div
  //         style={{
  //           display: "flex",
  //           flex: "none",
  //           alignItems: "center",
  //           justifyContent: "center",
  //           border: "1px solid #2d374c",
  //           height: "183px",
  //           width: "726px",
  //           borderRadius: "10px",
  //         }}>
  //         <LogoIcon width="686" height="143" fill="white" />
  //       </div>
  //       <p
  //         style={{
  //           marginTop: "20px",
  //           color: "white",
  //           fontSize: "50px",
  //           fontWeight: "bold",
  //         }}>
  //         {title || process.env.SITE_NAME || "Yateem Optician"}
  //       </p>
  //     </div>
  //   ),
  //   {
  //     width: 1200,
  //     height: 630,
  //     fonts: [
  //       {
  //         name: "Inter",
  //         data: await fetch(new URL("../fonts/Inter-Bold.ttf", import.meta.url), {
  //           cache: "no-store",
  //         }).then((res) => res.arrayBuffer()),
  //         style: "normal",
  //         weight: 700,
  //       },
  //     ],
  //   }
  // );
}
