"use client";

import Link from "next/link";
import React, { useContext, useEffect, useState } from "react";
import "./Preheader.scss";
import Image from "next/image";
import Select from "react-select";
import { useQueryClient } from "@tanstack/react-query";
import { TranslationContext } from "@/contexts/Translation";
import { useSearchParams } from "next/navigation";
import { useRouter } from "next/router";
import { CircleFlag } from 'react-circle-flags'
import { useLocaleContext } from "@/contexts/LocaleProvider";
import { Modal } from "react-bootstrap";
import LogoAnimation from "../LogoAnimation/LogoAnimation";
import OverLayLoader from "../LogoAnimation/OverLayLoader";

const locales = [
  "sa-en",
  "sa-ar",
  "ae-en",
  "ae-ar",
  "qa-en",
  "qa-ar",
  "om-en",
  "om-ar",
  "bh-en",
  "bh-ar"
]

function Preheader({ data, stores }: any) {

  const { changeLocale, currentLocale } = useLocaleContext()
  const [loading, setLoading] = useState(false);

  const [isMounted, setIsMounted] = useState(false);
  const { translation }: any = useContext(TranslationContext)
  const queryClient = useQueryClient();


  const countries = stores?.map((store: any) => {
    return {
      value: store?.storeId,
      label: (
        <div className="drop-item">
          <CircleFlag countryCode={store?.storeId} />
          {store?.name}
        </div>
      ),
    };
  })

  useEffect(() => {
    setIsMounted(true);
    queryClient.invalidateQueries({ queryKey: ["cart", "counts"] });
    queryClient.invalidateQueries({ queryKey: ["wishList"] });
  }, []);

  // const countries = [
  //   {
  //     value: "oman",
  //     label: (
  //       <div className="drop-item">
  //         <img src="/icons/flags/om.svg" alt="" />
  //         Oman
  //       </div>
  //     ),
  //   },
  //   {
  //     value: "uae",
  //     label: (
  //       <div className="drop-item">
  //         <img src="/icons/flags/ae.svg" alt="" />
  //         UAE
  //       </div>
  //     ),
  //   },
  // ];
  const languages = [
    {
      value: "ar",
      label: <p className="drop-item">العربية</p>,
    },
    {
      value: "en",
      label: <p className="drop-item">English</p>,
    },
  ];

  const [selectedCountry, setSelectedCountry] = useState<any>(() => {
    const country = currentLocale.split("-")[0]
    return countries?.find((e: any) => e.value === country)
  });
  const [selectedLanguage, setSelectedLanguage] = useState<any>(
    languages?.filter((e) => e.value === currentLocale.split("-")[1])[0]
  );
  const [index, setIndex] = useState(0);
  const [isVisible, setIsVisible] = useState(true);

  const toggleLanguage = (e: any) => {
    let locale: any = currentLocale.split("-")[0]
    locale = `${locale}-${e.value}`
    changeLocale(locale);
    setSelectedLanguage(e);
  };

  const toggleCountry = (e: any) => {
    setLoading(true)
    let locale: any = currentLocale.split("-")[1]
    locale = `${e.value}-${locale}`
    queryClient.clear()
    changeLocale(locale);
    setSelectedCountry(e);
  };

  useEffect(() => {
    const interval = setInterval(() => {
      setIsVisible(false);
      setTimeout(() => {
        setIndex((index + 1) % data?.announcements?.length);
        setIsVisible(true);
      }, 1000); // Adjust time for fade in/out
    }, 5000); // Adjust time between announcements

    return () => clearInterval(interval);
  }, [index, data?.announcements?.length]);
  return (
    <section className="preheader">
      <div className="container">
        <div className="preheader_wrapper">
          <span className={`announcement ${isVisible ? "" : "fade-out"}`}>
            {data?.announcements?.[index]?.text}
          </span>
          {isMounted && (
            <ul>
              <li style={{ direction: "ltr" }} className="preheader_select">
                <Select
                  isSearchable={false}
                  // styles={{ container: (base) => ({ ...base, opacity: 0.5 }) }}
                  components={{
                    IndicatorSeparator: () => null,
                  }}
                  classNames={{
                    control: (state) => (state.isFocused ? "select" : "select"),
                    menu: (state) => "menu",
                    indicatorsContainer: () => "indicators-container",
                  }}
                  defaultValue={selectedCountry}
                  onChange={toggleCountry}
                  options={countries}
                />
              </li>

              <li style={{ direction: "ltr" }} className="preheader_select">
                <Select
                  // isDisabled
                  isSearchable={false}
                  // styles={{ container: (base) => ({ ...base, opacity: 0.5 }) }}
                  components={{
                    IndicatorSeparator: () => null,
                  }}
                  classNames={{
                    control: (state) => (state.isFocused ? "select" : "select"),
                    menu: (state) => "menu",
                    indicatorsContainer: () => "indicators-container",
                  }}
                  defaultValue={selectedLanguage}
                  onChange={toggleLanguage}
                  options={languages}
                />
              </li>

              <li>
                <Link href="/store-locator">
                  <svg width="16px" height="16px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" strokeWidth="0"></g><g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="M12 21C15.5 17.4 19 14.1764 19 10.2C19 6.22355 15.866 3 12 3C8.13401 3 5 6.22355 5 10.2C5 14.1764 8.5 17.4 12 21Z" stroke="#ffffff" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></path> <path d="M12 12C13.1046 12 14 11.1046 14 10C14 8.89543 13.1046 8 12 8C10.8954 8 10 8.89543 10 10C10 11.1046 10.8954 12 12 12Z" stroke="#ffffff" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></path> </g></svg>
                  {translation?.myAccount?.storeLocator ?? "Store Locator"}
                </Link>
              </li>

              <li>
                <Link
                  href={`https://wa.me/${data?.whatsappNumber?.replace(
                    /[ +]/g,
                    ""
                  )}`}
                  target="_blank"
                >
                  <Image quality={100} priority
                    src={"/images/home/<USER>/whatsapp.svg"}
                    width={24}
                    height={24}
                    alt="logo"
                  />
                  {data?.whatsappNumber}
                </Link>
              </li>
            </ul>
          )}
        </div>
      </div>
      <Modal
        className="preheader-modal"
        style={{ width: "100%", height: "100%" }}
        show={loading}
        backdrop="static"
        keyboard={false}
        animation={false}
      >
        <Modal.Body style={{ width: "100%", height: "100%" }}>
          <LogoAnimation className="d-flex justify-content-center py-5" />
        </Modal.Body>
      </Modal>
    </section>
  );
}

export default Preheader;
