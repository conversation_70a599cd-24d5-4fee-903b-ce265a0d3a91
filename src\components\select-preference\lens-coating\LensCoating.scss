.lens-coating {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;

    @media (max-width: 575.98px) {
        grid-template-columns: repeat(1, 1fr);
        gap: 1.5rem;
    }

    &_box {
        border: 1px solid rgba(228, 228, 228, 1);
        border-radius: 1.5rem;
        padding: 2.3rem 2rem;
        cursor: pointer;

        &.isActive {
            border: 2px solid rgba(0, 0, 0, 1);

            @media (max-width: 575.98px) {
                border: 1px solid rgba(0, 0, 0, 1)
            }
        }

        h5 {
            font-size: 1.4rem;
            font-weight: 500;
            line-height: 1.7rem;
            color: #000000;
        }

        h6 {
            font-size: 1.3rem;
            font-weight: 400;
            line-height: 1.6rem;
            color: #000000;
            margin-top: 7px;
        }
    }
}