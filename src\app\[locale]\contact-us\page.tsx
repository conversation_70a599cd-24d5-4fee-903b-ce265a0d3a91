import "./contact.scss";
import { Fragment } from "react";
import Link from "next/link";
import BreadCrumbs from "@/components/breadcrumbs/BreadCrumbs";
import ContactForm from "@/components/contact-form/ContactForm";
import Image from "next/image";
import ContactBanner from "@/components/contact-banner/ContactBanner";
import { getStores } from "@/lib/methods/insurance";
import { Metadata } from "next";
import { getUser } from "@/lib/methods/auth";
import { getContactBanner } from "@/lib/methods/contact";
import GenericBackButton from "@/components/GenericBackButton/GenericBackButton";
import { endpoints } from "@/config/apiEndpoints";

export async function generateMetadata({
  params,
}: {
  params: any;
}): Promise<Metadata> {
  const contactDetails = await getContactBanner();
  const locale = params.locale;

  return {
    title: locale === "ar" ? contactDetails?.seoDetails?.title?.ar : contactDetails?.seoDetails?.title?.en || "Contact Us  | Yateem Optician",
    description: locale === "ar" ? contactDetails?.seoDetails?.description?.ar : contactDetails?.seoDetails?.description?.en || "Feel free to contact us anytime!",
    keywords: locale === "ar" ? contactDetails?.seoDetails?.keywords?.ar : contactDetails?.seoDetails?.keywords?.en || "Contact Us  | Yateem Optician",
    openGraph: {
      title: locale === "ar" ? contactDetails?.seoDetails?.title?.ar : contactDetails?.seoDetails?.title?.en || "Contact Us  | Yateem Optician",
      description: locale === "ar" ? contactDetails?.seoDetails?.description?.ar : contactDetails?.seoDetails?.description?.en || "Feel free to contact us anytime!",
      type: "website",
      images: contactDetails?.seoDetails?.ogImage,
    },
    alternates: {
      canonical: locale === "ar" ? contactDetails?.seoDetails?.canonical?.ar : contactDetails?.seoDetails?.canonical?.en,
    },
  };
}

const convertNewlinesToBreaks = (text: string) => {
  return text?.replace(/(\r\n|\n|\r)/g, "<br />") ?? "";
};

async function Contact() {
  const res = await getStores();
  const user = await getUser();
  const banner = await getContactBanner();
  const stores = res?.result;

  return (
    <main>
      <BreadCrumbs
        backHome="Home"
        currentPage="Contact Us"
        image="/images/common/banner.png"
      />
      <section className="contact-info">
        <div className="container">
          <div className="container position-relative top">
            <GenericBackButton style={{ top: "5px" }} />
            <h2 className="mx-auto">{banner?.pageTitle}</h2>
          </div>
          <div className="contact-info_flex">
            <Fragment>
              <div className="contact-info_items">
                <h6>{banner?.officeHoursTitle}</h6>
                <ul>
                  <li
                    dangerouslySetInnerHTML={{
                      __html: convertNewlinesToBreaks(banner?.officeHours),
                    }}
                  />
                </ul>
              </div>

              <div className="contact-info_items">
                <h6>{banner.storeOneTitle}</h6>
                <ul>
                  <li
                    dangerouslySetInnerHTML={{
                      __html: convertNewlinesToBreaks(banner.storeOneAddress),
                    }}
                  />
                </ul>
              </div>

              <div className="contact-info_items">
                <h6>{banner.storeTwoTitle}</h6>
                <ul>
                  <li
                    dangerouslySetInnerHTML={{
                      __html: convertNewlinesToBreaks(banner.storeTwoAddress),
                    }}
                  />
                </ul>
              </div>
            </Fragment>
          </div>
        </div>
      </section>

      <ContactForm title={banner?.formTitle} user={user} stores={stores} />

      <ContactBanner banner={banner} />
    </main>
  );
}

export default Contact;
