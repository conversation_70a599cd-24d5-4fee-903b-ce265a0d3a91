import { Modal } from "react-bootstrap";
import Image from "next/image";
import LensPrice from "../lens-price/LensPrice";
import { endpoints } from "@/config/apiEndpoints";
import api from "@/config/axios.interceptor";
import { useQuery } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { useLocaleContext } from "@/contexts/LocaleProvider";

const lensTypes = ["Plastic or CR3", "Polycarbonate", "Photocromic"];

export default function LensType({
  show,
  handleClose,
  lensData,
  handleLensData,
  setStep,
  back,
  sum,
  setSum,
}: any) {
  const {currencyCode} = useLocaleContext()
  const [localSum, setLocalSum] = useState(sum);
  const {
    data: typesData,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["lens-types", lensData?.brand],
    queryFn: () => {
      return api
        .get(`${endpoints.lensTypes}/${lensData?.brand}`)
        .then((res) => {
          if (res.status === 200) {
            return res.data?.result;
          } else {
            return [];
          }
        });
    },
  });
  const getPrice = (id: string | number) => {
    return typesData?.types?.find((power: any) => power.name === id)?.price;
  };
  const handleSelect = (e: any) => {
    handleLensData({ lensType: e.target.value });
    // setLocalSum(getPrice(e.target.value));
    setSum("lensType", getPrice(e.target.value));
  };

  useEffect(() => {
    if (typesData && typesData?.types?.length > 0) {
      setLocalSum(getPrice(typesData?.types[0]?.name));
    }
  }, [typesData]);
  return (
    <>
      <Modal
        className="buy-with"
        show={show}
        onHide={handleClose}
        backdrop="static"
        keyboard={false}
        centered
      >
        <Modal.Header closeButton></Modal.Header>
        <Modal.Body>
          <h2>
            <Image quality={100} priority
              onClick={() => back("lensType")}
              src="/images/common/bakarrow.png"
              width={40}
              height={40}
              alt="back arrow"
            />
            Type of Lens <LensPrice sum={sum} />
          </h2>
          {typesData?.types?.map((lensType: any) => (
            <label htmlFor={lensType?._id} key={lensType?._id}>
              <input
                id={lensType?._id}
                type="radio"
                value={lensType?.name}
                defaultChecked={lensData?.lensType === lensType?.name}
                onChange={handleSelect}
                name="lensType"
              />
              {lensType?.name}{" "}
              <span className="text-muted">{currencyCode + " "}{lensType?.price}</span>
            </label>
          ))}

          <button
            onClick={() => {
              setStep("lensType");
            }}
          >
            Next
          </button>
        </Modal.Body>
      </Modal>
    </>
  );
}
