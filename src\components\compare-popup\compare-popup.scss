.compare-popup {
  &.modal {
    .modal-dialog {
      max-width: 123.9rem;
      width: 100%;
    }

    .modal-header {
      padding: 4.2rem 6.4rem 4rem 5.3rem;
      border: none;

      @media (max-width: 575.98px) {
        padding: 4.9rem 1.6rem 3rem 2.1rem;
      }

      .btn-close {
        background-image: url(../../../public/images/common/close.png);
        width: 4.8rem;
        height: 4.8rem;
        background-position: center;
        background-repeat: no-repeat;
        background-size: contain;
        opacity: 1;
        padding: 0;

        @media (max-width: 575.98px) {
          width: 2.8rem;
          height: 2.8rem;
        }

        &:focus {
          box-shadow: none;
          outline: none;
          border: none;
        }
      }
    }

    .modal-content {
      border: none;
      border-radius: 3rem;

      @media (max-width: 575.98px) {
        border-radius: 2rem 2rem 0rem 0rem;
      }
    }

    .modal-body {
      padding: 0;
      display: flex;
      flex-direction: column;

      h2 {
        text-align: left;
        line-height: 4rem;
        display: flex;
        align-items: center;
        column-gap: 2rem;

        @media (max-width: 575.98px) {
          line-height: 3rem;
          font-size: 2.4rem;
        }

        img {
          width: 4rem;
          height: 4rem;
          object-fit: contain;

          @media (max-width: 575.98px) {
            display: none;
          }
        }
      }
    }
  }

  &_flex {
    display: flex;
    width: 100%;
    overflow-x: auto;

    .table {
      &_row {
        border-bottom: 1px solid #e6e6e6;
      }

      th {
        vertical-align: bottom;

        &.last-child {
          cursor: pointer;
          position: relative;

          h6 {
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            position: absolute;
            @media (max-width: 575.98px) {
             width: min-content; 
            }
          }

          img {
            height: auto;
          }
        }

        h6 {
          color: #000;
          font-size: 1.6rem;
          font-weight: 500;
          line-height: 2rem;
          max-width: 13.4rem;
          width: 100%;
          padding: 0;
          @media (max-width: 575.98px) {
              font-size: 1rem;
          }
        }

        img {
          width: 20.8rem;
          height: 12.9rem;
          border-radius: 1rem;
          margin: 2rem 0;
          padding: 0;
          @media (max-width: 575.98px) {
              width: 10rem;
              height: auto;
          }
        }
      }

      td {
        color: #726c6c;
        font-size: 1.6rem;
        font-weight: 400;
        line-height: 2rem;
        border-right: 1px solid #e6e6e6;
        padding: 1.5rem 2rem;
        width: calc(100% / 5);

        @media (max-width: 575.98px) {
              font-size: 1rem;
              padding: .5rem;
              p{
                font-size: 1rem;
              }
        }

        .add-to-cart {
          background-color: #000;
          color: #fff;
          width: 100%;
          height: 5.6rem;
          display: flex;
          justify-content: center;
          align-items: center;
          column-gap: 0.98rem;
          margin-top: 0;
          @media (max-width: 575.98px) {
              svg{
                display: none;
              }
          }
        }
      }

      .item {
        color: #000;
        font-size: 1.6rem;
        font-weight: 500;
        vertical-align: baseline;
        text-align: center;
        @media (max-width: 575.98px) {
              font-size: 1rem;
        }
      }
    }
  }

  &_head {
    padding: 0 2rem;

    h6 {
      line-height: 2rem;
      color: #000;
      font-size: 1.6rem;
      font-weight: 500;
      max-width: 13.4rem;
      width: 100%;
    }

    img {
      width: 20.8rem;
      height: 12.9rem;
      border-radius: 1rem;
      margin-top: 2rem;
    }
  }

  &_body {
    margin-top: 2rem;
    border-top: 1px solid #e6e6e6;
    border-right: 1px solid #e6e6e6;
    border-bottom: 0;
    color: #726c6c;
    font-size: 16px;
    font-weight: 400;
    padding: 3rem 2rem;
  }
}

.app.rtl {
  .modal-header {
    .btn-close {
      margin-right: auto;
      margin-left: unset;
    }
  }
}