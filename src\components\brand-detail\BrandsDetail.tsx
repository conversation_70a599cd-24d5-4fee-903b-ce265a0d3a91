import { endpoints } from "@/config/apiEndpoints";
import { cookies } from "next/headers";
import Image from "next/image";

async function BrandsDetail({ params }: any) {
  const locale = cookies().get("Next-Locale")?.value || "sa-en";
  const [storeId, language] = locale.split("-")
  const brandDetail = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.products,
    {
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
        language: language || "en",
        storeid: storeId || "sa"
      },
      method: "POST",
      body: JSON.stringify({
        keyword: params,
        brandPage: true,
        page: 1,
        limit: 1,
      }),
    }
  );
  const brandDetailData = await brandDetail.json();
  const brandData = brandDetailData.result;

  return (
    <>
      {brandData?.banner && (
        <Image
          quality={100}
          priority
          className="d-none d-lg-block h-100"
          style={{ maxHeight: "40vh", objectPosition: "center" }}
          src={brandData?.banner ?? ""}
          width={1366}
          height={375}
          alt="brands"
        />
      )}

      {brandData?.banner && (
        <Image
          quality={100}
          priority
          className="d-block d-lg-none h-100"
          src={brandData?.banner ?? ""}
          width={375}
          height={193}
          alt="brands"
        />
      )}
      {/* {brandData?.brandOverview?.overview && (
        <div
          style={{
            padding: "33px 0px 39px",
            display: "flex",
            alignItems: "center",
            background: "white",
          }}
        >
          <div className="container">
            <p style={{ fontWeight: 700, fontSize: 24 }}>Overview</p>
            <p
              style={{
                fontWeight: 400,
                fontSize: 15,
                padding: "16px 0px",
                wordBreak: "break-word",
              }}
            >
              {brandData?.brandOverview?.overview}
            </p>
            <div style={{ display: "flex", flexWrap: "wrap" }}>
              <p style={{ fontWeight: 700, fontSize: 14 }}>
                Available In :{"  "}{" "}
              </p>
              {brandData?.brandOverview?.stores?.map(
                (store: string, index: number) => (
                  <p
                    style={{
                      color: "#726C6C",
                      fontSize: 14,
                      fontWeight: 400,
                      padding: "0px 0px 0px 5px",
                    }}
                    key={index}
                  >
                    {store}
                    {index !== brandData?.brandOverview?.stores?.length - 1
                      ? " |"
                      : " ."}
                  </p>
                )
              )}
            </div>
          </div>
        </div>
      )} */}
    </>
  );
}

export default BrandsDetail;
