"use client";

import { useCounter } from "@/hooks/useCounter";
import { createContext, useEffect, useRef, useState } from "react";

interface CountContextType {
  count: number;
  increment: () => void;
  decrement: () => void;
}

export const CountContext = createContext<CountContextType | any>(null);

export const CountProvider = ({ children }: any) => {
  const { count, increment, decrement } = useCounter(1);
  const [subscriptionType, setSubscriptionType] = useState("oneTime");
  const [varient, setVarient] = useState<any>({});
  const [multi, setMulti] = useState<any>(false);
  const formRef = useRef<HTMLFormElement>(null);
  useEffect(() => {
  }, [varient]);

  return (
    <CountContext.Provider
      value={{
        count,
        increment,
        decrement,
        subscriptionType,
        setSubscriptionType,
        varient,
        setVarient,
        formRef,
        multi,
        setMulti
      }}>
      {children}
    </CountContext.Provider>
  );
};
