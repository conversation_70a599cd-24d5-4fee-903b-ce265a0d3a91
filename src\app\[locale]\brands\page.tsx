import Brands from "@/components/brands/Brands";
import "./brands.scss";

import { Metadata } from "next";

export async function generateMetadata({ params }: { params: any }): Promise<Metadata> {
  const baseURL = process.env.NEXT_PUBLIC_API_URL || "";
  const res = await fetch(`${baseURL}brands`);
  const data = await res.json();
  const brands = data.result;
  return {
    title: "Brands  | Yateem Optician",
    description: `${brands?.length} Brands available in Yateem Optician`,

    openGraph: {
      title: `Checkout these ${brands?.length} Brands available in Yateem Optician`,
      type: "website",
    },
  };
}
export default async function BrandsPage() {
  return <Brands />;
}

export const pages = [
  { link: "/login", title: "Login" },
  { link: "/about-us", title: "About-us" },
  { link: "/blogs", title: "Blogs" },
  { link: "/brands", title: "Brands" },
  { link: "/cart", title: "Cart" },
  { link: "/categories", title: "Categories" },
  { link: "/checkout", title: "Checkout" },
  { link: "/contact-lens", title: "Contact-lens" },
  { link: "/contact-us", title: "Contact-us" },
  { link: "/enquiry", title: "Enquiry" },
  { link: "/error", title: "Error" },
  { link: "/error.tsx", title: "Error.tsx" },
  { link: "/favicon.ico", title: "Favicon.ico" },
  { link: "/global-error.tsx", title: "Global-error.tsx" },
  { link: "/globals.scss", title: "Globals.scss" },
  { link: "/insurance", title: "Insurance" },
  { link: "/layout.tsx", title: "Layout.tsx" },
  { link: "/my-accounts", title: "My-accounts" },
  { link: "/not-found.tsx", title: "Not-found.tsx" },
  { link: "/opengraph-image.tsx", title: "Opengraph-image.tsx" },
  { link: "/page.module.scss", title: "Page.module.scss" },
  { link: "/page.tsx", title: "Page.tsx" },
  { link: "/policy", title: "Policy" },
  { link: "/product", title: "Product" },
  { link: "/products", title: "Products" },
  { link: "/search", title: "Search" },
  { link: "/store-locator", title: "Store-locator" },
];
