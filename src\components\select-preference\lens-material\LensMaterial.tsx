import Image from "next/image";
import React, { useState, useEffect } from "react";
import "./LensMaterial.scss";
import { endpoints } from "@/config/apiEndpoints";
import { useQuery } from "@tanstack/react-query";
import api from "@/config/axios.interceptor";
import OverlayTrigger from "react-bootstrap/OverlayTrigger";
import Tooltip from "react-bootstrap/Tooltip";
import { useLocaleContext } from "@/contexts/LocaleProvider";

const color = [
  {
    backgroundColor: "Gray",
    hex: "#CCCCCC",
  },
  {
    backgroundColor: "Brown",
    hex: "#A8916E",
  },
];

function LensMaterial({
  handleLensData,
  lensData,
  sum,
  setSum,
  setSelected,
}: any) {
  const [activeIndex, setActiveIndex] = useState(
    lensData?.photocromic ? color?.findIndex((item) => item.backgroundColor === lensData?.photocromic) : -1
  );
  const [activeMaterial, setActiveMaterial] = useState(-1);
  const {currencyCode} = useLocaleContext()

  const { data: typesData, isLoading, error, } = useQuery({
    queryKey: ["lens-types", lensData?.brand],
    queryFn: () => {
      return api.get(`${endpoints.lensTypes}/${lensData?.brand}`).then((res) => {
        if (res.status === 200) {
          return res.data?.result?.types;
        } else {
          return [];
        }
      });
    },
  });

  const setColor = () => {
    handleLensData({ photocromic: color[0]?.backgroundColor })
  }

  useEffect(() => {
    if (typesData?.length && lensData?.lensType) {
      setActiveMaterial(
        typesData?.findIndex((item: any) => item.name === lensData?.lensType)
      );
      setSum(
        "lensType",
        typesData[
          typesData?.findIndex((item: any) => item.name === lensData?.lensType)
        ]?.price || 0
      );
      setSelected(true);
    }

  }, [typesData, lensData?.lensType]);

  const handleDivClick = (index: number) => {
    setActiveMaterial(index);
  };

  useEffect(() => {
    if (typesData && typesData.length > 0 && sum.lensType === "") {
      handleLensData({ lensType: typesData[0].name });
      setSum("lensType", typesData[0]?.price);
    }
  }, []);

  useEffect(() => {
    setActiveIndex(0)
  }, [])

  return (
    <div className="lens-meterial">
      <div
        className={`lens-meterial_box ${activeMaterial === 2 ? "isActive" : ""
          }`}
      >
        <div
          className="lens-meterial_flex"
          onClick={() => {
            handleDivClick(2);
            handleLensData({ lensType: typesData?.[2]?.name, photocromic: null });
            setSum("lensType", typesData?.[2]?.price);
            setSelected(true);
          }}
        >
          <Image
            quality={100}
            priority
            src="/images/modal/l3.png"
            width={500}
            height={500}
            alt="Picture of the author"
          />
          <div>
            <h5>{typesData?.[2]?.name}</h5>
            <h6>+ {currencyCode + " "} {typesData?.[2]?.price}</h6>
          </div>
        </div>
      </div>

      <div
        className={`lens-meterial_box ${activeMaterial === 1 ? "isActive" : ""
          }`}
      >
        <div
          className="lens-meterial_flex"
          onClick={() => {
            handleDivClick(1);
            handleLensData({ lensType: typesData?.[1]?.name, photocromic: null });
            setSum("lensType", typesData?.[1]?.price);
            setSelected(true);
          }}
        >
          <Image
            quality={100}
            priority
            src="/images/modal/l1.png"
            width={500}
            height={500}
            alt="Picture of the author"
          />
          <div>
            <h5>{typesData?.[1]?.name}</h5>
            <h6>+ {currencyCode + " "} {typesData?.[1]?.price}</h6>
          </div>
        </div>
      </div>

      <div
        className={`lens-meterial_box ${activeMaterial === 0 ? "isActive" : ""
          }`}
      >
        <div
          className="lens-meterial_flex"
          onClick={() => {
            handleDivClick(0);
            handleLensData({ lensType: typesData?.[0]?.name, photocromic: null });
            setSum("lensType", typesData?.[0]?.price);
            setSelected(true);
            setColor();
          }}
        >
          <Image
            quality={100}
            priority
            src="/images/modal/l2.png"
            width={500}
            height={500}
            alt="Picture of the author"
          />
          <div>
            <h5>{typesData?.[0]?.name}</h5>
            <h6>+ {currencyCode + " "} {typesData?.[0]?.price}</h6>
          </div>
        </div>

        {activeMaterial == 0 && <div className="lens-meterial_choose-color">
          <span>Choose a Color</span>
          <ul>
            {color.map((item: any, index: number) => (
              <OverlayTrigger
                key={index}
                placement="top"
                overlay={<Tooltip>{item.backgroundColor}</Tooltip>}
              >
                <li
                  key={index}
                  style={{ backgroundColor: item.hex }}
                  className={index === +activeIndex ? "isActive" : ""}
                  onClick={() => {
                    setActiveIndex(index);
                    handleLensData({ photocromic: item?.backgroundColor });
                  }}
                ></li>
              </OverlayTrigger>
            ))}
          </ul>
        </div>}
      </div>
    </div>
  );
}

export default LensMaterial;
