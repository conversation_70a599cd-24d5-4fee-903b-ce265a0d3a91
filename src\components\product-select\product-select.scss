.product-select {

  &_input {
    &:last-child {
      margin-bottom: 2.5rem;

      @media (max-width: 575.98px) {
        margin-bottom: 3.7rem;
      }
    }

    h5 {
      color: #1d364d;
      font-size: 1.7rem;
      font-weight: 700;
      margin-top: 1.5rem;

      @media (max-width: 575.98px) {
        font-size: 1.5rem;
        margin-top: 1.8rem;
      }
    }

    h6 {
      color: #1d364d;
      font-size: 1.5rem;
      font-weight: 700;
      margin-top: 2rem;
    }

    label {
      display: flex;
      align-items: center;
      column-gap: 1.1rem;
      margin-top: 2.7rem;
      color: #726c6c;
      font-size: 1.4rem;
      font-weight: 400;
      line-height: 1.7rem;

      @media (min-width: 1200px) {
        min-width: max-content;
      }

      &.call {
        margin-top: 1.6rem;
      }

      @media (max-width: 575.98px) {
        margin-top: 1.6rem;
      }

      input {
        appearance: none;
        width: 2rem;
        height: 2rem;
        border-radius: 50%;
        border: 1px solid #000;
        position: relative;
        cursor: pointer;

        &:checked {
          &::after {
            content: "";
            width: 1.2rem;
            height: 1.2rem;
            background-color: #000;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            position: absolute;
            border-radius: 50%;
          }
        }
      }
    }
  }

  &_box {
    border-radius: 1.2rem;
    border: 1px solid #edeef2;
    background: #fff;
    margin-top: 2.1rem;
    padding: 1.5rem 8.3rem 2.1rem 1.7rem;

    @media (max-width: 575.98px) {
      padding: 1.5rem 1.9rem 2.5rem 1.4rem;
    }

    span {
      color: #000;
      font-size: 1.3rem;
      font-weight: 300;

      @media (max-width: 575.98px) {
        line-height: 1.6rem;
      }
    }

    .checkbox {
      color: #000;
      font-size: 1.3rem;
      font-weight: 300;
      display: flex;
      align-items: center;
      column-gap: 1rem;
      line-height: 1.6rem;
      margin-top: 1.7rem;
      margin-bottom: 2.2rem;

      input {
        width: 2rem;
        height: 2rem;
        border: 1px solid #000000;
        background-color: #d9d9d9;
      }
    }
  }

  .subscribeBtn {
    border-radius: 9.8rem;
    color: #fff;
    display: inline-block;
    background-color: #000;
    transition: all 0.3s ease;
    border: 1px solid #000;
    width: 100%;
    padding: 18px 0;
    font-weight: 600;
    margin-top: 10px;
  }

  &_select {
    display: flex;
    align-items: center;
    justify-content: space-between;
    column-gap: 20px;

    .inp{
      text-align: left;
      border-radius: 2.95rem;
      background: #f3f3f3;
      color: #000;
      font-size: 1.6rem;
      font-weight: 400;
      border: none;
      min-height: 4.5rem;
      padding: 0 2rem;
      width: 100%;
    }

    &.shipEvery {
      flex-direction: column;
      align-items: flex-start;
      row-gap: 10px;
    }

    &:not(:last-child) {
      margin-bottom: 1.5rem;
    }

    label {
      color: #000;
      font-size: 1.3rem;
      font-weight: 400;
      min-width: 70px;
    }

    .arrow-down {
      max-width: 26.7rem;
      width: 100%;
      position: relative;

      span {
        position: absolute;
        min-width: 110px;
        left: 50%;
        transform: translateX(-50%);
        bottom: 50px;
        font-weight: 500;
        text-transform: uppercase;
      }

      &::after {
        content: "";
        position: absolute;
        background-image: url(../../../public/images/product-detail/arrow-down.png);
        width: 1rem;
        height: 0.5rem;
        background-repeat: no-repeat;
        top: 50%;
        right: 21px;
        transform: translateY(-50%);
      }
    }

    select,
    .select {
      &.select-error {
        border: 1px solid rgb(177, 52, 52);
        background-color: #fff2f2;
      }

      text-align: left;
      border-radius: 2.95rem;
      background: #f3f3f3;
      color: #000;
      font-size: 1.6rem;
      font-weight: 400;
      border: none;
      min-height: 4.5rem;
      padding-left: 1.4rem;
      padding-right: 2rem;
      appearance: none;
      width: 100%;
      min-width: 10rem;

      &:focus-visible,
      :focus {
        border: none;
        outline: none;
        box-shadow: 0px 0px 1px #000;
      }
    }
  }

  .contactPrice {
    padding: 12px 4px 4px;

    .price {
      h5 {
        font-size: 22px;
        font-weight: 700;
      }
    }
  }
}