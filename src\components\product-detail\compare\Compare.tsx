"use client";

import ProductStickyBar from "@/components/product-sticky-bar/ProductStickyBar";
import { endpoints } from "@/config/apiEndpoints";
import api from "@/config/axios.interceptor";
import { TranslationContext } from "@/contexts/Translation";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useContext, useEffect, useState } from "react";
import { toast } from "sonner";
const getCompare = async () => {
  return api.get(endpoints.compare).then((res) => {
    if (res.data?.errorCode === 0) {
      return res.data?.result;
    } else {
      return [];
    }
  });
};

function Compare({ product }: any) {
  const queryClient = useQueryClient();
  const [isCompare, setIsCompare] = useState(!!product?.inCompare);
  const {
    data: compareData,
    isLoading,
    error,
  } = useQuery({ queryKey: ["compare"], queryFn: getCompare });

  const {translation: { compare } } = useContext(TranslationContext)

  const handleClick = () => {
    api
      .post(isCompare ? endpoints.removeFromCompare : endpoints.addToCompare, {
        product: product._id,
        type: "single",
      })
      .then((res) => {
        if (res.data?.errorCode === 0) {
          toast.success(res.data?.message);
          setIsCompare(true);
          queryClient.invalidateQueries({ queryKey: ["compare"] });
        }
      })
      .catch((err) => {
        toast.error(err.response.data.message);
      });
  };

  useEffect(() => {
    console.log(compareData)
    if (compareData) {
      setIsCompare(!!compareData.find((item: any) => item._id === product._id));
    } else {
      setIsCompare(false);
    }
  }, [compareData]);

  return (
    <>
      <button className="compare" onClick={handleClick}>
        {compare?.compareBtn ?? "Compare"}
        {!isCompare && (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="21"
            height="22"
            viewBox="0 0 21 22"
            fill="none">
            <path
              d="M1 7.18677C1.10406 5.08705 1.41537 3.77792 2.34664 2.84664C3.27792 1.91537 4.58705 1.60406 6.68677 1.5M20 7.18677C19.8959 5.08705 19.5846 3.77792 18.6534 2.84664C17.7221 1.91537 16.4129 1.60406 14.3132 1.5M14.3132 20.5C16.4129 20.3959 17.7221 20.0846 18.6534 19.1534C19.5846 18.2221 19.8959 16.9129 20 14.8132M6.68676 20.5C4.58705 20.3959 3.27792 20.0846 2.34664 19.1534C1.41537 18.2221 1.10406 16.9129 1 14.8132"
              stroke="#141B34"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M10.5 6V16M6.5 8V14M14.5 14V8"
              stroke="#141B34"
              stroke-width="1.5"
              stroke-linecap="round"
            />
          </svg>
        )}
        {isCompare && <input type="checkbox" checked />}
      </button>
      <ProductStickyBar compareData={compareData} />
    </>
  );
}

export default Compare;
