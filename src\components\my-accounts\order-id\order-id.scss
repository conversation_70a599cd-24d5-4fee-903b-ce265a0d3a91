.print-Padding {
  .order-detail_wrapper {
    padding: 0 20px;
  }
}

.order-detail {
  .printHeader {
    padding: 30px 20px;
    background-color: #000;
    text-align: center;

    img {
      width: 200px;
    }
  }

  &_wrapper {
    h2 {
      text-align: left;
      display: flex;
      align-items: center;
      column-gap: 1.6rem;

      @media (max-width: 1199.98px) {
        margin-top: 4.4rem;
      }

      @media (max-width: 767.98px) {
        padding-left: 5rem;
        column-gap: 1.3rem;
      }


      @media (max-width: 575.98px) {
        margin-top: 1.5rem;
      }
    }
  }

  .table {
    margin-top: 3.6rem;
    display: flex;
    column-gap: 3.4rem;

    @media (max-width: 991.98px) {
      flex-direction: column;
    }

    @media (max-width: 575.98px) {
      margin-top: 2.8rem;
    }

    &_body {
      width: 62%;

      @media (max-width: 991.98px) {
        width: 100%;
      }
    }
    
    &_wrapper{
      display: flex;
      flex-direction: column;
      gap: 1rem;
      
      &:not(:last-child) {
        border-bottom: 1px solid #bebcbd;
        padding-bottom: 2.3rem;
        margin-bottom: 2rem;
      }
    }

    &_row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-right: 2.5rem;

      @media (max-width: 575.98px) {
        flex-direction: column;
        align-items: flex-start;
      }
    }

    &_col-one {
      display: flex;
      align-items: center;
      column-gap: 2.5rem;
      padding: 0;

      @media (max-width: 575.98px) {
        column-gap: 2.1rem;
      }

      img {
        width: 12rem;
        height: 12rem;
        padding: 0;
        border-radius: 1.2rem;
        object-fit: contain;

        @media (max-width: 575.98px) {
          width: 9.6rem;
          height: 12.5rem;
        }
      }

      ul {
        li {
          color: #807d7e;
          font-size: 1.4rem;
          line-height: 1.7rem;
          font-weight: 400;
          margin-bottom: 0.4rem;

          &:nth-of-type(1) {
            color: #000;
            font-size: 1.6rem;
            font-weight: 600;
            letter-spacing: 0.032rem;
            margin-bottom: 1.1rem;
            line-height: 2rem;
            max-width: 28rem;
            width: 100%;

            @media (max-width: 575.98px) {
              margin-bottom: 0.5rem;
            }
          }

          &:last-child {
            margin-top: 1rem;
            color: #000;
            font-size: 1.6rem;
            font-weight: 600;
          }
        }
      }
    }

    &_col-two {
      @media (max-width: 575.98px) {
        display: none;
      }

      h5 {
        color: #000;
        font-size: 1.8rem;
        font-weight: 400;
      }
    }

    &_col-three {
      @media (max-width: 575.98px) {
        display: none;
      }

      h5 {
        color: #000;
        font-size: 1.8rem;
        font-weight: 600;
      }
    }

    &_summary {
      background: #f2f4f9;
      width: 38%;
      padding: 4rem 1.5rem 3rem 1.8rem;

      @media (max-width: 991.98px) {
        width: 100%;
        margin-top: 3rem;
      }

      @media (max-width: 575.98px) {
        padding: 2.2rem 2rem 2.4rem 2.2rem;
      }

      h4 {
        color: #000;
        font-size: 2.2rem;
        font-weight: 500;
      }

      button {
        font-size: 1.8rem;
        font-weight: 400;
        color: #000;
        border: none;
        background-color: transparent;
        border: 1px solid #000;
        margin-top: 4rem;
        padding: 1.1rem 9.8rem;
        border-radius: 9.8rem;
        width: 100%;

        @media (max-width: 991.98px) {
          padding: 1.1rem 9.8rem;
          width: auto;
          margin-left: auto;
          display: flex;
        }

        @media (max-width: 575.98px) {
          display: none;
        }
      }

      ul {
        margin-top: 3.9rem;

        @media (max-width: 575.98px) {
          margin-top: 2rem;
        }

        li {
          padding: 0;
          display: flex;
          justify-content: space-between;

          &:not(:last-child) {
            padding-bottom: 1.4rem;
            margin-bottom: 1.5rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.2);
          }
        }
      }

      &-title {
        h5 {
          color: #000;
          font-size: 1.5rem;
          font-weight: 600;
        }

        span {
          color: #374957;
          font-weight: 400;
        }
      }

      &-amount {
        h5 {
          color: #000;
          font-size: 1.4rem;
          font-weight: 600;
        }

        span {
          font-size: 1.8rem;
        }
      }

      &-totaltext {
        h5 {
          color: #000;
          font-size: 2.5rem;
          font-weight: 600;

          @media (max-width: 575.98px) {
            font-size: 1.8rem;
          }
        }
      }

      &-totalprice {
        h4 {
          color: #000;
          font-size: 2rem;
          font-weight: 600;

          @media (max-width: 575.98px) {
            font-size: 1.4rem;
          }
        }

        h5 {
          font-size: 1.4rem;
          font-weight: 600;
          color: #231F20;
          line-height: 1.7rem;

          span {
            font-size: 2rem;
            line-height: 2.5rem;

            @media (max-width: 575.98px) {
              font-size: 1.8rem;
              line-height: 2.2rem;
            }
          }
        }

        span {
          font-size: 2.8rem;

          @media (max-width: 575.98px) {
            font-size: 2.2rem;
          }
        }
      }
    }
  }

  .delivery-details {
    margin-top: 9rem;
    display: flex;
    column-gap: 2.7rem;

    @media (max-width: 767.98px) {
      flex-direction: column;
      margin-top: 2rem;
    }

    &_cutemer-details {
      width: 50%;

      @media (max-width: 767.98px) {
        width: 100%;
      }

      h4 {
        color: #000;
        font-size: 2.4rem;
        font-weight: 500;

        @media (max-width: 575.98px) {
          font-size: 2rem;
        }
      }

      .box {
        background: #f2f4f9;
        padding: 2rem 2rem 2rem 2.3rem;
        border-radius: 2rem;
        margin-top: 2rem;
        min-height: 159px;

        @media (max-width: 575.98px) {
          margin-top: 1.5rem;
          padding: 2.1rem 2rem 4rem 1.5rem;
        }

        ul {
          li {
            color: #000;
            font-weight: 400;
            font-size: 1.6rem;
            max-width: 40.2rem;
            width: 100%;
            line-height: 2.2rem;

            @media (max-width: 575.98px) {
              max-width: 26rem;
            }

            &:first-child {
              font-size: 2rem;
            }

            a {
              color: #000;
            }

            &:not(:last-child) {
              margin-bottom: 1.58rem;
            }
          }
        }
      }
    }

    &_shipping-details {
      width: 50%;

      @media (max-width: 767.98px) {
        width: 100%;
        margin-top: 2rem;
      }

      h4 {
        color: #000;
        font-size: 2.4rem;
        font-weight: 500;

        @media (max-width: 575.98px) {
          font-size: 2rem;
        }
      }

      .box {
        margin-top: 2rem;
        border-radius: 2rem;
        border: 1px solid #ebebeb;
        background: #fdfdfe;
        display: flex;
        justify-content: space-between;
        padding: 3rem 3.9rem 2.4rem 2.6rem;

        @media (max-width: 575.98px) {
          flex-direction: column;
          margin-top: 1.5rem;
          padding: 3rem 3.9rem 1rem 2.6rem;
          align-items: flex-start;
        }

        &_content {
          width: 70%;

          @media (max-width: 575.98px) {
            width: 100%;
          }
        }

        &_title {
          h5 {
            color: #242426;
            font-size: 1.6rem;
            font-weight: 600;
            line-height: 2.8rem;
          }
        }

        &_logo {
          padding-top: 2.1rem;
          margin-top: 1.4rem;
          border-top: 1px solid #e4e4e4;
          display: flex;
          align-items: center;

          img {
            width: 8.7rem;
            height: 3.9rem;
            object-fit: contain;
          }

          span {
            font-size: 1.5rem;
            font-weight: 400;
            color: #000;
            margin-left: 1.8rem;
            display: inline-block;
          }
        }

        &_barcode {
          @media (max-width: 575.98px) {
            margin-top: 1.4rem;
          }

          img {
            object-fit: contain;
          }
        }
      }
    }
  }

  .invoice {
    display: flex;
    justify-content: space-between;
    margin-top: 3rem;
    padding: 3.7rem 3rem 4.2rem 2.9rem;
    border-radius: 2rem;
    background: #f2f4f9;
    flex-wrap: wrap;
    row-gap: 2rem;

    @media (max-width: 575.98px) {
      flex-direction: column;
      padding: 2.8rem 1.8rem 2.8rem 1.8rem;
      row-gap: 0.7rem;
    }

    ul {
      li {
        font-size: 1.4rem;
        font-weight: 400;
        line-height: 1.7rem;
        color: #000;

        @media (max-width: 575.98px) {
          font-size: 1.3rem;
          line-height: 1.6rem;
        }

        &:not(:last-child) {
          margin-bottom: 2rem;

          @media (max-width: 575.98px) {
            margin-bottom: 0.7rem;
          }
        }

        span {
          color: #595959;
        }
      }
    }

    button {
      padding: 1.4rem 2.8rem;
      border-radius: 9.8rem;
      border: 1px solid #000;
      background-color: transparent;
      color: #000;
      font-size: 1.8rem;
      font-weight: 400;
      padding: 0.4rem 2.8rem;
      height: 5.1rem;

      @media (max-width: 575.98px) {
        margin-top: 1.7rem;
        font-size: 1.4rem;
        height: 4.5rem;
      }
    }
  }
}