.home-body {
  .preheader {
    background-color: rgba(0, 0, 0, 0.3);
    position: absolute;
  }
}

.announcement {
  transition: opacity 1s ease-in-out;
  opacity: 1;
}

.announcement.fade-out {
  opacity: 0;
}

.preheader-modal{
  .modal-dialog{
    width: 100%;
    height: 100%;
    padding: 0;
    margin: 0;
    display: block;
    max-width: 100%;
    transform: none;
    transition: none;

    .modal-content{
      height: 100%;

      .modal-body{
        div{
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }

  }
}

.preheader {


  .css-1nmdiq5-menu{
    width: auto !important;
    min-width: 100% !important;
  }

  padding: 0.8rem 0;
  width: 100%;
  position: relative;
  background-color: #0c111b;
  z-index: 120;

  @media (max-width: 670px) {
    display: none;
  }

  &_select {
    position: relative;

    &::after {
      content: "";
      //background-image: url(../../../public/images/common/chdown.png);
      background-repeat: no-repeat;
      background-size: contain;
      width: 1rem;
      height: 1rem;
      position: absolute;
      right: 0;
      top: 6px;
    }
  }

  &_wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;

    span {
      color: #fff;
      font-size: 1.4rem;
      font-weight: 500;
    }

    .menu {
      .drop-item {
        display: flex;
        column-gap: 8px;
        color: black;
        font-size: 1.3rem;
        width: max-content;
      }
    }

    select,
    .select {
      background-color: transparent;
      color: #fff !important;
      border: none;
      appearance: none;
      cursor: pointer;
      z-index: 200;
      border: none;
      color: white !important;

      .indicators-container {
        div {
          padding: 0;
        }
      }

      .css-1fdsijx-ValueContainer {
        padding-right: 0;
      }

      .css-tj5bde-Svg {
        filter: brightness(0) saturate(100%) invert(100%) sepia(12%)
          saturate(7453%) hue-rotate(198deg) brightness(109%) contrast(110%);
      }

      .drop-item {
        display: flex;
        align-items: center;
        column-gap: 8px;
        color: white !important;
        font-size: 1.3rem;
      }

      &:focus-visible {
        border: none;
        outline: none;
      }

      option {
        color: #fff;
        font-size: 1.3rem;
        font-weight: 400;
        background-color: #000;
        cursor: pointer;

        &:hover {
          background-color: #e0e0e0 !important;
        }
      }
    }

    ul {
      display: flex;
      align-items: center;
      column-gap: 3.4rem;

      li {
        color: #fff;
        font-size: 1.3rem;
        font-weight: 400;

        a {
          color: #fff;
          display: flex;
          align-items: center;
          column-gap: 0.5rem;
          &:hover {
            text-decoration: underline;
          }
        }

        img {
          width: 2rem;
          height: 2rem;
          object-fit: contain;
        }
      }
    }
  }
}
