"use client"

import Image from "next/image";
import "./product-card.scss";
import Link from "next/link";
import WishlistBtn from "@/components/wishlist/WishlistBtn";
import { addToCart as addToCartFn } from "@/lib/methods/cart";
import { useQueryClient } from "@tanstack/react-query";
import { useContext, useEffect, useRef, useState } from "react";
import {
  VirtualMirror,
  InitializationParams,
  InitializationOptions,
  RenderMirrorParams,
  VirtualMirrorCatalogue, UpcAvailability,
} from '@luxottica/virtual-mirror';
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { TranslationContext } from "@/contexts/Translation";
import { sendGTMEvent } from "@next/third-parties/google";
import { calculateSavings } from "@/app/[locale]/product/[slug]/ProductVariantSection";
import { createHmac } from "crypto";
import { AuthContext } from "@/contexts/AuthProvider";
import { useLocaleContext } from "@/contexts/LocaleProvider";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Navigation } from "swiper/modules";

const VMCatalogue = VirtualMirrorCatalogue.build({
  key: '9d892082-2c7d-4c68-8503-660f15f05c3b', // shared by VM team, if you have only the SSH key, open a ticket
});

export default function ProductCard({
  product,
  addToCart,
  noNav
}: {
  product: any;
  addToCart?: boolean;
  noNav?: boolean;
}) {

  const queryClient = useQueryClient();

  const router = useRouter()
  const [isAvailable, setIsAvailable] = useState(false)
  const { translation }: any = useContext(TranslationContext)
  const { userProfile } = useContext(AuthContext);
  const { currencyCode } = useLocaleContext()


  let main: any = Object.values(product).find((item: any) => item?.isDefaultVariant) ?? Object.values(product)[0];

  const [currentProduct, setCurrentProduct] = useState(main)
  const [imageSrc, setImageSrc] = useState(currentProduct?.thumbnail);

  useEffect(() => {
    let main: any = Object.values(product).find((item: any) => item?._id === currentProduct?._id) ?? Object.values(product)[0];
    setCurrentProduct(main)
  }, [product])

  let savings = calculateSavings(currentProduct?.price, currentProduct?.offerPrice);
  if (!currentProduct?.offerPrice) savings = 0;

  const handleCartClick = () => {
    let eventData: any = {
      event: "add_to_cart",
      ecommerce: {
        currency: currencyCode,
        value: currentProduct?.price - savings,
        items: [
          {
            item_id: currentProduct?.sku,
            item_name: currentProduct?.name,
            discount: savings,
            index: 0,
            item_brand: currentProduct?.brand,
            item_category: currentProduct?.category?.[0]?.name,
            item_category2: currentProduct?.category?.[1]?.name,
            item_variant: currentProduct?.color?.name,
            price: currentProduct?.price,
            // quantity: 1,
          }
        ]
      }
    }
    if (userProfile) {
      const mobile = createHmac('sha256', '123').update(userProfile?.mobile).digest('hex')
      let email = null;
      if (userProfile?.email) email = createHmac('sha256', '123').update(userProfile?.email).digest('hex')
      eventData.user = {
        mobile,
        email,
        user_id: userProfile?._id
      }
    }

    addToCartFn(currentProduct?._id, 1).finally(() => {
      queryClient.invalidateQueries({ queryKey: ["cart"] });
    });
    sendGTMEvent({ ecommerce: null })
    sendGTMEvent(eventData)
  };

  useEffect(() => {
    const result = VMCatalogue.isUpcSupported(currentProduct?.upc);
    result[currentProduct?.upc].then((response: UpcAvailability) => {
      if (response.isAvailable()) {
        setIsAvailable(true)
      } else {
        setIsAvailable(false)
      }
    })
  }, [currentProduct])

  const changeVariant = (product: any) => {
    setCurrentProduct(product)
    savings = calculateSavings(product?.price, product?.offerPrice);
    if (!product?.offerPrice) savings = 0;
    setImageSrc(product?.thumbnail)
  }

  const labelRef: any = useRef()
  const cashbackRef: any = useRef()
  let isCashback = false;
  let cashbackPercentage = 0
  const { cashback } = currentProduct ?? { cashback: {} }
  if (cashback?.brand?.isEnabled) {
    isCashback = true;
    cashbackPercentage = cashback?.brand?.percentage;
  } else if (cashback?.category?.isEnabled) {
    isCashback = true;
    cashbackPercentage = cashback?.category?.percentage;
  } else if (cashback?.currentProduct?.isEnabled) {
    isCashback = true;
    cashbackPercentage = cashback?.currentProduct?.percentage;
  }

  useEffect(() => {
    if (isCashback && labelRef.current && cashbackRef.current) {
      let visible = labelRef.current;
      let hidden = cashbackRef.current;
      const t = setInterval(() => {
        hidden?.classList?.add("delay");
        hidden?.classList?.add("show");
        visible?.classList?.remove("delay");
        visible?.classList?.remove("show");
        [visible, hidden] = [hidden, visible]
      }, 7000)

      const i = setTimeout(() => {
        visible?.classList?.add("show")
      }, 10)
      return () => {
        clearInterval(t)
        clearTimeout(i)
      }
    } else if (labelRef.current) {
      const i = setTimeout(() => {
        labelRef.current.classList?.add("show")
      }, 10)
      return () => {
        clearTimeout(i)
      }
    } else if (cashbackRef.current) {
      const i = setTimeout(() => {
        cashbackRef.current.classList?.add("show")
      }, 10)
      return () => {
        clearTimeout(i)
      }
    }
  }, [currentProduct])

  const sortDefault = (a: any, b: any) => {
    return (a?.isDefaultVariant === b?.isDefaultVariant) ? 0 : a?.isDefaultVariant ? -1 : 1;
  }

  if (!currentProduct) return null;

  return (
    <div className="product-card">
      <div style={{ position: "relative" }} className="d-flex justify-content-between align-items-center">

        {currentProduct?.label ? <label ref={labelRef} className="">{currentProduct?.label}</label> : <span></span>}
        {isCashback ? <label style={{ position: "absolute" }} ref={cashbackRef}>{cashbackPercentage}% Cashback</label> : <span></span>}

        <WishlistBtn price={currentProduct?.price} savings={savings} product={currentProduct} />
      </div>
      <Swiper
        className="mySwiper"
        modules={[Navigation]}
        navigation={noNav ? false : true}
        speed={500}
        loop={true}
        autoplay={{
          delay: 6000,
          disableOnInteraction: false,
          pauseOnMouseEnter: true,
        }}>
        {
          (currentProduct?.images ?? [currentProduct?.thumbnail])?.map((slide: any, i: number) => (
            <SwiperSlide key={slide + i} >
              <Link href={`/product/${currentProduct?.slug}`} style={{userSelect: "none"}} >
                <SliderImage src={slide} />
              </Link>
            </SwiperSlide>
          ))
        }
      </Swiper>
      <Link href={`/product/${currentProduct?.slug}`} className="product-card-link">
        <h5 className="brand-name">{currentProduct?.brand}</h5>
        <h5 className="product-modal">{currentProduct?.name}</h5>
        <div className="product-price-detail">
          <span className="real-price">
            {currentProduct?.price != 0 ? (

              <span>{currencyCode}</span>
            ) : ""}
            {/* {product?.offerPrice && currencyCode} */}
            {/* <span>{product?.offerPrice ? product?.offerPrice : product?.price}</span> */}
            <span>
              {currentProduct?.offerPrice
                ? currentProduct?.offerPrice === 0
                  ? "Price Not Available"
                  : currentProduct?.offerPrice
                : currentProduct?.price
                  ? currentProduct?.price === 0
                    ? "Price Not Available"
                    : currentProduct?.price
                  : "Price Not Available"}
            </span>
          </span>
          {currentProduct?.offerPrice ? <span className="offer-price">
            <span>{currentProduct?.price && currencyCode}</span>
            <span>{currentProduct?.price}</span>
          </span> : ""}
          {(currentProduct?.offerPrice && currentProduct?.showDiscountPercentage) ? <span className="percentage">
            {currentProduct?.offerPercentage}
            {currentProduct?.offerPercentage && "% OFF"}
          </span> : ""}
        </div>
      </Link >
      <div className="colors">
        {currentProduct?.productType !== "contactLens" && <div className="color-ul">
          {Object.values(product)?.sort(sortDefault)?.map((variant: any, index: number) => (
            <button
              key={index}
              onClick={() => changeVariant(variant)}
              title={variant?.color?.name}
              className={`color-li ${currentProduct?._id == variant?._id ? "isActive" : ""}`}
              style={{
                backgroundImage: `linear-gradient(180deg, ${variant?.color?.color?.[0]
                  } 46%, ${variant?.color?.color?.[1]
                    ? variant?.color?.color?.[1]
                    : variant?.color?.color?.[0]
                  } 46%)`,
                // outline: currentVariant === option.slug ? `2px solid ${option.color[0]}` : "none",
              }}
            ></button>
          ))}
        </div>}
      </div>

      {(isAvailable && currentProduct?.isVirtualTry) && <div className="vm" >
        <Image quality={100} priority
          src="/images/product-detail/sunglasses.svg"
          width={24}
          height={24}
          alt="gallery emoji"
        />
        {translation?.productListing?.virtualTry ?? "Virtual Try ON"}
      </div>
      }
      {
        addToCart && (
          <button onClick={handleCartClick} className="add-to-cart-btn">
            {translation?.productPage?.addToCart ?? "Add To Cart"}
          </button>
        )
      }
    </div >
  );
}

function SliderImage({ src }: any) {
  const [imageSrc, setImageSrc] = useState(src);

  return (
    <Image
      quality={100}
      priority
      src={imageSrc ?? ""}
      width={800}
      height={196}
      alt=""
      className="opacity-0"
      onError={() => setImageSrc("/images/product/noImage.jpg")}
      onLoad={(e: { target: any }) => e.target.classList.remove("opacity-0")}
    />
  )
}