"use client"

import { TranslationContext } from "@/contexts/Translation";
import "./empty-cart.scss";
import Image from "next/image";
import Link from "next/link";
import { useContext } from "react";

function EmptyCart() {
  const { translation: { cartPage } } = useContext(TranslationContext)
  return (
    <>
      <div className="empty-cart-container">
        <div className="empty-cart-image">
          <Image quality={100} priority src="/images/common/Cart illustartion.png" width={275} height={265} alt="logo" />
        </div>
        <div className="empty-cart-text">
          <h3>{cartPage?.yourCartIsEmpty ?? "Your Cart is Empty"}</h3>
          <p>
            {cartPage?.cartEmptyMsg ?? "Looks like you haven’t added anything to your cart yet"} <br />
          </p>

          <Link href="/">{cartPage?.goShop ?? "Go Shop"}</Link>
        </div>
      </div>
    </>
  );
}

export default EmptyCart;
