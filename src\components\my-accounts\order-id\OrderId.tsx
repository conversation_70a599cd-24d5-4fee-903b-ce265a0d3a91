"use client";

import OrderStatus from "@/components/order-status/OrderStatus";
import "./order-id.scss";
import Image from "next/image";
import Link from "next/link";
import useFormattedDate from "@/hooks/useFormattedDate";
import FormattedDate from "@/components/common/FormattedDate";
import RatingPopup from "@/components/rating-popup/ReviewPopUp";
import { useContext, useRef, useState } from "react";
import { useReactToPrint } from "react-to-print";
import { endpoints } from "@/config/apiEndpoints";
import api from "@/config/axios.interceptor";
import { useQuery } from "@tanstack/react-query";
import CancelOrder from "@/components/cancel-order/CancelOrder";
import ErrorPage from "@/components/404/ErrorPage";
import GenericBackButton from "@/components/GenericBackButton/GenericBackButton";
import { useRouter } from "next/navigation";
import { TranslationContext } from "@/contexts/Translation";
import { useLocaleContext } from "@/contexts/LocaleProvider";

const excludeStatuses = ["CANCELLED", "FAILED", "DELIVERED", "RETURNED"];

function OrderId({ order }: any) {
  const printRef = useRef<any>(null);
  const [imageSrc, setImageSrc] = useState<any>({});
  const [isLoading, setIsLoading] = useState(false);
  const { translation: { orderPage, productPage, cartPage } }: any = useContext(TranslationContext)
  const { currentLocale:locale, currencyCode } = useLocaleContext()
  const { data, status, error, isError } = useQuery({
    queryKey: ["orders", order, locale?.split("-")[0]],
    queryFn: () => {
      return api.get(`${endpoints.orderDetail}/${order}`).then((res: any) => {
        return res.data?.result;
      });
    },
  });

  const router = useRouter()

  const downloadInvoice = useReactToPrint({
    content: () => printRef.current,
    bodyClass: "print-Padding",
    onBeforeGetContent: () => setIsLoading(true),
    onBeforePrint: () => setIsLoading(false),
  });

  const download = () => {
    if (data?.invoiceUrl) {
      window.open(
        data.invoiceUrl,
        '_blank' // <- This is what makes it open in a new window.
      );
    }
    // else{
    //   downloadInvoice()
    // }
  }

  const convertToTitleCase = (str: string) => {
    return str
      ?.toLowerCase()
      ?.split(" ")
      ?.map(function (word) {
        return word.charAt(0).toUpperCase() + word.slice(1);
      })
      .join(" ");
  };
  if (isError) return <ErrorPage errorcode="404" />;
  if (!data) return <ErrorPage errorcode="404" />;
  return (
    <section className="order-detail" id="orderDetail" ref={printRef}>
      <div className="printHeader d-print-block d-none">
        <img src="/images/footer/logo.png" alt="" />
      </div>
      <div className="order-detail_wrapper position-relative">
        <GenericBackButton style={{ top: "5px" }} />
        <h2 className="order-no">
          <svg onClick={() => router.push("/my-accounts/my-orders")} className="d-md-block cursor-pointer d-none"
            width="40"
            height="40"
            viewBox="0 0 40 40"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M8.33203 20L33.3321 19.9996"
              stroke="#141B34"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M14.9993 11.666L7.37312 19.2922C7.03979 19.6256 6.87312 19.7922 6.87312 19.9993C6.87312 20.2065 7.03979 20.3731 7.37312 20.7065L14.9993 28.3327"
              stroke="#141B34"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
          {orderPage?.orderNo ?? "Order No"}: {data?.orderNo}
        </h2>
        <OrderStatus history={data?.history} />
        <div className="table">
          <div className="table_body">
            {data?.products?.map((product: any, index: number) => (
              <div key={product?._id} className="table_wrapper">
                <div className="table_row">
                  <div className="table_col-one">
                    <Link href={`/product/${product?.slug}`}>
                      <Image
                        quality={100}
                        priority
                        src={product?.thumbnail}
                        width={120}
                        height={120}
                        alt={product?.name}
                      // onError={() =>
                      //   setImageSrc({
                      //     [product?._id]: "/images/product/noImage.jpg",
                      //   })
                      // }
                      />
                    </Link>
                    <ul>
                      <Link href={`/product/${product?.slug}`}>
                        <li>{product?.name}</li>
                      </Link>
                      {product?.color && <li>{productPage?.color ?? "Color"} : {product?.color?.name}</li>}
                      {product?.size?.name && (
                        <li>{productPage?.size ?? "Size"} : {product?.size?.name}</li>
                      )}
                      <li className="d-sm-none d-block">
                        {currencyCode} {product?.price}
                      </li>
                      <li className="d-print-none">
                        {data?.orderStatus === ("DELIVERED" || "RETURNED") && (
                          <RatingPopup product={product} />
                        )}
                      </li>
                    </ul>
                  </div>

                  <div className="table_col-two">
                    <h5>
                      {currencyCode} {product?.price} x {product?.quantity}
                    </h5>
                  </div>

                  <div className="table_col-three">
                    <h5>
                      {currencyCode} {product?.priceTotal}
                    </h5>
                  </div>
                </div>
                {product?.contactLensDetails && (<div style={{ display: "flex", gap: "1rem" }}>
                  <div style={{ display: "flex", flexDirection: "column", gap: "1rem" }}>
                    {product.contactLensDetails?.sphLeft && <p>SPH Left: {product.contactLensDetails.sphLeft?.name}</p>}
                    {product.contactLensDetails?.cylLeft && <p>CYL Left: {product.contactLensDetails.cylLeft?.name}</p>}
                    {product.contactLensDetails?.axisLeft && <p>AXIS Left: {product.contactLensDetails.axisLeft?.name}</p>}
                  </div>
                  {product?.contactLensDetails?.multiple && <div style={{ display: "flex", flexDirection: "column", gap: "1rem" }}>
                    {product.contactLensDetails?.sphRight && <p>SPH Right: {product.contactLensDetails.sphRight?.name}</p>}
                    {product.contactLensDetails?.cylRight && <p>CYL Right: {product.contactLensDetails.cylRight?.name}</p>}
                    {product.contactLensDetails?.axisRight && <p>AXIS Right: {product.contactLensDetails.axisRight?.name}</p>}
                  </div>}
                  {product.contactLensDetails?.multiFocal && <p>Addition: {product.contactLensDetails.multiFocal}</p>}
                </div>)}
              </div>
            ))}
          </div>

          <div className="table_summary">
            <h4>{orderPage?.invoiceSummary ?? "Invoice Summary"}</h4>
            <ul>
              {data?.invoice?.map((amount: any, index: number) => (
                <li key={"invoice" + index}>
                  <div
                    className={
                      index !== data?.invoice?.length - 1
                        ? "table_summary-title"
                        : "table_summary-totaltext"
                    }
                  >
                    <h5>
                      {
                        amount?.label === "Subtotal" ? (cartPage?.subtotal ?? amount?.label)
                          : amount?.label === "Savings" ? (cartPage?.savings ?? amount?.label)
                            : amount?.label === "Loyalty Discount" ? (cartPage?.loyaltyDiscount ?? amount?.label)
                              : amount?.label == "Total" ? (cartPage?.total ?? amount?.label)
                                : amount?.label
                      }{" "}
                      {index === 0 && (
                        <span>({data?.products?.length} {cartPage?.items ?? "Items"})</span>
                      )}
                    </h5>
                  </div>
                  <div
                    className={
                      index !== data?.invoice?.length - 1
                        ? "table_summary-amount"
                        : "table_summary-totalprice"
                    }
                  >
                    <h5>
                      {currencyCode}
                      <span> {Number(amount?.value).toFixed(2)}</span>
                    </h5>
                  </div>
                </li>
              ))}
            </ul>

            {/* <button className="d-print-none">Cancel Order</button> */}
            {/* {!excludeStatuses?.includes(data?.orderStatus) && (
              <CancelOrder order={data?._id} />
            )} */}
          </div>
        </div>

        <div className="delivery-details">
          <div className="delivery-details_cutemer-details">
            <h4>{orderPage?.customerDetails ?? "Customer Details"}</h4>
            <div className="box">
              <ul>
                <li>{data?.address?.name}</li>
                <li>
                  <Link href="">{data?.address?.number}</Link>
                </li>
                <li>
                  {data?.address?.suiteUnit && (
                    <span>{data?.address?.suiteUnit},</span>
                  )}
                  {data?.address?.street}, {data?.address?.city},
                  {data?.address?.emirates},{data?.address?.country} -
                  {data?.address?.postalCode}
                </li>
                <li>
                  {data?.address?.countryCode}-{data?.address?.mobile}
                </li>
              </ul>
            </div>
          </div>
          {/*
          <div className="delivery-details_shipping-details">
            <h4>Shipping Details</h4>
            <div className="delivery-details_box">
              <div className="box">
                <div className="box_content">
                  <div className="box_title">
                    <h5>Arrives by Monday, Sep 20</h5>
                  </div>
                  <div className="box_logo">
                    <Image quality={100} priority
                      src="/images/common/fedex.png"
                      width={87}
                      height={39}
                      alt="fedex"
                    />
                    <span>FedEx Home Delivery</span>
                  </div>
                </div>
                <div className="box_barcode">
                  <Image quality={100} priority
                    src="/images/common/barcode.png"
                    width={172}
                    height={65}
                    alt="barcode"
                  />
                </div>
              </div>
            </div>
          </div> */}
        </div>

        <div className="invoice">
          <ul>
            <li>
              {orderPage?.orderDate ?? "Order Date"} :{" "}
              <span>
                <FormattedDate date={data?.orderDate} />
              </span>
            </li>
            {data?.orderStatus != "DELIVERED" && <li>
              {orderPage?.estimatedDelivery ?? "Estimated Delivery Date"} :{" "}
              <span>{new Date(data?.expectedDate)?.toDateString()}</span>
            </li>}
            <li>
              {orderPage?.paymentStatus ?? "Payment Status"} :{" "}
              <span>{
                data?.paymentStatus === "PENDING" ? orderPage?.pending
                  : data?.paymentStatus === "PAID" ? orderPage?.paid
                    : data?.paymentStatus
              }</span>
            </li>
            {data?.trackingId ? <li>
              Tracking: <a href={`https://app.ecofreight.ae/en/tracking/${data?.trackingId}`}>{data?.trackingId}</a>
            </li> : ""}
            {data?.awb ? <li>
              AWB: <a href={data?.awb}>AWB</a>
            </li> : ""}
          </ul>
          <ul>
            <li>
              {orderPage?.orderStatus ?? "Order Status"}{" "}
              <span>:

                {locale.includes("en") ? convertToTitleCase(data?.orderStatus)
                  : data?.orderStatus == "PLACED" ? orderPage?.orderPlaced
                    : data?.orderStatus == "CONFIRMED" || data?.orderStatus == "READY TO SHIP" ? orderPage?.confirm
                      : data?.orderStatus == "SHIPPED VIA ECO" || "SHIPPED VIA INHOUSE" ? orderPage?.shipped
                        : data?.orderStatus == "DELIVERED" ? orderPage?.delivered
                          : data?.orderStatus == "CANCELLED" ? orderPage?.cancelled
                            : data?.orderStatus
                }</span>
            </li>
            <li>
              {orderPage?.paymentMethod ?? "Payment Method"} <span>: {
                data?.paymentMethod === "COD" ? orderPage?.cod
                  : data?.paymentMethod === "ONLINE" ? orderPage?.online
                    : data?.paymentMethod
              } </span>
            </li>
            {data?.paymentMethod === "ONLINE" ? (
              <li>
                {orderPage?.gateway ?? "Gateway"} <span>: {data?.gateway}</span>
              </li>
            ): ""}
            <li>
              {orderPage?.shippingMethod ?? "Shipping Method"} <span>: {
                data?.shippingMethod === "click" ? (orderPage?.clickAndCollect ?? "Click & Collect")
                    : (orderPage?.doorStepDelivary ?? "Doorstep Delivery")
              } </span>
            </li>
          </ul>
          {data?.invoiceUrl && <button className="d-print-none" onClick={download}>
            {orderPage?.downloadInvoice ?? "Download Invoice"}
            {isLoading && (
              <span className="ms-2 spinner-border" role="status"></span>
            )}
          </button>}
        </div>

        {data?.shippingMethod === "click" && <div className="invoice">
          <div className="">
            <h4>Store information</h4>
          <p>{data?.store?.name}</p>
          <p>{data?.store?.address}</p>
          <p>{data?.store?.email}</p>
          <p>{data?.store?.countryCode}{" "}{data?.store?.mobile}</p>
          </div>
        </div>}
      </div>
    </section>
  );
}

export default OrderId;
