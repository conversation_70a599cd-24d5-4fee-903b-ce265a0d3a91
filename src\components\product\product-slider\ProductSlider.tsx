"use client";

import { Swiper, SwiperSlide } from "swiper/react";

import {
  Pagination,
  Navigation,
  Autoplay,
  Keyboard,
  Mousewheel,
} from "swiper/modules";
import { usePathname } from "next/navigation";
import ProductCard from "../product-card/ProductCard";
import "./productSlider.scss";

export default function ProductSlider(props: any, products: any) {
  const pathname = usePathname();
  if (props.status === true)
    return (
      <div className="productslider container">
        <div className="productslider_wrapper">
          <div style={{ width: "100%", gap: "10px" }} className="loaderGrid">
            <div className="productCardLoading skeletonLoader"></div>
            <div className="productCardLoading skeletonLoader"></div>
            <div className="productCardLoading skeletonLoader"></div>
          </div>
        </div>
      </div>
    );

  return (
    <div
      className={`productslider ${
        (pathname === "/ar" || pathname === "/en") && "product-slider-home"
      }`}
    >
      <div className="productslider_wrapper ps-md-5">
        <div style={{ width: "100%" }}>
          <Swiper
            // slidesOffsetBefore={110}
            // cssMode
            keyboard
            mousewheel={{
              enabled: true,
              forceToAxis: true,
            }}
            pagination={
              props?.products?.length > 3
                ? {
                    type: "progressbar",

                    progressbarFillClass: `swiper-pagination-progressbar-fill ${
                      props?.color === "white" ? "fill-white" : "fill-black"
                    }`,
                  }
                : false
            }
            navigation={true}
            modules={[
              Pagination,
              Navigation,
              Autoplay,
              Keyboard,
              Mousewheel
            ]}
            className="mySwiper"
            speed={500}
            autoplay={
              props?.products?.length > 3
                ? {
                    delay: 2000,
                    disableOnInteraction: false,
                    pauseOnMouseEnter: true,
                  }
                : false
            }
            breakpoints={{
              // Define responsive breakpoints here
              320: {
                slidesPerView: 1,
                slidesOffsetBefore: 0,
              },

              600: {
                slidesPerView: 2,
              },

              900: {
                slidesPerView: 2.5,
              },

              1024: {
                slidesPerView: 3.3,
              },
            }}
            spaceBetween={20}
            loop={true}
            // loopAddBlankSlides={true}
          >
            {props?.products?.map((card: any, index: number) => (
              <SwiperSlide  key={Object.values(card).map((v: any) => v._id).join("-") || index}>
                <ProductCard noNav={true} product={card} />
              </SwiperSlide>
            ))}
          </Swiper>
        </div>
      </div>
    </div>
  );
}
