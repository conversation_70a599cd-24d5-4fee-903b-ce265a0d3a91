"use client";

import ProductSlider from "@/components/product/product-slider/ProductSlider";
import Dropdown from "@/components/home/<USER>/DropDown";
import { useQuery } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import api from "@/config/axios.interceptor";
import { endpoints } from "@/config/apiEndpoints";

export default function CollectionSection({ styles, color, multiple, keyword, id, item }: any) {
  const [collection, setCollection] = useState(keyword[0]);
  const {
    data: collectionData,
    status,
    error,
    isLoading,
  } = useQuery({
    queryKey: ["collection", id, collection?._id],
    gcTime: 0,
    queryFn: () => {
      return api.get(`${endpoints.collections}/${collection?.keyword}`).then((res) => {
        if (res.status === 200) {
          return res.data?.result;
        } else throw error;
      });
    },
  });

  useEffect(() => {
    setCollection(keyword[0]);
  }, [keyword]);

  return (
    <>
      {multiple ? (
        <Dropdown
          collection={collection}
          keyword={keyword}
          setCollection={setCollection}
          className={styles}
          item={item}
        />
      ) : (
        <h2>{collectionData?.title}</h2>
      )}
      {/* <Dropdown className={styles} /> */}
      <ProductSlider products={collectionData?.products} status={isLoading} color={color} />
    </>
  );
}
