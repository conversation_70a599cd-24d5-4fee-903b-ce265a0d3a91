"use client";
import { endpoints } from "@/config/apiEndpoints";
import api from "@/config/axios.interceptor";
import { Alphabets } from "@/lib/brands";
import { useQuery } from "@tanstack/react-query";
import Link from "next/link";
import { useState, useEffect, useContext } from "react";
import LogoAnimation from "../LogoAnimation/LogoAnimation";
import BreadCrumbs from "../breadcrumbs/BreadCrumbs";
import EmptyState from "../empty-states/EmptyState";
import Image from "next/image";
import FallbackImage from "../fallback-image/FallbackImage";
import { TranslationContext } from "@/contexts/Translation";
import { useLocaleContext } from "@/contexts/LocaleProvider";

function Brands() {
  const [activeItem, setActiveItem] = useState("All");
  const { currentLocale } = useLocaleContext()
  const { translation } = useContext(TranslationContext)
  const {
    data: brandsData,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["brands", currentLocale?.split("-")[0]],
    queryFn: () => {
      return api.get(endpoints.brands).then((res) => {
        if (res.status === 200) {
          return res.data?.result;
        } else throw error;
      });
    },
  });
  const [filteredBrands, setFilteredBrands] = useState(brandsData);
  const handleAlphabetClick = (alphabet: string) => {
    setActiveItem(alphabet);
    if (alphabet === "All") {
      setFilteredBrands(brandsData);
    } else {
      const filtered = brandsData?.filter((brand: any) => brand.name[0].toLowerCase() === alphabet);
      setFilteredBrands(filtered);
    }
  };
  useEffect(() => {
    setFilteredBrands(brandsData);
  }, [brandsData]);
  return (
    <main>
      <BreadCrumbs
        backHome="Home"
        currentPage="Brands"
        image="/images/common/banner.png"
      />

      <section className="brands">
        <div className="brands_filter">
          <h2>{translation?.other?.ourBrands ?? "Our Brands"}</h2>
          <div className="container" style={{direction: "ltr"}}>
            <ul>
              {Alphabets.map((item: any, index: any) => (
                <li
                  key={index}
                  className={activeItem === item ? "active" : ""}
                  onClick={() => handleAlphabetClick(item)}
                >
                  {item}
                </li>
              ))}
            </ul>
          </div>
        </div>

        <div className="brandlogo">
          <div className="container">
            <div className="brandlogo_grids">
              {filteredBrands?.map((items: any, index: any) => {
                return (
                  <>
                    {items.image && (
                      <Link
                        className="brandlogo_image"
                        href={`/brands/${items?.slug}`}
                        key={items._id}
                        title={items?.name}
                      >
                        <FallbackImage
                          alt={items?.name}
                          width={210}
                          height={500}
                          fallbackSrc="/images/product/noImage.jpg"
                          src={items.image}
                          quality={100} />
                      </Link>
                    )}
                  </>
                );
              })}
            </div>

            {!isLoading && filteredBrands?.length === 0 && (
              <EmptyState icon="box" title="No Brands Found" />
            )}
            {isLoading && (
              <div className="page-center">
                {" "}
                <LogoAnimation />
              </div>
            )}
          </div>
        </div>
      </section>
    </main>
  );
}

export default Brands;
