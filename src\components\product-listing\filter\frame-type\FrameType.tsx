import Image from "next/image";

const FrametypeData = [
  {
    image: "/images/product-listing/sp1.png",
    type: "Full Rim",
  },

  {
    image: "/images/product-listing/sp2.png",
    type: "Half Rim",
  },

  {
    image: "/images/product-listing/sp3.png",
    type: "Rimless",
  },
];

function FrameType() {
  return (
    <>
      {FrametypeData.map((items, index) => (
        <label
          className="brands-border"
          key={index}
          htmlFor={`frame-type-${index}`}
        >
          <input type="checkbox" id={`frame-type-${index}`} />
          <Image quality={100} priority src={items.image} width={64} height={31} alt="frame type" />
          <span>{items.type}</span>
        </label>
      ))}
    </>
  );
}

export default FrameType;
