.coupon-modal {
    &.modal {
        padding: 0;

        .modal-dialog {
            max-width: 60rem;
            width: 100%;

            @media (max-width: 575.98px) {
                margin: auto;
            }
        }

        .modal-content {
            border: none;
            border-radius: 3rem;
            padding: 4.2rem 5.2rem 0 5.2rem;
            min-height: 58rem;

            @media (max-width: 575.98px) {
                height: 100vh;
                border-radius: 0;
                padding: 2rem 2rem 5.7rem 2rem;
            }
        }

        .modal-header {
            border: none;
            padding: 0;

            .btn-close {
                background-image: url(../../../public/images/common/close.png);
                width: 4.8rem;
                height: 4.8rem;
                background-position: center;
                background-repeat: no-repeat;
                background-size: contain;
                opacity: 1;
                padding: 0;
                position: absolute;
                right: 30px;

                @media (max-width: 575.98px) {
                    width: 2.8rem;
                    height: 2.8rem;
                }

                &:focus {
                    box-shadow: none;
                    outline: none;
                    border: none;
                }
            }
        }

        .modal-body {
            padding: 0;

            h2 {
                text-align: left;
            }

            h5 {
                color: #000;
                font-size: 2rem;
                font-weight: 500;
                line-height: normal;
                margin-bottom: 1.5rem;
                margin-top: 2.4rem;
            }

            h4 {
                color: #090909;
                font-size: 1.7rem;
                font-weight: 600;
                line-height: normal;
            }

            p {
                color: #1E2738;
                font-size: 1.3rem;
                font-weight: 500;
                margin-top: 0.8rem;
                max-width: 42.2rem;
                width: 100%;
            }
        }
    }

    &_scroll {
        overflow: auto;
        height: 570px;

        &::-webkit-scrollbar {
            display: none;
        }
    }

    &_box {
        border-radius: 1rem;
        border: 1px solid #D7DEEF;
        background: #F2F4F9;

        &:not(:last-child) {
            margin-bottom: 1.5rem;
        }
    }

    &_head {
        display: flex;
        justify-content: space-between;
        padding: 1.5rem 2rem;
        border-radius: 1rem 1rem 0rem 0rem;
        background: #1E2738;

        span {
            color: #FFF;
            font-size: 1.7rem;
            font-weight: 600;
            text-transform: uppercase
        }

        button {
            border: none;
            padding: 0;
            color: #FFF;
            text-align: right;
            font-size: 1.5rem;
            font-weight: 600;
            text-decoration-line: underline;
            background: none;
        }
    }

    &_body {
        padding: 2rem;
    }
}