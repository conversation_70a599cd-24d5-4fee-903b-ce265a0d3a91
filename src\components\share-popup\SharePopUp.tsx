import "./share-popup.scss";
import Modal from "react-bootstrap/Modal";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";
import { useCopyToClipboard } from "usehooks-ts";
import { toast } from "sonner";

function SharePopUp({ show, handleClose, data }: any) {
  const [url, setUrl] = useState("");
  useEffect(() => {
    if (typeof window !== "undefined") {
      setUrl(window.location.href);
    }
  }, []);
  const [value, copy] = useCopyToClipboard();

  const handleCopy = () => {
    copy(url);
    toast.success("Link Copied");
  };

  // const shareData = {
  //   title: `Hey Checkout ${data?.name} on Yateem Optician`,
  //   text: data?.name,
  //   image: data?.image,
  //   url: url,
  // };

  const nativeShare = () => {
    if (navigator.share) {
      navigator
        .share({
          title: `Hey Checkout ${data?.name} on Yateem Optician`,
          text: data?.name,
          url: url,
        })
        .then(() => {})
        .catch((error) => {});
    }
  };

  return (
    <>
      <Modal
        show={show}
        onHide={handleClose}
        backdrop="static"
        keyboard={false}
        centered
        className="share-popup">
        <Modal.Header closeButton></Modal.Header>
        <Modal.Body>
          <label>Copy Link</label>
          <div className="copy-btn">
            <input
              readOnly
              type="text"
              placeholder="https://yateemoptician.online/product/ray-b.."
              value={url}
            />
            <button onClick={handleCopy}>
              <Image quality={100} priority src="/images/common/icons/fi_copy.png" width={24} height={24} alt="copy" />
            </button>
          </div>

          <h6>Share</h6>
          <ul>
            <li>
              <Link
                target="_blank"
                href={`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`}>
                <Image quality={100} priority
                  src="/images/common/Facebook - Negative.png"
                  width={24}
                  height={24}
                  alt="social links"
                />
              </Link>
            </li>

            <li>
              <Link onClick={nativeShare} href="">
                <Image quality={100} priority
                  src="/icons/social/Instagram.svg"
                  width={24}
                  height={24}
                  alt="social links"
                />
              </Link>
            </li>
            <li>
              <Link
                target="_blank"
                href={`http://www.reddit.com/submit?url=${encodeURIComponent(
                  url
                )}&title=${encodeURIComponent(data?.name)}`}>
                <Image quality={100} priority src="/icons/social/Reddit.svg" width={24} height={24} alt="social links" />
              </Link>
            </li>

            <li>
              <Link
                target="_blank"
                href={`https://www.linkedin.com/sharing/share-offsite/?url=${url}`}>
                <Image quality={100} priority src="/icons/social/LinkedIn.svg" width={24} height={24} alt="social links" />
              </Link>
            </li>

            <li>
              <Link target="_blank" href={`http://www.tumblr.com/share/link?url=${url}`}>
                <Image quality={100} priority src="/icons/social/tumbler.svg" width={24} height={24} alt="social links" />
              </Link>
            </li>
            <li>
              <Link
                target="_blank"
                href={`https://telegram.me/share/url?url=${url}&text=Hey Checkout ${data?.name} from Yateem Optician!`}>
                <Image quality={100} priority src="/icons/social/Telegram.svg" width={24} height={24} alt="social links" />
              </Link>
            </li>
            <li>
              <Link
                href={`http://pinterest.com/pin/create/button/?url=${encodeURIComponent(
                  url
                )}&media=${encodeURIComponent(data?.images?.[0])}&description=${encodeURIComponent(
                  data?.name
                )}`}>
                <Image quality={100} priority
                  src="/icons/social/Pinterest.svg"
                  width={24}
                  height={24}
                  alt="social links"
                />
              </Link>
            </li>
            <li>
              <Link href={`https://vk.com/share.php?url=${encodeURIComponent(url)}`}>
                <Image quality={100} priority src="/icons/social/VK.svg" width={24} height={24} alt="social links" />
              </Link>
            </li>
            <li>
              <Link onClick={nativeShare} href="">
                <Image quality={100} priority src="/icons/social/Skype.svg" width={24} height={24} alt="social links" />
              </Link>
            </li>
            <li>
              <Link
                target="_blank"
                href={`https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}`}>
                <Image quality={100} priority
                  src="/icons/social/twitter-x-fill.svg"
                  width={24}
                  height={24}
                  alt="social links"
                />
              </Link>
            </li>
            <li>
              <Link
                target="_blank"
                href={`https://wa.me/?text=${encodeURIComponent(
                  `Hey! Checkout *${data?.name}*\n A new product from _Yateem Optician!_` +
                  ` ${url}`
                )}`}>
                <Image quality={100} priority
                  src="/icons/social/whatsapp-fill.svg"
                  width={24}
                  height={24}
                  alt="social links"
                />
              </Link>
            </li>
            <li>
              <Link onClick={nativeShare} href="">
                <Image quality={100} priority
                  src="/icons/social/share-fill.svg"
                  width={24}
                  height={24}
                  alt="social links"
                />
              </Link>
            </li>
          </ul>
        </Modal.Body>
      </Modal>
    </>
  );
}

export default SharePopUp;
