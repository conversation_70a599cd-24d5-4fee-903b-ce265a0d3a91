"use client";

import { createI18nClient } from "next-international/client";
import { createContext, useContext, useEffect, useState } from "react";

export const LocaleContext: any = createContext(null);

export const LocaleProvider = ({ children, locales, locale, countryCodes, currencyCode }: any) => {

  const obj: any = {}
  locales.forEach((item: any) => {
    obj[item] = async () => {
      await new Promise(resolve => setTimeout(resolve, 100));
      if (item.includes("en")) return import(`@/locales/en`);
      return import(`@/locales/ar`)
    }
  })

  const { useCurrentLocale, useChangeLocale, useI18n, I18nProviderClient } = createI18nClient(obj, {})

  return (
    <I18nProviderClient locale={locale}>
      <LocaleContext.Provider
        value={{
          useCurrentLocale: useCurrentLocale,
          useChangeLocale: useChangeLocale,
          useI18n: useI18n,
          countryCodes,
          currencyCode,
        }}>
        {children}
      </LocaleContext.Provider>
    </I18nProviderClient>
  );
};

export function useLocaleContext() {
  const locale: any = useContext(LocaleContext);
  if (!locale) {
    throw new Error("useLocale must be used within a LocaleProvider");
  }
  const currentLocale = locale.useCurrentLocale();
  const changeLocale = locale.useChangeLocale();
  const countryCodes = locale.countryCodes;
  const t = locale.useI18n();
  const currencyCode = locale.currencyCode;
  return { currentLocale, changeLocale, t, countryCodes, currencyCode };
}
