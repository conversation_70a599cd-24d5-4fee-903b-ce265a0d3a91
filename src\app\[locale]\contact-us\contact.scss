.contact-info {
  margin-top: 3.5rem;

  @media (max-width: 575.98px) {
    margin-top: 1.5rem;
  }

  &_flex {
    display: flex;
    justify-content: space-between;
    border-radius: 2rem;
    border: 1px solid #ebebeb;
    background: #f2f4f9;
    padding: 3.1rem 5.5rem 4.5rem 3.9rem;
    margin-top: 2rem;

    @media (max-width: 991.98px) {
      column-gap: 4rem;
    }

    @media (max-width: 767.98px) {
      flex-direction: column;
      padding: 3rem 2rem 4.2rem 2rem;
    }

    @media (max-width: 575.98px) {
      margin-top: 1.5rem;
    }
  }

  &_items {
    position: relative;
    width: calc(100% / 3);

    @media (max-width: 767.98px) {
      width: 100%;
    }

    &:not(:last-child) {
      margin-right: 2.3rem;
      padding-right: 2.3rem;
      border-right: 1px solid #000;

      @media (max-width: 767.98px) {
        border-right: 0;
      }
    }

    @media (max-width: 767.98px) {
      &:not(:last-child) {
        border-bottom: 1px solid #000;
        padding-bottom: 2rem;
        margin-bottom: 2rem;
        padding-right: 0rem;
        margin-right: 0rem;
      }
    }



    h6 {
      color: #1f2738;
      font-size: 1.9rem;
      font-weight: 700;

      @media (max-width: 575.98px) {
        font-size: 1.6rem;
      }
    }

    ul {
      margin-top: 1rem;

      @media (max-width: 575.98px) {
        margin-top: 1.4rem;
      }

      li {
        color: #000;
        font-size: 1.7rem;
        font-weight: 400;
        line-height: 2.1rem;

        @media (max-width: 575.98px) {
          font-size: 1.4rem;
          line-height: 1.7rem;
        }

        &:not(:last-child) {
          margin-bottom: 1rem;
        }

        a {
          color: #000;
          font-size: 1.7rem;
          font-weight: 400;

          @media (max-width: 575.98px) {
            font-size: 1.4rem;
          }
        }
      }
    }
  }
}

// CONNET SECTION STYLE
.connect {
  margin-top: 15.5rem;
  background: #f2f4f9;

  @media (max-width: 991.98px) {
    padding-top: 4.7rem;
    margin-top: 8rem;
  }

  &_wrapper {
    display: flex;
    align-items: center;
    position: relative;
    z-index: 2;

    &::after {
      position: absolute;
      content: "";
      background-image: url(../../../../public/images/common/ee.png);
      background-repeat: no-repeat;
      background-size: cover;
      z-index: -1;
      max-width: 63.8031rem;
      width: 100%;
      min-height: 31.076rem;
      right: 0;

      @media (max-width: 991.98px) {
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        right: unset;

        @media (max-width: 575.98px) {
          max-width: 100%;
          min-height: 18rem;
          background-size: contain;
          top: unset;
          bottom: 23px;
          transform: translateX(-50%);
        }
      }
    }

    @media (max-width: 991.98px) {
      flex-direction: column;
    }
  }

  &_content {
    width: 50%;

    @media (max-width: 991.98px) {
      width: 100%;
    }

    h2 {
      font-size: 5.2rem;
      font-weight: 600;
      line-height: 5.5rem;
      letter-spacing: -0.2rem;
      text-align: left;
      max-width: 42.6rem;
      width: 100%;

      @media (max-width: 767.98px) {
        font-size: 4.2rem;
        font-weight: 600;
        line-height: 4.5rem;
      }
    }

    p {
      margin-top: 1.2rem;
      font-size: 1.8rem;
      font-weight: 400;
      line-height: 3rem;
      color: #52525b;
      max-width: 46.1rem;
      width: 100%;

      @media (max-width: 767.98px) {
        font-size: 1.4rem;
        margin-top: 3.2rem;
      }
    }

    .primary-btn {
      background-color: #000;
      color: #fff;
      border: 1px solid #000;
      padding: 1.5rem 4.6rem;
      z-index: 1;
      position: relative;

      @media (max-width: 767.98px) {
        margin-top: 2.5rem;
        padding: 1.1rem 4.4rem;
      }

      &:hover {
        background-color: transparent;
        color: #000;
      }
    }
  }

  &_image {
    width: 50%;
    position: relative;
    margin-top: -9rem;

    @media (max-width: 991.98px) {
      width: 100%;
      margin-top: 0;

      img {
        object-fit: contain;
      }
    }
  }
}

.app.rtl {
  .top {
    button {
      svg {
        transform: rotate(180deg);
      }
    }
  }

  .contact-info_flex {
    padding: 3.1rem 3.9rem 4.5rem 5.5rem;

  }

  .contact-form_inputs input {
    padding-left: 0rem;
    padding-right: 1.5rem;
  }

  .contact-form_inputs:nth-of-type(4)::after {
    background-position: left;
    right: auto;
    left: 0;
  }

  .contact-form_inputs .countrycode #countryCode {
    background-position: left 23%;
    padding-right: 0;
  }

  .contact-form_inputs select {
    padding-left: 0rem;
    padding-right: 1.5rem;
  }

  .contact-form_inputs textarea {
    padding-left: 0rem;
    padding-right: 1.5rem;
  }

  .connect_content h2 {
    text-align: right;
  }

  .connect_wrapper::after {
    left: 0;
    right: unset;
  }


  .contact-info_items:not(:last-child){
    border-right: 0;
  }

  .contact-info_items:not(:first-child) {
    margin-right: 2.3rem;
    padding-right: 2.3rem;
    border-right: 1px solid black;

    @media (max-width: 767.98px) {
      border-right: 0;
      margin-right: 0;
    padding-right: 0;
    }
  }
}