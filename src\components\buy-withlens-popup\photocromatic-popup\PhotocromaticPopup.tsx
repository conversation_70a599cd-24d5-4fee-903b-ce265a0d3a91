import { Modal } from "react-bootstrap";
import Image from "next/image";

const lensColors = ["<PERSON>", "Gray"];

export default function PhotocromaticPopup({
  show,
  handleClose,
  lensData,
  handleLensData,
  setStep,
  back,
}: any) {
  return (
    <>
      <Modal
        className="buy-with"
        show={show}
        onHide={handleClose}
        backdrop="static"
        keyboard={false}
        centered>
        <Modal.Header closeButton></Modal.Header>
        <Modal.Body>
          <h2>
            <Image quality={100} priority
              onClick={() => back("photocromatic")}
              src="/images/common/bakarrow.png"
              width={40}
              height={40}
              alt="back arrow"
            />
            Photocromic
          </h2>
          {lensColors.map((lensType) => (
            <label htmlFor={lensType} key={lensType}>
              <input
                id={lensType}
                type="radio"
                value={lensType}
                defaultChecked={lensData?.photocromic === lensType}
                onChange={(e) => handleLensData({ photocromic: e.target.value })}
                name="lensType"
              />
              {lensType}
            </label>
          ))}

          <button onClick={() => setStep("photocromatic")}>Next</button>
        </Modal.Body>
      </Modal>
    </>
  );
}
