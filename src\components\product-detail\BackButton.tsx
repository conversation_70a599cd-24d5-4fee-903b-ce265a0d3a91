"use client";

import { HistoryContext } from "@/contexts/HistoryProvider";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useContext } from "react";

export default function BackButton() {
  const { prevPage } = useContext(HistoryContext);
  const router = useRouter();

  const capitalizeFirstLetter = (string: string | undefined): string => {
    if (!string) return "";
    return string?.charAt(0).toUpperCase() + string?.slice(1);
  };

  const capitalizedTitle = capitalizeFirstLetter(prevPage?.title);
  return (
    <h6 className="d-sm-none d-block" onClick={() => router.back()}>
      <Image
        quality={100}
        priority
        src="/images/product-detail/arrow-left.png"
        width={24}
        height={24}
        alt="arrow"
      />
      <span>{capitalizedTitle}</span>
    </h6>
  );
}
