import { Suspense } from "react";
import Login from "./login";
import { cookies } from "next/headers";
import { getTranslation } from "@/lib/methods/translation";
import { getMultiStores } from "@/lib/methods/multistore";
import { getUser } from "@/lib/methods/auth";

declare global {
  interface Window {
    confirmationResult?: any;
    recaptchaVerifier?: any;
  }
}

export default async function page() {

  const lang = cookies().get("Next-Locale")?.value || "ae-en"
  let code = "+971"
  const [stores, data, user] = await Promise.all([
    getMultiStores(),
    getTranslation(lang),
    getUser()
  ])

  let page = "number"

  if(user?.isGuest){
    page = "signup"
  }

  stores?.result?.forEach((item: any) => {
    if(lang.split("-")[0] == item?.storeId){
      code = item?.countryCode
    }
  })

  return (
    <Suspense>
      <Login lang={lang} translation={data?.result} code={code} page={page} />
    </Suspense>
  )
}