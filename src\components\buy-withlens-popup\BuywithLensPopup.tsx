"use client";
import React, { Suspense, useContext, useEffect } from "react";
import Modal from "react-bootstrap/Modal";
import "./buy-wthlens-popup.scss";
import { useState } from "react";
import Image from "next/image";
import { LensDataType, stepFlow } from "@/lib/types/lensTypes";
import OverLayLoader from "../LogoAnimation/OverLayLoader";
import { endpoints } from "@/config/apiEndpoints";
import api from "@/config/axios.interceptor";
import { toast } from "sonner";
import { useRouter, useSearchParams } from "next/navigation";
import { CountContext } from "@/contexts/AddBtnContext";
import { QueryClient, useQueryClient } from "@tanstack/react-query";
import Dropdown from "react-bootstrap/Dropdown";
import DropdownButton from "react-bootstrap/DropdownButton";
import { Accordion } from "react-bootstrap";
import Select from "react-select";
import { useQuery } from "@tanstack/react-query";
import { addToCart } from "@/lib/methods/cart";
import SelectPreferenceModal from "../select-preference/SelectPreferenceModal";
import { useLocaleContext } from "@/contexts/LocaleProvider";

const SingleVisionDrop = React.lazy(
  () => import("../single-vision-drop/SingleVisionDrop")
);
const BrandsPopup = React.lazy(() => import("./brands-popup/BrandsPopup"));
const CoatingPopup: any = React.lazy(
  () => import("../coasting-popup/CoastingPopup")
);
const UserDetailsPopup = React.lazy(
  () => import("./user-details/UserDetailsPopup")
);
const SuccessPopup = React.lazy(() => import("../success-popup/SuccessPopup"));
const LensTypePopup = React.lazy(() => import("./lens-type/LensType"));
const PhotocromaticPopup = React.lazy(
  () => import("./photocromatic-popup/PhotocromaticPopup")
);
const IndexPopup = React.lazy(() => import("./index-popup/IndexPopup"));

const brandOptions = [
  { value: "zeiss", label: "Zeiss" },
  { value: "carl_zent", label: "Carl Zeiss" },
  { value: "rayban", label: "Ray Ban" },
  { value: "maui_jim", label: "Maui Jim" },
  { value: "warby_parker", label: "Warby Parker" },
];

type StepsType =
  | "vision"
  | "singleVision"
  | "coating"
  | "progressive"
  | "brands"
  | "";

function BuywithLensPopup({ product, translation }: any) {
  const { currentLocale:locale, currencyCode } = useLocaleContext()
  const [price, setPrice] = useState({
    price: product?.price?.aed || product?.sizes?.[0]?.price,
    offerPrice: product?.offerPrice?.aed || product?.sizes?.[0]?.offerPrice,
  });
  const router = useRouter();
  const queryClient = useQueryClient();
  const { count, varient } = useContext(CountContext);
  const [sum, setSum] = useState(0);
  const [sumList, setSumList] = useState({
    lensType: 0,
    single: 0,
    progressive: 0,
    coating: 0,
    index: 0,
  });
  const [activeKey, setActiveKey] = useState<string | null>(null);
  const [cartUpdated, setCartUpdated] = useState(0);
  const handleSumList = (key: string, value: number, clear?: number) => {
    setSumList({ ...sumList, [key]: value ? value : 0 });
    if (clear) {
      setSumList({
        ...sumList,
        index: value ? value : 0,
        coating: value ? value : 0,
      });
    }
  };

  const searchParams = useSearchParams();
  const size =
    searchParams?.get("size") || product?.sizes?.[0]?.size?._id || null;
  const [lensData, setLensData] = useState<LensDataType>({
    product: product?._id,
    quantity: count || 1,
    vision: "",
    size: size,
    prescription: null,
    lensType: "Plastic or CR3",
    brand: "",
    photocromic: "",
    index: "",
    coating: "",
    userDetails: {
      name: "",
      phone: "",
      message: "",
    },
    sum: sum,
  });

  const handleClick = () => {
    addToCart(product?._id, count, size).then(() => {
      setCartUpdated(cartUpdated + 1);
    });
    handleBack("type");
  };

  const handleSkipClick = () => {
    api
      .post(endpoints.buyWithLens, {
        prescriptionType: "skip",
        product: lensData?.product,
        size: lensData?.size,
        quantity: lensData?.quantity,
        vision: lensData?.vision,
      })
      .then((res) => {
        setCartUpdated(cartUpdated + 1);
        handleBack("type");
        toast.success(res.data.message);
      })
      .catch((err) => {
        toast.error(err.response.data.message);
      });
  };

  const handleLensData = (data: any) => {
    // setLensData({ ...lensData, ...data });
    setLensData((prev) => {
      return { ...prev, ...data };
    });
  };
  const [step, setStep] = useState<any>("");

  const {
    data: coatings,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["lens-brands"],
    queryFn: () => {
      return api.get(endpoints.lensBrands).then((res) => {
        if (res.status === 200) {
          return res.data?.result;
        }
      });
    },
  });

  const {
    data: footer,
    // isLoading,
    // error,
  } = useQuery({
    queryKey: ["footer"],
    queryFn: () => {
      return api.get(endpoints.footer).then((res) => {
        if (res.status === 200) {
          return res.data?.result;
        }
      });
    },
  });

  useEffect(() => {
    if (cartUpdated) updateCartCount();
  }, [cartUpdated]);

  const updateCartCount = () => {
    queryClient.invalidateQueries({ queryKey: ["cart", "counts"] });
    queryClient.invalidateQueries({ queryKey: ["cart"] });
    setCartUpdated(0);
  };
  console.log(coatings)
  const brands =
    coatings?.map((coating: any) => ({
      label: coating.name,
      value: coating._id,
    })) || [];

  const buyWithLens = () => {
    return api.post(endpoints.buyWithLens, lensData);
  };

  const handleClose = () => {
    setStep("");
    setSum(0);
    setLensData({
      product: product?._id,
      quantity: count || 1,
      vision: "single",
      size: size,
      prescription: null,
      lensType: "Plastic or CR3",
      brand: "",
      photocromic: "",
      index: "",
      coating: "",
      userDetails: {
        name: "",
        phone: "",
        message: "",
      },
      sum: sum,
    });
    setSumList({
      lensType: 0,
      single: 0,
      progressive: 0,
      coating: 0,
      index: 0,
    });
    setActiveKey(null);
    queryClient.invalidateQueries({ queryKey: ["cart"] });
    queryClient.invalidateQueries({ queryKey: ["cart", "counts"] });
  };

  const handleNext = (currentStep: any) => {
    const currentStepIndex =
      stepFlow[lensData.vision as keyof typeof stepFlow].indexOf(currentStep);
    switch (currentStep) {
      case "lensType": {
        if (lensData.lensType !== "Photocromic") {
          const nextStep =
            stepFlow[lensData.vision as keyof typeof stepFlow][
            currentStepIndex + 2
            ];
          setStep(nextStep);
          break;
        } else {
          const nextStep =
            stepFlow[lensData.vision as keyof typeof stepFlow][
            currentStepIndex + 1
            ];
          setStep(nextStep);
          break;
        }
      }
      case "preference": {
        buyWithLens()
          .then((res) => {
            if (res.status === 200) {
              queryClient.invalidateQueries({ queryKey: ["cart"] });
              queryClient.invalidateQueries({ queryKey: ["cart", "counts"] });
              router.push("/cart");
              toast.success(res.data?.message);
            } else {
              toast.error(res.data?.message);
            }
          })
          .catch((e) => {
            toast.error(e.response.data.message);
          });
        break;
      }
      default: {
        const nextStep =
          stepFlow[lensData.vision as keyof typeof stepFlow][
          currentStepIndex + 1
          ];
        setStep(nextStep);
        break;
      }
    }
  };

  const handleBack = (currentStep: any) => {
    if (currentStep === "brands") {
      setStep("vision");
      return;
    }
    if (currentStep === "index") {
      if (lensData.lensType !== "Photocromic") {
        setStep("lensType");
        return;
      }
    }

    const currentStepIndex =
      stepFlow[lensData.vision as keyof typeof stepFlow].indexOf(currentStep);
    const nextStep =
      stepFlow[lensData.vision as keyof typeof stepFlow][currentStepIndex - 1];
    setStep(nextStep);
    // setSum(0);
    // setLensData({
    //   product: product?._id,
    //   quantity: count || 1,
    //   vision: "single",
    //   size: size,
    //   prescription: null,
    //   lensType: "Plastic or CR3",
    //   brand: lensData?.brand ? lensData?.brand : "",
    //   photocromic: "Gray",
    //   index: "",
    //   coating: "",
    //   userDetails: {
    //     name: "",
    //     phone: "",
    //     message: "",
    //   },
    //   sum: sum,
    // });
    // setSumList({
    //   lensType: 0,
    //   single: 0,
    //   progressive: 0,
    //   coating: 0,
    //   index: 0,
    // });
    // setActiveKey(null);
  };

  useEffect(() => {
    const total = Object.values(sumList).reduce((a, b) => a + b, 0);
    setSum(total);
  }, [sumList]);
  console.log(translation)
  const [checkOutConfirmationData, setCheckOutConfirmationData] = useState({
    title: translation?.enquirySuccess ?? "Our experts will contact you",
    description: " ",
    primaryBtnTxt: translation?.continue ?? "Continue",
    primaryBtnLink: "",
  });

  useEffect(() => { }, [count]);
  if (product?.customizable && varient?.stock === 0) {
    return null;
  }
  const brandIndex = brands?.findIndex((item: any) => item.value === lensData?.brand)
  const currentBrand = brandIndex !== -1 ? brands[brandIndex] : undefined

  return (
    <>
      <button onClick={() => setStep("type")} className="buy-with-lens">
        {translation?.chooseLens ?? "Choose Lenses"}
      </button>
      <Modal
        // className={`buy-with ${locale === "ar"? "popup-rtl": ""}`}
        className={`buy-with`}
        show={step === "type"}
        onHide={() => setStep("")}
        backdrop="static"
        keyboard={false}
        scrollable={true}
      >
        <div className="modal-wrap">
          <Modal.Header closeButton>
            <h2>
              <Image
                quality={100}
                priority
                src="/images/common/backarrow.svg"
                width={40}
                height={40}
                className="cursor-pointer"
                alt="back arrow cursor-pointer	"
                onClick={() => handleBack("type")}
              />
              {translation?.buyWithLensTitle ?? "Buy with Lens"}
            </h2>
          </Modal.Header>
          <Modal.Body>
            {/* <label htmlFor="radio1">
              <input
                id="radio1"
                type="radio"
                value={"single"}
                defaultChecked={lensData?.vision === "single"}
                onChange={(e) => handleLensData({ vision: e.target.value })}
                name="vision"
              />
              Single Vision
            </label>

            <label htmlFor="radio2">
              <input
                id="radio2"
                value={"progressive"}
                defaultChecked={lensData?.vision === "progressive"}
                onChange={(e) => handleLensData({ vision: e.target.value })}
                type="radio"
                name="vision"
              />
              Progressive
            </label> */}

            <Accordion
              className={`${activeKey === "single" ? "active" : ""}`}
              defaultActiveKey={lensData?.vision === "single" ? "0" : lensData?.vision === "progressive" ? "1" : null}
            >
              <Accordion.Item eventKey="0" className={`${activeKey === "single" ? "active" : ""}`}>
                <Accordion.Header onClick={() => {

                  if (brands.length === 1) {
                    handleLensData({
                      brand: brands[0]?.value,
                      vision: "single",
                    })
                    setActiveKey("single")
                  } else {
                    setLensData((prev) => ({ ...prev, vision: "single" }))
                  }
                }}>
                  <div className="dropDown_head">
                    <Image
                      quality={100}
                      priority
                      src="/images/modal/a2.svg"
                      width={82}
                      height={82}
                      alt="images"
                    />
                    <div>
                      <h5>{translation?.singleVisionTitle ?? "Single Vision"}</h5>
                      <p>{translation?.singleVisionDescription ?? "Single-vision lenses for distance or near vision"}</p>
                    </div>
                  </div>
                </Accordion.Header>
                <Accordion.Body>
                  <div className="dropDown_select">
                    <p>{translation?.doYouWantYourBrand ?? "Do you want your brand?"}</p>
                    <Select
                      isDisabled={brands.length === 1}
                      value={currentBrand}
                      options={brands}
                      onChange={(selectedOption: any) => {
                        handleLensData({
                          brand: selectedOption?.value,
                          vision: "single",
                        });
                        setActiveKey("single");
                      }}
                      placeholder="Select your brands"
                    />
                  </div>
                </Accordion.Body>
              </Accordion.Item>



              <Accordion.Item eventKey="1" className={`${activeKey === "progressive" ? "active" : ""}`}>
                <Accordion.Header onClick={() => {
                  if (brands.length === 1) {
                    handleLensData({
                      brand: brands[0]?.value,
                      vision: "progressive",
                    })
                    setActiveKey("progressive");
                  } else {
                    setLensData((prev) => ({ ...prev, vision: "progressive" }))
                  }
                }}>
                  <div className="dropDown_head">
                    <Image
                      quality={100}
                      priority
                      src="/images/modal/a3.svg"
                      width={82}
                      height={82}
                      alt="images"
                    />
                    <div>
                      <h5>{translation?.progressiveTitle ?? "Progressive"}</h5>
                      <p>
                        {translation?.progressiveDescription ?? "Seeing things close up, far away, and everything in between"}
                      </p>
                    </div>
                  </div>
                </Accordion.Header>
                <Accordion.Body>
                  <div className="dropDown_select">
                    <p>{translation?.doYouWantYourBrand ?? "Do you want your brand?"}</p>
                    <Select
                      options={brands}
                      isDisabled={brands.length === 1}
                      value={currentBrand}
                      onChange={(selectedOption: any) => {
                        handleLensData({
                          brand: selectedOption?.value,
                          vision: "progressive",
                        });
                        console.log(selectedOption?.value)
                        setActiveKey("progressive");
                      }}
                      placeholder="Select your brands"
                    />
                  </div>
                </Accordion.Body>
              </Accordion.Item>
            </Accordion>

            <div className="dropDown">
              <div
                className="dropDown_head cursor-pointer	"
                onClick={handleClick}
              >
                <Image
                  quality={100}
                  priority
                  src="/images/modal/a1.svg"
                  width={82}
                  height={82}
                  alt="images"
                />
                <div>
                  <h5>{translation?.frameOnlyTitle ?? "Frame Only"}</h5>
                  <p>{translation?.frameOnlyDescription ?? "Buy Only Frame"}</p>
                </div>
              </div>
            </div>

            <p className="cta" style={{direction: locale.includes("en")? "ltr": "rtl"}}>
              {translation?.buyWithLensText ?? "Not sure what to select? Call"}{" "}
              <a href={`tel:${footer?.contact?.mobileOne}`}>
              &#x200E;{footer?.contact?.mobileOne}
              </a>
            </p>
          </Modal.Body>
        </div>

        <Modal.Footer>
          <h5>
            {/* Subtotal:{currencyCode + " "} <span>{sum}</span> */}
            Subtotal:{currencyCode + " "} <span>{price?.offerPrice ? price.offerPrice : price.price}</span>
          </h5>
          <button
            onClick={() => handleNext("type")}
            className={activeKey === null ? "disabled" : ""}
            disabled={activeKey === null && true}
          >
            {translation?.next ?? "Next"}
          </button>
        </Modal.Footer>
      </Modal>
      {/* <SingleVisionPopup show={step === "singleVision"} handleClose={() => setStep("")} /> */}
      <Suspense fallback={<OverLayLoader />}>
        {step === "prescription" && (
          <SingleVisionDrop
            product={product}
            show={step === "prescription"}
            lensData={lensData}
            handleClose={handleClose}
            handleLensData={handleLensData}
            setStep={handleNext}
            back={handleBack}
            sum={price?.offerPrice ? price.offerPrice : price.price}
            setSum={handleSumList}
            handleClick={handleClick}
            handleSkipClick={handleSkipClick}
            successData={checkOutConfirmationData}
            handleSuccessData={setCheckOutConfirmationData}
            mobile={footer?.contact?.mobileOne}
          />
        )}

        {step === "brands" && (
          <BrandsPopup
            setStep={handleNext}
            show={step === "brands"}
            lensData={lensData}
            handleLensData={handleLensData}
            handleClose={() => setStep("")}
            back={handleBack}
          />
        )}

        {/* {step === "coating" && (
          <CoatingPopup
            setStep={handleNext}
            show={step === "coating"}
            handleClose={() => setStep("")}
            back={handleBack}
            handleLensData={handleLensData}
            sum={sum}
            lensData={lensData}
            setSum={handleSumList}
          />
        )} */}

        {step === "userDetails" && (
          <UserDetailsPopup
            center={false}
            setStep={handleNext}
            show={step === "userDetails"}
            lensData={lensData}
            handleLensData={handleLensData}
            handleClose={() => setStep("")}
            back={handleBack}
            successData={checkOutConfirmationData}
            handleSuccessData={setCheckOutConfirmationData}
            sum={price?.offerPrice ? price.offerPrice : price.price}
          />
        )}
        {step === "success" && (
          <SuccessPopup
            side={true}
            show={step === "success"}
            handleClose={handleClose}
            data={checkOutConfirmationData}
          />
        )}
        {step === "lensType" && (
          <LensTypePopup
            show={step === "lensType"}
            setStep={handleNext}
            lensData={lensData}
            handleLensData={handleLensData}
            handleClose={() => setStep("")}
            back={handleBack}
            sum={sum}
            setSum={handleSumList}
          />
        )}
        {step === "photocromatic" && (
          <PhotocromaticPopup
            show={step === "photocromatic"}
            setStep={handleNext}
            lensData={lensData}
            handleLensData={handleLensData}
            handleClose={() => setStep("")}
            back={handleBack}
          />
        )}
        {/* {step === "index" && (
          <IndexPopup
            show={step === "index"}
            setStep={handleNext}
            lensData={lensData}
            handleLensData={handleLensData}
            handleClose={() => setStep("")}
            back={handleBack}
            sum={sum}
            setSum={handleSumList}
          />
        )} */}
        {step === "preference" && (
          <SelectPreferenceModal
            show={step === "preference"}
            step={step}
            handleClose={handleClose}
            back={handleBack}
            setStep={handleNext}
            lensData={lensData}
            handleLensData={handleLensData}
            sum={sum}
            setSum={handleSumList}
          />
        )}
      </Suspense>
    </>
  );
}

export default BuywithLensPopup;
