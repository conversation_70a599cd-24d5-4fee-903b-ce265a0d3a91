.contact-form {
  margin-top: 3.5rem;

  @media (max-width: 575.98px) {
    margin-top: 2.5rem;
  }

  &_wrapper {
    margin-top: 2rem;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  &_inputs {
    width: 100%;
    position: relative;

    &:not(:last-child) {
      margin-bottom: 4rem;

      @media (max-width: 575.98px) {
        margin-bottom: 2rem;
      }
    }

    &:nth-of-type(2),
    &:nth-of-type(3) {
      width: 47%;

      @media (max-width: 575.98px) {
        width: 100%;
      }
    }

    &:nth-of-type(3) {
      input {
        padding-left: 11rem;
      }
    }

    &:nth-of-type(4) {
      &::after {
        content: "";
        background-image: url(../../../public/images/common/Icon.png);
        background-repeat: no-repeat;
        background-size: cover;
        background-position: right;
        width: 20px;
        height: 20px;
        position: absolute;
        right: 0;
        pointer-events: none;
      }
    }

    label {
      margin-bottom: 0.8rem;
      color: #808080;
      font-size: 1.3rem;
      font-weight: 400;
      position: relative;

      @media (max-width: 575.98px) {
        font-size: 1.4rem;
        font-size: 1.5rem;
      }

      &.email {
    
        
      }
    }

    input {
      width: 100%;
      border: none;
      border-bottom: 1px solid #e2e4e5;
      padding-left: 1.5rem;
      padding-bottom: 0.8rem;
      position: relative;

      &::placeholder {
        color: #dadada;
        font-size: 1.8rem;
        font-weight: 400;

        @media (max-width: 575.98px) {
          font-size: 1.5rem;
        }
      }

      &:focus-visible {
        border-bottom: 1px solid #e2e4e5;
        outline: none;
        box-shadow: none;
      }
    }

    input[type="number"]::-webkit-inner-spin-button,
    input[type="number"]::-webkit-outer-spin-button {
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
    }

    textarea {
      width: 100%;
      border: none;
      border-bottom: 1px solid #e2e4e5;
      padding-left: 1.5rem;
      padding-bottom: 0.8rem;
      resize: none;

      @media (max-width: 575.98px) {
        font-size: 1.5rem;
      }

      &:focus-visible {
        border-bottom: 1px solid #e2e4e5;
        outline: none;
        box-shadow: none;
      }

      &::placeholder {
        color: #e2e2e2;
        font-size: 1.8rem;
        font-weight: 400;

        @media (max-width: 575.98px) {
          font-size: 1.5rem;
        }
      }
    }

    .countrycode {
      display: flex;
      column-gap: 1rem;
      align-items: baseline;

      .countrycode-icon {
        position: absolute;
        width: 100%;
      }

      #countryCode {
        padding-left: 0;
        width: 80px;
        background-image: url("../../../public/images/common/Icon.png");
        background-position: right 23%;
        background-repeat: no-repeat;

      }

      select {
        border: none;
        border-bottom: 1px solid #e2e4e5;
        padding: 0 1.6rem;
        padding-bottom: 0.8rem;
        width: 8.6rem;

        &:focus-visible {
          outline: none;
          border-bottom: 1px solid #e2e4e5;
        }
      }
    }

    select {
      width: 100%;
      border: none;
      border-bottom: 1px solid #e2e4e5;
      padding-left: 1.5rem;
      padding-bottom: 0.8rem;
      appearance: none;
      color: #000000;
      background-color: transparent;

      &::placeholder {
        color: #d1d1d1;
        font-size: 1.8rem;
        font-weight: 400;
      }

      &:focus-visible {
        border-bottom: 1px solid #e2e4e5;
        outline: none;
        box-shadow: none;
      }
    }

    .select-container {
      .react-select {
        width: 100%;
        max-width: 80px;
        top: -4px;
        left: 0;
        z-index: 10;
        border: none;
        border-bottom: 1px solid #e2e4e5;
        box-shadow: none;
        opacity: 0;

        &::after {
          content: "";
          background-image: url("../../../public/images/common/Icon.png");
          width: 2.4rem;
          height: 2.4rem;
          position: absolute;
          right: -8px;
        }

        .css-1fdsijx-ValueContainer {
          &:focus-visible {
            border: none;
            outline: none;
          }
        }

        .css-qbdosj-Input {
          padding: 0;
          margin: 0;
        }

        .css-1fdsijx-ValueContainer {
          padding: 0;
          margin: 0;
        }
      }

      .drop-item img {
        width: 2rem;
      }

      .css-1hb7zxy-IndicatorsContainer {
        display: none;
      }

    }

    .select-box {
      display: flex;
      column-gap: 1.6rem;

      select {
        border: none;
        border-bottom: 1px solid #e2e4e5;
        padding: 0 1.5rem;
        padding-bottom: 0.8rem;
        width: 6.5rem;
        line-height: 2.8rem;

        &:focus-visible {
          outline: none;
          border-bottom: 1px solid #e2e4e5;
        }
      }
    }
  }

  &_btn {
    text-align: center;
    width: 100%;

    .primary-btn {
      background-color: #000000;
      color: #fff;
      border: 1px solid #000;
      padding: 1.5rem 3.9rem;

      @media (max-width: 575.98px) {
        margin-top: 4rem;
        padding: 1.1rem 3.9rem;
      }

      &:hover {
        background-color: #ffffff;
        color: #000;
      }
    }
  }
}