"use client";

import "./my-address-book.scss";
import Link from "next/link";
import { BookAddress } from "@/lib/book-address";
import MyAddressPopUp from "@/components/my-address-popup/MyAddressPopUp";
import { useContext, useState } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { getAddress } from "@/lib/methods/user";
import EmptyState from "@/components/empty-states/EmptyState";
import LogoAnimation from "@/components/LogoAnimation/LogoAnimation";
import { endpoints } from "@/config/apiEndpoints";
import api from "@/config/axios.interceptor";
import { toast } from "sonner";
import GenericBackButton from "@/components/GenericBackButton/GenericBackButton";
import { AuthProvider } from "@/contexts/AuthProvider";
import { TranslationContext } from "@/contexts/Translation";
import { useLocaleContext } from "@/contexts/LocaleProvider";

function MyAddressBook() {
  const [show, setShow] = useState(false);
  const { currentLocale } = useLocaleContext()
  const [editAddress, setEditAddress] = useState<any>(null);
  const { translation }: any = useContext(TranslationContext)
  const handleClose = () => {
    setEditAddress(null);
    setShow(false);
  };
  const handleShow = () => setShow(true);
  const { data: address, isLoading } = useQuery({
    queryKey: ["address", currentLocale?.split("-")[0]],
    queryFn: getAddress,
  });
  const queryClient = useQueryClient();
  const handleEdit = (data: any) => {
    setEditAddress(data);
    handleShow();
  };

  const setDefault = (address: any) => {
    api
      .put(`${endpoints.updateAddress}/${address?.refid}`, { ...address, isDefault: true })
      .then((res) => {
        if (res.data.errorCode === 0) {
          toast.success("Address set as default");
          queryClient.invalidateQueries({ queryKey: ["address"] });
        }
      });
  };
  const deleteAddress = (id: string) => {
    api.put(`${endpoints.deleteAddress}/${id}`).then((res) => {
      if (res.data.errorCode === 0) {
        toast.success("Address deleted");
        queryClient.invalidateQueries({ queryKey: ["address"] });
      }
    });
  };

  return (
    <section className="address-book position-relative">
      <GenericBackButton style={{ top: "5px" }} />
      <h2>{translation?.myAccount?.myAddressBook ?? "My Address Book"}</h2>
      <div className="address-book_wrapper">
        {address &&
          !isLoading &&
          address.length > 0 &&
          address
            ?.sort((a: any, b: any) => b.isDefault - a.isDefault)
            .map((items: any) => (
              <div
                className={`address-book_cards ${items?.isDefault ? "isActive" : ""}`}
                key={items._id}>
                <div style={{ display: "flex", gap: "1rem", alignItems: "center" }}>
                  <h5>{items.name}</h5>
                  <div style={{padding: "0.3rem 1rem", backgroundColor: "#0000001A", borderRadius: "0.4rem"}}>
                    {
                      items?.type === "Home" ? translation?.myAccount?.addressHome
                        : items?.type === "Work" ? translation?.myAccount?.addressWork
                          : translation?.myAccount?.addressOther
                    }
                  </div>
                </div>
                <ul>
                  <li>
                    <Link href="">{items.number}</Link>
                  </li>
                  <li>
                    {items.suiteUnit && <span>{items.suiteUnit},</span>}
                    {items.street}, {items.city},{items.emirates},{items.country} -
                    {items.postalCode}
                  </li>
                  <li>
                    {items?.countryCode}-{items?.mobile}
                  </li>

                  <li className="cta">
                    <button
                      className={items?.isDefault ? "activeBtn" : ""}
                      disabled={items?.isDefault}
                      onClick={() => setDefault(items)}>
                      {items?.isDefault
                        ? (translation?.myAccount?.addressDefault ?? "Default billing address")
                        : (translation?.myAccount?.setDefaultAddress ?? "Set as default billing address")}
                    </button>
                  </li>

                  <li className="btns">
                    <button onClick={() => deleteAddress(items?.refid)}>{translation?.myAccount?.addressRemove ?? "Remove"}</button>
                    <div className="bar"></div>
                    <button onClick={() => handleEdit(items)}>{translation?.myAccount?.addressEdit ?? "Edit"}</button>
                  </li>
                </ul>
              </div>
            ))}
      </div>
      {!isLoading && address?.length === 0 && (
        <EmptyState icon="address" title="No Address Found!" description="Please Add Address" />
      )}
      {isLoading && <LogoAnimation className="d-flex justify-content-center py-5" />}

      <div className="address-book_btn">
        <button onClick={handleShow}>{translation?.myAccount?.addressAdd ?? "Add New Address"}</button>
      </div>
      {show && <AuthProvider> <MyAddressPopUp show={show} address={editAddress} handleClose={handleClose} /> </AuthProvider>}
    </section>
  );
}

export default MyAddressBook;
