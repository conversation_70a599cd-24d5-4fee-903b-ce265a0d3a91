"use clinet";

import './withwithout.scss'

import {
  ReactCompareSlider,
  ReactCompareSliderImage,
} from "react-compare-slider";

function WithoutBlur() {
  return (
    <ReactCompareSlider
      itemOne={
        <ReactCompareSliderImage  srcSet="https://images.unsplash.com/photo-1682687220063-4742bd7fd538?auto=format&fit=crop&q=80&w=1375&ixlib=rb-4.0.3&ixid=M3wxMjA3fDF8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Image one" />
      }
      itemTwo={
        <ReactCompareSliderImage  srcSet="https://images.unsplash.com/photo-1682685794761-c8e7b2347702?auto=format&fit=crop&q=80&w=1470&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Image two" />
      }
    />
  );
}

export default WithoutBlur;
