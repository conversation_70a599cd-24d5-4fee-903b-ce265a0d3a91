.terms-pop-up {
  &.modal {
    .modal-dialog {
      max-width: 123.9rem;
      width: 100%;
      margin: 0 auto;
    }

    .modal-header {
      padding: 0;
      border: none;
      position: relative;

      .btn-close {
        position: absolute;
        right: 0;
        top: 0;
        background-image: url(../../../../public/images/common/close.png);
        width: 4.8rem;
        height: 4.8rem;
        background-position: center;
        background-repeat: no-repeat;
        background-size: contain;
        opacity: 1;
        padding: 0;

        @media (max-width: 575.98px) {
          width: 2.8rem;
          height: 2.8rem;
          transform: translateY(-36px);
        }

        &:focus {
          box-shadow: none;
          outline: none;
          border: none;
        }
      }

      h2 {
        line-height: 4rem;

        @media (max-width: 575.98px) {
          line-height: 3rem;
          text-align: center;
          width: 100%;
        }
      }
    }

    .modal-content {
      border: none;
      border-radius: 3rem;
      padding: 3.6rem 4.6rem 5.2rem 5.3rem;
      max-width: 123.9rem;

      @media (max-width: 575.98px) {
        padding: 8.3rem 1.7rem 4.2rem 1.7rem;
        border-radius: 0;
      }
    }

    .modal-body {
      padding-top: 2rem;
      display: flex;
      flex-direction: column;
      gap: 1rem;

      .btns {
        width: 100%;
        display: flex;
        column-gap: 3.1rem;
        justify-content: center;

        @media (max-width: 575.98px) {
          margin-top: 5.6rem;
          column-gap: 1.6rem;
        }

        .button {
          color: #fff;
          text-align: center;
          font-size: 1.5rem;
          font-weight: 500;
          background-color: #000;
          border-radius: 6rem;
          width: 13.8rem;
          height: 5.6rem;
          border: 1px solid #000;
          transition: .3s all ease;

          @media (max-width: 575.98px) {
            height: 4.5rem;
            width: 15.9rem;
          }

          &:hover {
            background-color: transparent;
            color: #000;
            transition: .3s all ease;
          }

          &.cancel {
            color: #000;
            background-color: transparent;
            transition: .3s all ease;

            &:hover {
              background-color: #000;
              color: #fff;
              transition: .3s all ease;
            }
          }
        }
      }
    }
  }

}