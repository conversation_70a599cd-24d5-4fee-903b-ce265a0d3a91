"use client";

import React, { useRef, useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination } from "swiper/modules";
import Image from "next/image";
import dynamic from "next/dynamic";
import VirtualSliderPopUp from "@/components/virtual-slider-popup/VirtualSliderPopUp";
const VirtualTryPopUp = dynamic(
  () => import("../virtual-try-popup/VirtualTryPopUp"),
  {
    loading: () => (
      <div className="spinner-border text-light mt-4" role="status"></div>
    ),
  }
);

function GallerySlider({ data, isVirtualTry, upc, vmPolicy }: any) {

  const [show, setShow] = useState(false)
  const handleClose = () => setShow(false)
  const handleShow = () => setShow(true)

  return (
    <div className="gallery_slider d-sm-none d-block">
      <Swiper
        className="mySwiper"
        spaceBetween={30}
        pagination={{
          clickable: true,
        }}
        modules={[Pagination]}
      >
        {data?.map((item: string, index: number) => {
          return (
            <SwiperSlide key={index}>
              <Image
                quality={100}
                priority
                src={item || "/images/product/noImage.jpg"}
                width={347}
                height={363}
                alt="gallery image"
                onClick={handleShow}
              />

              {isVirtualTry && <VirtualTryPopUp vmPolicy={vmPolicy} upc={upc} />}
            </SwiperSlide>
          );
        })}
      </Swiper>
      {show && (
          <VirtualSliderPopUp
            data={data}
            show={show}
            handleClose={handleClose}
          />
        )}
    </div>
  );
}

export default GallerySlider;
