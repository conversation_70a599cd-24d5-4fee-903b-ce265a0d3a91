import { unsubscribe } from "diagnostics_channel";

export const endpoints = {
  tabbyCheck: "tabby-check",
  about: "about-us",
  settings: "general-settings",
  availableStores: "available-stores",
  address: "address",
  cashbacks: "my-cashbacks",
  addAddress: "add-address",
  addToCart: "add-to-cart",
  addToCompare: "add-to-compare",
  addToTryCart: "add-try-cart",
  addReview: "add-review",
  applyCoupon: "apply-coupon",
  beforeAfter: "before-after",
  blogs: "blogs",
  blogDetail: "blog-detail",
  brands: "brands",
  buyWithLens: "buy-with-lens",
  cart: "cart",
  cartUpdate: "cart-update",
  categories: "categories",
  cancelOrder: "cancel-order",
  coating: "coating",
  collections: "collections",
  compare: "compare",
  contactBanner: "contact-banner",
  contactForm: "contact-form",
  contactLensPage: "contact-lens-page",
  contactLensCart: "contact-lens-cart",
  contactLensFaq: "faq/contactLens",
  contactLensPower: "contact-lens-power",
  counts: "counts",
  coupons: "coupons",
  deletePrescription: "delete-prescription",
  deleteAddress: "delete-address",
  faq: "faq",
  filters: "filters",
  footer: "footer",
  frameShapes: "frame-shapes",
  frameTypes: "frame-types",
  getContactBanner: "get-contact-banner",
  header: "header",
  multiStores: "multi-stores",
  getLocales: "get-locales",
  homeOrder: "home-order",
  imageMap: "imageMap",
  insuranceForm: "insurance-form",
  insuranceProviders: "insurance-providers",
  insuranceFaq: "faq/insurance",
  insuranceContent: "insurance-content",
  lensBrands: "lens-brands",
  lensPowers: "lens-powers",
  lensTypes: "lens-types",
  lensIndex: "lens-index",
  lensCoating: "coating",
  login: "login",
  manageWishlist: "manage-wishlist",
  newsLetterSubscription: "newsletter",
  orders: "orders",
  orderDetail: "order-detail",
  placeOrder: "place-order",
  prescriptions: "prescriptions",
  products: "products",
  productDetail: "product-detail",
  productBanner: "product-banner",
  productEnquiry: "product-enquiry",
  profile: "profile",
  register: "register",
  removeCoupon: "remove-coupon",
  removeFromCart: "remove-from-cart",
  removeTryCart: "remove-try-cart",
  removeFromCompare: "remove-from-compare",
  reviews: "reviews",
  search: "search",
  searchSuggestions: "suggestions",
  stores: "stores",
  subscribe: "subscribe",
  subscribeSummary: "subscribe-summary",
  subscriptionList: "my-subscriptions",
  tryCart: "try-cart",
  tryCartNotInterested: "empty-trycart",
  tryCartOrder: "try-cart-order",
  updateAddress: "update-address",
  updateProfile: "update-profile",
  uploadPrescription: "upload-prescription",
  verifyOtp: "verify-otp",
  getOtp: "login",
  verifyPayment: "verify-payment",
  variants: "variants",
  wishlist: "wishlist",
  shippingCharge: "shipping-charge",
  paymentCharge: "get-payment-method-fees",
  unsubscribe: "unsubscribe",
  getLoyality: "loyalty-points",
  radeemLoyality: "redeem-loyalty",
  removeLoyality: "remove-loyalty",
  getVmPolicy: "vm-policy",
  translation: "translation",
  nextFilterCount: "next-filter-count",
};
