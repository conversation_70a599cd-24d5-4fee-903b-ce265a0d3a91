import { getOrderStatus } from "@/lib/order";
import "./order-status.scss";
import { useContext } from "react";
import { TranslationContext } from "@/contexts/Translation";

function OrderStatus({ history }: any) {
  const { translation: {orderPage} }: any = useContext(TranslationContext)
  const checkIsActive = (status: string) => {
    const lastItem = history.at(-1);
    return history.includes(status)
      ? `isActive ${lastItem === status ? "last-item" : ""}`
      : "";
  };
  return (
    <>
      <div className="status d-print-none">
        <ul className="status_bar">
          {/* {getOrderStatus(history).map((item, index) => (
            <li className={"isActive"} key={index}>
              <span className="status_icon"></span>
              <span className="status_text">{item.text}</span>
            </li>
          ))} */}
          <li className={checkIsActive("PLACED")}>
            <span className="status_icon"></span>
            <span className="status_text">{orderPage?.orderPlaced ?? "Order Placed"}</span>
          </li>
          <li className={checkIsActive("CONFIRMED")}>
            <span className="status_icon"></span>
            <span className="status_text">{orderPage?.confirm ?? "Confirmed"}</span>
          </li>
          <li className={`${checkIsActive("SHIPPED VIA ECO")} ${checkIsActive("SHIPPED VIA INHOUSE") }`}>
            <span className="status_icon"></span>
            <span className="status_text">{orderPage?.shipped ?? "Shipped"}</span>
          </li>
          {/* <li className={checkIsActive("OUT FOR DELIVERY")}>
            <span className="status_icon"></span>
            <span className="status_text"> Out for Delivery</span>
          </li> */}

          <li className={checkIsActive("DELIVERED")}>
            <span className="status_icon"></span>
            <span className="status_text">{orderPage?.delivered ?? "Delivered"}</span>
          </li>

          {history.includes("CANCELLED") && (
            <li className={checkIsActive("CANCELLED")}>
              <span className="status_icon"></span>
              <span className="status_text">{orderPage?.cancelled ?? "Cancelled"}</span>
            </li>
          )}
          {/* {history.includes("RETURNED") && (
            <li className={checkIsActive("RETURNED")}>
              <span className="status_icon"></span>
              <span className="status_text">Returned</span>
            </li>
          )}
          {history.includes("REFUNDED") && (
            <li className={checkIsActive("REFUNDED")}>
              <span className="status_icon"></span>
              <span className="status_text">Refunded</span>
            </li>
          )}
          {history.includes("RETURNED") && (
            <li className={checkIsActive("FAILED")}>
              <span className="status_icon"></span>
              <span className="status_text">Failed</span>
            </li>
          )} */}
        </ul>
      </div>
    </>
  );
}

export default OrderStatus;
