// components/Map.js
"use client"

import React, { useEffect, useRef, useState } from 'react';
import { Autocomplete, GoogleMap, InfoWindow, LoadScript, Marker, OverlayView } from '@react-google-maps/api';
import { Button, Dropdown } from 'react-bootstrap';
import Link from 'next/link';
import Select from "react-select";
import { MdMyLocation } from "react-icons/md";
import { FaStoreAlt } from "react-icons/fa";
import { IoMdCloseCircle } from "react-icons/io";
import { motion, AnimatePresence } from "framer-motion";
import Image from 'next/image';
import { IoFilterCircleSharp } from "react-icons/io5";
import { IoIosCloseCircle } from "react-icons/io";


const mapContainerStyle = {
    width: '100%',
    height: '100%',
};

const center = {
    lat: 0,
    lng: 0,
};

const libraries = ["places"]

const countries: any = [
    { value: 'UAE', label: 'UAE', code: "AE" },
    { value: 'Qatar', label: 'Qatar', code: "QA" },
    { value: 'Oman', label: 'Oman', code: "OM" },
    { value: 'Bahrain', label: 'Bahrain', code: "BH" },
    { value: 'Saudi Arabia', label: 'Saudi Arabia', code: "SA" },
]

// const services: any = [
//     { value: 'Abu Dhabi Driving License Services', label: 'Abu Dhabi Driving License Services' },
//     { value: 'Eye Test', label: 'Eye Test' },
//     { value: 'RTA Driving License Services', label: 'RTA Driving License Services' },
//     { value: 'Sharjah Driving License Services', label: 'Sharjah Driving License Services' },
// ]

// const facilities: any = [
//     { value: 'Disabled access', label: 'Disabled access' },
//     { value: 'Elevator', label: 'Elevator' },
//     { value: 'Free Parking', label: 'Free Parking' },
//     { value: 'Free WiFi', label: 'Free WiFi' },
// ]

// const storeTypes: any = [
//     { value: 'Occhiali Optics', label: 'Occhiali Optics' },
//     { value: 'Sun Eye Optics', label: 'Sun Eye Optics' },
//     { value: 'Yateem Optician', label: 'Yateem Optician' },
//     { value: 'Yateem Optics', label: 'Yateem Optics' },
// ]

// utils/calculateDistance.js
export const calculateDistance = (lat1: any, lon1: any, lat2: any, lon2: any) => {
    const R = 6371; // Earth's radius in kilometers
    const dLat = (lat2 - lat1) * (Math.PI / 180);
    const dLon = (lon2 - lon1) * (Math.PI / 180);
    const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos(lat1 * (Math.PI / 180)) *
        Math.cos(lat2 * (Math.PI / 180)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c; // Distance in kilometers
    return distance;
};


const Map = ({ stores }: any) => {
    console.log(stores)
    const [currentLocation, setCurrentLocation] = useState<any>(null);
    const [radius, setRadius] = useState(5);
    const [load, setLoad] = useState(false)
    const [showInfoWindow, setShowInfoWindow] = useState(false);
    const [nearbyStores, setNearbyStores] = useState([]);
    const [mapCenter, setMapCenter] = useState(center);
    const [zoom, setZoom] = useState(10);
    const [autocomplete, setAutocomplete] = useState<google.maps.places.Autocomplete | null>(null);
    const [showFilters, setShowFilters] = useState(false);

    const [services, setServices] = useState<any>([]);
    const [facilities, setFacilities] = useState<any>([]);
    const [storeTypes, setStoreTypes] = useState<any>([]);
    const [brands, setBrands] = useState<any>([]);
    const [insurance, setInsurance] = useState<any>([]);
    const [specialities, setSpecialities] = useState<any>([]);

    const [filters, setFilters] = useState<any>({});
    const inputRef = useRef<HTMLInputElement>(null);
    const pRef = useRef<HTMLInputElement>(null);

    const setStoresByDistance = (lat: any, long: any, radius: any) => {
        const filteredStores = stores.filter((store: any) => {
            const distance = calculateDistance(
                lat,
                long,
                store?.coordinates?.lat,
                store?.coordinates?.long
            );
            return distance <= radius; // Store is within 5 km
        });
        if (filteredStores.length == 0 && pRef.current) pRef.current.innerHTML = `No stores found within ${radius} km of your location.`
        setNearbyStores(filteredStores);
    }

    function getCurrentLocation() {
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    setCurrentLocation({
                        lat: position.coords.latitude,
                        lng: position.coords.longitude,
                    });
                    setMapCenter({
                        lat: position.coords.latitude,
                        lng: position.coords.longitude,
                    });
                    setZoom(10)
                    window.location.replace("#map")
                    setStoresByDistance(position.coords.latitude, position.coords.longitude, radius)
                },
                () => {
                    alert('Location permission denied');
                }
            );
        }
    };

    useEffect(() => {
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    const userLat = position.coords.latitude;
                    const userLng = position.coords.longitude;
                    setCurrentLocation({
                        lat: position.coords.latitude,
                        lng: position.coords.longitude,
                    });
                    setMapCenter({
                        lat: position.coords.latitude,
                        lng: position.coords.longitude,
                    });
                    const filteredStores = stores.filter((store: any) => {
                        const distance = calculateDistance(
                            userLat,
                            userLng,
                            store?.coordinates?.lat,
                            store?.coordinates?.long
                        );
                        return distance <= radius; // Store is within 5 km
                    });

                    setNearbyStores(filteredStores);
                },
                () => {
                    alert('Location permission denied');
                }
            );
        }
    }, [radius]);

    useEffect(() => {
        let filteredStores = stores.filter((store: any) => {
            if (filters?.store) {
                if (store?.store == filters?.store) return true;
                return false;
            } else return true;
        })
        filteredStores = filteredStores.filter((store: any) => {
            if (filters?.country?.length > 0) {
                const value = countries?.find((item: any) => item.code == store?.storeId?.toUpperCase())?.value;
                if (filters?.country?.includes(value)) return true;
                return false;
            } else return true;
        })
        filteredStores = filteredStores.filter((store: any) => {
            if (filters?.services?.length > 0) {
                let flag = false;
                filters?.services?.forEach((service: any) => {
                    if (store?.services?.includes(service)) flag = true;
                })
                if (flag) return true;
                return false;
            } else return true;
        })
        filteredStores = filteredStores.filter((store: any) => {
            if (filters?.facilities?.length > 0) {
                let flag = false;
                filters?.facilities?.forEach((facility: any) => {
                    if (store?.facilities?.includes(facility)) flag = true;
                })
                if (flag) return true;
                return false;
            } else return true;
        })
        filteredStores = filteredStores.filter((store: any) => {
            if (filters?.brands?.length > 0) {
                let flag = false;
                filters?.brands?.forEach((brand: any) => {
                    if (store?.brands?.includes(brand)) flag = true;
                })
                if (flag) return true;
                return false;
            } else return true;
        })
        filteredStores = filteredStores.filter((store: any) => {
            if (filters?.insurance?.length > 0) {
                let flag = false;
                filters?.insurance?.forEach((insurance: any) => {
                    if (store?.insurance?.includes(insurance)) flag = true;
                })
                if (flag) return true;
                return false;
            } else return true;
        })
        filteredStores = filteredStores.filter((store: any) => {
            if (filters?.specialities?.length > 0) {
                let flag = false;
                filters?.specialities?.forEach((speciality: any) => {
                    if (store?.specialities?.includes(speciality)) flag = true;
                })
                if (flag) return true;
                return false;
            } else return true;
        })
        setNearbyStores(filteredStores);
        const newServices: any = [], newFacilities: any = [], newStore: any = [], newSpecialities: any = [], newBrands: any = [], newInsurance: any = [];
        for (let item of filteredStores) {
            if (item?.services?.length > 0) for (let service of item?.services) {
                if (service && !newServices.includes(service)) newServices.push(service);
            }
            if (item?.brands?.length > 0) for (let brand of item?.brands) {
                if (brand && !newBrands.includes(brand)) newBrands.push(brand);
            }
            if (item?.insurance?.length > 0) for (let insurance of item?.insurance) {
                if (insurance && !newInsurance.includes(insurance)) newInsurance.push(insurance);
            }
            if (item?.facilities?.length > 0) for (let facility of item?.facilities) {
                if (facility && !newFacilities.includes(facility)) newFacilities.push(facility);
            }
            if (item?.specialties?.length > 0) for (let speciality of item?.specialties) {
                if (speciality && !newSpecialities.includes(speciality)) newSpecialities.push(speciality);
            }
            if (item?.store && !newStore.includes(item?.store)) newStore.push(item?.store);
        }
        setServices(newServices?.map((item: any) => ({ value: item, label: item })));
        setFacilities(newFacilities?.map((item: any) => ({ value: item, label: item })));
        setStoreTypes(newStore?.map((item: any) => ({ value: item, label: item })));
        setBrands(newBrands?.map((item: any) => ({ value: item, label: item })));
        setInsurance(newInsurance?.map((item: any) => ({ value: item, label: item })));
        setSpecialities(newSpecialities?.map((item: any) => ({ value: item, label: item })));

    }, [filters])

    // useEffect(() => {
    //     if (load) {
    //         //@ts-ignore
    //         const placeAutocomplete = new google.maps.places.PlaceAutocompleteElement();
    //         console.log(placeAutocomplete)
    //         console.log(placeAutocomplete.shadowRoot)
    //         document.getElementById('places-autocomplete')?.appendChild(placeAutocomplete)
    //     }
    // }, [load])

    const onLoad = (autoC: google.maps.places.Autocomplete) => setAutocomplete(autoC);
    const onPlaceChanged = () => {
        if (autocomplete) {
            const place = autocomplete.getPlace();
            const location = place.geometry?.location;
            if (place?.types?.[0]) {
                if (place?.types?.[0] == "country") {
                    setZoom(4)
                    const country = place?.address_components?.find((component: any) => component.long_name == place?.name);
                    const filter = countries.find((item: any) => item.code == country?.short_name);
                    if (filter) {
                        setFilters({ ...filters, country: [filter.value] })
                    } else {
                        // setFilters({...filters, country: [] })
                        setStoresByDistance(location?.lat(), location?.lng(), 2000)
                    }
                    // setRadius(2000)
                }
                if (place?.types?.[0] == "administrative_area_level_1") {
                    setZoom(7)
                    const country = place?.address_components?.[place?.address_components?.length - 1];
                    const filter = countries.find((item: any) => item.code == country?.short_name);
                    if (filter) {
                        setFilters({ ...filters, country: [filter.value] })
                    } else {
                        // setFilters({...filters, country: [] })
                        setStoresByDistance(location?.lat(), location?.lng(), 2000)
                    }
                    // setStoresByDistance(location?.lat(), location?.lng(), 300)
                    // setRadius(300)
                }
                if (place?.types?.[0] == "locality") {
                    setZoom(10)
                    const country = place?.address_components?.[place?.address_components?.length - 1];
                    const filter = countries.find((item: any) => item.code == country?.short_name);
                    if (filter) {
                        setFilters({ ...filters, country: [filter.value] })
                    } else {
                        // setFilters({...filters, country: [] })
                        setStoresByDistance(location?.lat(), location?.lng(), 500)
                    }
                    // setStoresByDistance(location?.lat(), location?.lng(), 50)
                    // setRadius(50)
                }
                console.log(place)
            }
            if (location) {
                setMapCenter({
                    lat: location.lat(),
                    lng: location.lng()
                });
                setZoom(6.5)
                window.location.replace("#map")
            }
        }
    };

    const viewAllStores = () => {
        setFilters({});
        setNearbyStores(stores);
        setMapCenter({
            lat: 25.276987,
            lng: 55.296249
        })
        setZoom(5)
        window.location.replace("#map")
    };

    // const onLoad = () => {
    //     setLoad(true)
    // }

    const onClickCountry = (country: any) => {
        if (filters?.country?.includes(country)) {
            setFilters({ ...filters, country: filters?.country?.filter((item: any) => item !== country) ?? [] })
        } else {
            setFilters({ ...filters, country: [...filters?.country ?? [], country] })
        }
    }

    const onClickService = (service: any) => {
        if (filters?.services?.includes(service)) {
            setFilters({ ...filters, services: filters?.services?.filter((item: any) => item !== service) ?? [] })
        } else {
            setFilters({ ...filters, services: [...filters?.services ?? [], service] })
        }
    }

    const onClickFacility = (facility: any) => {
        if (filters?.facilities?.includes(facility)) {
            setFilters({ ...filters, facilities: filters?.facilities?.filter((item: any) => item !== facility) ?? [] })
        } else {
            setFilters({ ...filters, facilities: [...filters?.facilities ?? [], facility] })
        }
    }

    const onClickBrands = (brand: any) => {
        if (filters?.brands?.includes(brand)) {
            setFilters({ ...filters, brands: filters?.brands?.filter((item: any) => item !== brand) ?? [] })
        } else {
            setFilters({ ...filters, brands: [...filters?.brands ?? [], brand] })
        }
    }

    const onClickInsurance = (value: any) => {
        if (filters?.insurance?.includes(value)) {
            setFilters({ ...filters, insurance: filters?.insurance?.filter((item: any) => item !== value) ?? [] })
        } else {
            setFilters({ ...filters, insurance: [...filters?.insurance ?? [], value] })
        }
    }

    const onClickSpecialities = (value: any) => {
        if (filters?.specialities?.includes(value)) {
            setFilters({ ...filters, specialities: filters?.specialities?.filter((item: any) => item !== value) ?? [] })
        } else {
            setFilters({ ...filters, specialities: [...filters?.specialities ?? [], value] })
        }
    }

    console.log(filters)

    const onClickStores = (store: any) => {
        if (filters?.store == store) {
            setFilters({ ...filters, store: "" })
        } else {
            setFilters({ ...filters, store })
        }
    }

    const clearFilter = () => {
        setFilters({});
        setRadius(5);
        if (inputRef?.current) {
            inputRef.current.value = ""
        }
    }

    const removeFilter = (name: any, value: any) => {
        setFilters({ ...filters, [name]: filters?.[name]?.filter((item: any) => item !== value) ?? [] })
    }

    return (
        <LoadScript libraries={libraries as any} googleMapsApiKey="AIzaSyCfWpBm49CpF4tLdBMQ9OqZAfZ-Zoyf65o">
            <div className="map-page">
                <h1 style={{ marginTop: "2rem", textAlign: "start", fontWeight: "bold" }}>Find Our Stores</h1>
                <div className='search' style={{ display: "flex", justifyContent: "center", alignItems: "center" }}>
                    <div className='search-btns'>
                        <Autocomplete className='places-autocomplete' onLoad={onLoad} onPlaceChanged={onPlaceChanged}>
                            <>
                                <label>City Country or Location</label>
                                <input
                                    type="text"
                                    placeholder="Search for a location"
                                    ref={inputRef}
                                />
                            </>
                        </Autocomplete>
                        <div style={{ display: "flex", gap: "1rem", width: "100%" }} className="location-btns">
                            <button onClick={getCurrentLocation}> <MdMyLocation /> Use Current Location</button>
                            <Dropdown>
                                <Dropdown.Toggle variant="success" id="dropdown-basic">
                                    Radius: {radius} km
                                </Dropdown.Toggle>

                                <Dropdown.Menu>
                                    <Dropdown.Item onClick={() => setRadius(5)}>5 km</Dropdown.Item>
                                    <Dropdown.Item onClick={() => setRadius(10)}>10 km</Dropdown.Item>
                                    <Dropdown.Item onClick={() => setRadius(15)}>15 km</Dropdown.Item>
                                </Dropdown.Menu>
                            </Dropdown>
                            <button style={{ borderRadius: ".5rem", display: "flex", alignItems: "center", gap: ".5rem", justifyContent: "center", background: "black", color: "white" }} onClick={viewAllStores}><FaStoreAlt /> View all stores</button>
                        </div>
                        <div style={{ display: "flex", gap: "1rem", width: "100%", gridColumn: "1 / 7", alignItems: "center" }}>
                            <AnimatePresence>
                                {showFilters && (
                                    <motion.div
                                        initial={{ opacity: 0 }}
                                        animate={{ opacity: 1 }}
                                        exit={{ opacity: 0 }}
                                        style={{ display: "flex", gap: "1rem", width: "100%" }}
                                        transition={{ duration: 0.5 }}
                                    >
                                        {/* <> */}
                                        <div className="filters" style={{ display: "grid", gap: "1rem", gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))" }}>
                                            <Dropdown>
                                                <Dropdown.Toggle variant="success" id="dropdown-basic">
                                                    Country
                                                </Dropdown.Toggle>

                                                <Dropdown.Menu >
                                                    {countries?.map((country: any, index: number) => (
                                                        <Dropdown.Item key={index} active={filters?.country?.includes(country.value)} onClick={() => onClickCountry(country.value)}>{country.label}</Dropdown.Item>
                                                    ))}
                                                </Dropdown.Menu>
                                            </Dropdown>
                                            <Dropdown>
                                                <Dropdown.Toggle variant="success" id="dropdown-basic">
                                                    Services
                                                </Dropdown.Toggle>

                                                <Dropdown.Menu >
                                                    {services?.map((service: any, index: number) => (
                                                        <Dropdown.Item key={index} active={filters?.services?.includes(service.value)} onClick={() => onClickService(service.value)}>{service.label}</Dropdown.Item>
                                                    ))}
                                                </Dropdown.Menu>
                                            </Dropdown>
                                            <Dropdown>
                                                <Dropdown.Toggle variant="success" id="dropdown-basic">
                                                    Facilities
                                                </Dropdown.Toggle>

                                                <Dropdown.Menu >
                                                    {facilities?.map((facility: any, index: number) => (
                                                        <Dropdown.Item key={index} active={filters?.facilities?.includes(facility.value)} onClick={() => onClickFacility(facility.value)}>{facility.label}</Dropdown.Item>
                                                    ))}
                                                </Dropdown.Menu>
                                            </Dropdown>
                                            <Dropdown>
                                                <Dropdown.Toggle variant="success" id="dropdown-basic">
                                                    Stores
                                                </Dropdown.Toggle>

                                                <Dropdown.Menu >
                                                    {storeTypes?.map((store: any, index: number) => (
                                                        <Dropdown.Item key={index} active={filters?.store == store.value} onClick={() => onClickStores(store.value)}>{store.label}</Dropdown.Item>
                                                    ))}
                                                </Dropdown.Menu>
                                            </Dropdown>
                                            <Dropdown>
                                                <Dropdown.Toggle variant="success" id="dropdown-basic">
                                                    Brands
                                                </Dropdown.Toggle>

                                                <Dropdown.Menu >
                                                    {brands?.map((brand: any, index: number) => (
                                                        <Dropdown.Item key={index} active={filters?.brands?.includes(brand.value)} onClick={() => onClickBrands(brand.value)}>{brand.label}</Dropdown.Item>
                                                    ))}
                                                </Dropdown.Menu>
                                            </Dropdown>
                                            <Dropdown>
                                                <Dropdown.Toggle variant="success" id="dropdown-basic">
                                                    Insurance
                                                </Dropdown.Toggle>

                                                <Dropdown.Menu >
                                                    {insurance?.map((item: any, index: number) => (
                                                        <Dropdown.Item key={index} active={filters?.insurance?.includes(item.value)} onClick={() => onClickInsurance(item.value)}>{item.label}</Dropdown.Item>
                                                    ))}
                                                </Dropdown.Menu>
                                            </Dropdown>
                                            <Dropdown>
                                                <Dropdown.Toggle variant="success" id="dropdown-basic">
                                                    Specialities
                                                </Dropdown.Toggle>

                                                <Dropdown.Menu >
                                                    {specialities?.map((item: any, index: number) => (
                                                        <Dropdown.Item key={index} active={filters?.specialities?.includes(item.value)} onClick={() => onClickSpecialities(item.value)}>{item.label}</Dropdown.Item>
                                                    ))}
                                                </Dropdown.Menu>
                                            </Dropdown>
                                        </div>
                                    </motion.div>
                                )}
                            </AnimatePresence>
                            <button onClick={() => setShowFilters(!showFilters)} style={{ marginLeft: "auto", padding: "1rem", zIndex: 1, background: "none", border: "none" }}>
                                <AnimatePresence>
                                    {!showFilters && (
                                        <motion.div
                                            initial={{ opacity: 0 }}
                                            animate={{ opacity: 1, }}
                                            // exit={{ opacity: 0 }}
                                            transition={{ duration: 0.5 }}
                                        >
                                            <IoFilterCircleSharp size={50} />
                                        </motion.div>
                                    )}
                                    {showFilters && (
                                        <motion.div
                                            // initial={{ opacity: 0, transform: "rotate(0deg)" }}
                                            animate={{ opacity: 1, transform: "rotate(90deg)" }}
                                            exit={{ opacity: 0 }}
                                            transition={{ duration: 0.5 }}
                                        >
                                            <IoIosCloseCircle size={50} />
                                        </motion.div>
                                    )}
                                </AnimatePresence>
                            </button>
                        </div>
                    </div>
                    {
                        (
                            (filters?.country?.length > 0 || filters?.insurance?.length > 0)
                            ||
                            (filters?.services?.length > 0 || filters?.brands?.length > 0)
                        )
                        ||
                        (
                            (filters?.facilities?.length > 0 || filters?.store)
                            ||
                            (filters?.specialities?.length > 0)
                        )
                        && (
                            <>
                                <div className='filter-applied' style={{ display: "flex", gap: "1rem", flexWrap: "wrap", marginBottom: "1rem" }}>
                                    {filters?.country?.length > 0 && <span className='filter-item' ><p>Country</p>: {filters?.country?.map((country: any, index: number) => <span key={index}>{country} <button onClick={() => removeFilter("country", country)} ><IoMdCloseCircle color='white' /></button></span>)}</span>}
                                    {filters?.services?.length > 0 && <span className='filter-item' ><p>Services</p>: {filters?.services?.map((service: any, index: number) => <span key={index}>{service} <button onClick={() => removeFilter("services", service)} ><IoMdCloseCircle color='white' /></button></span>)}</span>}
                                    {filters?.facilities?.length > 0 && <span className='filter-item' ><p>Facilities</p>: {filters?.facilities?.map((facility: any, index: number) => <span key={index}>{facility} <button onClick={() => removeFilter("facilities", facility)} ><IoMdCloseCircle color='white' /></button></span>)}</span>}
                                    {filters?.store && <span className='filter-item' ><p>Store</p>:  <span>{filters.store} <button onClick={() => setFilters({ ...filters, store: "" })} ><IoMdCloseCircle color='white' /></button></span></span>}
                                    {filters?.brands?.length > 0 && <span className='filter-item' ><p>Brands</p>: {filters?.brands?.map((brand: any, index: number) => <span key={index}>{brand} <button onClick={() => removeFilter("brands", brand)} ><IoMdCloseCircle color='white' /></button></span>)}</span>}
                                    {filters?.insurance?.length > 0 && <span className='filter-item' ><p>Insurance</p>: {filters?.insurance?.map((insurance: any, index: number) => <span key={index}>{insurance} <button onClick={() => removeFilter("insurance", insurance)} ><IoMdCloseCircle color='white' /></button></span>)}</span>}
                                    {filters?.specialities?.length > 0 && <span className='filter-item' ><p>Specialities</p>: {filters?.specialities?.map((insurance: any, index: number) => <span key={index}>{insurance} <button onClick={() => removeFilter("specialities", insurance)} ><IoMdCloseCircle color='white' /></button></span>)}</span>}
                                </div>
                                <button style={{ background: "white", padding: ".5rem 1rem", borderRadius: "1rem" }} onClick={clearFilter}>Clear All</button>
                            </>
                        )
                    }
                </div>
                <div className='map-list' style={{ display: "flex", gap: "1rem", marginTop: "2rem" }}>
                    <div className="list">
                        {
                            nearbyStores.length === 0 ? (
                                <p ref={pRef}>No stores found within {radius} km of your location.</p>
                            ) :
                                <div className="list-items">
                                    {nearbyStores.map((store: any) => (
                                        <button key={store.id} className='list-item' onMouseEnter={() => { setShowInfoWindow(store._id); setZoom(10); setMapCenter({ lat: Number(store.coordinates.lat), lng: Number(store.coordinates.long) }) }}>
                                            {store?.thumbnail && <Image
                                                quality={100}
                                                priority
                                                src={store?.thumbnail ?? ""}
                                                width={180}
                                                height={180}
                                                alt="images"
                                            />}
                                            <div className="list-item-content">
                                                <h4>{store.name}</h4>
                                                <p>{store.address}</p>
                                                <div className="">
                                                    <p style={{ margin: 0 }}>Phone: <span style={{ fontWeight: "bold" }}>{store?.countryCode} {store?.mobile}</span></p>
                                                    <p style={{ margin: 0 }}>Email: <span style={{ fontWeight: "bold" }}>{store?.email}</span></p>
                                                </div>
                                                {store.location && <Link
                                                    target="_blank"
                                                    href={store.location}
                                                >
                                                    <Button style={{ background: "#000", border: "1px solid black" }} variant="primary">Get Directions</Button>
                                                </Link>}
                                            </div>
                                        </button>
                                    ))}
                                </div>
                        }

                    </div>
                    <div className="map">
                        <div style={{ transform: "translateY(-150px)", width: 0, height: 0 }} id='map'></div>
                        {currentLocation && (
                            <GoogleMap
                                mapContainerStyle={mapContainerStyle}
                                center={mapCenter}
                                zoom={zoom}

                            >
                                <Marker position={currentLocation} label="You" />
                                {nearbyStores.map((store: any) => (
                                    <ClickableMarker
                                        key={store._id}
                                        showInfoWindow={showInfoWindow}
                                        setShowInfoWindow={setShowInfoWindow}
                                        store={store}
                                    />
                                ))}
                            </GoogleMap>
                        )}
                    </div>
                </div>
            </div>
        </LoadScript >
    );
};

function ClickableMarker({ store, setShowInfoWindow, showInfoWindow }: any) {

    const getPixelPositionOffset = (width: number, height: number) => ({
        x: -(width * 2),
        y: -(height * 2),
    })

    return (
        <>
            <Marker
                onClick={() => setShowInfoWindow(store._id)}
                icon={{
                    url: '/new_icon.svg',
                    scaledSize: new google.maps.Size(40, 40),

                    labelOrigin: new google.maps.Point(20, 50),
                }}
                key={store._id}
                position={{
                    lat: Number(store.coordinates.lat),
                    lng: Number(store.coordinates.long),
                }}
                label={store.name}
            />
            {showInfoWindow == store._id && (
                // <InfoWindow
                //     position={{
                //         lat: Number(store.coordinates.lat),
                //         lng: Number(store.coordinates.long),
                //     }}
                //     onCloseClick={() => setShowInfoWindow(false)}
                //     options={{ content: `<h1>hi</h1>`, pixelOffset: new google.maps.Size(0, -20) }}
                // >
                //     <div className='info-window' style={{ width: '200px' }}>
                //         <h4 style={{ textAlign: "left" }}>{store.name}</h4>
                //         <p style={{ fontSize: '1.5rem' }}>{store.address}</p>
                //     </div>
                // </InfoWindow>
                <OverlayView
                    position={{
                        lat: Number(store.coordinates.lat),
                        lng: Number(store.coordinates.long),
                    }}
                    mapPaneName='overlayMouseTarget'
                    key={store._id}
                    getPixelPositionOffset={getPixelPositionOffset}


                >
                    <div className='info-window'>
                        <IoMdCloseCircle className='close-icon' size={25} onClick={() => setShowInfoWindow(false)} />
                        {store?.thumbnail && <Image
                            quality={100}
                            priority
                            src={store?.thumbnail ?? ""}
                            width={180}
                            height={180}
                            alt="images"
                        />}
                        <div className="list-item-content">
                            <h4 style={{ textAlign: "left", fontSize: "1.7rem" }}>{store.name}</h4>
                            {/* <p style={{ fontSize: '1.3rem' }}>{store.address}</p> */}
                            <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                                <p style={{ margin: 0, fontSize: "1.3rem" }}>Phone: <span style={{ fontWeight: "bold" }}>{store?.countryCode} {store?.mobile}</span></p>
                                <p style={{ margin: 0, fontSize: "1.3rem" }}>Email: <span style={{ fontWeight: "bold" }}>{store?.email}</span></p>
                            </div>
                            {store.location && <Link
                                target="_blank"
                                href={store.location}
                                style={{ marginTop: "1rem" }}
                            >
                                <Button style={{ background: "#000", border: "1px solid black" }} variant="primary">Get Directions</Button>
                            </Link>}
                        </div>
                    </div>
                </OverlayView>
            )}

        </>
    )
}

export default Map;
