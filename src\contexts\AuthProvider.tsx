"use client";
import { endpoints } from "@/config/apiEndpoints";
import api from "@/config/axios.interceptor";
import { getUser } from "@/lib/methods/auth";
import { useQuery } from "@tanstack/react-query";
import { redirect } from "next/navigation";
import { createContext, useEffect, useState } from "react";

export const AuthContext = createContext({} as any);

export function AuthProvider({ children }: any) {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [user, setUser] = useState(null);
  const { data: userProfile, isLoading, error } = useQuery({
    queryKey: ["user"],
    queryFn: getUser,
  });

  useEffect(() => {
    if (error) {
      // redirect("/login");
    }
  }, [error]);

  useEffect(() => {
    const token = document?.cookie.replace(/(?:(?:^|.*;\s*)access_token\s*=\s*([^;]*).*$)|^.*$/, "$1");
    if (token) {
      setIsLoggedIn(true);
      api.get(endpoints.profile).then((res) => {
        if (res.data.errorCode === 0) {
          setUser(res.data.result);
        }
      });
    }

    const deviceToken = document?.cookie.replace(/(?:(?:^|.*;\s*)device_token\s*=\s*([^;]*).*$)|^.*$/, "$1");
    if (!deviceToken) {
      const expirationDate = new Date();
      expirationDate.setDate(expirationDate.getDate() + 399); // because => https://developer.chrome.com/blog/cookie-max-age-expires/
      const token = btoa(
        crypto.randomUUID() + Date.now().toLocaleString() + Navigator + Math.random()
      );
      document.cookie = `device_token=${token}; expires=${expirationDate.toUTCString()}; path=/`;
    }
  }, []);

  return (
    <AuthContext.Provider
      value={{
        isLoggedIn,
        user,
        userProfile,
      }}>
      {children}
    </AuthContext.Provider>
  );
}
