.lens-index {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    column-gap: 2.2rem;
    row-gap: 2rem;

    @media (max-width: 575.98px) {
        grid-template-columns: repeat(1, 1fr);
        row-gap: 1.5rem;
    }

    &_box {
        display: flex;
        align-items: center;
        column-gap: 1.1rem;
        border: 1px solid rgba(228, 228, 228, 1);
        border-radius: 1.5rem;
        padding: 1.9rem 1.2rem;
        cursor: pointer;

        @media (max-width: 575.98px) {
            padding: 1.6rem 1.9rem;
        }

        &.isActive {
            border: 2px solid rgba(0, 0, 0, 1);

            @media (max-width: 575.98px) {
                border: 1px solid rgba(0, 0, 0, 1)
            }
        }

        img {
            width: auto;
        }

        h5 {
            font-size: 1.4rem;
            font-weight: 500;
            line-height: 2.1rem;
            letter-spacing: -0.011rem;
        }
    }
}