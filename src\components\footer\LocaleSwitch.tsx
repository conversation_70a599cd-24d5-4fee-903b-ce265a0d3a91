"use client";

import { useLocaleContext } from "@/contexts/LocaleProvider";

export const LocaleSwitch = () => {
  const { changeLocale, currentLocale } = useLocaleContext()
  const toggleLanguage = (e: any) => {
    changeLocale(e.target.value);
  };
  return (
    <>
      <label style={{ display: "none" }} htmlFor="langSwitch">
        Switch Language
      </label>
      <select
        title="Switch Language"
        id="langSwitch"
        name="language"
        value={currentLocale}
        onChange={toggleLanguage}>
        <option value="en">English</option>
        <option value="ar">عربي</option>
      </select>
    </>
  );
};
