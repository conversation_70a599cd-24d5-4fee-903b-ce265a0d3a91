import "./reading.scss";
import Modal from "react-bootstrap/Modal";
import Image from "next/image";

function Reading({ show, handleClose }: any) {
  return (
    <>
      <Modal
        centered
        show={show}
        onHide={handleClose}
        backdrop="static"
        keyboard={false}
        className="reading-popup"
      >
        <Modal.Header closeButton></Modal.Header>
        <Modal.Body>
          <h2>
            <Image quality={100} priority
              src="/images/common/bakarrow.png"
              width={40}
              height={40}
              alt="back arrow"
            />
            Reading
          </h2>

          <div className="reading-popup_radio">
            <label htmlFor="">
              <input type="radio" checked />
              Upload Prescription
            </label>

            <label htmlFor="">
              <input type="radio" />
              Manually Enter Specification
            </label>
          </div>

          <div className="reading-popup_move">
            <div className="reading-popup_select-box">
              <label htmlFor="">Select Diopters</label>
              <div className="icon">
                <select name="pets" id="pet-select">
                  <option value="cat">Please Select</option>
                  <option value="dog">Dog</option>
                  <option value="cat">Cat</option>
                </select>
              </div>
            </div>

            <div className="reading-popup_flex">
              <div className="reading-popup_select-box">
                <label htmlFor="">PD (LEFT EYE)</label>
                <div className="icon">
                  <select name="pets" id="pet-select">
                    <option value="cat">Please Select</option>
                    <option value="dog">Dog</option>
                    <option value="cat">Cat</option>
                  </select>
                </div>
              </div>

              <div className="reading-popup_select-box">
                <label htmlFor="">PD (RIGHT EYE)</label>
                <div className="icon">
                  <select name="pets" id="pet-select">
                    <option value="cat">Please Select</option>
                    <option value="dog">Dog</option>
                    <option value="cat">Cat</option>
                  </select>
                </div>
              </div>
            </div>
          </div>

          <button>Continue</button>
        </Modal.Body>
      </Modal>
    </>
  );
}
export default Reading;
