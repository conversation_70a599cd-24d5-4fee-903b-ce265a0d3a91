import { endpoints } from "@/config/apiEndpoints";
import api from "@/config/axios.interceptor";
import { useLocaleContext } from "@/contexts/LocaleProvider";
import { TranslationContext } from "@/contexts/Translation";
import { useQueryClient } from "@tanstack/react-query";
import React, { useContext, useState } from "react";
import { toast } from "sonner";

const convertNewlinesToBreaks = (text: string) => {
  return text?.replace(/(\r\n|\n|\r)/g, "<br />");
};

export default function Loyality({ loyalityApplied, loyalityPoints, isCardLoading }: any) {

  const queryClient = useQueryClient();
  const { translation: { cartPage } } = useContext(TranslationContext);
  const [amount, setAmount]:any = useState(0)
  const [loading, setLoading] = useState(false);
  const { currencyCode } = useLocaleContext()
  const [removeLoading, setRemoveLoading] = useState(false);
  const handleSubmit = () => {
    if(Number(amount) <= 0) return;
    setLoading(true)
    api
      .post(endpoints.radeemLoyality, {
        enteredValue: Number(amount),
        cardNumber: loyalityPoints?.cardNumber,
      })
      .then((res) => {
        queryClient.invalidateQueries({ queryKey: ["loyality"] });
        queryClient.invalidateQueries({ queryKey: ["cart", "counts"] });
        queryClient.invalidateQueries({ queryKey: ["cart"] });
        toast.success(
          res.data?.message || "Loyality points redeemed successfully"
        );
        setTimeout(() => {
          setLoading(false)
        }, 1000);
      })
      .catch((err) => {
        if(err.response?.data?.message?.includes("insufficient balance")){
          const msg = err.response?.data?.message?.split(":")[1]
          toast.error(msg || "Something went wrong!");
        }else{
          toast.error(err.response?.data?.message || "Something went wrong!");
        }
        setLoading(false)
      });
  };
  const handleRemove = () => {
    setRemoveLoading(true)
    api
      .post(endpoints.removeLoyality)
      .then((res) => {
        if (res.data?.errorCode === 0) {
          toast.success(res.data?.message || "Loyality removed successfully");
          queryClient.invalidateQueries({ queryKey: ["loyality"] });
          queryClient.invalidateQueries({ queryKey: ["cart", "counts"] });
          queryClient.invalidateQueries({ queryKey: ["cart"] });
        } else {
          setRemoveLoading(false)
          toast.error(res.data?.message);
        }
        setTimeout(() => {
          setRemoveLoading(false)
        }, 1000);
      })
      .catch((err) => {
        toast.error(err.response.data.message);
        setRemoveLoading(false)
      });
  };

  return (
    <div className="mt-5">
      <h5 style={{fontSize: "2rem", fontWeight: "600"}}>{cartPage?.loyalty ?? "Loyalty Rewards"}</h5>
      <div style={{ padding: '2rem', borderRadius: "2rem", marginTop: "2rem", border: "1px solid #e4e4e4", background: '#fdfdfe' }} className="d-flex flex-column">
        {/* <div className="d-flex justify-content-between align-items-center">
          <div className="d-flex align-items-center flex-shrink-0">
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15.39 5.21L16.7999 8.02999C16.9899 8.41999 17.4999 8.78999 17.9299 8.86999L20.48 9.28999C22.11 9.55999 22.49 10.74 21.32 11.92L19.3299 13.91C18.9999 14.24 18.81 14.89 18.92 15.36L19.4899 17.82C19.9399 19.76 18.9 20.52 17.19 19.5L14.7999 18.08C14.3699 17.82 13.65 17.82 13.22 18.08L10.8299 19.5C9.11994 20.51 8.07995 19.76 8.52995 17.82L9.09996 15.36C9.20996 14.9 9.01995 14.25 8.68995 13.91L6.69996 11.92C5.52996 10.75 5.90996 9.56999 7.53996 9.28999L10.0899 8.86999C10.5199 8.79999 11.03 8.41999 11.22 8.02999L12.63 5.21C13.38 3.68 14.62 3.68 15.39 5.21Z"
                stroke="black"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M8 5H2"
                stroke="black"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M5 19H2"
                stroke="black"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M3 12H2"
                stroke="black"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            <div className="d-flex flex-column">
              <span className="mx-4">Loyality point</span>
            </div>
          </div>
          <span className="flex-shrink-0">
            {loyalityPoints?.lifetimePoints}
          </span>
        </div> */}

        <div className="d-flex justify-content-between align-items-center">
          <span>{!loyalityApplied ? (cartPage?.redeemableAmount ?? `Redeemable amount`) : (cartPage?.redeemed ?? `Loyality point redeemed`)}</span>
          {!loyalityApplied ? (
            `${currencyCode} ${loyalityPoints?.redeemableAmount}`
          ) : (
            <button style={{margin: 0, opacity: removeLoading ? 0.5 : 1 }} disabled={removeLoading || isCardLoading}  onClick={handleRemove} className="loyality-button">
              {cartPage?.remove ?? "Remove"}
            </button>
          )}
        </div>
        {!loyalityApplied && (
          <>
            <div className="border-top my-3"></div>
            <div style={{alignItems: "center", gap: "1rem", justifyContent: "space-between"}} className="d-flex ">
              <input  className="no-arrow" style={{height: "fit-content", padding: "1rem", borderRadius: ".8rem", maxWidth: "900px"}} value={amount} onChange={(e)=>setAmount(e.target.value)} placeholder="Enter amount"  type="number" max={Number(loyalityPoints?.redeemableAmount)} />
              <button style={{margin: 0}} disabled={(+loyalityPoints?.redeemableAmount === 0 || Number(amount) === 0) || (loading || isCardLoading)} onClick={handleSubmit} className={`loyality-button ${+loyalityPoints?.redeemableAmount === 0 || loading ? 'opacity-50' : ''}`}>
                {cartPage?.redeem ?? "Redeem"}
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
