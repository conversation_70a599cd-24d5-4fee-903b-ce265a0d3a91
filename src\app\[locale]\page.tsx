import styles from "./page.module.scss";
import { endpoints } from "@/config/apiEndpoints";
import React, { Suspense } from "react";

import MainBanner from "@/components/home/<USER>/MainBanner";
import OurCategory from "@/components/home/<USER>/OurCategory";
import InsuranceBanner from "@/components/home/<USER>/InsuranceBanner";
import VideoBanner from "@/components/home/<USER>/VideoBanner";
import ProductMap from "@/components/home/<USER>/ProductMap";
import ThreeGridBanner from "@/components/home/<USER>/ThreeGridBanner";
import ShapeCategories from "@/components/home/<USER>/ShapeCategories";
import WithWithOut from "@/components/home/<USER>/WithWithOut";
import BrandsLogos from "@/components/home/<USER>/BrandsLogos";
import BrandsCollection from "@/components/home/<USER>/BrandsCollection";
import VirtualStoreBanner from "@/components/home/<USER>/VirtualStoreBanner";
import ShopByBrands from "@/components/home/<USER>/ShopByBrands";
import BannerSlider from "@/components/home/<USER>/BannerSlider";
import CollectionSection from "@/components/home/<USER>/CollectionSection";
import MainVideoBanner from "@/components/home/<USER>/MainBanner";
import { cookies } from "next/headers";
import Image from "next/image";
import Link from "next/link";
import HomePopup from "@/components/home/<USER>";
import BrandBannerSlider from "@/components/home/<USER>/BrandBannerSlider";
import TopSlider from "@/components/home/<USER>/TopSlider";

// const MainBanner = React.lazy(() => import("@/components/home/<USER>/MainBanner"));
// const OurCategory = React.lazy(() => import("@/components/home/<USER>/OurCategory"));
// const InsuranceBanner = React.lazy(
//   () => import("@/components/home/<USER>/InsuranceBanner")
// );
// const VideoBanner = React.lazy(() => import("@/components/home/<USER>/VideoBanner"));
// const ProductMap = React.lazy(() => import("@/components/home/<USER>/ProductMap"));
// const ThreeGridBanner = React.lazy(
//   () => import("@/components/home/<USER>/ThreeGridBanner")
// );
// const ShapeCategories = React.lazy(
//   () => import("@/components/home/<USER>/ShapeCategories")
// );
// const WithWithOut = React.lazy(() => import("@/components/home/<USER>/WithWithOut"));
// const BrandsLogos = React.lazy(() => import("@/components/home/<USER>/BrandsLogos"));
// const BrandsCollection = React.lazy(
//   () => import("@/components/home/<USER>/BrandsCollection")
// );
// const VirtualStoreBanner = React.lazy(
//   () => import("@/components/home/<USER>/VirtualStoreBanner")
// );
// const ShopByBrands = React.lazy(() => import("@/components/home/<USER>/ShopByBrands"));
// const BannerSlider = React.lazy(() => import("@/components/home/<USER>/BannerSlider"));
// const CollectionSection = React.lazy(
//   () => import("@/components/home/<USER>/CollectionSection")
// );

const defaultOrder = [
  {
    type: "mainBanner",
    _id: "65a0caef309fd0a4fdfc15c0",
  },
  {
    type: "categories",
    _id: "65a0caef309fd0a4fdfc15c1",
  },
  {
    type: "shopByBrands",
    _id: "65a0caef309fd0a4fdfc15c2",
  },
  {
    type: "collectionsSingle",
    _id: "65a0caef309fd0a4fdfc15c3",
  },
  {
    type: "imageMap",
    _id: "65a0caef309fd0a4fdfc15c4",
  },
  {
    type: "collectionsMultiple",
    _id: "65a0caef309fd0a4fdfc15c5",
  },
  {
    type: "insuranceBanner",
    _id: "65a0caef309fd0a4fdfc15c6",
  },
  {
    type: "brandVideoBanner",
    _id: "65a0caef309fd0a4fdfc15c7",
  },
  {
    type: "threeGridBanner",
    _id: "65a0caef309fd0a4fdfc15c8",
  },
  {
    type: "frameShape",
    _id: "65a0caef309fd0a4fdfc15c9",
  },
  {
    type: "beforeAfter",
    _id: "65a0caef309fd0a4fdfc15ca",
  },
  {
    type: "brands",
    _id: "65a0caef309fd0a4fdfc15cb",
  },
  {
    type: "brandsCollection",
    _id: "65a0caef309fd0a4fdfc15cc",
  },
  {
    type: "virtualTryBanner",
    _id: "65a0caef309fd0a4fdfc15cd",
  },
];

async function getShopByBrands(locale:string){
  const cookieLocale = cookies().get("Next-Locale")?.value || "sa-en";
  const [storeId, language] = cookieLocale.split("-");
  const res = await fetch(process.env.NEXT_PUBLIC_API_URL + endpoints.brands, {
    headers: {
      language: language || "en",
      storeid: storeId || "sa",
      contentType: "application/json",
      accept: "application/json",
    },
    next: {
      tags: ["shopByBrands", "home", "brands"],
      revalidate: 60 * 60 * 60 * 24, // 1 day
    },
  });

  const data = await res.json();
  return data;
}

async function getHomeOrder(locale: string) {
  // const cookieLocale = cookies().get("Next-Locale")?.value || "ae-en";
  const [storeId, language] = locale.split("-");
  const res = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.homeOrder,
    {
      headers: {
        "Content-Type": "application/json",
        language: language || "en",
        storeid: storeId || "sa"
      },
      next: {
        tags: ["home"],
      },
      cache: "force-cache"
    }
  );
  const data = await res.json();
  return data;
}

async function getImageMap(locale:string){
  // const locale = cookies().get("Next-Locale")?.value || "sa-en";
  const [storeId, language] = locale.split("-");
  const res = await fetch(
      process.env.NEXT_PUBLIC_API_URL + endpoints.imageMap,
      {
        headers: {
          "Content-Type": "application/json",
          language: language || "en",
          storeid: storeId || "sa"
        },
        next: {
          tags: ["home", "productMap"],
        },
        cache: "force-cache"
      }
    );
  
    const data= await res.json();
    return data;
}

async function getBeforeAfter(locale: string) {
  // const cookieLocale = cookies().get("Next-Locale")?.value || "sa-en";
  const [storeId, language] = locale.split("-");
  const res = await fetch(process.env.NEXT_PUBLIC_API_URL + endpoints.beforeAfter, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          language: language || "en",
          storeid: storeId || "sa"
        },
        next: { tags: ["withWithout", "home", "withWithout"], revalidate: 60 * 60 * 60 * 24 },
      });
      const data = await res.json();
      return data;
}

const reOrder = (order: any, locale: string, shopByBrands:any, imageMap:any, beforeAfter:any) => {
  return order.map((item: any) => {
    switch (item.type) {
      case "mainBanner":
        return (
          <Suspense fallback={<div>Loading...</div>}>
            <BannerSlider id={item._id} item={item} key={item._id} />
          </Suspense>
        );
      case "videoBanner":
        return (
          <Suspense fallback={<div>Loading...</div>}>
            {/* <MainVideoBanner
              data={item.items?.[0]}
              id={item._id}
              key={item._id}
            /> */}
            <TopSlider
              item={item}
              id={item._id}
              key={item._id}
            />
          </Suspense>
        );
      case "categories":
        return (
          <Suspense fallback={<div>Loading...</div>}>
            <OurCategory type="home" id={item._id} key={item._id} />
          </Suspense>
        );
      case "shopByBrands":
        return (
          <Suspense fallback={<div>Loading...</div>}>
            <ShopByBrands data={shopByBrands} id={item._id} item={item} locale={locale} key={item._id} />
          </Suspense>
        );
      case "collectionsSingle":
      case "newCollectionsSingle":
        return (
          <Suspense fallback={<div>Loading...</div>}>
            <section
              className={styles.newArrivalSlider}
              key={item._id}
              id={item?._id}
            >
              {/* <ProductSlider color="black" /> */}
              <CollectionSection
                key={item._id}
                id={item._id}
                keyword={item.keyword}
                item={item?.items?.[0]}
                color="black"
              />
            </section>
          </Suspense>
        );
      case "imageMap":
        return (
          <Suspense fallback={<div>Loading...</div>}>
            <ProductMap data={imageMap} key={item._id} id={item._id} />
          </Suspense>
        );
      case "collectionsMultiple":
      case "multiple-collection":
        return (
          <Suspense fallback={<div>Loading...</div>}>
            <section
              className={styles.bestSellerSlider}
              key={item._id}
              id={item?._id}
            >
              <CollectionSection
                item={item?.items?.[0]}
                id={item._id}
                key={item._id}
                keyword={item.keyword}
                color="white"
                multiple
                styles={styles.dropdown}
              />
            </section>
          </Suspense>
        );
      case "insuranceBanner":
        return (
          <Suspense fallback={<div>Loading...</div>}>
            <InsuranceBanner
              data={item.items?.[0]}
              key={item._id}
              id={item._id}
            />
          </Suspense>
        );
      case "brandVideoBanner":
        return (
          <Suspense fallback={<div>Loading...</div>}>
            {/* <VideoBanner key={item._id} data={item.items?.[0]} id={item._id} /> */}
            <BrandBannerSlider key={item._id} item={item} id={item._id} />
          </Suspense>
        );
      case "threeGridBanner":
        return (
          <Suspense fallback={<div>Loading...</div>}>
            <ThreeGridBanner id={item._id} data={item.items} key={item._id} />
          </Suspense>
        );
      case "frameShape":
        return (
          <Suspense fallback={<div>Loading...</div>}>
            <ShapeCategories id={item._id} key={item._id} item={item?.items?.[0]} />
          </Suspense>
        );
      case "beforeAfter":
        return (
          <Suspense fallback={<div>Loading...</div>}>
            <WithWithOut data={beforeAfter} locale={locale} item={item?.items?.[0]} key={item._id} id={item._id} />
          </Suspense>
        );
      case "brands":
        return (
          <Suspense fallback={<div>Loading...</div>}>
            <BrandsLogos key={item._id} item={item} id={item._id} locale={locale?.split("-")[1]} />
          </Suspense>
        );
      case "brandsCollection":
        return (
          <Suspense fallback={<div>Loading...</div>}>
            <BrandsCollection item={item} key={item._id} data={item.items} id={item._id} />
          </Suspense>
        );
      case "virtualTryBanner":
        return (
          <Suspense fallback={<div>Loading...</div>}>
            <VirtualStoreBanner
              id={item._id}
              data={item.items?.[0]}
              key={item._id}
            />
          </Suspense>
        );
      default:
        return (
          <MainBanner id={item._id} key={item._id} data={item.items?.[0]} />
        );
    }
  });
};
//test
export default async function Home({ params }: { params: { locale: string } }) {
  try {
    const [data, shopByBrands, imageMap, beforeAfter] = await Promise.all([
      getHomeOrder(params.locale),
      getShopByBrands(params.locale),
      getImageMap(params.locale),
      getBeforeAfter(params.locale)
    ]);

    return <>
      {/* <HomePopup /> */}
      {reOrder(data.result[0].items, params.locale, shopByBrands, imageMap, beforeAfter)}
      <Link target="_blank" className="whatsapp-wrapper" href={"https://wa.me/+97145066050"}>
        <Image className="whatsapp-float" alt="whatsapp" unoptimized src="/icons/whatsapp.svg" width={48} height={48} />
      </Link>
    </>
  } catch (e) {
    // return reOrder(defaultOrder, params.locale, shopByBrands);
  }
}
