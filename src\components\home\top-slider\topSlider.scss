.top-slider {
    position: relative;

    .swiper {
        .swiper-slide {
            display: flex;
            align-items: center;
        }

        .swiper-wrapper {
            align-items: center;
        }

        img {
            height: 100vh;
            width: 100%;

            @media (max-width: 575.98px) {
                height: 500px;
            }
        }
    }

    .swiper-button-prev {
        &::after {
            background-image: url(../../../../public/images/home/<USER>
            background-repeat: no-repeat;
            font-size: 0;
            width: 3rem;
            height: 3rem;
        }
    }

    .swiper-button-next {
        &::after {
            background-image: url(../../../../public/images/home/<USER>
            background-repeat: no-repeat;
            font-size: 0;
            width: 3rem;
            height: 3rem;
        }
    }

    .homebanner {
        position: relative;
        width: 100%;

        video {
            width: 100%;
            height: 100vh;
            object-fit: cover;

            @media (max-width: 575.98px) {
                height: 500px;
            }
        }
    }
}