import Image from "next/image";

type ErrorStateProps = {
  icon: "address" | "box" | "sad" | "x-doc" | "no-data" | "find" | "discount" | "glasses";
  title: string;
  description?: string;
};
export default function EmptyState({ icon, title, description }: ErrorStateProps) {
  return (
    <div style={{ display: "grid", placeItems: "center", padding: "3rem 0" }}>
      <div style={{ width: 300, height: 200 }}>
        <Image quality={100} priority
          style={{ objectFit: "contain" }}
          src={`/images/empty-states/${icon}.svg`}
          width={200}
          height={200}
          alt="empty state"
        />
      </div>
      <h4 className="text-center mt-2">{title}</h4>
      {description && <small className="text-muted mt-1">{description}</small>}
    </div>
  );
}
