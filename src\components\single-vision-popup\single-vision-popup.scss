.single-vision-popup {
  &.modal {
    .modal-dialog-centered {
      @media (max-width: 575.98px) {
        align-items: flex-end;
      }
    }
    .modal-header {
      padding: 0;
      border: none;

      .btn-close {
        background-image: url(../../../public/images/common/close.png);
        width: 4.8rem;
        height: 4.8rem;
        background-position: center;
        background-repeat: no-repeat;
        background-size: contain;
        opacity: 1;
        padding: 0;

        @media (max-width: 575.98px) {
          width: 2.8rem;
          height: 2.8rem;
        }

        &:focus {
          box-shadow: none;
          outline: none;
          border: none;
        }
      }
    }

    .modal-content {
      border: none;
      border-radius: 3rem;
      padding: 1.8rem 1.8rem 4.2rem 5.3rem;

      @media (max-width: 575.98px) {
        padding: 2rem 1.8rem 5.7rem 2.1rem;
        border-radius: 3rem 3rem 0 0;
      }
    }

    .modal-body {
      padding: 0;
      display: flex;
      flex-direction: column;

      h2 {
        text-align: left;
        line-height: 4rem;
        display: flex;
        align-items: center;
        column-gap: 2rem;

        @media (max-width: 575.98px) {
          line-height: 3rem;
        }

        img {
          width: 4rem;
          height: 4rem;
          object-fit: contain;

          @media (max-width: 575.98px) {
            display: none;
          }
        }
      }

      .file-upload {
        margin-top: 2.2rem;
        border-bottom: 1px solid #e2e4e5;
        max-width: 27.1rem;
        width: 100%;
        position: relative;
        margin-left: 3.5rem;

        &::after {
          content: "";
          background-image: url(../../../public/images/common/fi-rr-clip.png);
          background-position: right;
          background-repeat: no-repeat;
          background-size: cover;
          width: 1.6rem;
          height: 1.6rem;
          position: absolute;
          right: 0;
        }

        label {
          margin-top: 0;
          position: absolute;
          top: -4px;
        }

        input {
          border: none;
          opacity: 0;
          max-width: 27.1rem;
          width: 100%;
          cursor: pointer;

          &::placeholder {
            color: #242426;
            font-size: 1.5rem;
            font-weight: 400;
            line-height: 2.8rem;
          }
        }
      }

      label {
        display: flex;
        align-items: center;
        color: #000;
        font-size: 1.6rem;
        font-weight: 500;
        column-gap: 1.4rem;
        margin-top: 3rem;

        input {
          appearance: none;
          width: 2rem;
          height: 2rem;
          border-radius: 50%;
          border: 1px solid #000;
          position: relative;

          &:checked {
            &::after {
              content: "";
              width: 1.2rem;
              height: 1.2rem;
              background-color: #000;
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              position: absolute;
              border-radius: 50%;
            }
          }
        }
      }

      button {
        margin-top: 4rem;
        color: #fff;
        text-align: center;
        font-size: 1.5rem;
        font-weight: 500;
        border: none;
        background-color: #000;
        height: 5.6rem;
        width: 40rem;
        border-radius: 6rem;

        @media (max-width: 575.98px) {
          width: 33.4rem;
          height: 4.5rem;
          margin-top: 5.2rem;
        }
      }
    }
  }
}
