import { endpoints } from "@/config/apiEndpoints";
import api from "@/config/axios.interceptor";
import { useQueryClient } from "@tanstack/react-query";
import React, { useContext, useEffect } from "react";
import { toast } from "sonner";
import SuccessPopup from "../success-popup/SuccessPopup";
import { sendGTMEvent } from "@next/third-parties/google";
import { TranslationContext } from "@/contexts/Translation";
import Image from "next/image";

export default function Coupon({ modalCoupon, couponsModalClose, isApplied, couponCode, setModalCoupon, handleCouponShow, couponLoading, setCouponLoading }: any) {
  const [coupon, setCoupon] = React.useState(couponCode || "");
  const [showSuccess, setShowSuccess] = React.useState(false);
  const { translation: { cartPage } } = useContext(TranslationContext)
  const [successData, setSuccessData] = React.useState({
    title: "Coupon Applied Successfully",
    description: "Great you have Applied the coupon successfully!",
    primaryBtnTxt: "Continue",
  });
  const queryClient = useQueryClient();
  const handleSubmit = (externalCoupon?: any) => {
    if (!externalCoupon && coupon?.trim()?.length === 0) {
      toast.error("Please enter coupon code");
      return;
    }
    setCouponLoading(true)
    api
      .post(endpoints.applyCoupon, { coupon: externalCoupon || coupon })
      .then((res) => {
        if (res.data?.errorCode === 0) {
          setSuccessData({
            ...successData,
            title: res?.data?.message,
            description: `You have applied coupon ${coupon} successfully`,
          });
          queryClient.invalidateQueries({ queryKey: ["cart", "counts"] });
          queryClient.invalidateQueries({ queryKey: ["cart"] });
          // setShowSuccess(true);
          couponsModalClose();
          setCouponLoading(false)
        } else {
          if (res.data?.message?.toLowerCase()?.includes("error")) {
            const msg = res.data?.message?.split(":")
            toast.error(msg[msg?.length - 1]);
          } else {
            toast.error(res.data?.message);
          }
          setCouponLoading(false)
        }
      })
      .catch((err) => {
        if (err.response?.data?.message?.toLowerCase()?.includes("error")) {
          const msg = err.response?.data?.message?.split(":")
          toast.error(msg[msg?.length - 1]);
        } else {
          toast.error(err.response?.data?.message || "Something went wrong!");
        }
        setCouponLoading(false)
      });
  };
  const handleRemove = () => {
    setCouponLoading(true)
    api
      .post(endpoints.removeCoupon, { coupon: coupon })
      .then((res) => {
        if (res.data?.errorCode === 0) {
          setCoupon("");
          setModalCoupon("")
          toast.success("Coupon removed successfully");
          queryClient.invalidateQueries({ queryKey: ["cart", "counts"] });
          queryClient.invalidateQueries({ queryKey: ["cart"] });
        } else {
          toast.error(res.data?.message);
        }
        setCouponLoading(false)
      })
      .catch((err) => {
        setCouponLoading(false)
        toast.error(err.response.data.message);
      });
  };

  useEffect(() => {
    if (modalCoupon) {
      setCoupon(modalCoupon);
      handleSubmit(modalCoupon);
    }
    if (modalCoupon === null) {
      setCoupon("");
    }
  }, [modalCoupon]);
  return (
    <>
      <div>
        <label style={{marginTop: "1rem"}} htmlFor="">{cartPage?.couponCode ?? "Coupon Code"}</label>
        <div className="d-flex align-items-end">
          <input
            style={{ fontSize: "16px" }}
            value={coupon}
            defaultValue={couponCode}
            readOnly={couponCode && isApplied}
            onChange={(e) => setCoupon(e.target.value)}
            id="coupon"
            name="coupon"
            className="coupon-input"
            required
            type="text"
            placeholder="Ex: YAT3246556736573"
          />
          {!isApplied && (
            <button style={{ margin: 0, opacity: couponLoading ? 0.5 : 1 }} disabled={coupon?.trim()?.length === 0 || couponLoading} onClick={() => handleSubmit()}>
              {cartPage?.applyCoupon ?? "Apply Coupon"}
            </button>
          )}
          {isApplied && <button style={{ margin: 0 }} disabled={couponLoading} className="" onClick={() => handleRemove()}>{cartPage?.remove ?? "Remove"}</button>}
        </div>
      </div>

      <SuccessPopup
        show={showSuccess}
        handleClose={() => setShowSuccess(false)}
        data={successData}
      />
    </>
  );
}
