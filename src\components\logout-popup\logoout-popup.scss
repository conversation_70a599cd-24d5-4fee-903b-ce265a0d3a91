.logout-popup {
  &.modal {
    .modal-header {
      padding: 0;
      border: none;

      .btn-close {
        background-image: url(../../../public/images/common/close.png);
        width: 4.8rem;
        height: 4.8rem;
        background-position: center;
        background-repeat: no-repeat;
        background-size: contain;
        opacity: 1;
        padding: 0;

        @media (max-width: 575.98px) {
          width: 2.8rem;
          height: 2.8rem;
        }

        &:focus {
          box-shadow: none;
          outline: none;
          border: none;
        }
      }
    }

    .modal-content {
      border: none;
      border-radius: 3rem;
      padding: 1.8rem 2.1rem 4.2rem 2.1rem;

      @media (max-width: 575.98px) {
        padding: 2.1rem 1.7rem 4.2rem 1.7rem;
        border-radius: 2rem;
      }
    }

    .modal-body {
      padding: 0;
      display: flex;
      flex-direction: column;
      align-items: center;

      h2 {
        text-align: center;
        line-height: 4rem;
        width: 100%;
        margin-top: 2.6rem;

        @media (max-width: 575.98px) {
          line-height: 3rem;
          font-size: 2.2rem;
        }
      }

      .logout-popup-btn {
        display: flex;
        column-gap: 3.1rem;
        margin-top: 5.1rem;

        @media (max-width: 575.98px) {
          margin-top: 4.1rem;
          column-gap: 1.5rem;
        }
      }

      button {
        color: #fff;
        text-align: center;
        font-size: 1.5rem;
        font-weight: 500;
        background-color: #000;
        border-radius: 6rem;
        width: 13.8rem;
        height: 5.6rem;
        border: 1px solid #000;

        &.cancel-btn {
          color: #000;
          background-color: transparent;
        }

        @media (max-width: 575.98px) {
          height: 4.5rem;
        }
      }
    }
  }
}
