"use client";
import Image from "next/image";
import { Autoplay, Navigation } from "swiper/modules";
import "./banner-slider.scss";
import { Swiper, SwiperSlide } from "swiper/react";
import Link from "next/link";

function BrandBannerSlider({ id, item }: any) {
  return (
    <section className="brand-banner-slider" id={id}>
      <Swiper
        className="mySwiper"
        modules={[Autoplay, Navigation]}
        navigation={true}
        speed={500}
        loop={true}
        autoplay={{
          delay: 4000,
          disableOnInteraction: false,
          pauseOnMouseEnter: true,
        }}>
        {
          item?.items?.map((slide: any, i: number) => (
            <SwiperSlide key={i}>
              <div className="videosec_video" >
                <Link style={{ pointerEvents: !slide?.link ? "none" : "auto" }} href={slide?.link ?? ""}>
                  {slide?.video ? (
                    <video
                      className="gif"
                      src={slide?.video ? slide?.video : ""}
                      autoPlay
                      loop
                      muted
                      playsInline
                    />
                  ) : (
                    <Image quality={100} priority className="gif"
                      src={slide?.image ?? ""}
                      width={756}
                      height={324}
                      // fill={true}
                      sizes="100vw"
                      alt="Banner image"
                    />
                  )}
                  {/* {slide?.subImage && <div className="videosec_content">
                    <Image quality={100} priority
                      src={slide?.subImage ?? ""}
                      width={220}
                      height={60}
                      alt="Brand Logo"
                    />
                    <p>{slide?.description}</p>
                  </div>} */}
                </Link>
              </div>
            </SwiperSlide>
          ))
        }
      </Swiper>
    </section>
  );
}

export default BrandBannerSlider;
