"use client";
import React, { useContext, useEffect, useState } from "react";
import Image from "next/image";
import { Modal } from "react-bootstrap";
import {
  VirtualMirror,
  InitializationParams,
  InitializationOptions,
  RenderMirrorParams,
  VirtualMirrorCatalogue, UpcAvailability,
} from '@luxottica/virtual-mirror';
import Terms from "./Terms";
import { TranslationContext } from "@/contexts/Translation";

const VMCatalogue = VirtualMirrorCatalogue.build({
  key: '9d892082-2c7d-4c68-8503-660f15f05c3b', // shared by VM team, if you have only the SSH key, open a ticket
});


export default function VirtualTryPopUp({ upc, vmPolicy }: any) {
  const [show, setShow] = React.useState(false);
  const [showTerms, setShowTerms] = useState(false)
  const [isAvailable, setIsAvailable] = useState(false)
  const [isRendered, setIsRendered] = useState(false)

  const {translation: {productListing, formFields, productPage}} = useContext(TranslationContext)

  const initOptions: InitializationOptions = {
    key: '9d892082-2c7d-4c68-8503-660f15f05c3b', // shared by VM team, if you have only the SSH key, open a ticket
    brand: 'yateem', // to be defined with Luxottica's Analytics team
    storeId: '', // to be defined with Luxottica's Analytics team
    channel: 'licence', // to be defined with Luxottica's Analytics team
    locale: 'en-US',
    analyticsConfig: {
      onInitializeSuccess: () => {}, // called when initialization has been successfully completed
      onRender: (upc: string) => setIsRendered(true) // called when glasses 3D asset is loaded and passed to renderer
    }
  }
  
  const initParams: InitializationParams = {
    options: initOptions
  }



  const handleClose = async() => {
    if(isRendered){
      try {
        await VirtualMirror?.closeMirror({target:"my-fitmix-container"})
      } catch (error) {
        console.log(error)
      } finally{
        setShow(false)
        setIsRendered(false)
      }
    }else{
      setShow(false)
    }
  }

  useEffect(() => { 
    if (show) {
      VirtualMirror.initialize(initParams)
        .then(() => {
          const renderParams: RenderMirrorParams = {
            target: 'my-fitmix-container',
            upc: upc,
            options: {}
          };
          return VirtualMirror.renderMirror(renderParams);
        })
        .then(() => {
          // OK
        })
        .catch((error) => {
          console.error(error)
        })
    }

  }, [show])

  useEffect(()=>{
    const result = VMCatalogue.isUpcSupported(upc);
    result[upc].then((response: UpcAvailability) => {
      console.log("is VR available ", response.isAvailable())
      if(response.isAvailable()) setIsAvailable(true)
    })
  }, [])

  if(!isAvailable) return null

  return (
    <>
      <button style={{display: "flex", gap: ".5rem"}} onClick={() => setShowTerms(true)}>
        <span >{productListing?.virtualTry ?? "Virtual Try ON"}</span>{" "}
        <Image quality={100} priority
          src="/images/product-detail/emoji.png"
          width={24}
          height={24}
          alt="gallery emoji"
        />
      </button>
      {showTerms && (
        <Terms vmPolicy={vmPolicy} handleClose={()=> setShowTerms(false)} show={showTerms} showVm={setShow} />
      )}
      {show && (
        <Modal
          show={show}
          fullscreen
          onHide={handleClose}
          className="virtual-slider-modal"
        >
          <Modal.Header closeButton></Modal.Header>
          <Modal.Body className="p-0">
            {/* {show && ( */}
              <div className="virtual-slider">
                <div id="my-fitmix-container" style={{ height: "90dvh" }}></div>
              </div>
            {/* )} */}
          </Modal.Body>
        </Modal>
      )}
    </>
  );
}
