.image-comparison {
  background-color: #000;
  padding: 7rem 0;
  margin-top: 5rem;

  @media (max-width: 575.98px) {
    margin-top: 0;
    padding-top: 4.6rem;
    padding-bottom: 5rem;
  }

  &_title {
    h2 {
      color: #fff;
    }
  }

  .swiper {
    margin-top: 3rem;

    &-slide {
      &>div {
        border-radius: 1.5rem;
      }
    }

    @media (max-width: 575.98px) {
      margin-top: 2.4rem;
    }

    img {
      border-radius: 1.5rem;
    }

    .swiper-pagination {
      position: unset;
      background: rgba(97, 105, 129, 1) !important;
      height: 0.1rem;
      margin-top: 2.45rem;

      @media (min-width: 576px) {
        display: none;
      }

      .swiper-pagination-progressbar-fill {
        border-radius: 0.4rem 0.4rem 0rem 0rem;
        background: #FFFFFF;
        height: 0.2rem;
      }
    }
  }

  .swiper-paginaion {
    display: flex;
    column-gap: 1rem;
    justify-content: center;

    .swiper-button-next,
    .swiper-button-prev {
      width: 4rem;
      height: 4rem;
      border-radius: 50%;
      background-color: #fff;
      position: unset;
      margin: 0;
      margin-top: 2rem;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 2rem;
        height: 2rem;
        object-fit: contain;
      }

      &::after {
        display: none;
      }
    }
  }


  .__rcs-handle-button {
    position: relative;
    border-width: 0px !important;
    backdrop-filter: blur(0px) !important;
    box-shadow: none !important;
    background-color: transparent !important;
    height: 49px !important;

    &::after {
      content: "";
      background-image: url(../../../../public/images/home/<USER>
      width: 1.0552rem;
      height: 5rem;
      position: absolute;
      left: 50%;
      transform: translate(-50%, -50%);
      top: 50%;
    }
  }

  .__rcs-handle-arrow {
    opacity: 0;
  }

  .__rcs-handle-line {
    box-shadow: none !important;
  }

  .blured-img {
    filter: blur(3px);
  }
}

.app.rtl {

  .image-comparison .swiper-paginaion .swiper-button-next,
  .image-comparison .swiper-paginaion .swiper-button-prev {
    transform: rotate(180deg);
  }
}