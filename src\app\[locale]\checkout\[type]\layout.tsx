import { getUser } from '@/lib/methods/auth';
import { redirect } from 'next/navigation';
import React from 'react'
import Auth from './Auth';

export default async function Layout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: { id: string; type: string };
}) {
  console.log(params)
    const res = await getUser();

    return (
        <Auth params={params} user={res}>
            {children}
        </Auth>
    )
}