import "./shopByBrands.scss";
import Image from "next/image";
import Slider from "./slider/Slider";
import MarqueeSlider from "./slider/MarqueeSlider";
import { endpoints } from "@/config/apiEndpoints";
import Link from "next/link";

export default async function ShopByBrands({
  locale,
  id,
  item,
  data,
}: {
  locale: string;
  id: string;
  item: any;
  data: any;
}) {

  const brands = data?.result || [];

  const brandPosters = brands
    .filter((brand: any) => brand?.inHome === true)
    .map((brand: any) => ({
      image: brand?.poster || "/images/home/<USER>",
      id: brand?._id,
      slug: brand?.slug,
      name: brand?.name,
    }));

  return (
    <section className="gallery homeGallery" id={id}>
      <div className="galller_wrapper">
        <div className="gallery_marquee">
          <MarqueeSlider data={brands} />
        </div>

        <div className="container-fluid">
          <div className="gallery_title">
            <h2>{item?.items?.[0]?.title}</h2>
          </div>
          <div className="gallery_images d-none d-md-block">
            {brandPosters.map((items: any) => (
              <Link href={`/brands/${items?.slug}`} key={items?.id} className="gallery_item">
                <Image
                  quality={100}
                  priority
                  className="img-1"
                  key={items?.id}
                  src={items?.image ?? ""}
                  width={100000}
                  height={100000}
                  alt="image"
                />

                <div className="brand-name">
                  <h4>{items?.name}</h4>
                </div>
              </Link>
            ))}
          </div>

          <div className="gallery_images d-md-none d-block">
            <Slider data={brandPosters} />
          </div>
        </div>

        <div className="gallery_view-all">
          <Link href="/brands">{item?.items[0]?.buttonText}</Link>
        </div>
        <div className="gallery_marquee">
          <MarqueeSlider data={brands} />
        </div>
      </div>
    </section>
  );
}
