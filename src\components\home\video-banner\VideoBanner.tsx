import Link from "next/link";
import "./videoBanner.scss";
import Image from "next/image";

export default function VideoBanner({ id, data }: { id: any; data: any }) {
  return (
    <section className="videosec" id={id}>
      <div className="videosec_wrapper">
        <div className="videosec_video" >
          <Link style={{pointerEvents: !data?.link? "none": "auto"}} href={data?.link ?? ""}>
          {data?.video ? (
            <video
              className="gif"
              src={data?.video ? data?.video : ""}
              autoPlay
              loop
              muted
              playsInline
            />
          ) : (
            <Image quality={100} priority className="gif"
              src={data?.image ?? ""}
              width={756}
              height={324}
              // fill={true}
              sizes="100vw"
              alt="Banner image"
            />
          )}
            <div className="videosec_content">
              <Image quality={100} priority
                src={data?.subImage ?? ""}
                width={220}
                height={60}
                alt="Brand Logo"
              />
              <p>{data?.description}</p>
            </div>
          </Link>
        </div>
      </div>
    </section>
  );
}
