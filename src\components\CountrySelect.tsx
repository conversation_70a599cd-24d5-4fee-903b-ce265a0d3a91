import { countryCodeWithFlags } from "@/lib/countryCodeWithFlag";
import Select from "react-select";
const SelectList: any = countryCodeWithFlags.map((country) => {
  return {
    value: country?.dial_code,
    label: (
      <div className="drop-item">
        <img src={country.image} alt="" />
        {country.name} ({country.dial_code})
      </div>
    ),
  };
});
export default function CountrySelect({ onChange }: any) {
  return (
    <Select
      components={{
        IndicatorSeparator: () => null,
      }}
      classNames={{
        control: (state) => (state.isFocused ? "select" : "select"),
        menu: (state) => "menu",
        indicatorsContainer: () => "indicators-container",
        valueContainer: (state) => "value-container",
      }}
      defaultValue={"+971"}
      onChange={onChange}
      options={SelectList}
    />
  );
}
