"use client"

import { TranslationContext } from "@/contexts/Translation"
import { useContext } from "react"


export default function Text(){
    const { translation }: any = useContext(TranslationContext)
    return <>
    <h2>{translation?.other?.storeLocator ?? "Store Locator"}</h2>
    <p style={{textAlign: 'center'}}>{translation?.other?.storeLocatorText ?? "Find your nearest Yateem Optician showroom."}</p>
    </>
}