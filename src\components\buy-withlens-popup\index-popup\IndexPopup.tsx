import { Modal } from "react-bootstrap";
import Image from "next/image";
import { useEffect, useState } from "react";
import LensPrice from "../lens-price/LensPrice";
import { endpoints } from "@/config/apiEndpoints";
import api from "@/config/axios.interceptor";
import { useQuery } from "@tanstack/react-query";
import { useLocaleContext } from "@/contexts/LocaleProvider";

export default function IndexPopup({
  show,
  handleClose,
  lensData,
  handleLensData,
  setStep,
  back,
  sum,
  setSum,
}: any) {
  const {
    data: indexData,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["lens-index", lensData?.brand],
    queryFn: () => {
      return api
        .get(`${endpoints.lensIndex}/${lensData?.brand}`)
        .then((res) => {
          if (res.status === 200) {
            return res.data?.result?.index;
          } else {
            return [];
          }
        });
    },
  });
  const [localSum, setLocalSum] = useState(sum);
  const getPrice = (id: string | number) => {
    return indexData?.find((power: any) => power._id === id)?.price;
  };

  const {currencyCode} = useLocaleContext()

  const handleSelect = (e: any) => {
    handleLensData({ index: e.target.value });
    setSum("index", getPrice(e.target.value));
  };
  useEffect(() => {
    if (indexData && indexData?.length > 0) {
      handleLensData({ index: lensData?.index || indexData[0]?._id });
      setSum("index", getPrice(lensData?.index) || getPrice(indexData[0]?._id));
    }
  }, [indexData]);
  return (
    <>
      <Modal
        className="buy-with"
        show={show}
        onHide={handleClose}
        backdrop="static"
        keyboard={false}
        centered
      >
        <Modal.Header closeButton></Modal.Header>
        <Modal.Body>
          <h2>
            <Image quality={100} priority
              onClick={() => back("index")}
              src="/images/common/bakarrow.png"
              width={40}
              height={40}
              alt="back arrow"
            />
            Index <LensPrice sum={sum} />
          </h2>
          {indexData?.map((index: any) => (
            <label htmlFor={index.name} key={index.name}>
              <input
                id={index._id}
                type="radio"
                value={index._id}
                defaultChecked={lensData?.index === index._id}
                onChange={handleSelect}
                name="lensType"
                checked={lensData?.index === index._id}
              />
              {index.name} <span className="text-muted">{currencyCode + " "}{index?.price}</span>
            </label>
          ))}

          <button onClick={() => setStep("index")}>Next</button>
        </Modal.Body>
      </Modal>
    </>
  );
}
