.single-vision-drop {
  &.modal {
    &.show .modal-dialog {
      transform: none !important;
    }

    .modal-dialog {
      margin: 0;
      width: 100%;
      max-width: 68.3rem;
      position: fixed;
      right: 0;
      height: 100%;
    }

    .modal-header {
      padding: 0;
      border: none;

      h2 {
        text-align: left;
        line-height: 3.1rem;
        display: flex;
        align-items: center;
        column-gap: 1.4rem;
        font-size: 2.5rem;
        font-weight: 500;

        @media (max-width: 575.98px) {
          font-size: 2.2rem;
          color: #242731;
        }

        img {
          width: 4rem;
          height: 4rem;
          object-fit: contain;

          @media (max-width: 575.98px) {
            display: none;
          }
        }
      }

      .btn-close {
        background-image: url(../../../public/images/common/close.svg);
        width: 4.8rem;
        height: 4.8rem;
        background-position: center;
        background-repeat: no-repeat;
        background-size: contain;
        opacity: 1;
        padding: 0;

        @media (max-width: 575.98px) {
          width: 2.8rem;
          height: 2.8rem;
          transform: none;
        }

        &:focus {
          box-shadow: none;
          outline: none;
          border: none;
        }
      }
    }

    .modal-content {
      border: none;
      background: #fff;
      border-radius: 0rem;
      overflow-y: auto;
      height: 100vh;

      @media (max-width: 575.98px) {
        height: 100%;
      }

    }

    .modal-wrap {
      padding: 4rem 5rem 0rem 6.2rem;

      @media (max-width: 575.98px) {
        padding: 1.5rem;
        padding-bottom: 0;
      }

    }

    .modal-body {
      padding: 0;
      margin-top: 2.5rem;

      @media (max-width: 575.98px) {
        margin-top: 2.1rem;
      }

      .file-upload {
        margin-top: 2.2rem;
        border-bottom: 1px solid #e2e4e5;
        max-width: 27.1rem;
        width: 100%;
        position: relative;
        margin-left: 3.5rem;

        &::after {
          content: "";
          background-image: url(../../../public/images/common/fi-rr-clip.png);
          background-position: right;
          background-repeat: no-repeat;
          background-size: cover;
          width: 1.6rem;
          height: 1.6rem;
          position: absolute;
          right: 0;
        }

        label {
          margin-top: 0;
          position: absolute;
          top: -4px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 250px;
        }

        input {
          border: none;
          opacity: 0;
          max-width: 27.1rem;
          width: 100%;
          cursor: pointer;

          &::placeholder {
            color: #242426;
            font-size: 1.5rem;
            font-weight: 400;
            line-height: 2.8rem;
          }
        }
      }


      button#upload-btn {
        border-radius: 6rem;
        background: #000;
        background-image: linear-gradient(90deg, rgba(0, 0, 0, 1) 100%, rgba(70, 70, 70, 1) 0%);
        color: #fff;
        font-size: 1.5rem;
        font-weight: 500;
        height: 5.6rem;
        width: 100%;
        border: none;
        margin-top: 4rem;
        transition: all 0.3s ease;

        &[disabled] {
          background-image: linear-gradient(90deg, rgba(0, 0, 0, 1) 25%, rgba(70, 70, 70, 1) 25%);
          animation: loading 1s infinite;
        }
      }

      .cta {
        font-size: 1.2rem;
        font-weight: 400;
        line-height: 1.5rem;
        opacity: 50%;
        color: #000000;
        text-align: center;
        margin-top: 3rem;

        @media (max-width: 575.98px) {
          margin-top: 2.5rem;
        }

        a {
          color: #000000;
        }
      }
    }

    .modal-footer {
      padding: 0;
      border: none;
      background-color: #F2F4F9;
      padding: 0.8rem 4.5rem .8rem 6.2rem;
      margin-top: auto;
      display: flex;
      justify-content: space-between;

      @media (max-width: 575.98px) {
        padding: 1rem 1.5rem;
      }

      button {
        color: #fff;
        text-align: center;
        font-size: 1.5rem;
        font-weight: 500;
        border: none;
        background-color: #000;
        height: 4.5rem;
        width: 15rem;
        border-radius: 6rem;
        padding: 0;
        margin: 0;

        &.disabled {
          background-color: #797A7D;
        }
      }

      h5 {
        font-size: 1.4rem;
        font-weight: 600;
        color: #000000;
        margin: 0;

        span {
          font-size: 1.8rem;
        }
      }
    }
  }

  &_box {
    border: 1px solid #E4E4E4;
    border-radius: 1.5rem;
    cursor: pointer;

    &:not(:last-child) {
      margin-bottom: 1.5rem;
    }

    h5 {
      font-size: 1.6rem;
      font-weight: 600;
      line-height: 2.4rem;
      letter-spacing: -0.011em;

      @media (max-width: 575.98px) {
        font-size: 1.4rem;
        font-weight: 500;
        line-height: 1.7rem;
      }
    }

    p {
      font-size: 1.3rem;
      font-size: 400;
      line-height: 1.6rem;

      @media (max-width: 575.98px) {
        margin-top: 0.6rem;
        max-width: 19.8rem;
        width: 100%;
      }
    }
  }

  &_or {
    margin-bottom: 2.3rem;

    @media (max-width: 575.98px) {
      margin-bottom: 0;
    }

    img {
      width: 100%;
      height: 1.5rem;
    }
  }

  &_boxinner {
    display: flex;
    padding: 2.1rem;
    column-gap: 1.4rem;

    @media (max-width: 575.98px) {
      column-gap: 1.9rem;
    }

    img {
      width: 4.8rem;
      height: 4.8rem;
    }
  }

  &_select {
    display: flex;
    column-gap: 3rem;

    .icon {
      position: relative;
      display: inline-block;
      margin-right: 1.5rem;

      &::after {
        content: "";
        position: absolute;
        background-image: url(../../../public/images/common/Icon.png);
        background-repeat: no-repeat;
        width: 2rem;
        height: 2rem;
        top: 10px;
        right: 8px;
      }
    }

    table {
      border-collapse: separate;
      border-spacing: 0px 20px;

      tr {
        vertical-align: baseline;
      }

      th {
        color: #000;
        font-size: 1.3rem;
        font-weight: 400;
      }

      .span {
        color: #000;
        font-size: 1.3rem;
        font-weight: 400;
        margin-right: 1.5rem;
        display: block;
      }

      select {
        border-radius: 2.95rem;
        background: #f3f3f3;
        color: #000;
        font-size: 1.3rem;
        font-weight: 400;
        border: none;
        padding: 13px 34px 13px 17px;
        appearance: none;
        position: relative;

        &.error {
          border: 1px solid rgb(177, 52, 52);
          background-color: #fff2f2;
        }

        &:focus-visible {
          border: none;
          outline: none;
        }
      }
    }
  }
}


@keyframes loading {
  0% {
    background-position: 0 0;
  }

  50% {
    background-position: 100% 0;
  }

  100% {
    background-position: 0 0;
  }
}

.user-details-popup-input {
  &:not(:last-child) {
    margin-bottom: 3.6rem;

    @media (max-width: 575.98px) {
      margin-bottom: 2rem;
    }
  }

  label {
    margin-bottom: 0.8rem;
    color: #242426;
    font-size: 1.4rem;
    font-weight: 400;

    @media (max-width: 575.98px) {
      font-size: 1.3rem;
    }
  }

  input {
    width: 100%;
    border: none;
    border-bottom: 1px solid #e2e4e5;
    padding-left: 1.5rem;
    padding-bottom: 0.8rem;
    position: relative;

    &[type="number"] {
      -moz-appearance: none;
      appearance: none;
    }

    &::-webkit-inner-spin-button {
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
    }

    &::placeholder {
      color: #bcbcbc;
      font-size: 1.8rem;
      font-weight: 400;
      line-height: 2.8rem;

      @media (max-width: 575.98px) {
        font-size: 1.5rem;
      }
    }

    &:focus-visible {
      border-bottom: 1px solid #e2e4e5;
      outline: none;
      box-shadow: none;
    }
  }

  textarea {
    width: 100%;
    border: none;
    border-bottom: 1px solid #e2e4e5;
    padding-left: 1.5rem;
    padding-bottom: 6.8rem;
    resize: none;

    @media (max-width: 575.98px) {
      font-size: 1.5rem;
    }

    &:focus-visible {
      border-bottom: 1px solid #e2e4e5;
      outline: none;
      box-shadow: none;
    }

    &::placeholder {
      color: #242426;
      font-size: 1.8rem;
      font-weight: 400;

      @media (max-width: 575.98px) {
        font-size: 1.5rem;
      }
    }
  }
}

.app.rtl {
  .single-vision-drop.modal .modal-wrap {
    padding: 4rem 6.2rem 0rem 5rem;

    @media (max-width: 575.98px) {
      padding: 1.5rem;
    }


  }

  .single-vision-drop.modal .modal-header .btn-close {
    margin-left: 0;
  }

  .single-vision-drop.modal .modal-header h2 img {
    transform: rotate(180deg);
  }

  .upload-photo_header h2 img {
    transform: rotate(180deg);
  }

  .enter-manually {
    padding: 4rem 6.2rem 0rem 5rem;

    @media (max-width: 575.98px) {
      padding: 1.5rem;
    }
  }

  .enter-manually_header h2 img {
    transform: rotate(180deg);
  }

  .enter-manually_select .css-13cymwt-control .css-1xc3v61-indicatorContainer {
    padding-right: 0;
    padding-left: 0;
  }
}