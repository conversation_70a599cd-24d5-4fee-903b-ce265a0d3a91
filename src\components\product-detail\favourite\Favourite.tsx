"use client";

import { calculateSavings } from "@/app/[locale]/product/[slug]/ProductVariantSection";
import { endpoints } from "@/config/apiEndpoints";
import api from "@/config/axios.interceptor";
import { AuthContext } from "@/contexts/AuthProvider";
import { useLocaleContext } from "@/contexts/LocaleProvider";
import { sendGTMEvent } from "@next/third-parties/google";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { createHmac } from "crypto";
import { motion } from "framer-motion";
import { redirect, useRouter } from "next/navigation";
import { use, useContext, useEffect, useState } from "react";
import { toast } from "sonner";

function Favourite({ data, product }: any) {
  const [count, setCount] = useState(data?.count);
  const queryClient = useQueryClient();
  const router = useRouter();
  const { userProfile } = useContext(AuthContext);
  const {currencyCode} = useLocaleContext()

  const mutation = useMutation({
    mutationFn: () => {
      return api.post(endpoints.manageWishlist, { product: data?.id });
    },
    onSuccess: (res) => {
      if (data?.isWishlisted) {
        setCount(count - 1);
      } else {
        setCount(count + 1);
      }


      queryClient.invalidateQueries({ queryKey: ["wishList"] });
      queryClient.setQueryData(["product", data?.slug], (prev: any) => {
        return prev ? { ...prev, isWishlisted: !prev?.isWishlisted } : prev
      });
      const queryCache = queryClient.getQueryCache()
      const queryKeys = queryCache.getAll().map(cache => cache.queryKey)
      const productsKey = queryKeys.filter((key: any) => key[0] === "products");
      const productsSearch = queryKeys.filter((key: any) => key[0] === "search");

      [...productsKey, ...productsSearch].forEach((key: any) => {
        queryClient.setQueryData(key, (prev: any) => {
          const newData = prev?.pages?.map((page: any) =>
          ({
            ...page,
            products: page?.products?.map((item: any) => {
              let obj = {
                ...item
              }
              for (let [key, value] of Object.entries(item) as any) {
                if (value?._id === product?._id) {

                  obj[key] = {
                    ...value,
                    isWishlisted: !value?.isWishlisted
                  }
                }
              }
              return obj
            })
          }))
          return prev ? {
            ...prev,
            pages: newData
          } : prev
        })
      })

      if (!data?.isWishlisted) {
        const price = product?.customizable ? product?.productType == "contactLens" ? product?.contactSizes?.[0]?.price : product?.sizes?.[0]?.price : product?.price?.aed;
        const offerPrice = product?.customizable ? product?.productType == "contactLens" ? product?.contactSizes?.[0]?.offerPrice : product?.sizes?.[0]?.offerPrice : product?.offerPrice?.aed;
        let savings = calculateSavings(price, offerPrice);
        if (!offerPrice) savings = 0;
        sendGTMEvent({ ecommerce: null })
        let eventData: any = {
          event: "add_to_wishlist",
          ecommerce: {
            currency: currencyCode,
            value: price - savings,
            items: [
              {
                item_id: product?.sku,
                item_name: product?.name,
                discount: savings,
                index: 0,
                item_brand: product?.brand,
                item_category: product?.category?.[0]?.name,
                item_category2: product?.category?.[1]?.name,
                item_variant: product?.color?.name,
                price,
                // quantity: 1,
              }
            ]
          }
        }
        if (userProfile) {
          const mobile = createHmac('sha256', '123').update(userProfile?.mobile).digest('hex')
          let email = null;
          if (userProfile?.email) email = createHmac('sha256', '123').update(userProfile?.email).digest('hex')
          eventData.user = {
            mobile,
            email,
            user_id: userProfile?._id
          }
        }
        sendGTMEvent(eventData)
      }

      queryClient.invalidateQueries({ queryKey: ["cart", "counts"] });
      toast.success(res.data.message);

    },
    onError: (err: any) => {
      toast.error(err.response.data.message);
      if (err.response?.status === 401) {
        router.push("/login?type=wishlist&id=" + data?.id);
      }
    }
  })

  return (
    <motion.button
      whileTap={{ scale: 1.3 }}
      transition={{ type: "spring", stiffness: 400, damping: 10 }}
      className="fav"
      onClick={() => mutation.mutate()}
    >
      <motion.svg
        whileHover={{ scale: 1.3 }}
        transition={{ type: "spring", stiffness: 400, damping: 10 }}
        xmlns="http://www.w3.org/2000/svg"
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill={data?.isWishlisted ? "black" : "none"}
      >
        <path
          d="M17.5 6.875C17.5 4.80417 15.7508 3.125 13.5933 3.125C11.9808 3.125 10.5958 4.06333 10 5.4025C9.40417 4.06333 8.01917 3.125 6.40583 3.125C4.25 3.125 2.5 4.80417 2.5 6.875C2.5 12.8917 10 16.875 10 16.875C10 16.875 17.5 12.8917 17.5 6.875Z"
          stroke="black"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </motion.svg>
      {/* {isWishListed && count} */}
    </motion.button>
  );
}

export default Favourite;
