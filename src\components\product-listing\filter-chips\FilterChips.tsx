"use client";
import { useContext, useEffect, useState } from "react";
import "./filter-chips.scss";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import useFilter from "@/hooks/useFilter";
import { FilterContext } from "@/contexts/FilterContaxt";
import { useLocaleContext } from "@/contexts/LocaleProvider";

const dontInclude = ["priceFrom", "priceTo", "tryHome", "sort"];

export default function FilterChips({ activeFilters, allFilters }: any) {
  const pathname = usePathname();
  const router = useRouter();
  const [selectedFilters, setSelectedFilters] = useState<any>([]);
  const {currencyCode} = useLocaleContext()

  useEffect(() => {
    if (allFilters) {
      populateStateVariable(activeFilters, allFilters);
    }
  }, [activeFilters, allFilters]);

  const populateStateVariable = (originalObject: any, jsonObject: any) => {
    let newStateVariable: any = [];
    console.log(jsonObject)
    if (!pathname.includes("login")) {
      for (let key in originalObject) {
        originalObject[key].forEach((id: string) => {
          let foundObject = dontInclude.includes(key)
            ? null
            : jsonObject[key]?.find((obj: any) => obj._id === id);
          if (foundObject) {
            newStateVariable.push({ ...foundObject, filterType: key });
          }
        });
      }
    }

    if (activeFilters.priceFrom && activeFilters.priceTo) {
      newStateVariable.push({ filterType: 'priceRange', name: `${currencyCode} ${activeFilters.priceFrom} - ${currencyCode} ${activeFilters.priceTo}` })
    }

    setSelectedFilters(newStateVariable);
  };

  return (
    <div className="filter-chips-wrapper">
      {selectedFilters.map((item: any, index: number) => (
        <Chips key={index + item} item={item} />
      ))}
    </div>
  );
}

function Chips({ item }: any) {

  const searchParams = useSearchParams()
  const pathname = usePathname()

  const deleteFilter = (filter: any) => {
    if (filter.filterType !== 'priceRange') {
      const newParams = new URLSearchParams(searchParams.toString());
      newParams.delete(filter.filterType, filter._id);
      window.history.pushState(null, '', `${pathname}?${newParams.toString()}`)
      // router.push(`${pathname}?${newParams.toString()}`);
    } else {
      const newParams = new URLSearchParams(searchParams.toString());
      console.log(newParams.toString())
      newParams.delete('priceFrom', filter.name.split('-')[0].trim().split(" ")[1])
      newParams.delete('priceTo', filter.name.split('-')[1].trim().split(" ")[1])
      window.history.pushState(null, '', `${pathname}?${newParams.toString()}`)
      // router.push(`${pathname}?${newParams.toString()}`);
    }
    // allFilters?.[filter.filterType]?.setActive((prevActive:any) => {
    //   console.log(prevActive)
    //   if (prevActive.includes(filter?._id)) {
    //     return prevActive.filter((val:any) => val !== filter?._id);
    //   } else {
    //     return [...prevActive, filter?._id];
    //   }
    // });
  };

  return (
    <div className="filter-chips">
      <svg
        onClick={() => deleteFilter(item)}
        xmlns="http://www.w3.org/2000/svg"
        width="12"
        height="12"
        viewBox="0 0 12 12"
        fill="none">
        <path
          d="M1.63354 1.63403L10.9006 10.9011M10.9006 1.63403L1.63354 10.9011"
          stroke="black"
          stroke-width="1.5"
          stroke-linecap="round"
        />
      </svg>
      {item?.name}
    </div>
  )
}
