"use client";
import Image from "next/image";
import "./personal-info.scss";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { getUser } from "@/lib/methods/auth";
import { SubmitHand<PERSON>, useForm } from "react-hook-form";
import api from "@/config/axios.interceptor";
import { endpoints } from "@/config/apiEndpoints";
import { toast } from "sonner";
import ImageCreator from "@/components/ImageCreator";
import { useContext, useEffect, useRef, useState } from "react";
import { imageToBase64, objectToFormData } from "@/lib/utils/utils";
import DocumentCard from "@/components/document-card/DocumentCard";
import GenericModal from "@/components/generic-modal/GenericModal";
import Select from "react-select";
import { TranslationContext } from "@/contexts/Translation";
import { useLocaleContext } from "@/contexts/LocaleProvider";

interface Inputs {
  name: string;
  email: string;
  mobile: string;
  countryCode: string;
  emirates: string;
  insuranceId: string | number;
  image: any;
  file: any;
}

function PersonalInfo({ user }: any) {
  const { currentLocale:locale } = useLocaleContext()
  const {
    data: userProfile,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["user"],
    queryFn: getUser,
  });
  const queryClient = useQueryClient();
  const { translation: { formFields, myAccount } }: any = useContext(TranslationContext)

  const [imageUrl, setImageUrl] = useState<any>("");
  const [filePreview, setFilePreview] = useState<any>("");
  const [deleteModal, setDeleteModal] = useState(false);

  const {
    register,
    handleSubmit,
    setFocus,
    watch,
    setValue,
    getValues,
    reset,
    formState: { errors, isDirty, defaultValues },
  } = useForm<Inputs>({ defaultValues: user, values: userProfile });

  const deletePic = () => {
    setValue("image", "", { shouldDirty: true });
  };
  const onSubmit: SubmitHandler<Inputs> = (data) => {
    const image = data.image
      ? data.image.replace(process.env.NEXT_PUBLIC_IMAGE_DOMAIN_CDN, "")
      : "";
    const file = data.file && data.file.replace(process.env.NEXT_PUBLIC_IMAGE_DOMAIN_CDN, "");
    const formData = objectToFormData(data);
    api.post(endpoints.updateProfile, { ...data, image, file }).then((res) => {
      if (res.data.errorCode === 0) {
        toast.success(res.data.message);
        queryClient.invalidateQueries({ queryKey: ["user"] });
      }
    });
  };

  const imageObs = watch("image");
  const fileObs = watch("file");

  const handleImageUpload = (file: FileList) => {
    if (!file) return;
    imageToBase64(file[0]).then((base64) => {
      setImageUrl(base64);
    });
    const formData = new FormData();
    formData.append("file", file[0]);
    api.post("/media-upload", formData).then((res) => {
      setValue("image", res.data?.fileUrl, { shouldDirty: true });
    });
  };

  const handleFileUpload = (event: any) => {
    const file = event.target.files;
    const formData = new FormData();
    formData.append("file", file[0]);
    api.post("/media-upload", formData).then((res) => {
      setValue("file", res.data?.fileUrl, { shouldDirty: true });
      userProfile.file = process.env.NEXT_PUBLIC_IMAGE_DOMAIN_CDN + res.data?.fileUrl;
    });
  };

  const confirmDelete = (id: string) => {
    setDeleteModal(true);
  };

  useEffect(() => {
    if (imageObs && imageObs?.length === 1 && typeof imageObs === "object") {
      handleImageUpload(imageObs);
    }
  }, [imageObs]);

  useEffect(() => {
    if (userProfile) {
      setImageUrl(userProfile.image);
      setFocus("name");
    }
  }, [userProfile]);

  const handleFileDelete = () => {
    setDeleteModal(false);
    setValue("file", "", { shouldDirty: true });
    toast.success(locale.includes("en")? "File deleted successfully": "تم حذف الملف بنجاح");
  };

  const emiratesOptions = [
    {
      label: locale.includes("en") ? "Abu Dhabi" : "أبو ظبي",
      value: "Abu Dhabi",
    },
    {
      label: locale.includes("en") ? "Ajman" : "عجمان",
      value: "Ajman",
    },
    {
      label: locale.includes("en") ? "Dubai" : "دبي",
      value: "Dubai",
    },
    {
      label: locale.includes("en") ? "Fujairah" : "الفجيرة",
      value: "Fujairah",
    },
    {
      label: locale.includes("en") ? "Ras Al Khaimah" : "رَأْس ٱلْخَيْمَة",
      value: "Ras Al Khaimah",
    },
    {
      label: locale.includes("en") ? "Sharjah" : "الشارقة",
      value: "Sharjah",
    },
    {
      label: locale.includes("en") ? "Umm Al Quwain" : "أم القيوين",
      value: "Umm Al Quwain",
    },
  ];

  const selectRef: any = useRef()

  useEffect(() => {
    if (!isLoading) {
      selectRef
      .current
      .commonProps
      .setValue(emiratesOptions.find((item: any) => item.value == userProfile?.emirates))
    }
  }, [isLoading])

  return (
    <>
      <section className="personal-info">
        <form className="personal-info_wrapper" onSubmit={handleSubmit(onSubmit)}>
          <div className="personal-info_head">
            <h2>
              {myAccount?.myProfile ?? "My Profile"}
            </h2>
            <div className="avatar">
              {imageUrl && <Image quality={100} priority src={imageUrl} fill alt="user" />}
              {!imageUrl && <ImageCreator text={userProfile?.name} phone={userProfile?.mobile} />}            </div>

            <div className="personal-info_profile-btn">
              <button onClick={deletePic}>
                <Image quality={100} priority src="/images/common/deletecon.png" width={14} height={18} alt="delete" />
              </button>

              <label>
                <input
                  type="file"
                  {...register("image")}
                  accept="image/*"
                  style={{ display: "none" }}
                />
                <Image quality={100} priority
                  style={{ width: "2.3rem" }}
                  src="/images/common/useredit.png"
                  width={24}
                  height={18}
                  alt="edit"
                />
              </label>
            </div>
          </div>

          <div className="personal-info_flex">
            <div className="personal-info_inputs">
              <label htmlFor="">{formFields?.fullName ?? "Full name"}</label>
              <input
                {...register("name", { required: formFields?.fullNameRequiredError ?? "Name is required" })}
                type="text"
                name="name"
                placeholder="Alex Smith"
              />
              <small className="form-error text-danger">{errors.name?.message}</small>
            </div>

            <div className="personal-info_inputs">
              <label htmlFor="">{formFields?.emailAddress ?? "Email Address"}</label>
              <input
                type="email"
                {...register("email", {
                  pattern: {
                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                    message: formFields?.emailAddressInvalidError ?? "Invalid email address",
                  },
                })}
                placeholder="<EMAIL>"
              />
              <small className="form-error text-danger">{errors.email?.message}</small>
            </div>

            <div className="personal-info_inputs">
              <label htmlFor="">{formFields?.phoneNumber ?? "Phone Number"}</label>
              <div className="countrycode">
                <div className="countrycode-icon">
                  <select {...register("countryCode")} disabled id="countryCode" name="countryCode">
                    <option value="+971">+971</option>
                    <option value="+91">+91</option>
                    <option value="+965">+965</option>
                  </select>
                </div>
                <input
                  disabled
                  {...register("mobile")}
                  type="number"
                  name="name"
                  placeholder="************"
                />
              </div>
            </div>

            <div className="personal-info_inputs">
              <label htmlFor="">{formFields?.emirates ?? "Emirates"}</label>

              <Select
                ref={selectRef}
                options={emiratesOptions}
                onChange={(e: any) => setValue("emirates", e?.value, { shouldDirty: true })}
                placeholder={formFields?.selectEmirates ?? "Select Emirates"}
                isLoading={isLoading}
              />
              <input
                className="inp"
                {...register("emirates", { required: formFields?.emiratesRequiredError ?? "Emirates is required" })}
                id="emirates"
                name="emirates"
              />
              <small className="form-error text-danger">{errors.emirates?.message}</small>
            </div>

            <div className="personal-info_inputs" style={{ height: "min-content" }}>
              <label htmlFor="">{formFields?.emiratesId ?? "Emirates ID"} or {formFields?.insuranceId ?? "Insurance ID"}</label>
              <input
                {...register("insuranceId", {
                  pattern: { value: /^\d{15}$/, message: formFields?.emiratesIdInvalidError ?? "Invalid Emirates Id" },
                })}
                type="text"
                name="insuranceId"
                placeholder="3264XXXXXXXXX44"
              />
              <small className="form-error text-danger">{errors.insuranceId?.message}</small>
            </div>
            <div className="personal-info_inputs">
              <label htmlFor="">{formFields?.uploadEmirates ?? "Upload Emirates Id or Insurance Id"}</label>
              {userProfile?.file && fileObs && (
                <DocumentCard items={userProfile} confirmDelete={confirmDelete} />
              )}
              {!fileObs && (
                <input
                  type="file"
                  name="file"
                  accept="image/*, application/pdf"
                  onChange={handleFileUpload}
                />
              )}
            </div>
          </div>

          <div className="personal-info_btn">
            <input
              type="submit"
              disabled={!isDirty}
              className="save button"
              value={formFields?.submit ?? "Save Changes"}
            />

            <button disabled={!isDirty} onClick={()=> {
              reset(userProfile);
              selectRef
              .current
              .commonProps
              .setValue(emiratesOptions.find((item: any) => item.value == userProfile?.emirates));
            }} type="button" className="cancel-btn button">
              {formFields?.cancel ?? "Cancel"}
            </button>
          </div>
        </form>
      </section>
      <GenericModal
        callBack={handleFileDelete}
        show={deleteModal}
        primary={locale.includes("en")? "Yes": "نعم"}
        secondary={locale.includes("en")? "No": "لا"}
        closeFn={() => setDeleteModal(false)}
        title={locale.includes("en")? "Are you sure you want to delete this file?": "هل أنت متأكد أنك تريد حذف هذا الملف؟"}
      />
    </>
  );
}

export default PersonalInfo;
