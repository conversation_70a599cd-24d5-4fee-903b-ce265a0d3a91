'use client';
// import { SettingsContext } from '@/contexts/SettingsProvider';
import React, { useEffect, useState } from 'react';


const TabbyPromo = ({ product }: any) => {
    const [isTabbyLoaded, setIsTabbyLoaded] = useState(false);
    // const { settings } = useContext(SettingsContext)
    // const [price, setPrice] = useState({
    //     price: product?.price?.aed,
    //     offerPrice: product?.offerPrice?.aed || null,
    // });

    useEffect(() => {
        // Function to load Tabby script
        const loadTabbyScript = () => {
            const script = document.createElement('script');
            script.src = 'https://checkout.tabby.ai/tabby-promo.js';
            script.async = true;
            script.onload = () => setIsTabbyLoaded(true);
            document.head.appendChild(script);
        };

        // Check if script is already loaded
        if (!document.querySelector('script[src*="tabby-promo.js"]')) {

            setTimeout(() => {
                loadTabbyScript();
            }, 7000)
            // loadTabbyScript();
        } else {
            setIsTabbyLoaded(true);
        }

        // Cleanup function to remove script on component unmount
        return () => {
            const script = document.querySelector('script[src*="tabby-promo.js"]');
            if (script) {
                script.remove();
            }
        };
    }, []); // Empty dependency array as we only want to load the script once

    useEffect(() => {
        // Initialize Tabby only when both script is loaded and price is available
        if (isTabbyLoaded && window.TabbyPromo) {
            console.log("🤣🤣🤣🤣🤣🤣");
            
            try {
                new window.TabbyPromo({
                    selector: '#TabbyPromo',
                    currency: 'AED',
                    price: 200,
                    installmentsCount: 4,
                    lang: 'en',
                    source: 'product',
                    publicKey: process.env.NEXT_PUBLIC_TABBY_PUBLIC_KEY,
                    merchantCode: 'YOUAE'
                });
            } catch (error) {
                console.error('Error initializing TabbyPromo:', error);
            }
        }
    }, [isTabbyLoaded, product]); // Dependencies: script loaded status and price

    return (
        <div className='w-full'>
             <div id="TabbyPromo"  style={{ minHeight: '30px' }} />
        </div>
    );
};

export default TabbyPromo;