"use client";

import ProductCard from "@/components/product/product-card/ProductCard";
import "./search.scss";
import { useInfiniteQuery, useQuery } from "@tanstack/react-query";
import { useRouter, useSearchParams } from "next/navigation";
import api from "@/config/axios.interceptor";
import { endpoints } from "@/config/apiEndpoints";
import SearchSuggestions from "@/components/search-suggestions/SearchSuggestions";
import { useContext, useEffect, useRef } from "react";
import { HistoryContext } from "@/contexts/HistoryProvider";
import EmptyState from "@/components/empty-states/EmptyState";
import { Spinner } from "react-bootstrap";
import LogoAnimation from "@/components/LogoAnimation/LogoAnimation";
import { InView } from "react-intersection-observer";
import { sendGTMEvent } from "@next/third-parties/google";
import { TranslationContext } from "@/contexts/Translation";
import { AuthContext } from "@/contexts/AuthProvider";
import { createHmac } from "crypto";
import Link from "next/link";
import FallbackImage from "@/components/fallback-image/FallbackImage";
import Image from "next/image";

function Page({ suggestions }: { suggestions: any }) {
  const router = useRouter();
  const resultsRef = useRef<HTMLDivElement>(null);
  const { translation: { search: translation, other } }: any = useContext(TranslationContext)

  const search = async (key: string, signal: any, { page = 1 }: any) => {
    try {
      const res = await api
        .post(
          endpoints.search,
          { keyword: key, page: page, limit: 10 },
          // { signal }
        );
      console.log(res)
      if (res.data.result.notListed) {
        router.push("/enquiry?brand=" + key);
        return new Promise((res, rej) => { });
      }
      // resultsRef.current?.scrollIntoView({
      //   behavior: "smooth",
      //   block: "center",
      //   inline: "nearest",
      // });
      return res.data.result;
    } catch (error) {
      console.log(error)
    }
  };
  const { setPrevPage } = useContext(HistoryContext);
  const params = useSearchParams();
  const searchKey = params.get("q") || "";
  const { userProfile } = useContext(AuthContext);

  const {
    data: searchData,
    error,
    fetchNextPage,
    hasNextPage,
    isFetching,
    isFetchingNextPage,
    status,
  } = useInfiniteQuery({
    queryKey: ["search", searchKey],
    queryFn: ({ pageParam, signal }) =>
      search(searchKey, signal, { page: pageParam }),
    getNextPageParam: (lastPage: any, allPages) => {
      const nextPage = lastPage?.nextPage;
      return nextPage;
    },
    enabled: searchKey?.length > 0 ? true : false,
    initialPageParam: 1, // Add this line with an appropriate initial value
  });

  useEffect(() => {
    if (typeof window !== "undefined") {
      setPrevPage({
        title: searchData?.pages?.[0]
          ? ` ${searchData?.pages?.[0]?.count} Results for ${searchKey}`
          : `Search for ${searchKey} products`,
        url: window.location.href,
      });
    }
  }, [searchData]);

  useEffect(() => {

    if (searchData && searchData?.pages?.length > 0) {
      sendGTMEvent({ ecommerce: null })
      let eventData: any = {
        event: "view_item_list",
        ecommerce: {
          item_list_id: "search-" + searchKey,
          item_list_name: "search-" + searchKey,
          items: searchData.pages.flatMap((item: any, page: number) => (
            item.products.map((product: any, index: number) => {
              let savings: undefined | number;
              let price;
              if (product?.offerPrice) {
                savings = product.offerPrice - product.price;
                price = product.offerPrice;
              } else {
                price = product.price;
              }
              return {
                item_id: product?.sku,
                item_name: product?.name,
                brand: product?.brand,
                discount: savings,
                index: (page * 15) + index,
                item_brand: product?.brand,
                item_category: product?.category?.[0]?.name,
                item_category2: product?.category?.[1]?.name,
                item_variant: product?.color?.name,
                price,
                quantity: 1,
              }
            })
          ))
        }
      }
      if (userProfile) {
        const mobile = createHmac('sha256', '123').update(userProfile?.mobile).digest('hex')
        let email = null;
        if (userProfile?.email) email = createHmac('sha256', '123').update(userProfile?.email).digest('hex')
        eventData.user = {
          mobile,
          email,
          user_id: userProfile?._id
        }
      }
      sendGTMEvent(eventData)
    }

  }, [searchData])

  return (
    <main>
      <div className="search">
        <div className="container">
          <div className="search_wrapper">
            <SearchSuggestions data={suggestions} translation={translation} />
            <div className="search_right" ref={resultsRef}>
              {((searchData?.pages?.[0]?.count === 0 && searchData?.pages?.[0]?.brands?.length === 0) && searchKey.length > 0) && (
                <h5><span>{translation?.noResult ?? "No results found"}</span>{" "}<span>{'"'}{searchKey}{'"'}</span></h5>
              )}
              {
                (searchData?.pages?.[0]?.brands?.length > 0 && searchKey.length > 0) && (
                  <>
                    <h5>{translation?.resultBrand ?? "Results by Brand"}</h5>
                    <div style={{ display: "flex", gap: "1rem", margin: "1rem 0", padding: "1rem 0" }}>
                      {searchData?.pages?.[0]?.brands?.map((brand: any, index: number) => (
                        <Link style={{ display: "flex", flexWrap: "wrap", justifyContent: "center", alignItems: "center", boxShadow: "rgba(0, 0, 0, 0.16) 0px 1px 4px", borderRadius: "1rem", padding: "1rem" }} href={`/brands/${brand?.slug}`} key={brand?._id + index}>
                          <Image
                            alt={brand?.name}
                            width={102}
                            height={128}
                            style={{ height: "60px", aspectRatio: "auto 128 / 102" }}
                            src={brand.image}
                            quality={100} />
                        </Link>
                      ))}
                    </div>
                  </>
                )
              }
              {searchData?.pages?.[0]?.count > 0 && <h5>{translation?.result ?? "Results by Product"}</h5>}
              {searchData?.pages?.[0]?.count === 0 && (
                <EmptyState icon="find" title={translation?.noResult ?? "No results found"} />
              )}
              <div className="search_products">
                {searchData?.pages?.map((page: any, index: number) =>
                  <>
                    {page?.products?.map((product: any, i: number) => {
                      let key = Object.values(product).map((v: any) => v._id).join("-") + i;
                      return (
                        <ProductCard product={product} key={key} />
                      )
                    })}
                  </>
                )}
                {/* <ProductCard product={spectacleProducts} />
                <ProductCard product={spectacleProducts} />
                <ProductCard product={spectacleProducts} /> */}
              </div>
              <div style={{ display: "flex", flexDirection: "column", alignItems: "center" }}>
                {(isFetching && searchKey.length > 0) && (
                  <div className="d-grid" style={{ placeItems: "center", padding: "60px 0" }}>
                    <Spinner />
                    {other?.loading ?? "Loading"}
                  </div>
                )}
                {/* {searchData?.pages?.[0]?.products?.length !== 0 && (
                    <button
                      onClick={() => fetchNextPage()}
                      className="load-more btn"
                      style={{ borderColor: "transparent" }}
                      disabled={!hasNextPage || isFetchingNextPage}>
                      {isFetchingNextPage
                        ? "Loading more..."
                        : hasNextPage
                          ? "Load More"
                          : "Nothing more to load"}
                    </button>
                  )} */}
              </div>
              <InView as="div" onChange={(inView, entry) => { if (inView) fetchNextPage(); }}></InView>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}

export default Page;
