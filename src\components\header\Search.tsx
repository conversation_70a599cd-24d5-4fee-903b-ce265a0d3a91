import { Form, OverlayTrigger } from "react-bootstrap";
import Image from "next/image";
import api from "@/config/axios.interceptor";
import { useEffect, useRef, useState } from "react";
import Link from "next/link";
import axios from "axios";
import { useParams } from "next/navigation";
export default function Search({
  handleSearch,
  query,
  setQuery,
  searchParams,
  closeSearch,
  tooltip,
  translation
}: any) {
  const [autoSuggest, setAutoSuggest] = useState<any>([]); // [1,2,3,4,5,6,7,8,9,10

  const autoSuggestion = (keyword: string, source: any) => {
    api.get(`/autocomplete?keyword=${keyword}`, { cancelToken: source.token, }).then((res) => {
      setAutoSuggest(res.data?.result);
    }).catch((err) => { });
  };

  const [search, setSearch] = useState<any>("")

  useEffect(() => {
    console.log(autoSuggest)
  }, [autoSuggest])

  let source:any;

  useEffect(() => {
    source = axios.CancelToken.source()
    if (search?.length < 1) setAutoSuggest([]);
    if (search?.length <= 2) return;
    autoSuggestion(search, source);

    return () => {
      source.cancel('Request canceled due to user action');
    }
  }, [search])

  const suggestionPanel = useRef<any>(null);
  const searchInput = useRef<any>(null);

  useEffect(() => {
    setQuery(searchParams?.get("q") || "");
    setAutoSuggest([]);
    source?.cancel('Request canceled due to user action');
  }, [searchParams]);
  return (
    <>
      <div className="container">
        {/* <label htmlFor="" className="d-sm-none d-block">
          {translation?.myAccount?.search ?? "Search"}
        </label> */}
        <Form onSubmit={handleSearch} className="d-flex search-icon position-relative ">
          <OverlayTrigger
            placement="bottom"
            show={query.length > 0 && !searchParams?.get("q")}
            overlay={tooltip}
            trigger={["hover", "focus"]}>
            <Form.Control
              ref={searchInput}
              onKeyDown={(e) => {
                if (e.key === "ArrowDown") {
                  if (autoSuggest.length === 0) return;

                  suggestionPanel.current.firstChild.firstChild.focus();
                }
                if (e.key === "Enter") {
                  handleSearch();
                  source.cancel('Request canceled due to user action');
                }
                if (e.key === "Escape") {
                  closeSearch();
                }
              }}
              onChange={(e) => {
                setQuery(e.target.value);
                setSearch(e.target.value);
                // router.push(`/search?q=${e.target.value}`);
              }}
              value={query}
              type="text"
              title="Hit enter to search"
              placeholder={translation?.search?.search ?? "Search"}
              enterKeyHint="search"
              className="me-2"
              aria-label="Search"
            />
          </OverlayTrigger>
          <span className="close-btn" onClick={closeSearch}>
            <Image quality={100} priority src={"/images/search/close.png"} width={24} height={24} alt="logo" />
          </span>
        </Form>

        {autoSuggest?.length > 0 && (
          <div className="autoSuggest">
            <ul ref={suggestionPanel}>
              {autoSuggest.map((item: any, index: number) => (
                <li key={index + item}>
                  <Link
                    tabIndex={0}
                    onKeyDown={(e) => {
                      if (e.key === "ArrowDown") {
                        e.preventDefault();
                        if (
                          suggestionPanel?.current &&
                          index + 1 <= suggestionPanel?.current?.children.length
                        )
                          suggestionPanel?.current?.children[index + 1]?.firstChild?.focus();
                      }
                      if (e.key === "ArrowUp") {
                        e.preventDefault();
                        if (suggestionPanel?.current && index - 1 >= 0)
                          suggestionPanel?.current?.children[index - 1]?.firstChild?.focus();
                        if (index === 0) {
                          searchInput.current.focus();
                        }
                      }
                    }}
                    href={"/search?q=" + item}>
                    {/* {item.suggestion} */}
                    <SuggestionText text={item} inputText={search} />
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </>
  );
}

function SuggestionText({ text, inputText }: { text: string, inputText: string }): any {
  const boldRef: any = useRef()
  const textRef: any = useRef()
  // useEffect(() => {
  //   boldRef.current.innerHTML = ""
  //   let currentIndex = null
  //   let splitted = inputText?.split("")
  //   for (let index in splitted) {
  //     currentIndex = index
  //     if (splitted?.[index] === text?.[index]) {
  //       boldRef.current.innerHTML += splitted?.[index]
  //     } else {
  //       break
  //     }
  //   }

  //   if (currentIndex) {
  //     textRef.current.innerHTML = text?.slice(Number(currentIndex) + 1)
  //   }

  // }, [text, inputText])
  return <>
    <p>
      <span style={{ fontWeight: "bold" }} ref={boldRef}></span>
      <span ref={textRef}></span>
      {text}
    </p>
  </>
}
