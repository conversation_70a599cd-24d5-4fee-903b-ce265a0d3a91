.blogs {

  &_head {
    margin-top: 4rem;

    @media (max-width: 575.98px) {
      margin-top: 1.5rem;
    }

    h2 {
      text-align: left;
      line-height: 4rem;
      max-width: 51.7rem;
      width: 100%;

      @media (max-width: 767.98px) {
        line-height: 3.2rem;
      }
    }
  }

  &_flex {
    display: flex;
    justify-content: space-between;
    margin-top: 2.5rem;
  }

  &_user {
    display: flex;
    column-gap: 1.5rem;

    img {
      width: 5.1rem;
      height: 5.1rem;
    }

    h5 {
      color: #000;
      font-size: 1.6rem;
      font-style: normal;
      font-weight: 500;
    }

    span {
      color: #878787;
      font-size: 1.4rem;
      font-weight: 300;
      margin-top: 0.6rem;
    }
  }

  &_icons {
    ul {
      display: flex;
      column-gap: 3.3rem;
      align-items: center;

      @media (max-width: 575.98px) {
        justify-content: center;
      }

      li {
        color: #000;
        font-size: 1.6rem;
        font-weight: 600;

        a {
          img {
            width: 2rem;
            height: 2rem;
            object-fit: contain;
            transform: scale(1);
          }

          &:hover {
            transition: all 0.3s ease;

            img {
              transform: scale(1.2);
              transition: all 0.3s ease;
            }
          }
        }
      }
    }
  }

  &_image {
    margin-top: 2.5rem;
    margin-bottom: 1rem;

    @media (max-width: 575.98px) {
      margin-top: 1.8rem;
      margin-bottom: 2rem;
    }

    img {
      border-radius: 1.3rem;
    }
  }
}

.image-content {
  margin: 0 auto;
  max-width: 80.2rem;
  width: 100%;
  padding-bottom: 5rem;

  img {
    border-radius: 1.3rem;
    margin-top: 3rem;

    @media (max-width: 575.98px) {
      margin-top: 0;
    }
  }

  p {
    margin-top: 3rem;
    line-height: 2.72rem;
  }
}

.app.rtl {
  .blogs_head h2 {
    text-align: right;
  }
}