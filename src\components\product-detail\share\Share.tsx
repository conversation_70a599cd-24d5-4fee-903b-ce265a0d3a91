"use client";

import SharePopUp from "@/components/share-popup/SharePopUp";
import { motion } from "framer-motion";
import { useState } from "react";

function Share({ data }: any) {
  const [ShareShow, setShareShow] = useState(false);
  const handleCloseShare = () => setShareShow(false);
  const handleShowShare = () => setShareShow(true);

  return (
    <>
      <motion.button
        whileHover={{ rotate: "-20deg" }}
        whileTap={{ scale: 0.95 }}
        transition={{ type: "spring", stiffness: 400, damping: 17 }}
        className="share"
        onClick={handleShowShare}>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 20 20"
          fill="none">
          <path
            d="M6.0139 9.08891C5.81212 8.72584 5.49549 8.44005 5.11371 8.2764C4.73193 8.11275 4.30662 8.08051 3.90453 8.18473C3.50245 8.28895 3.14635 8.52375 2.89214 8.85225C2.63793 9.18075 2.5 9.58437 2.5 9.99975C2.5 10.4151 2.63793 10.8187 2.89214 11.1472C3.14635 11.4757 3.50245 11.7105 3.90453 11.8148C4.30662 11.919 4.73193 11.8867 5.11371 11.7231C5.49549 11.5594 5.81212 11.2737 6.0139 10.9106M6.0139 9.08891C6.1639 9.35891 6.24974 9.66891 6.24974 9.99975C6.24974 10.3306 6.1639 10.6414 6.0139 10.9106M6.0139 9.08891L13.9856 4.66058M6.0139 10.9106L13.9856 15.3389M13.9856 4.66058C14.1025 4.8807 14.262 5.0753 14.455 5.23302C14.648 5.39074 14.8704 5.50841 15.1094 5.57914C15.3484 5.64988 15.5991 5.67227 15.8468 5.64499C16.0945 5.61772 16.3343 5.54133 16.5522 5.4203C16.77 5.29927 16.9616 5.13602 17.1156 4.94009C17.2696 4.74417 17.3831 4.5195 17.4493 4.27923C17.5154 4.03896 17.5331 3.78791 17.5011 3.54074C17.4691 3.29358 17.3882 3.05527 17.2631 2.83975C17.0165 2.41503 16.6131 2.10403 16.1396 1.97359C15.6661 1.84316 15.1603 1.9037 14.731 2.14221C14.3017 2.38072 13.9831 2.77815 13.8437 3.24908C13.7043 3.72 13.7552 4.22682 13.9856 4.66058ZM13.9856 15.3389C13.866 15.5542 13.7899 15.791 13.7618 16.0357C13.7337 16.2804 13.7541 16.5282 13.8217 16.7651C13.8894 17.0019 14.0031 17.2231 14.1562 17.416C14.3094 17.6089 14.499 17.7697 14.7143 17.8893C14.9296 18.0089 15.1664 18.085 15.4111 18.1131C15.6558 18.1412 15.9036 18.1208 16.1405 18.0532C16.3773 17.9855 16.5985 17.8718 16.7914 17.7187C16.9843 17.5655 17.1451 17.3759 17.2647 17.1606C17.5063 16.7257 17.5652 16.2127 17.4286 15.7344C17.2919 15.2561 16.9708 14.8517 16.536 14.6102C16.1011 14.3686 15.5881 14.3097 15.1099 14.4463C14.6316 14.583 14.2271 14.9041 13.9856 15.3389Z"
            stroke="#3A4980"
            stroke-width="1.5"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </motion.button>
      <SharePopUp show={ShareShow} data={data} handleClose={handleCloseShare} />
    </>
  );
}

export default Share;
