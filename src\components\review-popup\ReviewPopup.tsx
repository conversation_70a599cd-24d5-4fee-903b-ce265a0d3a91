import Image from "next/image";
import "./review-popup.scss";
import Modal from "react-bootstrap/Modal";
import StarRating from "../star-rating/StarRating";
import EmptyState from "../empty-states/EmptyState";

function ReviewPopup({ show, handleClose, reviews, rating, count }: any) {
  return (
    <>
      <Modal
        show={show}
        onHide={handleClose}
        backdrop="static"
        keyboard={false}
        centered
        className="review-popup">
        <Modal.Header closeButton></Modal.Header>
        <Modal.Body>
          <h2>Rating and Reviews ({count || 0})</h2>

          <div className="review-popup_starts">
            <span className="rate">{rating}</span>
            <StarRating color="#d48d3b" defaultRating={rating} disabled />
          </div>

          <div className="review-popup_scroll">
            {reviews.map((items: any) => (
              <div key={items?._id} className="review-popup_box">
                <Image quality={100} priority src="/images/common/user3.png" width={64} height={64} alt="user image" />
                <div className="review-popup_content">
                  <p>{items.message}</p>

                  <div className="review-popup_footer">
                    <h6>{items?.customer}</h6>
                    <span>{new Date(items.date).toDateString()}</span>
                  </div>
                </div>
              </div>
            ))}

            {reviews.length === 0 && <EmptyState title="No reviews found" icon="x-doc" />}
          </div>
        </Modal.Body>
      </Modal>
    </>
  );
}

export default ReviewPopup;
