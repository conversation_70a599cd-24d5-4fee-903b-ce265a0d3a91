.timeline {
  background: #f2f4f9;
  margin-top: 7.4rem;
  padding: 5rem 0;
  overflow: hidden;

  @media (max-width: 575.98px) {
    margin-top: 5rem;
  }

  &_head {
    p {
      margin: 0 auto;
      margin-top: 1.5rem;
      max-width: 71.8rem;
      width: 100%;
      text-align: center;
      line-height: 2.72rem;
    }

    h2 {
      font-size: 3.2rem;
      line-height: 4.2rem;
    }
  }

  &_wrapper {
    position: relative;
    padding-top: 3rem;
    width: calc(100% - 8.4rem);
    margin: 0 auto;

    @media (max-width: 575.98px) {
      width: 100%;
      padding-top: 10rem;
    }

    @media (min-width: 1400px) {
      padding-left: 7rem;
      padding-right: 7rem;
    }

    &:before {
      content: "";
      position: absolute;
      left: 50%;
      width: 1px;
      height: 100%;
      background: #000;

      @media (max-width: 767px) {
        height: 100%;
        top: 87px;
      }
    }

    ul li {
      position: relative;
      width: 40%;

      @media (max-width: 767.98px) {
        width: 100%;
        animation: auto cubic-bezier(0.075, 0.82, 0.165, 1) slide-from-bottom both !important;

        &:last-child {
          padding-bottom: 0;
        }

        &:not(:last-child) {
          padding-bottom: 11rem !important;
        }

        &:nth-last-child(2) {
          padding-bottom: 0 !important;
        }
      }

      &:nth-child(odd) {
        float: left;
        text-align: right;
        clear: both;
        animation: auto cubic-bezier(0.075, 0.82, 0.165, 1) slide-from-left both;
        animation-timeline: view();
        animation-range: entry 25% cover 40%;

        &::before {
          content: "";
          position: absolute;
          width: 5.3rem;
          height: 5.3rem;
          top: 0px;
          right: -132px;
          background: #000;
          border-radius: 50%;

          @media (max-width: 1199.98px) {
            right: -117px;
          }

          @media (max-width: 991.98px) {
            right: -101px;
          }
        }

        &::after {
          content: "";
          background-image: url(../../../../public/images/about/arrow.png);
          background-repeat: no-repeat;
          background-position: right;
          width: 20px;
          height: 20px;
          position: absolute;
          top: 15px;
          right: -17px;
        }
      }

      &:nth-child(even) {
        animation: auto linear slide-from-right both;
        animation-timeline: view();
        animation-range: entry 25% cover 40%;
        float: right;
        text-align: left;
        clear: both;

        &::before {
          content: "";
          position: absolute;
          width: 5.3rem;
          height: 5.3rem;
          top: 0px;
          left: -132px;
          background: #000;
          border-radius: 50%;

          @media (max-width: 1199.98px) {
            left: -117px;
          }

          @media (max-width: 991.98px) {
            left: -101px;
          }
        }

        &::after {
          content: "";
          background-image: url(../../../../public/images/about/arrow.png);
          background-repeat: no-repeat;
          background-position: left;
          width: 20px;
          height: 20px;
          position: absolute;
          top: 15px;
          left: -13px;
          transform: rotate(60deg);
        }
      }



      .content {
        padding: 2rem;
        border-radius: 1.3rem;
        background: #fff;
        text-align: left;

        @media (max-width: 575.98px) {
          padding: 1.5rem;
          font-size: 1.8rem;
          line-height: 2.0493rem;
        }

        img {
          border-radius: 1.3rem;

          @media (max-width: 575.98px) {
            border-radius: 0.9rem;
          }
        }

        h3 {
          color: #000;
          font-size: 2.4rem;
          font-weight: 600;
          max-width: 37.9rem;
          margin-top: 2rem;
          line-height: 2.7rem;

          @media (max-width: 767.98px) {
            max-width: 100%;
          }

          @media (max-width: 575.98px) {
            font-size: 1.8rem;
            line-height: 2.0493rem;
          }
        }

        p {
          margin-top: 1.5rem;
          max-width: 37.9rem;
          width: 100%;

          @media (max-width: 767.98px) {
            max-width: 100%;
          }

          @media (max-width: 575.98px) {
            font-size: 1.2rem;
            line-height: 2.0493rem;
          }
        }
      }

      .time {
        h4 {
          font-size: 1.6rem;
          font-weight: 600;
        }
      }

      &:nth-child(odd) .time {
        position: absolute;
        top: 15px;
        right: -122px;
        color: #fff;

        @media (max-width: 1199.98px) {
          right: -108px;
        }

        @media (max-width: 991.98px) {
          right: -92px;
        }
      }

      &:not(:first-child):nth-child(odd) .time {
        h4 {
          transform: translateX(3px);
        }
      }

      &:nth-child(even) .time {
        position: absolute;
        top: 15px;
        left: -122px;
        color: #fff;

        @media (max-width: 1199.98px) {
          left: -108px;
        }

        @media (max-width: 991.98px) {
          left: -92px;
        }
      }

      &:not(:nth-child(2)):nth-child(even) .time {
        h4 {
          transform: translateX(-3px);
        }
      }
    }
  }
}

@media (max-width: 767px) {

  .timeline ul li:nth-child(odd),
  .timeline ul li:nth-child(even) {
    text-align: left;
    padding-bottom: 0rem;
  }

  .timeline ul li:nth-child(odd):before,
  .timeline ul li:nth-child(even):before {
    top: -66px;
    left: 50%;
    transform: translateX(-50%);
  }

  .timeline ul li:nth-child(odd) .time,
  .timeline ul li:nth-child(even) .time {
    top: -50px;
    left: 50%;
    transform: translateX(-50%);
    right: inherit;
  }
}

@keyframes slide-from-left {
  from {
    opacity: 0;
    transform: translateX(-100%);
    filter: blur(5px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
    filter: blur(0);
  }
}

@keyframes slide-from-right {
  from {
    opacity: 0;
    transform: translateX(100%);
    filter: blur(5px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
    filter: blur(0);
  }
}

@keyframes slide-from-bottom {
  from {
    opacity: 0;
    transform: translateY(100%);
    filter: blur(5px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
    filter: blur(0);
  }
}