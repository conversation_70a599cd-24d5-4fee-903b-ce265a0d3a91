.brands-popup {
  &.modal {
    &.show .modal-dialog {
      transform: none !important;
    }

    .modal-dialog {
      margin: 0;
      max-width: 100%;
    }

    .modal-header {
      padding: 0;
      border: none;

      .btn-close {
        background-image: url(../../../../public/images/common/close.svg);
        width: 4.8rem;
        height: 4.8rem;
        background-position: center;
        background-repeat: no-repeat;
        background-size: contain;
        opacity: 1;
        padding: 0;

        @media (max-width: 575.98px) {
          width: 2.8rem;
          height: 2.8rem;
        }

        &:focus {
          box-shadow: none;
          outline: none;
          border: none;
        }
      }


      h2 {
        text-align: left;
        line-height: 4rem;
        display: flex;
        align-items: center;
        column-gap: 2rem;

        @media (max-width: 575.98px) {
          line-height: 3rem;
          margin-bottom: 1.8rem;
        }

        img {
          width: 4rem;
          height: 4rem;
          object-fit: contain;

          @media (max-width: 575.98px) {
            display: none;
          }
        }
      }
    }

    .modal-content {
      border: none;
      padding: 1.8rem 5.3rem 4.2rem 5.3rem;
      border-radius: 0;
      width: 100%;
      max-width: 68.3rem;
      position: fixed;
      right: 0;
      overflow-y: auto;
      height: 100%;

      @media (max-width: 575.98px) {
        padding: 2rem 4rem 4.2rem 4rem;
      }
    }

    .modal-body {
      padding: 0;
      display: flex;
      flex-direction: column;



      button {
        margin-top: 3rem;
        color: #fff;
        text-align: center;
        font-size: 1.5rem;
        font-weight: 500;
        border: none;
        background-color: #000;
        height: 5.6rem;
        width: 40rem;
        border-radius: 6rem;

        @media (max-width: 575.98px) {
          width: auto;
          height: 4.5rem;
          margin-top: 3.8rem;
        }
      }
    }
  }

  &_flex {
    display: flex;
    align-items: center;
    border-radius: 1.5rem;
    position: relative;

    span.check {
      position: absolute;
      width: 33px;
      height: 33px;
      background-color: #000;
      z-index: 5;
      display: grid;
      place-items: center;
      border-radius: 50%;
      margin-left: 20px;
    }

    cursor: pointer;

    &:not(:last-child) {
      margin-bottom: 1.5rem;
    }
  }

  &_image {
    clip-path: polygon(0 0, 85% 0, 100% 100%, 0% 100%);
    width: 60%;

    img {
      border-radius: 1.5rem 0 0 1.5rem;
    }
  }

  &_content {
    width: 40%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding-left: 1.5rem;
    padding-right: 1.5rem;

    img {
      object-fit: contain;
      mix-blend-mode: multiply;
    }

    h5 {
      color: #000;
      font-size: 1.6rem;
      font-weight: 600;
      line-height: 2rem;
      max-width: 10.8rem;
      width: 100%;

      @media (max-width: 575.98px) {
        font-size: 1.1121rem;
        line-height: 1.4rem;
      }
    }

    span {
      margin-top: 1rem;
      display: inline-block;
      font-size: 1.6rem;
      font-weight: 700;
      line-height: 2rem;

      @media (max-width: 575.98px) {
        font-size: 1.1121rem;
        line-height: 1.7rem;
      }
    }
  }
}