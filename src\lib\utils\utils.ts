export const imageToBase64 = async (file: File) => {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result);
        reader.onerror = error => reject(error);
    });
}

export const objectToFormData = (obj: any) => {
    const formData = new FormData();
    Object.keys(obj).forEach((key) => {
        if (key === "file" || key === "image") {
            if (obj[key] instanceof FileList) {
                formData.append(key, obj[key][0]);
            } else {
                formData.append(key, obj[key]);
            }
        } else {
            formData.append(key, obj[key]);
        }
    });
    return formData;
}

export function sortPower(a:any, b:any) {
    if(a?.name?.[0] === "+" && b?.name?.[0] === "-") return 1;
    if(a?.name?.[0] === "-" && b?.name?.[0] === "+") return -1;
    if(a?.name?.[0] === b?.name?.[0] && (a?.name?.[0] === "+" || a?.name?.[0] === "-")){
      if(Number(a?.name?.slice(1)) > Number(b?.name?.slice(1))){
        // if(a?.name?.[0] === "+") return -1;
        // if(a?.name?.[0] === "-") return 1;  
        return 1;
      }else{
        // if(a?.name?.[0] === "+") return 1;
        // if(a?.name?.[0] === "-") return -1;
        return -1;
      }
    }
    if(a?.name?.[0] !== "+" && a?.name?.[0] !== "-") return -1
    if(b?.name?.[0] !== "-" && b?.name?.[0] !== "+") return 1
  }