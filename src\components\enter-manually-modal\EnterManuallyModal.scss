.enter-manually {
    padding: 4rem 5rem 0rem 6.2rem;

    @media (max-width: 575.98px) {
        padding: 1.5rem;
        padding-bottom: 2.5rem;
    }

    &_header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 2rem;

        h2 {
            text-align: left;
            line-height: 3.1rem;
            display: flex;
            align-items: center;
            column-gap: 1.4rem;
            font-size: 2.5rem;
            font-weight: 500;

            @media (max-width: 575.98px) {
                font-size: 2.2rem;
                color: #242731;
            }

            img {
                width: 4rem;
                height: 4rem;
                object-fit: contain;

                @media (max-width: 575.98px) {
                    display: none;
                }
            }
        }

        button {
            background-color: transparent;
            border: none;
            padding: 0;
        }
    }

    &_box {
        display: flex;
        border: 1px solid #E4E4E4;
        border-radius: 1.5rem;

        @media (max-width: 575.98px) {
            flex-direction: column;
            padding: 1.5rem;
        }

        &:not(:last-child) {
            margin-bottom: 2rem;

            @media (max-width: 575.98px) {
                margin-bottom: 1.5rem;
            }
        }

        span {
            background-color: #F2F4F9;
            font-size: 1.3rem;
            font-weight: 400;
            border-radius: 1.5rem 0rem 0 1.5rem;
            border-right: 1px solid #E4E4E4;
            width: 23%;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: #000000;

            @media (max-width: 575.98px) {
                width: 100%;
                background-color: transparent;
                justify-content: flex-start;
                font-size: 1.4rem;
                font-weight: 500;
                border: none;
            }
        }
    }

    &_boxflex {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        column-gap: 3.2rem;
        padding: 1.8rem 4rem 2.5rem 2.3rem;
        flex: 1;
        row-gap: 2.3rem;

        @media (max-width: 575.98px) {
            padding: 0;
            margin-top: 1.8rem;
            grid-template-columns: repeat(2, 1fr);
        }


        input[type="number"] {
            width: 100px;
            border: 1px solid rgba(0, 0, 0, 0.1490196078);
            border-radius: 7px;
            padding: 11px;

            &:focus-within {
                outline: none;
                border: 1px solid rgba(0, 0, 0, 0.1490196078);

            }
        }

        input[type="text"] {
            max-width: 20rem;
            border: 1px solid rgba(0, 0, 0, 0.1490196078);
            border-radius: 7px;
            padding: 11px;

            &:focus-within {
                outline: none;
                border: 1px solid rgba(0, 0, 0, 0.1490196078);

            }
        }

    }

    &_max-content {
        width: max-content;
    }

    &_select {
        flex: 1;

        label {
            font-size: 1.3rem;
            font-weight: 400;
            line-height: 1.6rem;
            color: #000000;
            padding-bottom: 1rem;
            display: flex;
            align-items: center;
            column-gap: 1.2rem;

            input {
                width: 1.8rem;
                height: 1.8rem;
                border-radius: 50%;
                appearance: none;
                border: 1px solid #000000;
                position: relative;

                &:checked::after {
                    content: "";
                    position: absolute;
                    width: 1rem;
                    height: 1rem;
                    border-radius: 50%;
                    background-color: #000000;
                    left: 50%;
                    top: 50%;
                    transform: translate(-50%, -50%);
                }
            }
        }

        select {
            color: #242426;
            font-size: 1.3rem;
            font-weight: 400;
            line-height: 2.8rem;
            border: none;
            border-bottom: 1px solid #00000026;
            appearance: none;
            display: flex;
            background-image: url(../../../public/images/modal/Arrow-Down1.svg);
            background-repeat: no-repeat;
            background-position: 95% 59%;
            background-size: 14px;
            width: 100%;
            color: #242426;
            opacity: 50%;

            &::placeholder {
                color: #242426;
                opacity: 50%;
            }


            &:focus-within {
                outline: none;
                border-bottom: 1px solid #00000026;
            }
        }

        .error {
            color: red;
            font-size: 1.4rem;
            line-height: 2rem;
            padding-bottom: 0;
            padding-top: 0 !important;
        }

        .inp{
            display: none;
        }

            p {
              font-size: 1.3rem;
              font-weight: 400;
              line-height: 1.9rem;
              letter-spacing: -0.011em;
              color: rgba(0, 0, 0, 0.8);
              margin-top: 1.5rem;
              opacity: 50%;
            }
  
            .css-1jqq78o-placeholder {
              font-size: 1.3rem;
              font-weight: 400;
              color: #999999;
              line-height: 1.6rem;
            }
  
            .css-13cymwt-control {
              border: none;
              border-bottom: 1px solid rgba(0, 0, 0, 0.15) !important;
              border-radius: 0;
              max-width: 33.8rem;
              width: 100%;
  
              .css-1fdsijx-ValueContainer {
                padding: 0 !important;
                margin: 0 !important;
  
                .css-qbdosj-Input {
                  padding: 0;
                  margin: 0;
                }
              }
  
              .css-1u9des2-indicatorSeparator {
                display: none;
              }
  
              .css-1xc3v61-indicatorContainer {
                padding-right: 0;
              }
  
              .css-13cymwt-control {
                background-color: red;
  
                &:hover {
                  border: none;
                  background-color: blue;
                }
              }
            }
  
            .css-1dimb5e-singleValue {
              color: #000000;
              font-size: 1.3rem;
              font-weight: 400;
            }
  
            .css-t3ipsp-control {
              border: none !important;
              border-bottom: 1px solid rgba(0, 0, 0, 0.15) !important;
              outline: none;
              box-shadow: none;
              padding: 0;
              max-width: 33.8rem;
              width: 100%;
  
              .css-1fdsijx-ValueContainer {
                padding: 0;
                margin: 0;
              }
  
              .css-1xc3v61-indicatorContainer {
                padding-right: 0;
              }
            }
  
            .css-1nmdiq5-menu {
              color: #000000;
              font-size: 1.3rem;
              font-weight: 400;
              z-index: 10;
  
              div {
                &>div {
                  transition: .2s;
                  &[aria-selected="true"] {
                    background-color: black;
                    color: white;
                  }
                  &[aria-selected="false"] {
                    background-color: white;
                    color: black;
                  }
                  &:hover {
                    background-color: black;
                    color: white;
                  }
                }
              }
  
            }
  
            .css-1u9des2-indicatorSeparator {
              display: none;
            }
    }
}