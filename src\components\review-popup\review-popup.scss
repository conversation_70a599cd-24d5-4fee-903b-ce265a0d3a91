.review-popup {
  &.modal {
    .modal-dialog-centered {
      @media (max-width: 575.98px) {
        align-items: flex-end;
      }
    }

    .modal-dialog {
      max-width: 88.6rem;
      width: 100%;
      margin: 0 auto;
    }

    .modal-header {
      padding: 0;
      border: none;

      .btn-close {
        background-image: url(../../../public/images/common/close.png);
        width: 4.8rem;
        height: 4.8rem;
        background-position: center;
        background-repeat: no-repeat;
        background-size: contain;
        opacity: 1;
        padding: 0;
        transform: translateX(33px);

        @media (max-width: 575.98px) {
          width: 2.8rem;
          height: 2.8rem;
          transform: none;
        }

        &:focus {
          box-shadow: none;
          outline: none;
          border: none;
        }
      }
    }

    .modal-content {
      border: none;
      border-radius: 3rem;
      padding: 1.8rem 5.3rem 0rem 5.3rem;

      @media (max-width: 575.98px) {
        padding: 2rem 2rem 5.7rem 2rem;
        border-radius: 2rem 2rem 0rem 0rem;
      }
    }

    .modal-body {
      padding: 0;
      display: flex;
      flex-direction: column;

      h2 {
        text-align: left;
        line-height: 4rem;
        display: flex;
        align-items: center;
        column-gap: 2rem;

        @media (max-width: 575.98px) {
          line-height: 3rem;
          font-size: 2.4rem;
        }

        img {
          width: 4rem;
          height: 4rem;
          object-fit: contain;

          @media (max-width: 575.98px) {
            display: none;
          }
        }
      }
    }
  }

  &_scroll {
    overflow-y: auto;
    height: 45.6rem;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  &_starts {
    display: flex;
    column-gap: 1rem;
    align-items: center;
    padding-bottom: 1.7rem;
    margin: 1.5rem 0;
    border-bottom: 1px solid #e4e4e4;

    @media (max-width: 575.98px) {
      margin-top: 1rem;
      padding-bottom: 1.4rem;
    }

    .rate {
      color: #d48d3b;
      font-size: 2.5rem;
      font-weight: 500;
      line-height: 3.1rem;

      @media (max-width: 575.98px) {
        font-size: 1.8rem;
        line-height: 2.2rem;
      }
    }

    .star-svg {
      width: 2.9rem;
      height: 2.9rem;

      @media (max-width: 575.98px) {
        width: 2.1rem;
      }
    }
  }

  &_box {
    border-radius: 1.5rem;
    border: 1px solid #e4e4e4;
    display: flex;
    column-gap: 1.5rem;
    padding: 2rem 2.7rem 1.5rem 2rem;

    @media (max-width: 575.98px) {
      padding: 1.5rem;
      flex-direction: column;
    }

    &:not(:last-child) {
      margin-bottom: 1.5rem;
    }

    img {
      width: 6.4rem;
      height: 6.4rem;
      border-radius: 50%;
    }

    p {
      color: #202020;
      font-size: 1.4rem;
      font-weight: 400;
      line-height: 2.3rem;
      max-width: 54.4rem;
      width: 100%;
    }
  }

  &_content {
    width: 100%;

    @media (max-width: 575.98px) {
      margin-top: 1.5rem;
    }
  }

  &_footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 1.8rem;

    h6 {
      color: #202020;
      font-size: 1.4rem;
      font-weight: 600;
    }

    span {
      color: #575f6e;
      font-size: 1.4rem;
      font-weight: 400;
    }
  }
}
