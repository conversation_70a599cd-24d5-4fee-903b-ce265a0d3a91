.home-body {
  .header {
    position: absolute;
    background-color: transparent;
    top: 54px;
    // top: 37px;
    background-color: #000000a3;

    @media (max-width: 670px) {
      top: 17px;
    }

    &_navbar {
      @media (max-width: 670px) {
        padding: 0;
      }
    }
  }
}

.no-after::after {
  display: none;
}

.header {
  position: relative;
  width: 100%;
  z-index: 99;
  background-color: #111827;
  top: 0;
  // transition: all 0.8s ease;

  @media (max-width: 670px) {
    padding: 1.5rem 0;
  }

  &.sticky {
    position: sticky;
    top: 0;
    // transition: all 0.8s ease;
    background-color: #111827;

    @media screen and (max-width: 991.98px) {
      padding: 1.5rem 0;
    }

    @media screen and (max-width: 575.98px) {
      padding: 1.5rem 0;
    }

    .header_navbar {
      gap: 1rem;

      @media (max-width: 991.98px) {
        padding: 0;
      }
    }
  }

  &.sticky.home-nav {
    background-color: #111827;
    transition: all 0.8s ease;
  }

  &_wrapper {
    &.hidden {
      display: none;
    }
  }

  &_navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 2.5rem 0;
    gap: 1rem;

    @media (max-width: 670px) {
      padding: 0;
    }

    .navbar-toggler {
      border: none;
      padding: 0;

      .navbar-toggler-icon {
        background-image: url(../../../public/images/common/menu-1.svg);
        background-position: unset;
        width: 2.4rem;
        height: 2.4rem;
      }

      &:focus {
        box-shadow: none;
        border: none;
        outline: none;
      }
    }

    .offcanvas-body {
      justify-content: space-between;
    }

    .searchIcon {
      object-fit: contain;
    }

    &-logo {
      width: 15.3rem;
      height: 3.5372rem;
      object-fit: contain;

      @media (max-width: 575.98px) {
        width: 13.9129rem;
        height: 3.6402rem;
      }
    }
  }

  &_links {
    display: flex;
    column-gap: 3.4rem;
    margin: 0 auto;
    align-items: flex-end;

    @media (max-width: 1199.98px) {
      column-gap: 2rem;
      display: none !important;
    }

    a {
      font-size: 1.6rem;
      font-style: normal;
      font-weight: 400;
      color: #fff;
      position: relative;
      padding-bottom: 1px;

      &::after {
        content: "";
        border-bottom: solid 2px #ffffff;
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        transform: scaleX(0);
        transform-origin: right;
        transition: transform 0.25s;
      }

      &:hover {
        color: #fff;

        &::after {
          transform-origin: left;
          transform: scaleX(1);
        }
      }

      @keyframes a {
        from {
          transform: scaleX(0);
        }

        to {
          transform: scaleX(1);
        }
      }
    }
  }

  &_mobileMenu {
    @media (min-width: 1200px) {
      display: none !important;
    }
  }

  &_iconlist {
    display: flex;
    align-items: center;
    column-gap: 3rem;

    select,
    .select {
      background-color: transparent;
      color: #fff !important;
      border: none;
      appearance: none;
      cursor: pointer;
      z-index: 200;
      border: none;
      color: white !important;

      .indicators-container {
        div {
          padding: 0;
        }
      }

      .css-1fdsijx-ValueContainer {
        padding-right: 0;
      }

      .css-tj5bde-Svg {
        filter: brightness(0) saturate(100%) invert(100%) sepia(12%)
          saturate(7453%) hue-rotate(198deg) brightness(109%) contrast(110%);
      }

      .drop-item {
        display: flex;
        align-items: center;
        column-gap: 8px;
        color: white !important;
        font-size: 1.3rem;

        img{
          width: 2rem;
          height: 2rem;
        }
      }

      &:focus-visible {
        border: none;
        outline: none;
      }

      option {
        color: #fff;
        font-size: 1.3rem;
        font-weight: 400;
        background-color: #000;
        cursor: pointer;

        &:hover {
          background-color: #e0e0e0 !important;
        }
      }
    }

    @media (min-width: 1200px) {

      // li:nth-of-type(1),
      // li:nth-of-type(2),
      // li:nth-of-type(3),
      li {
        transition: all 300ms ease;

        &:not(:has(#dropdown-profile)):hover {
          // transform: translateY(-2px);
          filter: drop-shadow(0px 0px 1px white);
        }
      }
    }

    .dropdown {

      &-item,
      &-item a {
        color: #000;
        font-size: 1.6rem;
        font-weight: 400;
        line-height: 2rem;
        padding: 0;
        display: flex;
        align-items: center;
        column-gap: 1.2rem;

        img {
          width: auto;
          // height: auto;
        }

        &:not(:last-child) {
          margin-bottom: 2.2rem;
        }

        &:active {
          background-color: transparent !important;
          color: #000 !important;
        }

        &:hover {
          background-color: transparent !important;
          text-decoration: underline;
        }
      }

      &-menu {
        &[data-bs-popper] {
          left: unset;
          right: 0;
          padding: 2.4rem 2rem 2.8rem 2rem;
          border-radius: 1rem;
          background: #fff;
          box-shadow: 0px 2px 10px 4px rgba(182, 182, 182, 0.25);
          min-width: 23.7rem;
          top: 5rem;

          &::after {
            content: "";
            background-image: url(../../../public/images/common/Polygon.png);
            background-repeat: no-repeat;
            position: absolute;
            top: -9px;
            right: 5px;
            width: 1.7rem;
            height: 1.5rem;
          }
        }
      }
    }

    .dropdown-toggle {
      background-color: transparent !important;
      border: none;
      padding: 0;
      position: relative;

      .white-dot {
        position: absolute;
        right: -7px;
        top: -4px;
        width: 15px;
        height: 15px;
        border-radius: 50%;
        color: #000;
        background-color: #fff;
        // &::after {
        //   content: "";
        //   display: inline-block;
        //   border-radius: 50%;
        //   top: 0;
        //   right: -5px;
        //   transform-origin: 6px 6px;
        //   width: 10px;
        //   height: 10px;
        //   background-color: #fff;
        //   animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
        // }
      }

      &:hover {
        background-color: transparent !important;
      }

      &:active {
        background-color: transparent !important;
      }

      &::after {
        content: none;
      }
    }

    @media (min-width: 1200px) {
      li {
        &.try-cart {
          position: relative;

          button {
            border: none;
            border-radius: 0.5rem;
            background: #fff;
            color: #000;
            font-size: 1.3rem;
            font-weight: 400;
            text-transform: uppercase;
            width: 10rem;
            height: 3.3rem;
            display: flex;
            align-items: center;
            justify-content: center;
            column-gap: 0.6rem;
            padding: 0;

            img {
              width: 2.5001rem;
              height: 1.9714rem;
            }
          }
        }
      }

      &-link {
        display: none;
      }
    }

    @media (max-width: 1199.98px) {
      align-items: flex-start;
      column-gap: 3rem;
      flex-direction: column;
      margin-top: 1.5rem;

      li {
        display: flex;
        width: 100%;
        column-gap: 1rem;
        margin-bottom: 1.4rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        padding: 0 1.3rem 1.4rem 1.3rem;

        a {
          font-size: 1.7rem;
          font-weight: 400;
          color: #fff;
        }

        img {
          width: auto;
        }

        .drop-item{
          display: flex;
          gap: 8px;
          align-items: center;
          img{
            width: 2rem;
            height: 2rem;
          }
        }
      }
    }
  }

  &_intro {
    h4 {
      color: #fff;
      font-size: 2.4rem;
      font-weight: 400;
      line-height: 3rem;
    }

    a {
      color: #fff;
      font-size: 1.3rem;
      font-weight: 300;
      line-height: 1.6rem;
    }
  }

  &_log {
    &.btn {
      position: absolute;
      right: 0rem;
    }

    display: flex;
    flex-direction: column;
    align-items: center;

    img {
      width: auto;
    }

    span {
      font-size: 1.2rem;
      font-weight: 300;
      color: #fff;
      margin-top: 3px;
      display: block;
    }
  }
}

// MOBILE MENU STYLE

.main-body {
  .offcanvas {
    @media (max-width: 1199.98px) {
      background-color: #111827;

      .btn-close {
        filter: invert(1);
        opacity: 1;
      }

      &-header {
        padding-top: 3rem;
        padding-bottom: 2.5rem;
      }

      &-body {
        padding: 20px 4.5rem;
        flex-grow: 1;
        height: 320px;
        overflow-y: auto;

        &::-webkit-scrollbar {
          display: none;
        }
      }
    }

    @media (max-width: 575.98px) {
      &.show {
        width: 100%;
      }
    }
  }
}

.bottom-sheet-profile {
  flex-direction: column;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  padding: 0 4.5rem;
  overflow-y: auto;
  min-height: calc(100% - 420px);

  &::-webkit-scrollbar {
    display: none;
  }

  span {
    width: 40px;
    height: 4px;
    border-radius: 2px;
    background-color: #d0d0d0;
    display: block;
  }

  .text-center {
    margin-top: auto;
    font-size: 14px;
    padding-top: 20px;
    padding-bottom: 20px;
  }

  ul {
    padding: 10px 0 20px;

    li {
      position: relative;
      padding: 0 1.4rem;

      a {
        color: #000;
        font-size: 1.7rem;
        font-weight: 400;
        padding: 0 1.6rem;
        cursor: pointer;
        padding: 0;
        position: relative;
        display: inline-block;
        width: 100%;


        &::after {
          content: "";
          position: absolute;
          background-image: url(../../../public/images/common/arrow-down.png);
          background-position: right;
          width: 1.2rem;
          height: 1.2rem;
          background-repeat: no-repeat;
          background-size: contain;
          top: 50%;
          transform: translateY(-50%);
          right: 10px;
        }
      }



      &:not(:last-child) {
        margin-bottom: 1.4rem;
        padding-bottom: 1.4rem;
        border-bottom: 1px solid rgba(163, 163, 163, 0.2);
      }
    }
  }

  .accordion {
    .accordion-item {
      border: none;
      box-shadow: none;
      background-color: transparent;

      &:not(:last-child) {
        margin-bottom: 1.4rem;
        padding-bottom: 2rem;
        border-bottom: 1px solid #A3A3A333;
      }
    }

    .accordion-button {
      font-size: 1.7rem;
      font-weight: 400;
      line-height: 2.41rem;
      color: #000000;
      background-color: transparent;
      box-shadow: none;
      padding: 0;

      &::after {
        background-image: url(../../../public/icons/ionic-ios-arrow-down.svg);
        background-size: contain;
      }

      &:focus {
        box-shadow: none;
      }
    }

    .accordion-body {
      padding: 0;

      ul {
        padding-top: 8px;
        padding-bottom: 1.3rem;
        margin-left: 2.1rem;

        li {
          margin: 0;
          padding: 0;
          font-size: 1.6rem;
          font-weight: 300;
          line-height: 3rem;
          border: none;
          color: #000000;

          &:not(:last-child) {
            margin-bottom: 10px;
          }
        }
      }
    }

  }
}

.sub-menu {
  background-color: #fff;
  position: absolute;
  width: 100vw;
  left: 0;
  z-index: 500;
  top: 93%;

  a {
    overflow: hidden;

    img {
      transition: all 800ms cubic-bezier(0.075, 0.82, 0.165, 1);
    }

    &:hover img {
      scale: 1.2;
      rotate: -5deg;
    }

    &::after {
      content: "";
      background: linear-gradient(180deg, rgba(0, 0, 0, 0) 58.85%, #000 100%);
      position: absolute;
      height: 100%;
      width: 100%;
      left: 0;
    }
  }

  span {
    color: #fff;
    font-size: 2.3rem;
    font-weight: 600;
    position: absolute;
    left: 3.5rem;
    bottom: 2.1rem;
    z-index: 2;

    @media (max-width: 575.98px) {
      font-size: 2rem;
      font-weight: 500;
      letter-spacing: 0.1rem;
    }
  }
}

.menuWrapper {
  img {
    object-fit: cover;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

.search-input {
  backdrop-filter: blur(20px);
  background: #111827;
  padding: 2.7rem 0 2.6rem 0;
  display: none;

  @media (max-width: 575.98px) {
    background: #fff;
    padding: 2.7rem 0rem 0;
  }

  &.visible {
    display: block;
  }

  .close-btn {
    border: none;
    background: none;
    padding: 0;
    position: absolute;
    right: 0;
    cursor: pointer;

    img {
      @media (max-width: 575.98px) {
        filter: brightness(0) saturate(100%) invert(0%) sepia(100%) saturate(7500%) hue-rotate(112deg) brightness(109%) contrast(107%);
      }
    }
  }

  label {
    color: #242426;
    font-size: 1.4rem;
    font-weight: 400;
    line-height: 2rem;
    padding-bottom: 1.2rem;
  }

  form {
    &.search-icon {
      &::before {
        content: "";
        position: absolute;
        background-image: url(../../../public/images/search/Magnifier.png);
        width: 2.4rem;
        height: 2.4rem;

        @media (max-width: 575.98px) {
          filter: brightness(0) saturate(100%) invert(0%) sepia(100%) saturate(7500%) hue-rotate(112deg) brightness(109%) contrast(107%);
        }
      }
    }

    input {
      border: none;
      border-bottom: 1px solid #e4e4e4;
      background: none;
      border-radius: 0;
      color: #fff;
      font-size: 1.6rem;
      font-weight: 400;
      line-height: 2.8rem;
      padding: 0 0 1.1rem 4.8rem;

      @media (max-width: 575.98px) {
        color: #242426;
        padding: 0 0 0.4rem 3.2rem;
      }

      &:focus {
        background: none;
        color: #fff;

        @media (max-width: 575.98px) {
          color: #000;
        }
      }

      &::placeholder {
        color: #fff;
        font-size: 1.5rem;
        font-weight: 400;
        line-height: 2.8rem;

        @media (max-width: 575.98px) {
          color: #242426;
        }
      }
    }
  }
}

.avatar {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  overflow: hidden;
  position: relative;
  cursor: pointer;
}

.autoSuggest {
  background-color: #fff;
  padding: 1rem 2rem;
  margin-top: 5px;

  ul {
    li {
      padding: 1rem 0;
      border-bottom: 1px solid #e4e4e4;

      &:has(a:focus) {
        background-color: #f5f5f5;
      }

      &:last-child {
        border-bottom: none;
      }

      a {
        color: #000;
        font-size: 1.6rem;
        font-weight: 400;
        line-height: 2rem;
        // padding: 0;
        padding: 0 10px;
        display: flex;
        align-items: center;
        column-gap: 1.2rem;

        &:focus {
          outline: none;
        }
      }
    }
  }
}

@keyframes ping {

  75%,
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

.app.rtl {
  margin-right: auto;
  margin-left: 0;

  .search-input form input {
    padding: 0 4.8rem 1.1rem 0rem;

    @media (max-width: 575.98px) {
      padding: 0 3.2rem 0.4rem 0rem;
    }
  }

  .search-input .close-btn {
    right: auto;
    left: 0;
  }

  .navbar-toggler-icon {
    transform: rotateY(180deg);
  }
}