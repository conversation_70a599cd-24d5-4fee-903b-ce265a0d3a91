"use client";

import { useEffect } from "react";

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    if (typeof window !== "undefined") {
      window.location.reload();
    }
  }, []);
  return (
    <html>
      <body>
        <div className="d-flex flex-col justify-content-center align-items-center">
          <h2 className="text-center display-6">Something went wrong!</h2>
          <button className="btn btn-outline-primary" onClick={() => reset()}>
            Try again
          </button>
        </div>
      </body>
    </html>
  );
}
