import Link from "next/link";
import "./toast.scss";
import { useContext } from "react";
import { TranslationContext } from "@/contexts/Translation";
type ToasterProps = {
  title: string;
  subTotal?: string;
  link: string;
};

function Toaster({ title, subTotal, link }: ToasterProps) {
  const { translation: { popup } } = useContext(TranslationContext)
  return (
    <div id="toasterPos">
      <div className="toaster">
        <div className="toaster_text">
          <h5> {title}</h5>
          <span>{popup?.basketSubtotal ?? "Basket Subtotal"}: {subTotal}</span>
        </div>
        <Link href={link}>{popup?.viewCart ?? "View Cart"}</Link>
      </div>
    </div>
  );
}

export default Toaster;
