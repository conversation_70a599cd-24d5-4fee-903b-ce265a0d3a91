import React from 'react'
import "./my-cashbacks.scss"

const bannerItems = [
    {
        title: "Total Cashback earned",
        amount: 786,
        icon: "/images/cashbacks/save-money.svg"
    },
    {
        title: "Total Cashback Spent",
        amount: 45,
        icon: "/images/cashbacks/cashback.svg"
    },
    {
        title: "Cashback Balance",
        amount: 310,
        icon: "/images/cashbacks/wallet.svg"
    },
]

const activity = [
    ...[1, 2, 3].map(() => (
        {
            title: "Cashback Spent",
            orderNo: "#YATE-0001",
            date: "05 Apr 2024",
            amountText: "-310 AED",
            expires: "Expires on 05 Apr 2024"
        }
    )),
    ...[1, 2, 3].map(() => (
        {
            title: "Cashback Earned",
            orderNo: "#YATE-0001",
            date: "05 Apr 2024",
            amountText: "+310 AED",
            expires: ""
        }
    ))
]

export default function MyCashbacksLoading() {
    return (
        <div className='my-cashbacks'>
            <div className="skeletonLoader">
                <h2 style={{ visibility: "hidden" }}>My Cashbacks</h2>
            </div>
            <div className="banner skeletonLoader">
                {bannerItems?.map((item: any, key: number) => (
                    <div className="banner-item" key={key}>
                        <div className="skeletonLoader" style={{ width: "60px", height: "60px" }}></div>
                        <div className="skeletonLoader">
                            <p style={{ visibility: "hidden" }}>{item.title}</p>
                        </div>
                        <div className="skeletonLoader">
                            <h4 style={{ visibility: "hidden" }}>AED {item?.amount}</h4>
                        </div>
                    </div>
                ))}
            </div>
            <div className="activity">
                {activity?.map((item: any, key: number) => (
                    <div className="activity-item" key={key}>
                        <div className="activity-item-info skeletonLoader">
                            <div className="skeletonLoader">
                                <h6 style={{ visibility: "hidden" }}>{item.title}</h6>
                            </div>
                            <div className="skeletonLoader">
                                <p style={{ visibility: "hidden" }}>Order no: {item.orderNo} | {item.date}</p>
                            </div>
                        </div>
                        <div className="activity-item-amount skeletonLoader">
                            <div className="skeletonLoader">
                                <h6 style={{ visibility: "hidden" }}>{item.amountText}</h6>
                            </div>
                            <div className="skeletonLoader">
                                <p style={{ visibility: "hidden" }}> {item.expires}</p>
                            </div>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    )
}
